# Open Architecture Framework - Git Ignore Rules
# Created: 2025-06-16 06:35:02 +03
# Authority: President & CEO, E.Z. Consultancy

# ============================================================================
# NODE.JS & NPM
# ============================================================================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.npm
.yarn-integrity
.pnp
.pnp.js

# ============================================================================
# PROJECT DOCUMENTATION & REFERENCE FILES
# ============================================================================
.my-docs/
ref/
docs/tmp/
backup/

# ============================================================================
# TYPESCRIPT COMPILATION
# ============================================================================
*.tsbuildinfo
dist/
build/
lib/
out/

# ============================================================================
# ENVIRONMENT & CONFIGURATION
# ============================================================================
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local
config/local.json
config/production.json

# ============================================================================
# LOGS & DEBUGGING
# ============================================================================
logs/
*.log
*.log.*
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# ============================================================================
# IDE & EDITOR FILES
# ============================================================================
.vscode/settings.json
.vscode/launch.json
.vscode/extensions.json
.idea/
*.swp
*.swo
*~

# ============================================================================
# TESTING & COVERAGE
# ============================================================================
coverage/
.nyc_output
.coverage
*.lcov
test-results/
junit.xml
.jest-cache/

# ============================================================================
# TEMPORARY & CACHE FILES
# ============================================================================
.cache/
.parcel-cache/
.next/
.nuxt/
.tmp/
tmp/
temp/
*.tmp
*.temp

# ============================================================================
# DATABASE FILES
# ============================================================================
*.sqlite
*.sqlite3
*.db
*.db-journal
*.db-wal
*.db-shm

# ============================================================================
# SECURITY & SENSITIVE FILES
# ============================================================================
*.pem
*.key
*.crt
*.p12
*.pfx
secrets/
private/
.secrets
.private

# ============================================================================
# OA FRAMEWORK SPECIFIC
# ============================================================================
# Generated tracking files (will be created during initialization)
.oa-implementation-progress.json
.oa-session-log.jsonl
.oa-governance-log.json
.oa-analytics-cache.json
.oa-smart-path-resolution.json
.oa-cross-reference-validation.json
.oa-authority-compliance.json
.oa-orchestration-coordination.json

# Generated governance files (will be created per standards)
governance/milestones/*/
governance/templates/generated/
governance/archive/

# Generated tracking implementation (will be created during setup)
tracking/src/
tracking/dist/
tracking/data/
tracking/config/generated/

# Generated scripts and utilities
scripts/generated/
utils/generated/

# Dashboard and monitoring files
.dashboard/
monitoring/generated/

# ============================================================================
# PLATFORM SPECIFIC
# ============================================================================
# Windows
*.exe
*.msi
*.msm
*.msp

# macOS
.AppleDouble
.LSOverride
Icon

# Linux
*~

# ============================================================================
# BACKUP & ARCHIVE FILES
# ============================================================================
*.bak
*.backup
*.old
*.orig
*.save
*.swp
*.swo
archive/
backups/

# ============================================================================
# DOCUMENTATION GENERATION
# ============================================================================
docs/generated/
docs/api/
docs/coverage/
typedoc/
jsdoc/

# ============================================================================
# DEPLOYMENT & PRODUCTION
# ============================================================================
.serverless/
.terraform/
*.tfstate
*.tfstate.backup
.terraform.lock.hcl
docker-compose.override.yml
k8s/secrets/
helm/values-*.yaml

# ============================================================================
# KEEP ESSENTIAL FILES
# ============================================================================
# Always keep these OA Framework core files
!docs/core/
!docs/processes/
!docs/tracking/
!docs/plan/
!.cursor/
!IMPLEMENTATION_GUIDE_FOR_NOVICE.md
!README.md 

# Compiled output
/dist/

# Don't track compiled files in source directories
server/src/**/*.js
shared/src/**/*.js  
client/src/**/*.js
server/src/**/*.d.ts
shared/src/**/*.d.ts
client/src/**/*.d.ts
server/src/**/*.js.map
shared/src/**/*.js.map
client/src/**/*.js.map

# Keep test files if they exist
!**/*.test.js
!**/*.spec.js