oa-prod$ cd /home/<USER>/dev/web-dev/oa-prod && npm test -- --coverage --testPathPattern="CleanupCoordinatorEnhanced.test.ts" --collectCoverageFrom="shared/src/base/CleanupCoordinatorEnhanced.ts"

> oa-framework@1.0.0 test
> jest --coverage --testPathPattern=CleanupCoordinatorEnhanced.test.ts --collectCoverageFrom=shared/src/base/CleanupCoordinatorEnhanced.ts

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 PASS  shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts (267 MB heap size)
  CleanupCoordinatorEnhanced - Comprehensive Tests
    Initialization and Delegation
      ✓ should initialize all managers successfully (10 ms)
      ✓ should delegate operations to appropriate managers (3 ms)
      ✓ should delegate health status checks to HealthStatusManager (4 ms)
    Template System Integration
      ✓ should provide template management interface (7 ms)
      ✓ should get registered templates (2 ms)
      ✓ should provide template metrics (4 ms)
    Dependency Resolution Integration
      ✓ should build dependency graphs through delegation (3 ms)
      ✓ should analyze dependencies through delegation (6 ms)
    Rollback System Integration
      ✓ should create checkpoints through delegation (6 ms)
      ✓ should rollback to checkpoints through delegation (4 ms)
      ✓ should validate rollback capability through delegation (3 ms)
    Operation Execution Integration
      ✓ should schedule and process operations through delegation (3 ms)
      ✓ should cancel operations through delegation (3 ms)
      ✓ should wait for completion through delegation (6 ms)
    Enhanced Cleanup Integration
      ✓ should provide enhanced cleanup interface (3 ms)
      ✓ should perform enhanced cleanup with rollback capability (4 ms)
    Metrics and Monitoring Integration
      ✓ should provide enhanced metrics through delegation (2 ms)
      ✓ should perform health checks through delegation (2 ms)
    Component Registry Integration
      ✓ should register and manage components through delegation (2 ms)
      ✓ should unregister components through delegation (2 ms)
    Factory Functions and Backward Compatibility
      ✓ should create enhanced cleanup coordinator via factory function (2 ms)
      ✓ should get enhanced cleanup coordinator via getter function (2 ms)
      ✓ should maintain backward compatibility with base CleanupCoordinator (2 ms)
    Error Handling and Edge Cases
      ✓ should handle template execution with non-existent template (68 ms)
      ✓ should handle rollback with non-existent checkpoint (7 ms)
      ✓ should handle rollback with no checkpoints for operation (2 ms)
      ✓ should handle disabled rollback system (3 ms)
    Core Operation Lifecycle - Comprehensive Coverage
      ✓ should handle operation scheduling with all configuration options (8 ms)
      ✓ should handle operation cancellation in different states (3 ms)
      ✓ should handle operation status queries for various states (4 ms)
    Metrics and Monitoring - Comprehensive Coverage
      ✓ should provide comprehensive metrics with all fields (2 ms)
      ✓ should provide enhanced metrics with modular component data (2 ms)
      ✓ should update metrics manually in test mode (2 ms)
    Component Registry Management - Comprehensive Coverage
      ✓ should register and manage components with various operations (2 ms)
      ✓ should handle component unregistration gracefully (3 ms)
    Error Handling and Edge Cases - Comprehensive Coverage
      ✓ should handle template execution errors with enhanced error context (13 ms)
      ✓ should handle checkpoint creation errors gracefully (2 ms)
      ✓ should handle rollback operations for non-existent checkpoints (2 ms)
      ✓ should handle rollback operations for non-existent operations (2 ms)
      ✓ should handle template rollback for non-existent executions (2 ms)
    Enhanced Cleanup Operations - Comprehensive Coverage
      ✓ should perform enhanced cleanup with template execution (2 ms)
      ✓ should perform enhanced cleanup with fallback to standard cleanup (2 ms)
      ✓ should handle enhanced cleanup with rollback on failure (2 ms)
      ✓ should skip checkpoint creation when requested (2 ms)
    Singleton Pattern and Factory Functions - Comprehensive Coverage
      ✓ should create and manage singleton instance (2 ms)
      ✓ should reset singleton instance properly (2 ms)
      ✓ should handle factory function getCleanupCoordinator (2 ms)
      ✓ should handle factory function resetCleanupCoordinator (8 ms)
      ✓ should handle enhanced coordinator factory functions (1 ms)
    Timing Infrastructure and Performance - Comprehensive Coverage
      ✓ should provide timing metrics (2 ms)
      ✓ should clear timing metrics (2 ms)
      ✓ should provide timing reliability metrics (2 ms)
      ✓ should start queue processing (2 ms)
    System Health and Diagnostics - Comprehensive Coverage
      ✓ should perform comprehensive health check (2 ms)
      ✓ should provide system diagnostics (3 ms)
      ✓ should provide module status (1 ms)
      ✓ should provide health status (1 ms)
      ✓ should reset to operational state (3 ms)
      ✓ should check health status in test mode (2 ms)
      ✓ should get system status (2 ms)
      ✓ should perform system health check (2 ms)
      ✓ should create system snapshot (1 ms)
    Dependency Analysis and Optimization - Comprehensive Coverage
      ✓ should build dependency graph from operations (1 ms)
      ✓ should analyze dependencies for operations (1 ms)
      ✓ should optimize operation order (1 ms)
      ✓ should handle circular dependencies in optimization (2 ms)
    Advanced Operation Execution - Comprehensive Coverage
      ✓ should handle operation execution with retries (2 ms)
      ✓ should handle operation cancellation of running operations (2 ms)
      ✓ should handle wait for completion without operation ID (2 ms)
      ✓ should handle operation execution in non-test mode (5 ms)
    Logging and Error Context Enhancement - Comprehensive Coverage
      ✓ should handle all logging methods (2 ms)
      ✓ should handle error context enhancement with various error types (2 ms)
    Configuration Edge Cases - Comprehensive Coverage
      ✓ should handle coordinator with minimal configuration (1 ms)
      ✓ should handle coordinator with maximum configuration (2 ms)
      ✓ should handle disabled features gracefully (2 ms)
    Shutdown and Cleanup Edge Cases - Comprehensive Coverage
      ✓ should handle shutdown with pending operations (2 ms)
      ✓ should handle shutdown errors gracefully (2 ms)
      ✓ should handle multiple shutdown calls (2 ms)
    Memory Safety and Resource Management - Comprehensive Coverage
      ✓ should handle memory pressure scenarios (2 ms)
      ✓ should handle resource cleanup during high load (2 ms)
    Targeted Coverage for Remaining Lines
      ✓ should handle timing infrastructure errors during shutdown (2 ms)
      ✓ should handle template execution registration errors (2 ms)
      ✓ should handle checkpoint creation with timing errors (5 ms)
      ✓ should handle enhanced cleanup with template execution failure (2 ms)
      ✓ should handle operation cancellation of running operations (1 ms)
      ✓ should handle health check in non-test mode (1 ms)
      ✓ should handle auto-start queue processing in non-test mode (3 ms)
    Surgical Precision Tests for 100% Line Coverage
      ✓ should execute cleanupCheckpoints method (Line 505) (2 ms)
      ✓ should throw error on template execution failure (Line 540) (2 ms)
      ✓ should execute health check callback in system diagnostics (Line 741) (2 ms)
      ✓ should call parent isHealthy() in non-test mode (Line 772) (2 ms)
      ✓ should execute health check callback in getHealthStatus (Line 741) (2 ms)
      ✓ should call parent isHealthy() when not in test mode (Line 772) (2 ms)
      ✓ should trigger line 772 by forcing parent isHealthy call (3 ms)
      ✓ should trigger rollback error logging with direct method call (Line 563) (7 ms)
      ✓ should trigger error logging in _startQueueProcessing async IIFE (Line 1028) (4 ms)

-------------------------------|---------|----------|---------|---------|---------------------------------------------------------------
File                           | % Stmts | % Branch | % Funcs | % Lines | Uncovered Line #s                                             
-------------------------------|---------|----------|---------|---------|---------------------------------------------------------------
All files                      |     100 |    79.43 |   98.61 |     100 |                                                               
 CleanupCoordinatorEnhanced.ts |     100 |    79.43 |   98.61 |     100 | 222,239,326,342,393-522,535,549-550,567,690-691,764,1083-1091 
-------------------------------|---------|----------|---------|---------|---------------------------------------------------------------
Test Suites: 1 passed, 1 total
Tests:       96 passed, 96 total
Snapshots:   0 total
Time:        2.868 s, estimated 7 s
Ran all test suites matching /CleanupCoordinatorEnhanced.test.ts/i.