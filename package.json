{"name": "oa-framework", "version": "1.0.0", "description": "Open Architecture Framework - Enterprise-grade component tracking and governance system", "main": "server/src/index.ts", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:implementation-tracker": "jest tests/platform/tracking/core-data/ImplementationProgressTracker", "test:T-TSK-01": "jest tests/platform/tracking/core-data/", "test:T-TSK-02": "jest tests/platform/tracking/core-trackers/", "test:G-TSK-01": "jest tests/platform/governance/rule-management/core/", "test:G-TSK-02": "jest tests/platform/governance/rule-management/", "test:m0-components": "jest tests/platform/", "test:performance": "NODE_OPTIONS='--max-old-space-size=4096 --expose-gc' TEST_TYPE=performance jest --runInBand --testPathPattern=\"GovernanceTrackingSystem.performance.test.ts\" --verbose", "test:governance:isolated": "node --expose-gc --max-old-space-size=4096 node_modules/.bin/jest --testPathPattern='GovernanceTrackingSystem.*\\.test\\.ts$' --runInBand --no-cache --maxWorkers=1 --logHeapUsage --verbose", "test:governance:debug": "node --expose-gc --max-old-space-size=4096 node_modules/.bin/jest --testPathPattern='GovernanceTrackingSystem.*\\.test\\.ts$' --runInBand --no-cache --maxWorkers=1 --detectOpenHandles --forceExit --verbose", "test:unit": "jest --testPathPattern='GovernanceTrackingSystem\\.test\\.ts$' --runInBand --no-cache", "test:integration": "jest --testPathPattern='GovernanceTrackingSystem\\.integration\\.test\\.ts$' --runInBand --no-cache", "test:security": "jest --testPathPattern='GovernanceTrackingSystem\\.security\\.test\\.ts$' --runInBand --no-cache", "build": "npm run clean && tsc", "build:watch": "tsc --watch", "build:production": "npm run clean && npm run lint && tsc --project tsconfig.json", "clean": "<PERSON><PERSON><PERSON> dist", "clean:full": "node scripts/clean-build.js", "build:info": "node scripts/clean-build.js --info", "clean:build": "npm run clean && npm run build", "dev": "ts-node server/src/index.ts", "dev:watch": "ts-node --watch server/src/index.ts", "start": "node dist/server/src/index.js", "start:prod": "NODE_ENV=production node dist/server/src/index.js", "lint": "eslint . --ext .ts,.tsx", "lint:fix": "eslint . --ext .ts,.tsx --fix", "postbuild": "echo '✅ Build completed successfully! Output in ./dist directory'"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "^18.15.0", "@typescript-eslint/eslint-plugin": "^5.57.0", "@typescript-eslint/parser": "^5.57.0", "eslint": "^8.37.0", "jest": "^29.7.0", "rimraf": "^6.0.1", "ts-jest": "^29.4.0", "ts-node": "^10.9.0", "typescript": "^5.0.0"}, "dependencies": {"inversify": "^7.5.4", "lodash": "^4.17.21", "reflect-metadata": "^0.2.2", "uuid": "^9.0.0"}, "keywords": ["framework", "tracking", "governance", "enterprise", "typescript", "architecture"], "author": "E.Z. Consultancy", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/ez-consultancy/oa-framework.git"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}