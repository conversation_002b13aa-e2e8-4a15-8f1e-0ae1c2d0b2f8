#!/bin/bash

# --- Help Function ---
display_help() {
    echo "Usage: $0 [directory_name | -h | --help]"
    echo ""
    echo "Description: Archives TypeScript (.ts, .tsx) files into two categories: "
    echo "             'oa-tests-archive.txt' and 'oa-implementation-archive.txt'."
    echo ""
    echo "Parameters:"
    echo "  [directory_name]  (Optional) Specify a single directory to archive. If provided,"
    echo "                    only files within this directory and its subdirectories will"
    echo "                    be processed. The directory must exist."
    echo "  -h, --help        Display this help message and exit."
    echo ""
    echo "Examples:"
    echo "  $0"
    echo "      # Archives default directories: server, shared, client"
    echo ""
    echo "  $0 client"
    echo "      # Archives only the 'client' directory"
    echo ""
    echo "  $0 shared/src/base/utils"
    echo "      # Archives only the 'shared/src/base/utils' directory"
    echo ""
    echo "Prerequisites:"
    echo "  - Bash shell"
    echo "  - 'find' utility"
    echo "  - 'cat' utility"
    echo "  - <PERSON><PERSON>t must have execute permissions (chmod +x create-archive.sh)"
    echo "  - Directories 'server', 'shared', 'client' (or specified directory) must exist."
    echo ""
    exit 0
}

# --- Parameter Handling for Help ---
if [[ "$1" == "--help" || "$1" == "-h" ]]; then
    display_help
fi

# Initialize counters
test_count=0
implementation_count=0

# Define default directories
default_dirs=("server" "shared" "client")

# Handle command-line argument
if [ -n "$1" ]; then
    target_dirs=("$1")
    # Validate if the provided directory exists
    if [ ! -d "${target_dirs[0]}" ]; then
        echo "Error: Directory '${target_dirs[0]}' not found."
        echo "Usage: $0 [directory_name]" 
        exit 1
    fi
else
    target_dirs=("${default_dirs[@]}")
fi

# Create/clear the archive files
echo "Creating TypeScript archives..."
echo "" > oa-tests-archive.txt
echo "" > oa-implementation-archive.txt

# Function to check if a file is a test file
is_test_file() {
    local file="$1"
    # Check if file matches test patterns
    if [[ "$file" == *".test.ts" ]] || [[ "$file" == *".test.tsx" ]] || \
       [[ "$file" == *".spec.ts" ]] || [[ "$file" == *".spec.tsx" ]] || \
       [[ "$file" == *"__tests__"* ]] || [[ "$file" == *"/test/"* ]] || \
       [[ "$file" == *"/tests/"* ]]; then
        return 0  # true
    else
        return 1  # false
    fi
}

# Function to archive a file to the appropriate archive
archive_file() {
    local file="$1"
    local archive_file="$2"
    
    echo "" >> "$archive_file"
    echo "============================================" >> "$archive_file"
    echo "File: $file" >> "$archive_file"
    echo "============================================" >> "$archive_file"
    echo "" >> "$archive_file"
    cat "$file" >> "$archive_file"
    echo "" >> "$archive_file"
    echo "" >> "$archive_file"
}

# Process each directory
for dir in "${target_dirs[@]}"; do
    if [ -d "$dir" ]; then
        echo "Processing $dir directory..."
        
        # Find all TypeScript files in the directory
        find "$dir" -name "*.ts" -o -name "*.tsx" | while read -r file; do
            if is_test_file "$file"; then
                archive_file "$file" "oa-tests-archive.txt"
            else
                archive_file "$file" "oa-implementation-archive.txt"
            fi
        done
    else
        # This else branch should ideally not be reached due to validation above
        # but kept for robustness in case of default_dirs issues.
        echo "Warning: $dir directory not found (during processing loop)"
    fi
done

# Count files for summary
test_count=0
implementation_count=0
for dir in "${target_dirs[@]}"; do
    if [ -d "$dir" ]; then
        # Count test files
        while read -r file; do
            if is_test_file "$file"; then
                ((test_count++))
            else
                ((implementation_count++))
            fi
        done < <(find "$dir" -name "*.ts" -o -name "*.tsx")
    fi
done

echo ""
echo "Archives created successfully:"
echo "- Test files: oa-tests-archive.txt"
echo "- Implementation files: oa-implementation-archive.txt"
echo ""

# Display summary
total_count=$((test_count + implementation_count))

echo "Summary:"
echo "Test files: $test_count"
echo "Implementation files: $implementation_count"
echo "Total files archived: $total_count"

echo ""
echo "Test file patterns detected:"
echo "- *.test.ts, *.test.tsx"
echo "- *.spec.ts, *.spec.tsx" 
echo "- Files in __tests__ directories"
echo "- Files in test/tests directories"
