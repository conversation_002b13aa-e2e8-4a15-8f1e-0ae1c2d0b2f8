#!/bin/bash
# detect-inheritance-conflicts.sh

echo "🔍 Detecting inheritance conflicts..."

SERVICES=(
  "server/src/platform/tracking/core-trackers/AnalyticsTrackingEngine.ts"
  "server/src/platform/tracking/analytics/AnalyticsCacheManager.ts"
  "server/src/platform/tracking/core-managers/RealTimeManager.ts"
  "server/src/platform/tracking/core-managers/TrackingManager.ts"
  "server/src/platform/tracking/core-managers/FileManager.ts"
  "server/src/platform/tracking/core-managers/DashboardManager.ts"
  "server/src/platform/tracking/core-data/SessionLogTracker.ts"
  "server/src/platform/tracking/core-trackers/SessionTrackingCore.ts"
)

echo "📊 Compilation Results:"
for service in "${SERVICES[@]}"; do
  echo "Testing: $service"
  if npx tsc --noEmit "$service" 2>/dev/null; then
    echo "✅ PASSED: $service"
  else
    echo "❌ FAILED: $service"
    npx tsc --noEmit "$service" 2>&1 | grep -E "(error TS|Property.*private|incorrectly extends)"
  fi
  echo "---"
done