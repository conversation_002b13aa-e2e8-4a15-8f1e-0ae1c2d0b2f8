#!/bin/bash

# --- Help Function ---
display_help() {
    echo "Usage: $0 [-o <output_file>] [target_directory ...] | -h | --help"
    echo ""
    echo "Description: Generates JSON indices of directory trees using the 'tree' command."
    echo "             The script recursively navigates through all subdirectories."
    echo "             When multiple directories are specified, their tree structures are"
    echo "             consolidated into a single JSON array."
    echo ""
    echo "Parameters:"
    echo "  -o <output_file>  (Optional) Specify the output JSON file name."
    echo "                    If omitted, output goes to 'repo-index.json'."
    echo "  [target_directory](Optional) One or more starting directories for the tree generation."
    echo "                    If no directories are specified, the current directory ('.') is used."
    echo "  -h, --help        Display this help message and exit."
    echo ""
    echo "Examples:"
    echo "  $0"
    echo "      # Generates 'repo-index.json' for the current directory (full depth)"
    echo ""
    echo "  $0 -o my_custom_index.json"
    echo "      # Generates 'my_custom_index.json' for the current directory (full depth)"
    echo ""
    echo "  $0 client server"
    echo "      # Generates 'repo-index.json' containing trees of 'client' and 'server'"
    echo ""
    echo "  $0 -o my-sources.json client server shared/src/base/utils"
    echo "      # Generates 'my-sources.json' containing trees of all specified directories"
    echo ""
    echo "Prerequisites:"
    echo "  - Bash shell"
    echo "  - 'tree' utility must be installed and available in PATH"
    echo "  - 'jq' utility must be installed and available in PATH (for JSON consolidation)"
    echo "  - Script must have execute permissions (chmod +x tree-index.sh)"
    echo "  - All specified target directories must exist."
    echo ""
    exit 0
}

# --- Initialize Defaults ---
DEFAULT_OUTPUT_FILE="repo-index.json"
OUTPUT_FILE="$DEFAULT_OUTPUT_FILE"
TARGET_DIRS=()

# --- Parameter Handling ---
while getopts ":o:h" opt; do
    case ${opt} in
        o )
            OUTPUT_FILE="$OPTARG"
            ;;
        h )
            display_help
            ;;
        \? )
            echo "Error: Invalid option '-$OPTARG'." >&2
            display_help
            ;;
        : )
            echo "Error: Option '-$OPTARG' requires an argument." >&2
            display_help
            ;;
    esac
done
shift $((OPTIND -1))

# Handle optional target directory arguments
if [ $# -eq 0 ]; then
    TARGET_DIRS=(".")
else
    TARGET_DIRS=($@)
fi

# --- Input Validation ---
# Check if 'tree' command exists
if ! command -v tree &> /dev/null; then
    echo "Error: 'tree' command not found. Please install it to use this script." >&2
    exit 1
fi

# Check if 'jq' command exists for JSON processing
if ! command -v jq &> /dev/null; then
    echo "Error: 'jq' command not found. Please install it to use this script (e.g., sudo apt-get install jq)." >&2
    exit 1
fi

# Validate if all target directories exist
ALL_DIRS_EXIST=true
for dir in "${TARGET_DIRS[@]}"; do
    if [ ! -d "$dir" ]; then
        echo "Error: Target directory '$dir' not found." >&2
        ALL_DIRS_EXIST=false
    fi
done

if [ "$ALL_DIRS_EXIST" == "false" ]; then
    exit 1
fi

# --- Main Logic ---
TEMP_JSON_FILES=()
SUCCESS_COUNT=0
FAILURE_COUNT=0

for TARGET_DIR_ITEM in "${TARGET_DIRS[@]}"; do
    echo "Generating temporary JSON for '$TARGET_DIR_ITEM'..."
    TEMP_FILE=$(mktemp)
    if tree -J "$TARGET_DIR_ITEM" > "$TEMP_FILE"; then
        TEMP_JSON_FILES+=("$TEMP_FILE")
        SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
    else
        echo "Error: Failed to generate tree index for '$TARGET_DIR_ITEM'. Skipping." >&2
        FAILURE_COUNT=$((FAILURE_COUNT + 1))
        rm -f "$TEMP_FILE" # Clean up temp file if generation failed
    fi
done

# Check if any JSON was successfully generated
if [ ${#TEMP_JSON_FILES[@]} -eq 0 ]; then
    echo "Error: No valid directory trees could be generated. Exiting." >&2
    exit 1
fi

echo "Consolidating JSON outputs into '$OUTPUT_FILE'..."

# Consolidate all temporary JSON files into one using jq
# tree -J outputs an array [ { ... } ], so 'add' will concatenate these arrays
jq -s 'add' "${TEMP_JSON_FILES[@]}" > "$OUTPUT_FILE"

# Clean up temporary files
rm "${TEMP_JSON_FILES[@]}"

if [ $? -eq 0 ]; then
    echo "Successfully generated consolidated tree index to '$OUTPUT_FILE'."
    exit 0
else
    echo "Error: Failed to consolidate tree indices." >&2
    exit 1
fi
