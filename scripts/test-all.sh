# Test Run 1
echo "=== TEST RUN 1 - $(date) ===" > test-run-1.txt
npm test -- --testPathPattern="shared/src/base/__tests__" --verbose 2>&1 >> test-run-1.txt

# Test Run 2  
echo "=== TEST RUN 2 - $(date) ===" > test-run-2.txt
npm test -- --testPathPattern="shared/src/base/__tests__" --verbose 2>&1 >> test-run-2.txt

# Test Run 3
echo "=== TEST RUN 3 - $(date) ===" > test-run-3.txt
npm test -- --testPathPattern="shared/src/base/__tests__" --verbose 2>&1 >> test-run-3.txt

