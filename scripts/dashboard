#!/bin/bash

# OA Framework Dashboard Wrapper Script
# Author: AI Assistant (E.Z. Consultancy)
# Created: 2025-06-12 08:27:00 +03
# Authority: President & CEO, E<PERSON>Z. Consultancy

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DASHBOARD_DIR="$SCRIPT_DIR/.dashboard"

# Check if .dashboard directory exists
if [[ ! -d "$DASHBOARD_DIR" ]]; then
    echo "❌ Error: .dashboard directory not found"
    echo "Expected location: $DASHBOARD_DIR"
    exit 1
fi

# Check if start-dashboard.sh exists
if [[ ! -f "$DASHBOARD_DIR/start-dashboard.sh" ]]; then
    echo "❌ Error: start-dashboard.sh not found in .dashboard directory"
    exit 1
fi

# Change to project root and run the dashboard script
cd "$SCRIPT_DIR"
exec "$DASHBOARD_DIR/start-dashboard.sh" "$@" 