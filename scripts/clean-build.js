#!/usr/bin/env node

/**
 * @file Clean Build Script
 * @filepath scripts/clean-build.js
 * @description Utility script for clean builds and artifact management
 * @created 2025-07-12
 * @authority President & CEO, E.Z. Consultancy
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * Remove directory recursively
 * @param {string} dirPath - Path to directory to remove
 */
function removeDir(dirPath) {
  if (fs.existsSync(dirPath)) {
    fs.rmSync(dirPath, { recursive: true, force: true });
    console.log(`✅ Removed: ${dirPath}`);
  } else {
    console.log(`ℹ️  Directory not found: ${dirPath}`);
  }
}

/**
 * Clean build artifacts
 */
function cleanBuildArtifacts() {
  console.log('🧹 Cleaning build artifacts...');
  
  const projectRoot = path.join(__dirname, '..');
  
  // Remove dist directory
  removeDir(path.join(projectRoot, 'dist'));
  
  // Remove any .js files in source directories (optional cleanup)
  const sourceDirs = ['server', 'client', 'shared'];
  sourceDirs.forEach(dir => {
    const dirPath = path.join(projectRoot, dir);
    if (fs.existsSync(dirPath)) {
      console.log(`🔍 Cleaning ${dir} directory...`);
      try {
        // Remove .js, .d.ts, and .js.map files from source directories
        execSync(`find "${dirPath}" -name "*.js" -not -path "*/node_modules/*" -not -name "*.test.js" -not -name "*.spec.js" -delete`, { stdio: 'inherit' });
        execSync(`find "${dirPath}" -name "*.d.ts" -not -path "*/node_modules/*" -delete`, { stdio: 'inherit' });
        execSync(`find "${dirPath}" -name "*.js.map" -not -path "*/node_modules/*" -delete`, { stdio: 'inherit' });
      } catch (error) {
        console.log(`⚠️  Warning: Could not clean ${dir} directory: ${error.message}`);
      }
    }
  });
  
  console.log('✨ Clean complete!');
}

/**
 * Display build information
 */
function displayBuildInfo() {
  console.log('\n📊 Build Configuration:');
  console.log('├── Output Directory: ./dist');
  console.log('├── Server Output: ./dist/server/src/');
  console.log('├── Shared Output: ./dist/shared/src/');
  console.log('├── Client Output: ./dist/client/src/ (if exists)');
  console.log('└── Source Maps: Enabled (.js.map files)');
  console.log('└── Type Declarations: Enabled (.d.ts files)');
  
  console.log('\n🚀 Available Scripts:');
  console.log('├── npm run build        - Clean build with TypeScript compilation');
  console.log('├── npm run build:watch  - Watch mode for development');
  console.log('├── npm run clean        - Remove dist directory only');
  console.log('├── npm run clean:build  - Full clean + build');
  console.log('├── npm start            - Run compiled application');
  console.log('└── npm run dev          - Run in development mode with ts-node');
}

/**
 * Main function
 */
function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log('🛠️  Clean Build Script');
    console.log('Usage: node scripts/clean-build.js [options]');
    console.log('');
    console.log('Options:');
    console.log('  --info, -i    Display build configuration information');
    console.log('  --help, -h    Show this help message');
    console.log('');
    console.log('Default: Clean build artifacts');
    return;
  }
  
  if (args.includes('--info') || args.includes('-i')) {
    displayBuildInfo();
    return;
  }
  
  cleanBuildArtifacts();
}

if (require.main === module) {
  main();
}

module.exports = { 
  removeDir, 
  cleanBuildArtifacts, 
  displayBuildInfo, 
  main 
};
