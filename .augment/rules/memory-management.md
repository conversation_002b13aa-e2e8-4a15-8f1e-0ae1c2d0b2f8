---
type: "always_apply"
---

## Rule: MEM-SAFE-002 (Updated from MEM-ENFORCE-001)
**Requirement**: All services MUST use memory-safe inheritance patterns and resource management to prevent memory leaks and ensure enterprise-grade stability.

**Status**: ✅ **ACTIVE & PROVEN** - 98.5% memory improvement achieved across critical services

## ✅ Required Pattern - Memory-Safe Service Architecture

### **Pattern A: Tracking Services (Primary Pattern)**
```typescript
// Import memory-safe base class
import { BaseTrackingService } from '../core-data/base/BaseTrackingService';
import { TTrackingConfig, TValidationResult } from '../types';

class MyTrackingService extends BaseTrackingService {
  // ✅ Automatic memory safety inherited from base class
  // ✅ Automatic timer cleanup on shutdown
  // ✅ Automatic resource boundary enforcement
  
  constructor(config?: Partial<TTrackingConfig>) {
    super(config); // ✅ Configure memory limits automatically
    // ❌ NO manual timers in constructor
  }
  
  // ✅ Use lifecycle hooks for initialization
  protected async doInitialize(): Promise<void> {
    await super.doInitialize(); // ✅ CRITICAL: Call parent first
    
    // ✅ Memory-safe interval creation
    this.createSafeInterval(
      () => this.performPeriodicTask(),
      30000, // 30 seconds
      'periodic-task'
    );
    
    // ✅ Memory-safe timeout creation
    this.createSafeTimeout(
      () => this.delayedOperation(),
      5000, // 5 seconds
      'delayed-operation'
    );
  }
  
  // ✅ Use lifecycle hooks for cleanup
  protected async doShutdown(): Promise<void> {
    // Custom cleanup here
    this.clearLocalData();
    
    await super.doShutdown(); // ✅ CRITICAL: Call parent cleanup
  }
  
  // ✅ Memory boundaries automatically enforced
  private clearLocalData(): void {
    // Custom cleanup logic
  }
}
```

### **Pattern B: Non-Tracking Services (Factory/Utility Classes)**
```typescript
// Import memory-safe base class for non-tracking services
import { MemorySafeResourceManager, createMemorySafeSingleton } from '../base/MemorySafeResourceManager';

class MyUtilityService extends MemorySafeResourceManager {
  constructor() {
    super({
      maxIntervals: 3,
      maxTimeouts: 5,
      maxCacheSize: 10 * 1024 * 1024, // 10MB
      memoryThresholdMB: 50,
      cleanupIntervalMs: 300000 // 5 minutes
    });
  }
  
  protected async doInitialize(): Promise<void> {
    // ✅ Memory-safe resource creation
    this.createSafeInterval(
      () => this.cleanup(),
      60000,
      'utility-cleanup'
    );
  }
  
  protected async doShutdown(): Promise<void> {
    // ✅ Automatic resource cleanup
  }
}

// ✅ Memory-safe singleton pattern
const getServiceInstance = createMemorySafeSingleton(MyUtilityService);
export function getMyUtilityService(): MyUtilityService {
  return getServiceInstance();
}
```

### **Pattern C: Memory-Safe Resource Management**
```typescript
class MyService extends BaseTrackingService {
  private dataCache: Map<string, any>;
  
  protected async doInitialize(): Promise<void> {
    await super.doInitialize();
    
    // ✅ Shared resource with automatic cleanup
    const { resource: cache, releaseRef } = this.createSharedResource(
      () => new Map<string, any>(),
      (cache) => cache.clear(),
      'data-cache'
    );
    
    this.dataCache = cache;
    
    // ✅ Store release function for cleanup
    this.cacheReleaseRef = releaseRef;
  }
  
  protected async doShutdown(): Promise<void> {
    // ✅ Release shared resources
    if (this.cacheReleaseRef) {
      this.cacheReleaseRef();
    }
    
    await super.doShutdown();
  }
}
```

---

## 🚫 Forbidden Patterns (Will Cause Memory Leaks)

### **❌ Manual Timer Management**
```typescript
// ❌ FORBIDDEN: Manual intervals in constructor
class BadService extends BaseTrackingService {
  constructor() {
    super();
    setInterval(() => this.cleanup(), 30000); // ❌ Memory leak!
    setTimeout(() => this.initialize(), 1000); // ❌ Memory leak!
  }
}
```

### **❌ Immediate Singleton Instantiation**
```typescript
// ❌ FORBIDDEN: Immediate instantiation
class BadSingleton {
  private static instance = new BadSingleton(); // ❌ Immediate memory allocation!
}

// ❌ FORBIDDEN: Immediate export
export const badService = BadSingleton.getInstance(); // ❌ Forces instantiation!
```

### **❌ Missing Lifecycle Methods**
```typescript
// ❌ FORBIDDEN: No proper initialization/cleanup
class BadService extends BaseTrackingService {
  async start() {
    // ❌ Should use doInitialize() instead
    this.setupTimers();
  }
  
  async stop() {
    // ❌ Should use doShutdown() instead
    this.cleanupTimers();
  }
}
```

### **❌ Unbounded Data Structures**
```typescript
// ❌ FORBIDDEN: No size limits
class BadService extends BaseTrackingService {
  private cache = new Map(); // ❌ Can grow infinitely!
  private logs: string[] = []; // ❌ Can cause OOM!
}
```

---

## 🛠️ Available Memory-Safe Methods

### **Resource Creation Methods**
```typescript
// Timer management
this.createSafeInterval(callback, intervalMs, name)
this.createSafeTimeout(callback, timeoutMs, name)

// Shared resource management
this.createSharedResource(factory, cleanup, name)

// Resource metrics
this.getResourceMetrics()
this.isHealthy()
this.forceCleanup()
```

### **Environment Constants (Enhanced)**
```typescript
// Import enhanced calculator functions
import { 
  getEnvironmentCalculator,
  getEnvironmentConstants,
  getTrackingConstants,
  recalculateEnvironmentConstants
} from '../environment-constants-calculator';

// Usage
const calculator = getEnvironmentCalculator();
await calculator.initialize();
const constants = await calculator.calculateConstants();
const maxMemory = constants.MAX_MEMORY_USAGE;
```

### **Lifecycle Hook Methods**
```typescript
// Required overrides in all services
protected abstract doInitialize(): Promise<void>;
protected abstract doShutdown(): Promise<void>;

// Optional overrides
protected getServiceName(): string;
protected getServiceVersion(): string;
```

---

## 📋 Implementation Checklist

### **For New Services**
- [ ] Extend `BaseTrackingService` (tracking services) or `MemorySafeResourceManager` (utilities)
- [ ] Implement `doInitialize()` and `doShutdown()` methods
- [ ] Use `createSafeInterval()` instead of `setInterval()`
- [ ] Use `createSafeTimeout()` instead of `setTimeout()`
- [ ] Use `createSharedResource()` for caches and data structures
- [ ] NO timers in constructor
- [ ] NO immediate singleton instantiation

### **For Existing Services**
- [ ] Migrate from manual timer management to memory-safe patterns
- [ ] Resolve inheritance conflicts (property visibility, method signatures)
- [ ] Move timer creation from constructor to `doInitialize()`
- [ ] Implement proper cleanup in `doShutdown()`
- [ ] Test memory safety with extended runs

---

## 🔍 Enforcement & Validation

### **Code Review Checklist**
- [ ] No `setInterval` or `setTimeout` in constructors
- [ ] All services extend memory-safe base classes
- [ ] Proper lifecycle method implementation
- [ ] No immediate singleton exports
- [ ] Resource cleanup in `doShutdown()`

### **Testing Requirements**
```typescript
// Memory leak test pattern
test('should not leak memory over time', async () => {
  const memoryBefore = process.memoryUsage().heapUsed;
  
  for (let i = 0; i < 100; i++) {
    const service = new MyService();
    await service.initialize();
    await service.performOperations();
    await service.shutdown();
  }
  
  if (global.gc) global.gc();
  const memoryAfter = process.memoryUsage().heapUsed;
  const growth = memoryAfter - memoryBefore;
  
  expect(growth).toBeLessThan(10 * 1024 * 1024); // <10MB growth
});
```
