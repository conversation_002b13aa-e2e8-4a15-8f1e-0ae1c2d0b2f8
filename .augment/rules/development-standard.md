---
description: 
globs: 
alwaysApply: true
type: "always_apply"
---
# CRITICAL NOTE:  MISTAKES MEANS EITHER VIOLATION OR CRITICAL STRATEGIC BOMB SHELL LEADS TO OPEN ARCHETICTURE FRAMEWORK PROJECT CRISES , NEEDS TO AVOID THE CRITICAL OF REI-NVENTING THE WHEEL BECAUSE OF SMALL MISTAKE DONE DURING EARLIER MILESTONES WERE WE BUILD UPON, MAKE SURE YOU THOROUGHLY ANALYZE YOUR STEPS BEFORE IMPLEMENTING THE TASKS, SUGGESTING SOLUTIONS, OR SOLVING AN ISSUE. THIS IS NOT NEGOTIATABLE! 

# IMPORTANT: THE OPEN ARCHITECTURE FRAMEWORK PROJECT (OAF) IS A SOLO PROJECT PLUS AI ASSISTANT, THEREFORE, ALL PROCESSES RELATED TO DOCUMENTATIONS IMPLEMENTATION SHOULD CONSIDER THIS

# OA Framework File Size Enforcement Rules - Solo + AI Development

**Authority**: President & CEO, <PERSON><PERSON><PERSON><PERSON> Consultancy  
**Status**: <PERSON><PERSON><PERSON><PERSON><PERSON> ENFORCEMENT  
**Compliance**: CONTEXT-AWARE TOLERANCE  
**Development Context**: Solo Developer + AI Assistant  
**Effective Date**: 2025-06-27  

---

## 🎯 **DEVELOPMENT CONTEXT**

This document establishes file size and complexity rules optimized for **solo developer + AI assistant** workflows, replacing team-based constraints with AI-optimized guidelines that balance maintainability with development velocity.

### **Key Differences from Team Development**
- **No merge conflicts** = larger files acceptable
- **AI context limitations** = new primary constraint
- **Solo cognitive load** = different optimization targets
- **Future handover consideration** = long-term maintainability

---

## 📏 **REVISED FILE SIZE LIMITS**

| Metric | Target | Warning Threshold | Critical Threshold | Action Required |
|--------|---------|-------------------|-------------------|-----------------|
| **Lines per File** | ≤ 700 | ≤ 1400 | ≤ 2300 | IMMEDIATE REFACTOR |
| **File Size** | ≤ 20KB | ≤ 50KB | ≤ 75KB | IMMEDIATE REFACTOR |
| **AI Context Chunks** | ≤ 6 sections | ≤ 10 sections | ≤ 12 sections | RESTRUCTURE |
| **Logical Sections** | ≤ 5 domains | ≤ 8 domains | ≤ 10 domains | SPLIT REQUIRED |

---

## 🏗️ **RELAXED STRUCTURE LIMITS**

| Element | Team Rule | Solo + AI Rule | Enforcement | AI Optimization |
|---------|-----------|----------------|-------------|-----------------|
| **Classes per File** | 1 + 2 helper | 1 + 4 helper | FLEXIBLE | Document relationships |
| **Interfaces per File** | 1 + 5 related | 1 + 10 related | FLEXIBLE | Group by domain |
| **Methods per Class** | 15 | 25 | MONITOR | AI navigation friendly |
| **Lines per Method** | 50 | 100 | MONITOR | Heavy documentation required |
| **Parameters per Method** | 5 | 8 | MONITOR | AI type assistance |
| **Nested Levels** | 4 | 6 | MONITOR | Clear section breaks |

---

## 📋 **AI-OPTIMIZED TYPE LIMITS**

| Element | Team Rule | Solo + AI Rule | Enforcement | AI Consideration |
|---------|-----------|----------------|-------------|------------------|
| **Types per File** | 20 | 35 | FLEXIBLE | Logical grouping by domain |
| **Properties per Interface** | 15 | 25 | MONITOR | AI can help with large interfaces |
| **Union Type Members** | 10 | 15 | MONITOR | AI handles complex unions well |
| **Generic Type Parameters** | 4 | 6 | MONITOR | AI assists with generic complexity |

---

## ⚙️ **ENHANCED CONFIGURATION LIMITS**

| Element | Team Rule | Solo + AI Rule | Enforcement | Documentation Requirement |
|---------|-----------|----------------|-------------|---------------------------|
| **Constants per File** | 25 | 50 | FLEXIBLE | Group by logical domain |
| **Enum Members** | 20 | 35 | FLEXIBLE | Document each member |
| **Config Object Properties** | 12 | 20 | MONITOR | AI-friendly property naming |

---

## 🤖 **AI-SPECIFIC REQUIREMENTS**

### **Mandatory AI Context Optimization**

| Requirement | Implementation | Enforcement |
|-------------|----------------|-------------|
| **Section Headers** | Every 100-200 lines | MANDATORY |
| **AI Context Comments** | Start of each major section | MANDATORY |
| **Method Documentation** | JSDoc for methods >20 lines | MANDATORY |
| **Complex Logic Comments** | Inline explanation for AI | MANDATORY |
| **File Overview Comment** | Purpose, scope, AI navigation | MANDATORY |

### **AI-Friendly File Structure Template**

```typescript
/**
 * ============================================================================
 * AI CONTEXT: [Component Name] - [Primary Responsibility]
 * Purpose: [Brief description of what this file does]
 * Complexity: [Simple/Moderate/Complex] - [Justification if complex]
 * AI Navigation: [Number] logical sections, [Number] major domains
 * ============================================================================
 */

// ============================================================================
// SECTION 1: IMPORTS & DEPENDENCIES (Lines 1-50)
// AI Context: "External dependencies and type imports"
// ============================================================================

// ============================================================================
// SECTION 2: TYPE DEFINITIONS (Lines 51-200)
// AI Context: "Core interfaces and types for [domain]"
// ============================================================================

// ============================================================================
// SECTION 3: CONSTANTS & CONFIGURATION (Lines 201-350)
// AI Context: "Configuration constants and default values"
// ============================================================================

// ============================================================================
// SECTION 4: MAIN IMPLEMENTATION (Lines 351-800)
// AI Context: "Primary business logic for [functionality]"
// ============================================================================

// ============================================================================
// SECTION 5: HELPER METHODS (Lines 801-1000)
// AI Context: "Utility methods supporting main implementation"
// ============================================================================

// ============================================================================
// SECTION 6: ERROR HANDLING (Lines 1001-1200)
// AI Context: "Error handling, validation, and edge cases"
// ============================================================================
```

---

## 🚨 **ENFORCEMENT LEVELS - SOLO + AI CONTEXT**

| Violation Level | Lines | AI Impact | Action Required | Timeline |
|----------------|-------|-----------|-----------------|----------|
| **GREEN** | 1-700 | Optimal AI context | Continue development | N/A |
| **YELLOW** | 701-1000 | Good AI navigation | Monitor complexity | Next iteration |
| **ORANGE** | 1001-1200 | AI needs sectioning | Heavy documentation | This iteration |
| **RED** | 1201-1500 | AI context challenges | Consider refactoring | This sprint |
| **CRITICAL** | 2200+ | AI effectiveness drops | Mandatory refactor | Immediate |

---

## ✅ **ACCEPTABLE COMPLEXITY EXCEPTIONS**

### **Large File Justification Criteria**

When 1200+ LOC files are acceptable:

✅ **Single Domain Responsibility**
- All code serves one cohesive purpose
- Natural boundaries would create artificial splits
- Complexity is inherent to the domain, not poor design

✅ **AI Navigation Optimized**
- Clear section headers and documentation
- Logical flow that AI can follow
- Complex areas heavily commented

✅ **High Cohesion**
- All methods work together
- Shared state makes sense
- Breaking apart would duplicate logic

✅ **Future Handover Ready**
- Comprehensive documentation
- Clear architecture decisions recorded
- AI-assisted code walkthrough possible

### **Large File Requirements Checklist**

When creating 1200+ LOC files, ensure:

- [ ] **AI Context Comments**: Each section has AI-friendly descriptions
- [ ] **Method Documentation**: All complex methods have JSDoc
- [ ] **Inline Explanations**: Complex logic explained for AI
- [ ] **Section Boundaries**: Clear separation between logical areas
- [ ] **Navigation Aids**: Table of contents comment at file start
- [ ] **Error Handling**: Comprehensive error scenarios documented
- [ ] **Type Safety**: Strong TypeScript typing throughout
- [ ] **Performance Notes**: Any performance considerations documented

---

## 🚫 **MANDATORY REFACTOR TRIGGERS**

Regardless of line count, immediate refactoring required when:

❌ **AI Navigation Failure**
- AI assistant struggles to understand file structure
- Context switching between unrelated domains
- AI gives inconsistent advice about the code

❌ **Development Velocity Impact**
- Finding specific code takes >2 minutes
- IDE performance degrades noticeably
- Debugging becomes significantly harder

❌ **Maintenance Pain Points**
- Multiple unrelated responsibilities in one file
- Frequent conflicts between different logical areas
- Code changes require understanding entire file

❌ **Future Risk Indicators**
- File would be impossible to hand over to another developer
- No clear way to split responsibilities
- Architecture decisions not documented

---

## 📊 **MONITORING & METRICS**

### **Automated Checks**

Implement these automated validations:

```typescript
// File complexity metrics to monitor
interface FileComplexityMetrics {
  lineCount: number;
  sectionCount: number;
  methodCount: number;
  cyclomaticComplexity: number;
  aiContextChunks: number;
  documentationDensity: number; // Comments per 100 lines
}

// Warning thresholds
const COMPLEXITY_THRESHOLDS = {
  lineCount: { warning: 1000, critical: 1500 },
  sectionCount: { warning: 8, critical: 12 },
  methodCount: { warning: 20, critical: 30 },
  cyclomaticComplexity: { warning: 50, critical: 100 },
  documentationDensity: { minimum: 15 } // 15% documentation
};
```

### **Regular Assessment Questions**

Monthly file complexity review:

1. **AI Effectiveness**: Can AI assistant navigate and explain the file effectively?
2. **Development Speed**: Are changes taking longer than expected?
3. **Cognitive Load**: Do you need significant context switching to work on the file?
4. **Future Maintainability**: Would this file be clear to you in 6 months?
5. **Handover Readiness**: Could another developer understand this file with AI assistance?

---

## 🎯 **IMPLEMENTATION GUIDELINES**

### **For New Large Components (1000+ LOC)**

1. **Design Phase**
   - Document architecture decisions
   - Plan AI-friendly section structure
   - Define clear domain boundaries

2. **Implementation Phase**
   - Write section headers first
   - Add AI context comments as you go
   - Document complex logic immediately

3. **Review Phase**
   - Test AI navigation effectiveness
   - Verify documentation completeness
   - Assess future maintainability

### **For Existing Large Files**

1. **Assessment**
   - Measure against new criteria
   - Identify refactoring opportunities
   - Document current complexity

2. **Incremental Improvement**
   - Add AI context comments
   - Improve section organization
   - Enhanced documentation

3. **Strategic Refactoring**
   - Split only when clear benefits
   - Maintain logical cohesion
   - Preserve AI navigability

---

## 📋 **COMPLIANCE VERIFICATION**

### **Pre-Commit Checklist**

For files >1000 LOC:

- [ ] AI context comments present at major sections
- [ ] File overview comment explains purpose and structure
- [ ] Complex methods have JSDoc documentation
- [ ] Section boundaries are clear and logical
- [ ] Error handling is comprehensive and documented
- [ ] Performance considerations noted where relevant
- [ ] File can be effectively navigated with AI assistance

### **Code Review Guidelines**

When reviewing large files:

1. **AI Navigation Test**: Can AI explain the file structure coherently?
2. **Documentation Adequacy**: Is complex logic explained sufficiently?
3. **Logical Organization**: Are sections well-organized and cohesive?
4. **Future Maintainability**: Will this be manageable long-term?
5. **Refactoring Opportunities**: Are there clear split points if needed?

---

## Naming Conventions

### Files and Directories
- Use **kebab-case** for directories: `user-management/`
- Use **PascalCase** for component files: `UserProfile.tsx`
- Use **kebab-case** for configuration and utility files: `api-config.ts`
- Use **camelCase** for hook files: `useAuthentication.ts`
- Use .ts extension for TypeScript files
- Use .tsx extension for React component files

### Components
- Use **PascalCase** for component names: `UserProfile`
- Use **PascalCase** with descriptive suffixes for specialized components:
  - Containers: `UserProfileContainer`
  - HOCs: `withAuthentication`
  - Pages: `UserProfilePage`

### Variables, Functions, and Classes
- Use **camelCase** for variables and functions: `getUserData`
- Use **PascalCase** for classes and React components: `UserManager`
- Use **UPPER_SNAKE_CASE** for constants: `MAX_RETRY_COUNT`
- Boolean variables should use prefixes like `is`, `has`, `should`: `isLoading`
- Event handlers should use `handle` prefix: `handleSubmit`
- Prefix interfaces with 'I': `IUserProfile`
- Prefix type definitions with 'T': `TUserRole`

### CSS Classes
- Use **kebab-case** for CSS class names: `user-profile-container`
- Use BEM (Block, Element, Modifier) naming convention:
  - Block: `user-card`
  - Element: `user-card__title`
  - Modifier: `user-card--highlighted`

### Database Entities
- Use **snake_case** for table and column names: `user_profiles`
- Use singular nouns for entity names: `user` not `users`
- Primary keys should be named `id`
- Foreign keys should use format `entity_id`: `user_id`

## Code Formatting
- Use ESLint and Prettier for consistent code formatting
- Indent using 2 spaces
- Maximum line length: 100 characters
- Always use semicolons
- Use single quotes for strings
- Always use curly braces for control structures
- Always use parentheses around arrow function parameters
- Place opening braces on the same line
- Add trailing commas for multi-line arrays and objects

## JavaScript/TypeScript Standards

### TypeScript Usage
- Use TypeScript for all new code
- Define explicit types for all variables, parameters, and return types
- Avoid use of `any` type where possible
- Use interfaces for object shapes
- Use type aliases for complex types
- Use enums for fixed sets of values
- Use generics for reusable components
- Enable strict TypeScript compiler options

### ES6+ Features
- Use arrow functions instead of function expressions where appropriate
- Use template literals instead of string concatenation
- Use destructuring for objects and arrays
- Use spread and rest operators where appropriate
- Use optional chaining and nullish coalescing operators
- Use async/await instead of promises for asynchronous code
- Use `let` and `const` instead of `var`

### React Best Practices
- Use functional components with hooks
- Separate logic from presentation using custom hooks
- Use context for state that needs to be accessed by many components
- Avoid prop drilling more than 2 levels deep
- Memoize expensive calculations with useMemo
- Memoize callbacks with useCallback when passed to child components
- Use fragments to avoid unnecessary DOM elements
- Use portals for modals and tooltips
- Avoid inline styles
- Use keys for list items

## CSS/Styling Standards
- Use CSS modules or styled-components for component styling
- Follow a component-based styling approach
- Use CSS variables for theming
- Use relative units (rem, em) instead of pixels
- Implement mobile-first responsive design
- Use flexbox and grid for layouts
- Avoid !important
- Minimize CSS nesting (max 3 levels)
- Implement dark mode support

## Documentation Standards

### Code Documentation
- Use JSDoc for all public APIs
- Document complex functions and methods
- Include parameter and return type descriptions
- Document exceptions and edge cases
- Add examples for complex operations

### Component Documentation
- Document props with PropTypes or TypeScript interfaces
- Include usage examples
- Document component state and side effects
- Explain component lifecycle considerations
- Reference related change records (ADRs, DCRs)

## Testing Standards
- Minimum code coverage requirement: 80%
- Unit test all business logic
- Integration test all API endpoints
- End-to-end test critical user flows
- Test edge cases and error handling
- Use appropriate test doubles (mocks, stubs, spies)
- Tests should be independent and idempotent
- Follow AAA pattern (Arrange, Act, Assert)
- Use descriptive test names

## API Design Standards
- Follow RESTful principles
- Use resource-oriented URLs
- Use appropriate HTTP methods
- Use consistent response structures
- Use proper HTTP status codes
- Implement pagination for list endpoints
- Provide filtering, sorting, and search capabilities
- Version APIs appropriately
- Document APIs using OpenAPI/Swagger
- Implement proper error handling and validation

## Performance Guidelines
- Implement code splitting for large applications
- Lazy load components and routes
- Optimize bundle size
- Implement caching strategies
- Minimize DOM manipulations
- Optimize images and assets
- Implement proper loading states
- Monitor and optimize render performance
- Avoid unnecessary re-renders in React components
- Use memoization for expensive calculations

## Security Guidelines
- Validate and sanitize all user input
- Implement proper authentication and authorization
- Use HTTPS for all communications
- Implement CSRF protection
- Implement proper error handling
- Avoid exposing sensitive information
- Implement content security policy
- Use parameterized queries for database operations
- Keep dependencies up-to-date
- Follow OWASP security guidelines

## Accessibility Guidelines
- Follow WCAG 2.1 AA standards
- Use semantic HTML
- Implement proper keyboard navigation
- Provide alternative text for images
- Ensure sufficient color contrast
- Make forms accessible
- Support screen readers
- Implement proper focus management
- Test with accessibility tools
- Support user preferences (reduced motion, etc.)

## Version Control
- Use feature branches for development
- Use descriptive branch names: `feature/user-profile`
- Commit small, logical changes
- Write descriptive commit messages
- Follow conventional commits format
- Rebase or merge with the main branch before submission
- Squash commits before merging to main
- Tag releases with semantic versioning
- Keep the main branch stable
- Delete branches after merging


**Authority**: President & CEO, E.Z. Consultancy  
**Effective**: Immediate implementation for all OA Framework development  
**Review Cycle**: Quarterly assessment and rule refinement based on AI technology evolution
