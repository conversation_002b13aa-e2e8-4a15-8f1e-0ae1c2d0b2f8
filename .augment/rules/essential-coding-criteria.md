---
alwaysApply: true
type: "always_apply"
---
# Resilient Timing Integration - AI Assistant Decision Rule

## **🎯 MANDATORY Integration (Add Timing)**

**Always implement dual-field pattern when component:**

- Has `Enhanced` suffix (e.g., `MemorySafeResourceManagerEnhanced`)
- Located in `/modules/` or `/enhanced/` directories  
- Performs buffer operations (eviction, strategy management, analytics)
- Has performance targets <10ms or enterprise SLA requirements
- Manages resources (pools, coordination, orchestration)
- Extends base classes to add enterprise capabilities

**Implementation Pattern:**
```typescript
private _resilientTimer!: ResilientTimer;
private _metricsCollector!: ResilientMetricsCollector;
```

## **❌ NO Integration Required**

**Never add timing to:**

- Base infrastructure classes (`MemorySafeResourceManager`, `EventHandlerRegistry`, `TimerCoordinationService`, `AtomicCircularBuffer`)
- Simple interfaces and utilities (`LoggingMixin`, `ILoggingService`)
- Pure type definitions and constants
- Components with >100ms acceptable response times

## **🚨 Quick Decision Rule**

1. **Class name contains "Enhanced"?** → ✅ Add timing
2. **In modules/ directory?** → ✅ Add timing  
3. **Base infrastructure class?** → ❌ No timing
4. **Simple utility/interface?** → ❌ No timing
5. **Performance-critical operations?** → ✅ Add timing

**When in doubt:** Check if component extends a base class to add enterprise features = Add timing