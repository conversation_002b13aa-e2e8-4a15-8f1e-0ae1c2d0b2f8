/**
 * @file Test Setup for Individual Test Files
 * @description Provides memory management and cleanup for each test file
 * @created 2025-07-13
 */

import { beforeAll, afterAll } from '@jest/globals';

class TestMemoryManager {
  private static fileBaseline: NodeJS.MemoryUsage | null = null;
  private static testFileName: string = '';
  
  static async setFileBaseline(testFileName: string) {
    try {
      this.testFileName = testFileName;

      // Single garbage collection to prevent timeout issues
      if (global.gc) {
        global.gc();
      }

      this.fileBaseline = process.memoryUsage();
      console.log(`[MEMORY BASELINE] ${testFileName}: ${(this.fileBaseline.heapUsed / 1024 / 1024).toFixed(1)}MB`);
    } catch (error) {
      console.warn(`[MEMORY BASELINE ERROR] ${testFileName}: ${error}`);
      // Set a fallback baseline
      this.fileBaseline = process.memoryUsage();
    }
  }
  
  static async cleanupFile(testFileName: string) {
    try {
      console.log(`[MEMORY CLEANUP] Starting cleanup for ${testFileName}`);

      // Clean up environment variables
      delete process.env.TEST_TYPE;
      delete (global as any).memoryTracker;

      // Single garbage collection to prevent timeout issues
      if (global.gc) {
        global.gc();
      }

      const postCleanup = process.memoryUsage();
      const memoryFreed = this.fileBaseline
        ? (this.fileBaseline.heapUsed - postCleanup.heapUsed) / 1024 / 1024
        : 0;

      console.log(`[MEMORY CLEANUP] ${testFileName}: ${(postCleanup.heapUsed / 1024 / 1024).toFixed(1)}MB (freed: ${memoryFreed.toFixed(1)}MB)`);
      this.fileBaseline = null;
    } catch (error) {
      console.warn(`[MEMORY CLEANUP ERROR] ${testFileName}: ${error}`);
      this.fileBaseline = null; // Reset baseline even on error
    }
  }
  
  static isMemoryPressureHigh(): boolean {
    const current = process.memoryUsage();
    const currentMB = current.heapUsed / 1024 / 1024;
    
    // Test-type aware thresholds
    let threshold: number;
    switch (process.env.TEST_TYPE) {
      case 'performance':
        threshold = 300;
        break;
      case 'security':
        threshold = 250;
        break;
      case 'integration':
        threshold = 200;
        break;
      default:
        threshold = 150;
    }
    
    const isHigh = currentMB > threshold;
    if (isHigh) {
      console.log(`[MEMORY PRESSURE] High pressure detected: ${currentMB.toFixed(1)}MB > ${threshold}MB (${process.env.TEST_TYPE || 'unit'} test)`);
    }
    
    return isHigh;
  }
  
  static getMemoryGrowthLimit(): number {
    const testType = process.env.TEST_TYPE;
    
    switch (testType) {
      case 'performance':
        return 150; // Allow 150MB growth for performance tests
      case 'security':
        return 50;  // Allow 50MB growth for security tests
      case 'integration':
        return 75;  // Allow 75MB growth for integration tests
      default:
        return 25;  // Allow 25MB growth for unit tests
    }
  }
}

beforeAll(async () => {
  // ✅ FIX: Use a safer method to get test file name without expect.getState()
  const testFileName = process.env.JEST_WORKER_ID ?
    `test-worker-${process.env.JEST_WORKER_ID}` :
    'global-test-setup';

  try {
    await TestMemoryManager.setFileBaseline(testFileName);
    console.log(`[TEST FILE START] ${testFileName}`);
  } catch (error) {
    console.warn(`[TEST SETUP WARNING] Failed to set baseline: ${error}`);
    // Don't fail the test setup, just log the warning
  }
}, 30000); // Reduced timeout to 30 seconds

afterAll(async () => {
  const testFileName = process.env.JEST_WORKER_ID ?
    `test-worker-${process.env.JEST_WORKER_ID}` :
    'global-test-setup';

  try {
    await TestMemoryManager.cleanupFile(testFileName);
    console.log(`[TEST FILE END] ${testFileName}`);
  } catch (error) {
    console.warn(`[TEST CLEANUP WARNING] Failed to cleanup: ${error}`);
    // Don't fail the test cleanup, just log the warning
  }
}, 30000); // Reduced timeout to 30 seconds

// Export for use in GovernanceTrackingSystem
(global as any).TestMemoryManager = TestMemoryManager;
