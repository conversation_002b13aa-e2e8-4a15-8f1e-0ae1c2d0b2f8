/**
 * @file Global Test Setup for GovernanceTrackingSystem
 * @description Initializes global test environment and memory tracking
 * @created 2025-07-13
 */

export default async function globalSetup() {
  console.log('[GLOBAL SETUP] Initializing test environment for GovernanceTrackingSystem');
  
  // Reset all environment variables
  delete process.env.TEST_TYPE;
  delete process.env.MEMORY_PRESSURE_HIGH;
  
  // Initialize global memory tracking
  (global as any).testMemoryTracker = {
    baseline: process.memoryUsage(),
    testFileBaselines: new Map(),
    currentTestFile: null,
    startTime: Date.now()
  };
  
  // Force initial garbage collection if available
  if (global.gc) {
    for (let i = 0; i < 3; i++) {
      global.gc();
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }
  
  const initialMemory = process.memoryUsage();
  console.log(`[GLOBAL SETUP] Test environment initialized - Initial memory: ${(initialMemory.heapUsed / 1024 / 1024).toFixed(1)}MB`);
}
