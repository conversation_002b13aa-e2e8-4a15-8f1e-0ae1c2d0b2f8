/**
 * @file Custom Test Sequencer for GovernanceTrackingSystem
 * @description Ensures proper test execution order to prevent memory pressure issues
 * @created 2025-07-13
 */

const Sequencer = require('@jest/test-sequencer').default;

class CustomSequencer extends Sequencer {
  sort(tests) {
    return tests.sort((testA, testB) => {
      // Performance tests should run LAST to prevent memory pressure affecting other tests
      if (testA.path.includes('performance.test.ts')) return 1;
      if (testB.path.includes('performance.test.ts')) return -1;
      
      // Security tests before performance
      if (testA.path.includes('security.test.ts')) return 1;
      if (testB.path.includes('security.test.ts')) return -1;
      
      // Integration tests before security
      if (testA.path.includes('integration.test.ts')) return 1;
      if (testB.path.includes('integration.test.ts')) return -1;
      
      // Unit tests first (lowest memory usage)
      return testA.path.localeCompare(testB.path);
    });
  }
}

module.exports = CustomSequencer;
