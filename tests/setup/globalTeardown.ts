/**
 * @file Global Test Teardown for GovernanceTrackingSystem
 * @description Cleans up global test environment and performs final memory cleanup
 * @created 2025-07-13
 */

export default async function globalTeardown() {
  console.log('[GLOBAL TEARDOWN] Starting test environment cleanup');

  // Get final memory usage before cleanup
  const preCleanupMemory = process.memoryUsage();
  console.log(`[GLOBAL TEARDOWN] Pre-cleanup memory: ${(preCleanupMemory.heapUsed / 1024 / 1024).toFixed(1)}MB`);

  // CRITICAL FIX: Force cleanup of MemorySafeResourceManager global state
  try {
    // Import and call the global cleanup function
    const { MemorySafeResourceManager } = await import('../../shared/src/base/MemorySafeResourceManager');
    console.log('[GLOBAL TEARDOWN] Forcing MemorySafeResourceManager global cleanup');
    MemorySafeResourceManager.forceGlobalCleanup();
  } catch (error) {
    console.warn('[GLOBAL TEARDOWN] Error during MemorySafeResourceManager cleanup:', error);
  }

  // Clean up global singleton map
  if ((global as any).__memorySafeSingletons) {
    console.log('[GLOBAL TEARDOWN] Clearing global singleton map');
    (global as any).__memorySafeSingletons.clear();
    delete (global as any).__memorySafeSingletons;
  }

  // CRITICAL FIX: Clear Jest's internal state that causes memory leaks
  try {
    // CRITICAL FIX: Aggressive module cache clearing
    if (require.cache) {
      const cacheKeys = Object.keys(require.cache);
      console.log(`[GLOBAL TEARDOWN] Clearing ${cacheKeys.length} modules from require cache`);

      // Clear all cached modules
      cacheKeys.forEach(key => {
        try {
          delete require.cache[key];
        } catch (error) {
          // Some modules can't be deleted, ignore errors
        }
      });

      // Force garbage collection after cache clearing
      if (global.gc) {
        global.gc();
      }
    }

    // Clear Jest timers if available (jest may not be available in global teardown)
    try {
      if (typeof jest !== 'undefined' && jest.clearAllTimers) {
        console.log('[GLOBAL TEARDOWN] Clearing all Jest timers');
        jest.clearAllTimers();
      }
    } catch (error) {
      console.log('[GLOBAL TEARDOWN] Jest not available in global teardown context (expected)');
    }

    // Clear any global timer references
    if (global.clearInterval && global.clearTimeout) {
      console.log('[GLOBAL TEARDOWN] Clearing global timer references');
      // Clear any remaining global timers (this is a fallback)
      for (let i = 1; i < 10000; i++) {
        try {
          global.clearInterval(i);
          global.clearTimeout(i);
        } catch {
          // Ignore errors for non-existent timers
        }
      }
    }
  } catch (error) {
    console.warn('[GLOBAL TEARDOWN] Error during Jest cleanup:', error);
  }

  // Force multiple garbage collection cycles for thorough cleanup
  if (global.gc) {
    console.log('[GLOBAL TEARDOWN] Performing aggressive garbage collection');
    for (let i = 0; i < 10; i++) { // Increased from 5 to 10 cycles
      global.gc();
      await new Promise(resolve => setTimeout(resolve, 50)); // Reduced delay
    }
  }

  // Clean up global state
  delete (global as any).testMemoryTracker;
  delete (global as any).memoryTracker;
  delete (global as any).TestMemoryManager;

  // CRITICAL FIX: Clear all global variables that might hold references
  const globalKeys = Object.keys(global).filter(key =>
    key.startsWith('__') ||
    key.includes('test') ||
    key.includes('jest') ||
    key.includes('memory')
  );

  console.log(`[GLOBAL TEARDOWN] Clearing ${globalKeys.length} global variables`);
  globalKeys.forEach(key => {
    try {
      delete (global as any)[key];
    } catch (error) {
      // Some globals can't be deleted, ignore errors
    }
  });

  // Reset environment variables
  delete process.env.TEST_TYPE;
  delete process.env.MEMORY_PRESSURE_HIGH;

  // CRITICAL FIX: Emergency memory cleanup if memory usage is still high
  const intermediateMemory = process.memoryUsage();
  const currentMemoryMB = intermediateMemory.heapUsed / 1024 / 1024;

  if (currentMemoryMB > 300) { // Lowered threshold from 500MB to 300MB
    console.log(`[GLOBAL TEARDOWN] HIGH MEMORY DETECTED (${currentMemoryMB.toFixed(1)}MB) - Performing emergency cleanup`);

    // CRITICAL FIX: More aggressive emergency cleanup
    if (global.gc) {
      console.log('[GLOBAL TEARDOWN] Performing 30 aggressive GC cycles');
      for (let i = 0; i < 30; i++) { // Increased from 20 to 30
        global.gc();
        // Shorter delay for faster cleanup
        await new Promise(resolve => setTimeout(resolve, 5));
      }
    }

    // CRITICAL FIX: Force V8 heap compaction if available
    try {
      if (global.gc && typeof global.gc === 'function') {
        // Multiple GC calls to ensure thorough cleanup
        for (let i = 0; i < 10; i++) {
          global.gc();
        }
      }
    } catch (error) {
      console.warn('[GLOBAL TEARDOWN] Error during V8 heap compaction:', error);
    }

    // Force process memory cleanup (Node.js specific)
    if (typeof process.nextTick === 'function') {
      process.nextTick(() => {
        if (global.gc) global.gc();
      });
    }
  }

  // Final memory check
  const postCleanupMemory = process.memoryUsage();
  const memoryFreed = (preCleanupMemory.heapUsed - postCleanupMemory.heapUsed) / 1024 / 1024;

  console.log(`[GLOBAL TEARDOWN] Test environment cleaned up - Final memory: ${(postCleanupMemory.heapUsed / 1024 / 1024).toFixed(1)}MB (freed: ${memoryFreed.toFixed(1)}MB)`);

  // CRITICAL FIX: Log warning if memory cleanup was insufficient
  if (memoryFreed < 0) {
    console.warn(`[GLOBAL TEARDOWN] ⚠️  WARNING: Memory increased during cleanup (${memoryFreed.toFixed(1)}MB)`);
  } else if (memoryFreed < 100) {
    console.warn(`[GLOBAL TEARDOWN] ⚠️  WARNING: Insufficient memory freed (${memoryFreed.toFixed(1)}MB) - possible memory leak`);
  } else {
    console.log(`[GLOBAL TEARDOWN] ✅ Memory cleanup successful (${memoryFreed.toFixed(1)}MB freed)`);
  }
}
