/**
 * @file Test Setup Configuration
 * @filepath tests/setup.ts
 * @description Jest test environment setup for OA Framework testing
 * @created 2025-06-26 00:26:23 +03
 * @authority President & CEO, E.Z. Consultancy
 */

// Configure Jest for TypeScript environment
// This file should be referenced in jest.config.js as setupFilesAfterEnv

// Global test utilities and mocks
global.console = {
  ...console,
  // Suppress console.log in tests unless explicitly needed
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Test timeout configuration
jest.setTimeout(10000); // 10 seconds for async tests

// Mock common Node.js modules that might not be available in test environment
jest.mock('fs', () => ({
  promises: {
    readFile: jest.fn(),
    writeFile: jest.fn(),
    mkdir: jest.fn(),
    access: jest.fn(),
  },
  readFileSync: jest.fn(),
  writeFileSync: jest.fn(),
  existsSync: jest.fn(),
}));

// Setup test environment variables
process.env.NODE_ENV = 'test';
process.env.OA_FRAMEWORK_ENV = 'testing';

// Test utilities for OA Framework components
export const TestUtils = {
  /**
   * Create a mock timestamp
   */
  createMockTimestamp: (): string => {
    return new Date().toISOString();
  },

  /**
   * Generate unique test ID
   */
  generateTestId: (prefix: string = 'test'): string => {
    return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  },

  /**
   * Wait for specified milliseconds
   */
  wait: (ms: number): Promise<void> => {
    return new Promise(resolve => setTimeout(resolve, ms));
  },

  /**
   * Create mock tracking data with defaults
   */
  createMockTrackingData: (overrides: any = {}) => ({
    componentId: TestUtils.generateTestId('component'),
    status: 'in-progress',
    timestamp: TestUtils.createMockTimestamp(),
    context: {
      contextId: 'test-context',
      milestone: 'M0',
      category: 'testing',
      dependencies: [],
      dependents: [],
    },
    metadata: {
      phase: 'testing',
      progress: 50,
      priority: 'P1',
      tags: ['test'],
      custom: {},
    },
    progress: {
      completion: 50,
      tasksCompleted: 5,
      totalTasks: 10,
      timeSpent: 60,
      estimatedTimeRemaining: 60,
      quality: {
        codeCoverage: 80,
        testCount: 10,
        bugCount: 1,
        qualityScore: 85,
        performanceScore: 90,
      },
    },
    authority: {
      level: 'standard',
      validator: 'test-validator',
      validationStatus: 'validated',
      validatedAt: TestUtils.createMockTimestamp(),
      complianceScore: 95,
    },
    ...overrides,
  }),
};

export default TestUtils; 