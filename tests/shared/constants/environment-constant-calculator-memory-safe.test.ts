/**
 * @file Memory Leak Prevention Test Suite
 * @filepath tests/shared/constants/environment-constants-calculator-memory-safe.test.ts
 * @task-id SECURITY-PATCH-001-TEST
 * @component memory-leak-prevention-tests
 * @reference M0-security-integration-tests
 * @template memory-safety-validation
 * @tier T0
 * @context foundation-context
 * @category Security Testing
 * @created 2025-07-15
 * @modified 2025-07-15 12:30:00 +03
 * 
 * @description
 * Comprehensive test suite that validates memory leak prevention measures
 * in the updated EnvironmentConstantsCalculator and MemorySafeResourceManager.
 * 
 * Test Coverage:
 * - Memory leak prevention validation
 * - Resource cleanup verification
 * - Process exit handling
 * - Container environment safety
 * - Performance degradation detection
 * - Reference counting accuracy
 * 
 * 🛡️ MEMORY SAFETY VALIDATION AUTHORITY
 * @authority-level critical-security-testing
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-security-001-memory-leak-testing
 * @governance-status approved
 * @governance-compliance security-test-validated
 */

import { EnvironmentConstantsCalculator, 
         getEnvironmentConstants, 
         getTrackingConstants,
         getEnvironmentSummary,
         recalculateEnvironmentConstants,
         shutdownEnvironmentCalculator,
         getEnvironmentCalculator } from '../../../shared/src/constants/platform/tracking/environment-constants-calculator';
import { MemorySafeResourceManager } from '../../../shared/src/base/MemorySafeResourceManager';

// Test utilities for memory monitoring
interface MemorySnapshot {
  heapUsed: number;
  heapTotal: number;
  external: number;
  timestamp: number;
}

function takeMemorySnapshot(): MemorySnapshot {
  const memUsage = process.memoryUsage();
  return {
    heapUsed: memUsage.heapUsed,
    heapTotal: memUsage.heapTotal,
    external: memUsage.external,
    timestamp: Date.now()
  };
}

function calculateMemoryGrowth(before: MemorySnapshot, after: MemorySnapshot): number {
  return after.heapUsed - before.heapUsed;
}

// Helper to force garbage collection in tests
function forceGarbageCollection(): Promise<void> {
  return new Promise((resolve) => {
    if (global.gc) {
      global.gc();
      global.gc(); // Run twice for more thorough cleanup
    }
    // Give time for GC to complete
    setTimeout(resolve, 100);
  });
}

describe('Memory Leak Prevention Test Suite', () => {
  
  describe('🛡️ Memory-Safe Resource Manager Base Class', () => {
    
    test('should prevent interval memory leaks', async () => {
      class TestManager extends MemorySafeResourceManager {
        protected async doInitialize(): Promise<void> {
          // Create multiple test intervals
          this.createSafeInterval(() => {}, 100, 'test1');
          this.createSafeInterval(() => {}, 200, 'test2');
          this.createSafeInterval(() => {}, 300, 'test3');
        }
        
        protected async doShutdown(): Promise<void> {
          // Base class handles cleanup automatically
        }
      }
      
      const manager = new TestManager();
      await manager.initialize();
      
      // Verify intervals are tracked
      const metrics = manager.getResourceMetrics();
      expect(metrics.activeIntervals).toBe(3);
      
      // Shutdown should clean up all intervals
      await manager.shutdown();
      
      const finalMetrics = manager.getResourceMetrics();
      expect(finalMetrics.activeIntervals).toBe(0);
      expect(finalMetrics.totalResources).toBe(0);
    });
    
    test('should enforce resource limits', async () => {
      class TestManager extends MemorySafeResourceManager {
        constructor() {
          super({ maxIntervals: 2 }); // Limit to 2 intervals
        }
        
        protected async doInitialize(): Promise<void> {}
        protected async doShutdown(): Promise<void> {}
      }
      
      const manager = new TestManager();
      await manager.initialize();
      
      // Create intervals up to limit
      manager.createSafeInterval(() => {}, 100, 'test1');
      manager.createSafeInterval(() => {}, 200, 'test2');
      
      // Third interval should throw error
      expect(() => {
        manager.createSafeInterval(() => {}, 300, 'test3');
      }).toThrow('Resource limit exceeded');
      
      await manager.shutdown();
    });
    
    test('should handle reference counting correctly', async () => {
      class TestManager extends MemorySafeResourceManager {
        protected async doInitialize(): Promise<void> {}
        protected async doShutdown(): Promise<void> {}
        
        public testSharedResource() {
          return this.createSharedResource(
            () => ({ data: 'test' }),
            (resource) => resource.data = null,
            'shared-test'
          );
        }
      }
      
      const manager = new TestManager();
      await manager.initialize();
      
      // Create first reference
      const ref1 = manager.testSharedResource();
      expect(ref1.resource.data).toBe('test');
      
      // Create second reference to same resource
      const ref2 = manager.testSharedResource();
      expect(ref2.resource).toBe(ref1.resource); // Same instance
      
      // Release first reference
      ref1.releaseRef();
      
      // Resource should still exist (second reference active)
      let metrics = manager.getResourceMetrics();
      expect(metrics.totalResources).toBeGreaterThan(0);
      
      // Release second reference
      ref2.releaseRef();
      
      // Resource should be cleaned up
      await new Promise(resolve => setTimeout(resolve, 10));
      metrics = manager.getResourceMetrics();
      expect(metrics.totalResources).toBe(0);
      
      await manager.shutdown();
    });
    
  });

  describe('🔧 Environment Constants Calculator Memory Safety', () => {
    
    let calculator: EnvironmentConstantsCalculator;
    
    afterEach(async () => {
      if (calculator) {
        await calculator.shutdown();
      }
      // Clean up any global state
      await shutdownEnvironmentCalculator();
    });
    
    test('should not create intervals in test environment', async () => {
      // Ensure test environment
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'test';
      
      try {
        calculator = new EnvironmentConstantsCalculator();
        await calculator['initialize'](); // Access protected method for testing
        
        const metrics = calculator.getResourceMetrics();
        expect(metrics.activeIntervals).toBe(0); // No recalculation interval in tests
        
      } finally {
        process.env.NODE_ENV = originalEnv;
      }
    });
    
    test('should create intervals only in production environment', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';
      
      try {
        calculator = new EnvironmentConstantsCalculator();
        await calculator['initialize']();
        
        const metrics = calculator.getResourceMetrics();
        expect(metrics.activeIntervals).toBeGreaterThan(0); // Should have recalculation interval
        
      } finally {
        process.env.NODE_ENV = originalEnv;
      }
    });
    
    test('should prevent memory leaks with multiple instantiations', async () => {
      const memoryBefore = takeMemorySnapshot();
      
      // Create multiple instances
      const instances: EnvironmentConstantsCalculator[] = [];
      
      for (let i = 0; i < 10; i++) {
        const instance = new EnvironmentConstantsCalculator();
        await instance.calculateConstants();
        instances.push(instance);
      }
      
      await forceGarbageCollection();
      const memoryAfter = takeMemorySnapshot();
      
      // Clean up all instances
      for (const instance of instances) {
        await instance.shutdown();
      }
      
      await forceGarbageCollection();
      const memoryFinal = takeMemorySnapshot();
      
      // Memory should return close to original level after cleanup
      const growthAfterCleanup = calculateMemoryGrowth(memoryBefore, memoryFinal);
      expect(growthAfterCleanup).toBeLessThan(5 * 1024 * 1024); // Less than 5MB growth
    });
    
    test('should handle graceful shutdown', async () => {
      calculator = new EnvironmentConstantsCalculator();
      
      // Initialize and verify it's working
      const constants = await calculator.calculateConstants();
      expect(constants).toBeDefined();
      expect(calculator.isHealthy()).toBe(true);
      
      // Shutdown gracefully
      await calculator.shutdown();
      
      // Should be marked as shutting down
      expect(calculator.isHealthy()).toBe(false);
      
      const metrics = calculator.getResourceMetrics();
      expect(metrics.totalResources).toBe(0);
    });
    
  });

  describe('🔄 Utility Functions Memory Safety', () => {
    
    afterEach(async () => {
      await shutdownEnvironmentCalculator();
    });
    
    test('should not leak memory with repeated utility calls', async () => {
      const memoryBefore = takeMemorySnapshot();
      
      // Call utility functions multiple times
      for (let i = 0; i < 100; i++) {
        await getEnvironmentConstants();
        await getTrackingConstants();
        await getEnvironmentSummary();
        
        if (i % 10 === 0) {
          await recalculateEnvironmentConstants();
        }
      }
      
      await forceGarbageCollection();
      const memoryAfter = takeMemorySnapshot();
      
      const memoryGrowth = calculateMemoryGrowth(memoryBefore, memoryAfter);
      
      // Should not grow significantly (< 10MB for 100 iterations)
      expect(memoryGrowth).toBeLessThan(10 * 1024 * 1024);
    });
    
    test('should reuse singleton instance', async () => {
      const instance1 = getEnvironmentCalculator();
      const instance2 = getEnvironmentCalculator();
      
      expect(instance1).toBe(instance2); // Same instance
      
      // Both should work
      const constants1 = await instance1.calculateConstants();
      const constants2 = await instance2.calculateConstants();
      
      expect(constants1).toEqual(constants2);
    });
    
    test('should handle concurrent access safely', async () => {
      // Create multiple concurrent requests
      const promises = Array.from({ length: 50 }, async (_, i) => {
        const constants = await getEnvironmentConstants();
        expect(constants.calculatedAt).toBeInstanceOf(Date);
        return constants;
      });
      
      const results = await Promise.all(promises);
      
      // All results should be valid
      expect(results).toHaveLength(50);
      results.forEach(result => {
        expect(result.MAX_MEMORY_USAGE).toBeGreaterThan(0);
        expect(result.MAX_CACHE_SIZE).toBeGreaterThan(0);
      });
    });
    
  });

  describe('🐳 Container Environment Safety', () => {
    
    test('should handle missing cgroup files gracefully', async () => {
      // This tests the fallback behavior when not in a container
      calculator = new EnvironmentConstantsCalculator();
      
      const resources = calculator.getSystemResources();
      
      expect(resources.totalMemoryMB).toBeGreaterThan(0);
      expect(resources.totalCPUCores).toBeGreaterThan(0);
      expect(resources.nodeHeapLimitMB).toBeGreaterThan(0);
      
      // Should not throw errors
      const constants = await calculator.calculateConstants();
      expect(constants).toBeDefined();
    });
    
    test('should adapt to environment profiles correctly', async () => {
      const environments = ['development', 'testing', 'staging', 'production'];
      const originalEnv = process.env.NODE_ENV;
      
      try {
        for (const env of environments) {
          process.env.NODE_ENV = env;
          
          calculator = new EnvironmentConstantsCalculator();
          const constants = await calculator.calculateConstants();
          
          expect(constants.environmentProfile).toContain(env.charAt(0).toUpperCase() + env.slice(1));
          
          // Production should have higher limits
          if (env === 'production') {
            expect(constants.MAX_RESPONSE_TIME).toBe(1000);
          } else {
            expect(constants.MAX_RESPONSE_TIME).toBe(5000);
          }
          
          await calculator.shutdown();
        }
      } finally {
        process.env.NODE_ENV = originalEnv;
      }
    });
    
  });

  describe('⚡ Performance and Resource Monitoring', () => {
    
    test('should complete calculations within performance limits', async () => {
      calculator = new EnvironmentConstantsCalculator();
      
      const startTime = performance.now();
      const constants = await calculator.calculateConstants();
      const endTime = performance.now();
      
      const executionTime = endTime - startTime;
      
      expect(executionTime).toBeLessThan(100); // Should complete in <100ms
      expect(constants).toBeDefined();
    });
    
    test('should provide accurate resource metrics', async () => {
      calculator = new EnvironmentConstantsCalculator();
      await calculator['initialize']();
      
      const metrics = calculator.getResourceMetrics();
      
      expect(metrics.totalResources).toBeGreaterThanOrEqual(0);
      expect(metrics.activeIntervals).toBeGreaterThanOrEqual(0);
      expect(metrics.activeTimeouts).toBeGreaterThanOrEqual(0);
      expect(metrics.memoryUsageMB).toBeGreaterThan(0);
    });
    
    test('should maintain health status correctly', async () => {
      calculator = new EnvironmentConstantsCalculator();
      
      // Should be healthy initially
      expect(calculator.isHealthy()).toBe(true);
      
      const healthStatus = calculator.getHealthStatus();
      expect(healthStatus.healthy).toBe(true);
      expect(healthStatus.details).toContain('within limits');
      
      // Should become unhealthy after shutdown
      await calculator.shutdown();
      expect(calculator.isHealthy()).toBe(false);
    });
    
  });

  describe('🚨 Error Handling and Edge Cases', () => {
    
    test('should handle system resource detection failures', async () => {
      // Mock os module to simulate failures
      jest.mock('os', () => ({
        totalmem: () => { throw new Error('Mock error'); },
        freemem: () => 512 * 1024 * 1024,
        cpus: () => [{}],
        platform: () => 'test',
        arch: () => 'test'
      }));
      
      calculator = new EnvironmentConstantsCalculator();
      
      // Should not throw, should use fallback values
      expect(() => calculator.getSystemResources()).not.toThrow();
      
      const resources = calculator.getSystemResources();
      expect(resources.totalMemoryMB).toBe(1024); // Fallback value
    });
    
    test('should handle invalid environment configurations', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'invalid-environment';
      
      try {
        calculator = new EnvironmentConstantsCalculator();
        const constants = await calculator.calculateConstants();
        
        // Should fall back to development profile
        expect(constants.environmentProfile).toBe('Development');
        
      } finally {
        process.env.NODE_ENV = originalEnv;
      }
    });
    
    test('should emit errors for critical failures', async () => {
      calculator = new EnvironmentConstantsCalculator();
      
      let errorEmitted = false;
      calculator.on('error', () => {
        errorEmitted = true;
      });
      
      // Force an error by mocking a critical method
      const originalCalculate = calculator['calculateConstants'];
      calculator['calculateConstants'] = async () => {
        throw new Error('Mock calculation error');
      };
      
      try {
        await calculator.calculateConstants();
      } catch (error) {
        expect(error.message).toContain('Mock calculation error');
      }
      
      // Restore original method
      calculator['calculateConstants'] = originalCalculate;
    });
    
  });

  describe('🔄 Integration with Existing Test Suite', () => {
    
    test('should maintain backward compatibility with existing tests', async () => {
      // Test the main functions used in the original test suite
      const constants = await getEnvironmentConstants();
      
      // Verify all expected properties exist
      expect(constants).toHaveProperty('MAX_MEMORY_USAGE');
      expect(constants).toHaveProperty('MAX_CACHE_SIZE');
      expect(constants).toHaveProperty('MAX_BATCH_SIZE');
      expect(constants).toHaveProperty('MAX_CONCURRENT_OPERATIONS');
      expect(constants).toHaveProperty('calculatedAt');
      expect(constants).toHaveProperty('environmentProfile');
      expect(constants).toHaveProperty('systemResources');
      
      // Verify types are correct
      expect(typeof constants.MAX_MEMORY_USAGE).toBe('number');
      expect(constants.calculatedAt).toBeInstanceOf(Date);
      expect(typeof constants.environmentProfile).toBe('string');
    });
    
    test('should work with tracking constants integration', async () => {
      const trackingConstants = await getTrackingConstants();
      
      expect(trackingConstants).toHaveProperty('TRACKING_CONSTANTS');
      expect(trackingConstants).toHaveProperty('ANALYTICS_CONSTANTS');
      expect(trackingConstants).toHaveProperty('PERFORMANCE_THRESHOLDS');
      expect(trackingConstants).toHaveProperty('ENVIRONMENT_METADATA');
      
      // Verify structure matches expected format
      expect(trackingConstants.TRACKING_CONSTANTS).toHaveProperty('MAX_MEMORY_USAGE');
      expect(trackingConstants.ANALYTICS_CONSTANTS).toHaveProperty('MAX_CACHE_SIZE');
      expect(trackingConstants.PERFORMANCE_THRESHOLDS).toHaveProperty('MAX_RESPONSE_TIME');
    });
    
  });

});

// Helper test to run memory leak detection over time
describe('🕒 Long-running Memory Leak Detection', () => {
  
  test('should not leak memory over extended usage', async () => {
    const memorySnapshots: MemorySnapshot[] = [];
    
    // Take initial snapshot
    memorySnapshots.push(takeMemorySnapshot());
    
    // Simulate extended usage
    for (let iteration = 0; iteration < 10; iteration++) {
      // Create and use calculator multiple times
      for (let i = 0; i < 10; i++) {
        const constants = await getEnvironmentConstants();
        expect(constants).toBeDefined();
        
        if (i % 3 === 0) {
          await recalculateEnvironmentConstants();
        }
      }
      
      // Force garbage collection and take snapshot
      await forceGarbageCollection();
      memorySnapshots.push(takeMemorySnapshot());
      
      // Small delay to allow for cleanup
      await new Promise(resolve => setTimeout(resolve, 50));
    }
    
    // Cleanup
    await shutdownEnvironmentCalculator();
    await forceGarbageCollection();
    
    // Analyze memory growth trend
    const initialMemory = memorySnapshots[0].heapUsed;
    const finalMemory = memorySnapshots[memorySnapshots.length - 1].heapUsed;
    const memoryGrowth = finalMemory - initialMemory;
    
    // Should not grow significantly over 100 operations
    expect(memoryGrowth).toBeLessThan(20 * 1024 * 1024); // Less than 20MB growth
    
    // Check for memory growth trend (should be relatively stable)
    const midpointMemory = memorySnapshots[Math.floor(memorySnapshots.length / 2)].heapUsed;
    const midpointGrowth = midpointMemory - initialMemory;
    const finalGrowthFromMidpoint = finalMemory - midpointMemory;
    
    // Growth in second half should not be significantly higher than first half
    expect(finalGrowthFromMidpoint).toBeLessThanOrEqual(midpointGrowth * 2);
    
  }, 30000); // Allow 30 seconds for this extended test
  
});