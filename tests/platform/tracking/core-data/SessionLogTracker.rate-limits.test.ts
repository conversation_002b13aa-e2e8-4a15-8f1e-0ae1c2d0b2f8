import { SessionLogTracker } from '../../../../server/src/platform/tracking/core-data/SessionLogTracker';
import { jest } from '@jest/globals';
import * as fs from 'fs';

// Mock fs module
jest.mock('fs', () => ({
  mkdirSync: jest.fn(),
  existsSync: jest.fn(() => true),
  writeFileSync: jest.fn(),
  appendFileSync: jest.fn(),
  readFileSync: jest.fn(() => '[]'),
  promises: {
    writeFile: jest.fn(),
    appendFile: jest.fn(),
    readFile: jest.fn(() => Promise.resolve('[]')),
  }
}));

describe('SessionLogTracker Rate Limiting and Event Counting', () => {
  let sessionLogTracker: SessionLogTracker;

  beforeEach(() => {
    jest.useFakeTimers();
    // Reset all mocks
    jest.clearAllMocks();
    sessionLogTracker = new SessionLogTracker();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  describe('Rate Limiting', () => {
    it('should enforce rate limits before event counts', async () => {
      const sessionId = 'rate-limit-test';
      const actor = 'test-actor';
      
      await sessionLogTracker.startSession(sessionId, actor);

      // Fill up to rate limit
      for (let i = 0; i < sessionLogTracker['_maxEventsPerMinute']; i++) {
        await sessionLogTracker.logSessionEvent(
          sessionId,
          'info',
          'RATE_TEST',
          `Event ${i}`
        );
      }

      // Attempt one more event - should fail due to rate limit
      await expect(
        sessionLogTracker.logSessionEvent(
          sessionId,
          'info',
          'RATE_TEST',
          'Rate limit exceeded'
        )
      ).rejects.toThrow('Event rate limit exceeded');

      // Advance time to reset rate limit window
      jest.advanceTimersByTime(sessionLogTracker['_rateLimitWindowMs'] + 1000);

      // Should now be able to log events again
      await expect(
        sessionLogTracker.logSessionEvent(
          sessionId,
          'info',
          'RATE_TEST',
          'After window reset'
        )
      ).resolves.not.toThrow();
    });

    it('should reset rate limit counter after window expires', async () => {
      const sessionId = 'rate-window-test';
      const actor = 'test-actor';
      
      await sessionLogTracker.startSession(sessionId, actor);

      // Log half the rate limit
      const halfLimit = Math.floor(sessionLogTracker['_maxEventsPerMinute'] / 2);
      for (let i = 0; i < halfLimit; i++) {
        await sessionLogTracker.logSessionEvent(
          sessionId,
          'info',
          'RATE_TEST',
          `First half ${i}`
        );
      }

      // Advance time past window
      jest.advanceTimersByTime(sessionLogTracker['_rateLimitWindowMs'] + 1000);

      // Should be able to log full rate limit again
      for (let i = 0; i < sessionLogTracker['_maxEventsPerMinute']; i++) {
        await sessionLogTracker.logSessionEvent(
          sessionId,
          'info',
          'RATE_TEST',
          `Second batch ${i}`
        );
      }

      // Next event should fail
      await expect(
        sessionLogTracker.logSessionEvent(
          sessionId,
          'info',
          'RATE_TEST',
          'Should fail'
        )
      ).rejects.toThrow('Event rate limit exceeded');
    });
  });

  describe('Event Counting', () => {
    it('should only increment event count after security checks pass', async () => {
      const sessionId = 'event-count-test';
      const actor = 'test-actor';
      
      await sessionLogTracker.startSession(sessionId, actor);

      // Fill up to rate limit
      for (let i = 0; i < sessionLogTracker['_maxEventsPerMinute']; i++) {
        await sessionLogTracker.logSessionEvent(
          sessionId,
          'info',
          'COUNT_TEST',
          `Event ${i}`
        );
      }

      const countBefore = sessionLogTracker['_sessionEventCounts'].get(sessionId);

      // Try to exceed rate limit
      try {
        await sessionLogTracker.logSessionEvent(
          sessionId,
          'info',
          'COUNT_TEST',
          'Should fail'
        );
      } catch (error) {
        // Expected error
      }

      const countAfter = sessionLogTracker['_sessionEventCounts'].get(sessionId);

      // Count should not have increased
      expect(countAfter).toBe(countBefore);
    });

    it('should maintain accurate event counts across rate limit windows', async () => {
      const sessionId = 'count-accuracy-test';
      const actor = 'test-actor';
      
      await sessionLogTracker.startSession(sessionId, actor);

      // Log events in first window
      const firstBatch = Math.floor(sessionLogTracker['_maxEventsPerMinute'] / 2);
      for (let i = 0; i < firstBatch; i++) {
        await sessionLogTracker.logSessionEvent(
          sessionId,
          'info',
          'COUNT_TEST',
          `First batch ${i}`
        );
      }

      // Advance time past window
      jest.advanceTimersByTime(sessionLogTracker['_rateLimitWindowMs'] + 1000);

      // Log events in second window
      for (let i = 0; i < firstBatch; i++) {
        await sessionLogTracker.logSessionEvent(
          sessionId,
          'info',
          'COUNT_TEST',
          `Second batch ${i}`
        );
      }

      // Total count should be sum of both batches
      const totalCount = sessionLogTracker['_sessionEventCounts'].get(sessionId);
      expect(totalCount).toBe(firstBatch * 2);
    });

    it('should enforce total event limit across rate limit windows', async () => {
      const sessionId = 'total-limit-test';
      const actor = 'test-actor';
      
      await sessionLogTracker.startSession(sessionId, actor);

      // Log events up to near the total limit
      const batchSize = Math.floor(sessionLogTracker['_maxEventsPerMinute'] / 2);
      const numBatches = Math.floor(sessionLogTracker['_maxEventsPerSession'] / batchSize);

      for (let batch = 0; batch < numBatches; batch++) {
        // Log batch of events
        for (let i = 0; i < batchSize; i++) {
          await sessionLogTracker.logSessionEvent(
            sessionId,
            'info',
            'TOTAL_TEST',
            `Batch ${batch} Event ${i}`
          );
        }

        // Advance time to next window
        jest.advanceTimersByTime(sessionLogTracker['_rateLimitWindowMs'] + 1000);
      }

      // Try one more event - should fail due to total limit
      await expect(
        sessionLogTracker.logSessionEvent(
          sessionId,
          'info',
          'TOTAL_TEST',
          'Should fail'
        )
      ).rejects.toThrow('Maximum events per session limit reached');
    });
  });

  describe('Security Integration', () => {
    it('should properly handle concurrent event logging attempts', async () => {
      const sessionId = 'concurrent-test';
      const actor = 'test-actor';
      
      await sessionLogTracker.startSession(sessionId, actor);

      // Try to log multiple events concurrently
      const promises = Array.from({ length: sessionLogTracker['_maxEventsPerMinute'] + 5 }, (_, i) =>
        sessionLogTracker.logSessionEvent(
          sessionId,
          'info',
          'CONCURRENT_TEST',
          `Event ${i}`
        )
      );

      // Some should succeed, some should fail
      const results = await Promise.allSettled(promises);
      
      const succeeded = results.filter(r => r.status === 'fulfilled').length;
      const failed = results.filter(r => r.status === 'rejected').length;

      // Should have exactly maxEventsPerMinute successes
      expect(succeeded).toBe(sessionLogTracker['_maxEventsPerMinute']);
      expect(failed).toBe(5);

      // Event count should match successful events
      const finalCount = sessionLogTracker['_sessionEventCounts'].get(sessionId);
      expect(finalCount).toBe(succeeded);
    });

    it('should maintain security metrics during rate limiting', async () => {
      const sessionId = 'metrics-test';
      const actor = 'test-actor';
      
      await sessionLogTracker.startSession(sessionId, actor);

      // Fill up to rate limit
      for (let i = 0; i < sessionLogTracker['_maxEventsPerMinute']; i++) {
        await sessionLogTracker.logSessionEvent(
          sessionId,
          'info',
          'METRICS_TEST',
          `Event ${i}`
        );
      }

      // Try to exceed limit
      try {
        await sessionLogTracker.logSessionEvent(
          sessionId,
          'info',
          'METRICS_TEST',
          'Should fail'
        );
      } catch (error) {
        // Expected error
      }

      // Check security metrics
      expect(sessionLogTracker['_sessionSecurityMetrics'].rateLimitViolations).toBeGreaterThan(0);
      expect(sessionLogTracker['_sessionSecurityMetrics'].totalEventChecks).toBe(
        sessionLogTracker['_maxEventsPerMinute'] + 1
      );
    });
  });
}); 