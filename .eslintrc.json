{"env": {"node": true, "es6": true, "jest": true}, "extends": ["eslint:recommended"], "parserOptions": {"ecmaVersion": 2020, "sourceType": "module"}, "rules": {"no-unused-vars": "off", "no-undef": "off"}, "overrides": [{"files": ["**/*.ts"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "rules": {"@typescript-eslint/no-unused-vars": "warn"}}, {"files": ["**/__tests__/**/*.ts", "**/*.test.ts"], "rules": {"@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "off"}}], "ignorePatterns": ["dist/", "coverage/", "node_modules/", "*.js"]}