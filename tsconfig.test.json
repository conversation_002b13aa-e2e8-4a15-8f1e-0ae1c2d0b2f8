{"extends": "./tsconfig.json", "compilerOptions": {"target": "es2020", "module": "commonjs", "lib": ["es2020", "dom"], "allowJs": true, "checkJs": false, "declaration": false, "declarationMap": false, "sourceMap": true, "outDir": "./dist/test", "removeComments": false, "noEmit": false, "incremental": false, "tsBuildInfoFile": null, "strict": true, "noImplicitAny": false, "strictNullChecks": true, "strictFunctionTypes": true, "strictBindCallApply": true, "strictPropertyInitialization": false, "noImplicitReturns": true, "noImplicitThis": true, "alwaysStrict": true, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "noImplicitOverride": false, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": false, "noFallthroughCasesInSwitch": true, "moduleResolution": "node", "baseUrl": "./", "paths": {"@/*": ["./*"], "@server/*": ["./server/src/*"], "@shared/*": ["./shared/src/*"], "@tracking/*": ["./server/src/platform/tracking/*"], "@governance/*": ["./server/src/platform/governance/*"], "@tests/*": ["./tests/*"]}, "rootDirs": ["./server", "./shared", "./tests"], "typeRoots": ["./node_modules/@types", "./types"], "types": ["node", "jest"], "allowSyntheticDefaultImports": true, "esModuleInterop": true, "preserveSymlinks": false, "allowUmdGlobalAccess": false, "resolveJsonModule": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "skipDefaultLibCheck": false, "skipLibCheck": true, "forceConsistentCasingInFileNames": true}, "include": ["server/**/*", "shared/**/*", "tests/**/*", "**/__tests__/**/*", "**/*.test.ts", "**/*.spec.ts", "jest.setup.js", "jest.config.js"], "exclude": ["node_modules", "dist", "coverage", ".jest-cache", "docs"], "ts-node": {"esm": false, "experimentalSpecifierResolution": "node", "transpileOnly": true, "files": true, "compilerOptions": {"target": "es2020", "module": "commonjs", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "experimentalDecorators": true, "emitDecoratorMetadata": true}}}