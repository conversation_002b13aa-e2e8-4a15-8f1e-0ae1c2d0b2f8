# TypeScript Build Configuration

## 📋 Overview

The OA Framework is configured to compile all TypeScript files to a centralized `/dist` directory, providing clean separation between source code and compiled output.

## 🏗️ Build Structure

```
project-root/
├── dist/                          # 🎯 All compiled output
│   ├── server/
│   │   └── src/
│   │       ├── *.js              # Compiled JavaScript
│   │       ├── *.d.ts            # Type declarations
│   │       └── *.js.map          # Source maps
│   ├── shared/
│   │   └── src/
│   │       ├── *.js
│   │       ├── *.d.ts
│   │       └── *.js.map
│   └── client/                    # (if exists)
│       └── src/
├── server/                        # 📝 TypeScript source
│   └── src/
├── shared/                        # 📝 TypeScript source
│   └── src/
└── client/                        # 📝 TypeScript source (if exists)
    └── src/
```

## ⚙️ Configuration Files

### Main TypeScript Configuration (`tsconfig.json`)

```json
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "commonjs",
    "lib": ["ES2020"],
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "outDir": "./dist",              // 🎯 Output to dist directory
    "rootDir": "./",                 // 🎯 Root source directory
    "declaration": true,             // 🎯 Generate .d.ts files
    "sourceMap": true,               // 🎯 Generate source maps
    "baseUrl": ".",
    "paths": {
      "@shared/*": ["shared/src/*"],
      "@server/*": ["server/src/*"],
      "@client/*": ["client/src/*"]
    }
  },
  "include": [
    "server/**/*",
    "shared/**/*",
    "client/**/*"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "**/*.test.ts",
    "**/*.spec.ts"
  ]
}
```

### Test Configuration (`tsconfig.test.json`)

- Extends main configuration
- Includes test files
- Outputs to `./dist/test`
- Configured for Jest testing environment

## 🚀 Available Scripts

### Build Scripts

```bash
# Clean build (recommended)
npm run build

# Watch mode for development
npm run build:watch

# Production build with linting
npm run build:production

# Clean build artifacts
npm run clean

# Full clean including source artifacts
npm run clean:full

# Display build information
npm run build:info
```

### Development Scripts

```bash
# Run in development mode (ts-node)
npm run dev

# Run with watch mode (ts-node)
npm run dev:watch

# Run compiled application
npm start

# Run in production mode
npm run start:prod
```

## 🛠️ Build Process

1. **Clean**: Remove existing `dist` directory
2. **Compile**: TypeScript compilation to `dist`
3. **Generate**: Type declarations (`.d.ts`) and source maps (`.js.map`)
4. **Verify**: Build completion confirmation

## 📁 Output Details

### JavaScript Files (`.js`)
- Compiled from TypeScript source
- ES2020 target with CommonJS modules
- Optimized for Node.js runtime

### Type Declarations (`.d.ts`)
- Generated for all TypeScript files
- Enables type checking for consumers
- Maintains full type information

### Source Maps (`.js.map`)
- Maps compiled JavaScript back to TypeScript
- Enables debugging in original TypeScript
- Essential for development workflow

## 🔧 Path Resolution

The build maintains the original directory structure:

```
Source: server/src/platform/tracking/core.ts
Output: dist/server/src/platform/tracking/core.js
Types:  dist/server/src/platform/tracking/core.d.ts
Maps:   dist/server/src/platform/tracking/core.js.map
```

## 🚫 Excluded from Build

- `node_modules/` - Dependencies
- `**/*.test.ts` - Test files
- `**/*.spec.ts` - Specification files
- `dist/` - Previous build output
- `coverage/` - Test coverage reports

## 🎯 Benefits

### ✅ Clean Separation
- Source code remains in original directories
- All compiled output in dedicated `dist` directory
- No JavaScript files mixed with TypeScript source

### ✅ Development Workflow
- Watch mode for real-time compilation
- Source maps for debugging
- Type declarations for IDE support

### ✅ Production Ready
- Optimized JavaScript output
- Complete type information
- Easy deployment from `dist` directory

### ✅ Maintenance
- Simple cleanup with `npm run clean`
- Clear build artifacts management
- Consistent output structure

## 🔍 Verification

After building, verify the structure:

```bash
npm run build
tree dist -I node_modules
```

Expected output shows organized JavaScript, declaration, and source map files in the `dist` directory.

## 📝 Notes

- The `dist` directory is excluded from version control (`.gitignore`)
- Build artifacts should be regenerated for each deployment
- Source TypeScript files remain the authoritative source
- All imports and path mappings work correctly with compiled output

---

**🎯 This configuration provides a professional, maintainable TypeScript build system with clean separation of source and compiled code.**
