# AI Tool Override Implementation Guide

**Document Type**: Practical Implementation Guide  
**Version**: 1.0.0  
**Created**: 2025-06-19  
**Purpose**: How to use Template Creation Policy Override with AI Tools in IDE  
**Authority**: President & CEO, E<PERSON>Z. Consultancy  

## 🎯 **Quick Implementation**

### **1. Document Placement**
Place the **Template Creation Policy Override** document in your project root for AI Tool access:

```
project-root/
├── docs/
│   ├── governance/
│   ├── plan/
│   └── policies/
│       └── template-creation-policy-override.md  ← HERE
├── src/
└── templates/
```

### **2. AI Tool Instruction Pattern**
When implementing any component, use this instruction pattern:

```bash
AI Tool, implement [COMPONENT_NAME] following the Template Creation Policy Override.

CONTEXT: 
- Component: [COMPONENT_NAME] from [M0/M1] milestone
- Override Policy: docs/policies/template-creation-policy-override.md
- Latest Standards: docs/core/development-standards.md
- Governance Rules: docs/governance/rules/primary-governance-rules.json

REQUIREMENTS:
- Use on-demand template creation (ignore milestone template paths)
- Inherit latest governance and development standards
- Apply server/shared/client project structure
- Follow component specifications from milestone document

PROCESS:
1. Analyze component requirements from milestone spec
2. Create template dynamically with current standards
3. Apply template to implement component
4. Save template for future use
```

## 🔧 **Practical Usage Examples**

### **Example 1: M0 Governance Component**
```bash
AI Tool, implement GovernanceRuleExecutionContext following the Template Creation Policy Override.

CONTEXT:
- Component: governance-rule-execution-context from M0 milestone
- Override Policy: docs/policies/template-creation-policy-override.md
- Milestone Spec: docs/plan/milestone-00-governance-tracking.md
- Target Path: server/src/platform/governance/rule-management/

OVERRIDE INSTRUCTIONS:
- IGNORE: Template path specified in M0 document
- USE: On-demand template creation with latest standards
- INHERIT: Current governance standards (v2.x)
- IMPLEMENT: IExecutionContext, IGovernanceService interfaces
- APPLY: governance-service inheritance pattern

Please create the template dynamically and implement the component.
```

### **Example 2: M1 Infrastructure Component**
```bash
AI Tool, implement DatabaseService following the Template Creation Policy Override.

CONTEXT:
- Component: platform-database-service from M1 milestone  
- Override Policy: docs/policies/template-creation-policy-override.md
- Milestone Spec: docs/plan/milestone-01-core-infrastructure.md
- Target Path: server/src/platform/infrastructure/database/

OVERRIDE INSTRUCTIONS:
- IGNORE: Template path specified in M1 document
- USE: On-demand template creation with latest standards
- INHERIT: Current development standards (v2.x)
- IMPLEMENT: IDatabaseService, IPlatformService interfaces
- APPLY: platform-service inheritance pattern

Please create the template dynamically and implement the component.
```

## 🤖 **AI Tool Workflow Integration**

### **Step-by-Step Process**

#### **Step 1: Project Setup**
```bash
# Ensure AI Tool has access to override policy
AI Tool, please confirm you have access to:
- docs/policies/template-creation-policy-override.md
- docs/core/development-standards.md  
- docs/governance/rules/primary-governance-rules.json

# Verify project structure understanding
AI Tool, confirm the project uses server/shared/client structure, not src/platform/.
```

#### **Step 2: Component Implementation**
```bash
# Standard implementation command with override
AI Tool, implement [COMPONENT] using Template Creation Policy Override:

MILESTONE: [M0/M1/M2]
COMPONENT: [component-name]
OVERRIDE_POLICY: docs/policies/template-creation-policy-override.md
STANDARDS_MODE: latest-inheritance
TEMPLATE_STRATEGY: on-demand-creation

# Follow the override process:
1. Read component spec from milestone document
2. Ignore any template path specifications  
3. Create template dynamically with current standards
4. Implement component using generated template
```

#### **Step 3: Verification**
```bash
# Verify override was applied correctly
AI Tool, confirm that:
1. Template was created on-demand (not pre-existing)
2. Latest standards were inherited
3. Component follows server/shared/client structure
4. Template was saved to correct location
```

## 🎛️ **IDE Integration Patterns**

### **VS Code Integration**

#### **Workspace Settings (.vscode/settings.json)**
```json
{
  "ai.governance.templatePolicy": "docs/policies/template-creation-policy-override.md",
  "ai.governance.standards": "docs/core/development-standards.md",
  "ai.governance.rules": "docs/governance/rules/primary-governance-rules.json",
  "ai.project.structure": "server-shared-client",
  "ai.template.strategy": "on-demand-creation"
}
```

#### **Custom AI Commands (if supported)**
```json
{
  "ai.customCommands": {
    "implementWithOverride": {
      "description": "Implement component with Template Creation Policy Override",
      "template": "AI Tool, implement ${componentName} following Template Creation Policy Override. Component: ${componentName} from ${milestone} milestone. Override Policy: docs/policies/template-creation-policy-override.md"
    }
  }
}
```

### **Command Shortcuts**

#### **Create component-specific prompts:**
```bash
# Save as snippet or macro
AI Tool, implement ${COMPONENT_NAME} following Template Creation Policy Override.
CONTEXT: Component ${COMPONENT_NAME} from ${MILESTONE} milestone
OVERRIDE: docs/policies/template-creation-policy-override.md  
STANDARDS: latest inheritance mode
STRUCTURE: server/shared/client
PROCESS: on-demand template creation → component implementation
```

## 📋 **Project Context Integration**

### **Method 1: Direct Reference**
```bash
AI Tool, I'm implementing components from M0/M1 milestones. 

IMPORTANT: Always follow the Template Creation Policy Override document at docs/policies/template-creation-policy-override.md

This overrides any template paths specified in milestone documents. Use on-demand template creation with latest standards instead.

Ready to implement [COMPONENT_NAME]?
```

### **Method 2: Project Context File**
Create a `.ai-context` file in project root:
```markdown
# AI Tool Project Context

## Template Strategy
- **Policy**: docs/policies/template-creation-policy-override.md
- **Mode**: On-demand template creation
- **Standards**: Latest inheritance (docs/core/development-standards.md)
- **Structure**: server/shared/client (NOT src/platform)

## Override Rules
- IGNORE milestone template paths
- USE on-demand creation with current standards
- APPLY server/shared/client project structure
- INHERIT latest governance and development standards

## Implementation Process
1. Read component spec from milestone
2. Apply override policy
3. Create template dynamically
4. Implement with current standards
```

### **Method 3: Session Context**
```bash
# At start of coding session
AI Tool, for this session please:

1. Reference docs/policies/template-creation-policy-override.md for all template behavior
2. Use on-demand template creation (ignore milestone template paths)
3. Apply server/shared/client structure (not src/platform)
4. Inherit latest standards from docs/core/development-standards.md

Confirm you understand this context for the session.
```

## 🔄 **Workflow Examples**

### **Daily Development Workflow**

#### **Morning Setup**
```bash
AI Tool, starting development session.

CONTEXT SETUP:
- Project: OA Framework
- Override Policy: docs/policies/template-creation-policy-override.md
- Standards Mode: latest-inheritance
- Structure: server/shared/client
- Template Strategy: on-demand-creation

Ready to implement M0/M1 components with override policy.
```

#### **Component Implementation**
```bash
# For each component:
AI Tool, implement [COMPONENT] from [MILESTONE]:
- Follow Template Creation Policy Override
- Use latest standards inheritance  
- Apply on-demand template creation
- Target: server/src/platform/[module]/[component]
```

#### **Verification**
```bash
AI Tool, verify the implementation:
- Template created on-demand? ✅/❌
- Latest standards applied? ✅/❌  
- Correct file structure? ✅/❌
- Template saved for reuse? ✅/❌
```

### **Team Development Coordination**

#### **Onboarding New Developers**
```bash
# Share this context with team members
Team AI Tool Setup:

1. Add override policy to project context
2. Use on-demand template creation instructions
3. Always reference latest standards
4. Follow server/shared/client structure

Template: "AI Tool, implement [COMPONENT] following Template Creation Policy Override..."
```

## 🎯 **Success Indicators**

### **AI Tool Should Display:**
```bash
✅ "Reading Template Creation Policy Override..."
✅ "Ignoring milestone template path specification"
✅ "Creating template on-demand with latest standards"
✅ "Inheriting current governance rules v2.x"
✅ "Template created: templates/server/platform/[module]/[component].ts.template"
✅ "Component implemented with current standards"
```

### **AI Tool Should NOT Display:**
```bash
❌ "Template not found: templates/milestones/m0/..."
❌ "Using pre-created template from milestone spec"
❌ "Template creation failed - using fallback"
❌ "Following src/platform structure"
```

## 🔧 **Troubleshooting**

### **Common Issues & Solutions**

#### **AI Tool ignores override policy:**
```bash
# Be more explicit
AI Tool, CRITICAL: You must follow docs/policies/template-creation-policy-override.md 
This overrides ALL template specifications in milestone documents.
Use on-demand template creation, not pre-created templates.
```

#### **AI Tool uses wrong project structure:**
```bash
# Correct structure explicitly  
AI Tool, IMPORTANT: Project structure is server/shared/client, NOT src/platform.
Components go in: server/src/platform/[module]/
Templates go in: templates/server/platform/[module]/
```

#### **AI Tool references old standards:**
```bash
# Emphasize latest standards
AI Tool, CRITICAL: Always use LATEST standards inheritance.
Get current version from docs/core/development-standards.md
Don't use cached or old standard versions.
```

## ✅ **Implementation Checklist**

- [ ] Override policy document placed in docs/policies/
- [ ] AI Tool has access to project context
- [ ] Team members trained on override instructions
- [ ] Component implementation commands prepared
- [ ] Verification process established
- [ ] Project structure clarified (server/shared/client)
- [ ] Standards inheritance mode configured (latest)

**This guide ensures the Template Creation Policy Override is properly utilized with AI Tools in your IDE environment! 🎯**