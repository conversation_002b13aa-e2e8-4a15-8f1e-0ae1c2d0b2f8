# OA Framework Governance Rule System

## Overview
This directory contains the governance rule engine that overrides all framework standards.

## Structure
- `rules/` - Rule files with cryptographic integrity protection
- `scripts/` - Management utilities for rule maintenance

## Rule Files
- `primary-governance-rules.json` - Master rule file with inheritance chain
- `company-branding-rules.json` - E.Z. Consultancy branding standards
- `document-formatting-rules.json` - Headers, versioning, formatting rules
- `ai-behavior-constraints.json` - AI operation constraints

## Usage
Rules automatically override governance standards when documents are generated by the Enhanced Orchestration Driver.

## Integrity Management
All rule files protected with SHA256 integrity hashes.

### Update Rule Integrity (after editing rules)
```bash
node scripts/update-rule-integrity.js
```

### Validate Rule Integrity
```bash
node scripts/validate-all-rules.js
```

### Check Rule Files Exist
```bash
node scripts/create-rule-files.js
```

## Security
- Rule files contain SHA256 integrity hashes
- Orchestration driver validates integrity before using rules
- System refuses to operate with tampered rules

## Rule Inheritance Chain
1. `primary-governance-rules.json` (master)
   - Inherits from: `company-branding-rules.json`
   - Inherits from: `document-formatting-rules.json`
   - Inherits from: `ai-behavior-constraints.json`

## Authorization
All rules authorized by: **President & CEO, E.Z. Consultancy**

---
*Generated: 2025-06-12 07:20:59 +03* 