{"$schema": "http://json-schema.org/draft-07/schema#", "title": "OA Framework Governance Rule Schema", "type": "object", "required": ["ruleMetadata"], "properties": {"ruleMetadata": {"type": "object", "required": ["version", "created", "authorizedBy", "integrityHash"], "properties": {"version": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+$"}, "created": {"type": "string", "format": "date"}, "authorizedBy": {"type": "string", "const": "President & CEO, E.Z. Consultancy"}, "integrityHash": {"type": "string", "pattern": "^SHA256:[a-f0-9]{64}$"}, "lastVerified": {"type": "string", "format": "date-time"}, "description": {"type": "string"}}}, "inheritedRules": {"type": "array", "items": {"type": "string", "pattern": "^\\./.*\\.json$"}}, "overrideRules": {"type": "object"}, "branding": {"type": "object", "properties": {"companyName": {"type": "string", "const": "E.Z. Consultancy"}, "executiveAuthority": {"type": "string", "const": "President & CEO, E.Z. Consultancy"}, "authorshipFormat": {"type": "string"}, "reviewerFormat": {"type": "string"}}}, "headers": {"type": "object"}, "versionHistory": {"type": "object"}, "documentStructure": {"type": "object"}, "constraints": {"type": "object"}}}