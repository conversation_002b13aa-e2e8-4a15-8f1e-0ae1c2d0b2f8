const fs = require('fs').promises;
const crypto = require('crypto');
const path = require('path');

class RuleIntegrityValidator {
  static async calculateSHA256(content) {
    return crypto.createHash('sha256').update(content).digest('hex');
  }

  static async validateRuleFile(filePath) {
    const content = await fs.readFile(filePath, 'utf8');
    const rules = JSON.parse(content);
    
    const storedHash = rules.ruleMetadata?.integrityHash?.replace('SHA256:', '');
    if (!storedHash) {
      return { valid: false, error: 'No integrity hash found' };
    }
    
    const contentForHashing = { ...rules };
    delete contentForHashing.ruleMetadata.integrityHash;
    delete contentForHashing.ruleMetadata.lastVerified;
    
    const currentHash = await this.calculateSHA256(JSON.stringify(contentForHashing, null, 2));
    
    return {
      valid: storedHash === currentHash,
      stored: storedHash,
      current: currentHash,
      file: path.basename(filePath)
    };
  }

  static async validateAllRules() {
    const rulesDir = path.join(__dirname, '../rules');
    const files = await fs.readdir(rulesDir);
    const results = [];
    
    for (const file of files.filter(f => f.endsWith('.json'))) {
      const result = await this.validateRuleFile(path.join(rulesDir, file));
      results.push(result);
      
      if (result.valid) {
        console.log(`✅ ${result.file}: VALID`);
      } else {
        console.log(`❌ ${result.file}: INVALID - ${result.error || 'Hash mismatch'}`);
      }
    }
    
    const allValid = results.every(r => r.valid);
    console.log(`\n🔍 Overall Status: ${allValid ? 'ALL RULES VALID' : 'INTEGRITY VIOLATIONS DETECTED'}`);
    
    return allValid;
  }
}

RuleIntegrityValidator.validateAllRules().catch(console.error); 