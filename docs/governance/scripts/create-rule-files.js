const fs = require('fs').promises;
const path = require('path');

class RuleFileCreator {
  static async createRuleFile(fileName, ruleContent) {
    const rulesDir = path.join(__dirname, '../rules');
    const filePath = path.join(rulesDir, fileName);
    
    const ruleData = {
      ruleMetadata: {
        version: "1.0.0",
        created: new Date().toISOString().split('T')[0],
        authorizedBy: "President & CEO, E.Z. Consultancy",
        integrityHash: "SHA256:PLACEHOLDER",
        ...ruleContent.metadata
      },
      ...ruleContent.data
    };
    
    await fs.writeFile(filePath, JSON.stringify(ruleData, null, 2));
    console.log(`✅ Created rule file: ${fileName}`);
  }
  
  static async ensureRuleFilesExist() {
    const rulesDir = path.join(__dirname, '../rules');
    
    try {
      await fs.access(rulesDir);
    } catch {
      await fs.mkdir(rulesDir, { recursive: true });
      console.log('📁 Created rules directory');
    }
    
    const requiredFiles = [
      'primary-governance-rules.json',
      'company-branding-rules.json',
      'document-formatting-rules.json',
      'ai-behavior-constraints.json'
    ];
    
    for (const file of requiredFiles) {
      try {
        await fs.access(path.join(rulesDir, file));
        console.log(`✅ ${file} exists`);
      } catch {
        console.log(`⚠️ ${file} missing - would need to be created`);
      }
    }
  }
}

RuleFileCreator.ensureRuleFilesExist().catch(console.error); 