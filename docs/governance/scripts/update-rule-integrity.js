const fs = require('fs').promises;
const crypto = require('crypto');
const path = require('path');

class RuleIntegrityManager {
  static async calculateSHA256(content) {
    return crypto.createHash('sha256').update(content).digest('hex');
  }

  static async updateRuleFileIntegrity(filePath) {
    const content = await fs.readFile(filePath, 'utf8');
    const rules = JSON.parse(content);
    
    const contentForHashing = { ...rules };
    delete contentForHashing.ruleMetadata.integrityHash;
    delete contentForHashing.ruleMetadata.lastVerified;
    
    const hash = await this.calculateSHA256(JSON.stringify(contentForHashing, null, 2));
    
    rules.ruleMetadata.integrityHash = `SHA256:${hash}`;
    rules.ruleMetadata.lastVerified = new Date().toISOString();
    
    await fs.writeFile(filePath, JSON.stringify(rules, null, 2));
    console.log(`✅ Updated integrity hash for: ${path.basename(filePath)}`);
  }

  static async updateAllRuleFiles() {
    const rulesDir = path.join(__dirname, '../rules');
    const files = await fs.readdir(rulesDir);
    
    for (const file of files.filter(f => f.endsWith('.json'))) {
      await this.updateRuleFileIntegrity(path.join(rulesDir, file));
    }
  }
}

RuleIntegrityManager.updateAllRuleFiles().catch(console.error); 