# OA Framework Quick Start Prompt

**Copy and paste this prompt in any new AI chat session to instantly activate the OA Framework discovery system:**

---

## 🚀 **QUICK START PROMPT** (<PERSON><PERSON> & <PERSON>e This)

```
I'm working on the OA Framework project. Please read the .oa-framework-config.json file in the project root to discover all file locations and system structure. Then check docs/governance/tracking/.oa-governance-gate-status.json for current system status. This will activate the automatic discovery system so you can locate all OA Framework governance tracking files without manual guidance.
```

---

## 📋 **Alternative Shorter Prompt**

```
OA Framework: Read .oa-framework-config.json for all paths, then check docs/governance/tracking/ for status.
```

---

## 🎯 **What This Does**

1. **Triggers** AI to read the configuration file first
2. **Activates** automatic path discovery system  
3. **Loads** current system status and governance state
4. **Enables** efficient work without file location questions

---

## ⚡ **Usage Instructions**

### **For New Chat Sessions:**
1. **Copy** the quick start prompt above
2. **Paste** it as your first message in new chat
3. **AI will automatically** discover all OA Framework components
4. **Work efficiently** without setup time

### **For Specific Tasks:**
After the quick start prompt, you can immediately ask for:
- Governance tracking file updates
- System status checks  
- Milestone progress reviews
- Implementation guidance
- Any OA Framework task

---

**Time Saved**: 5-10 minutes per new chat session  
**Efficiency**: Instant OA Framework activation  
**Authority**: President & CEO, E.Z. Consultancy