# MemorySafeResourceManagerEnhanced API Reference

**Document Type**: API Reference  
**Version**: 1.0.0  
**Created**: 2025-07-22 16:45:00 +03  
**Authority**: President & CEO, <PERSON><PERSON><PERSON><PERSON> Consultancy  
**Classification**: Foundation-Context-API-Reference  

---

## 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)

**Authority Level**: critical-api-documentation  
**Authority Validator**: "President & CEO, E.Z. Consultancy"  
**Governance ADR**: ADR-foundation-001-tracking-architecture  
**Governance DCR**: DCR-foundation-001-tracking-development  
**Governance Status**: approved  
**Governance Compliance**: authority-validated  

## 🔗 CROSS-CONTEXT REFERENCES (v2.1)

**Component Path**: `shared/src/base/MemorySafeResourceManagerEnhanced.ts`  
**Related Documentation**: [Component Documentation](../components/memory-safe-resource-manager-enhanced.md)  
**Integration Guides**: [Resource Pool Guide](../guides/resource-pool-management.md)  

---

## 📋 CLASS: MemorySafeResourceManagerEnhanced

### **Inheritance**
```typescript
class MemorySafeResourceManagerEnhanced extends MemorySafeResourceManager
```

### **Constructor**

#### **constructor(limits?: IResourceLimits)**

Creates a new instance of MemorySafeResourceManagerEnhanced.

**Parameters:**
- `limits?: IResourceLimits` - Optional resource limits configuration

**Example:**
```typescript
const manager = new MemorySafeResourceManagerEnhanced({
  maxIntervals: 1000,
  maxTimeouts: 1000,
  maxCacheSize: 10000,
  cleanupIntervalMs: 60000
});
```

---

## 🏊 RESOURCE POOL MANAGEMENT

### **createResourcePool<T>(name, factory, cleanup, config)**

Creates a new resource pool with the specified configuration.

**Signature:**
```typescript
createResourcePool<T>(
  name: string,
  factory: () => T,
  cleanup: (resource: T) => void,
  config: IResourcePoolConfig
): IResourcePool<T>
```

**Parameters:**
- `name: string` - Unique identifier for the pool
- `factory: () => T` - Function to create new resources
- `cleanup: (resource: T) => void` - Function to cleanup resources
- `config: IResourcePoolConfig` - Pool configuration options

**Returns:** `IResourcePool<T>` - The created resource pool

**Throws:**
- `ResourcePoolError` - If pool creation fails
- `Error` - If manager is not initialized

**Example:**
```typescript
const dbPool = manager.createResourcePool(
  'database-connections',
  () => new DatabaseConnection(),
  (conn) => conn.close(),
  {
    minSize: 5,
    maxSize: 20,
    idleTimeoutMs: 30000,
    validationInterval: 5000,
    autoScale: true,
    scalingPolicy: 'adaptive'
  }
);
```

### **getResourcePool<T>(name)**

Retrieves an existing resource pool by name.

**Signature:**
```typescript
getResourcePool<T>(name: string): IResourcePool<T> | undefined
```

**Parameters:**
- `name: string` - Name of the pool to retrieve

**Returns:** `IResourcePool<T> | undefined` - The pool if found, undefined otherwise

**Example:**
```typescript
const pool = manager.getResourcePool<DatabaseConnection>('database-connections');
if (pool) {
  const connection = await pool.borrowResource();
}
```

### **removeResourcePool(name)**

Removes and shuts down a resource pool.

**Signature:**
```typescript
async removeResourcePool(name: string): Promise<boolean>
```

**Parameters:**
- `name: string` - Name of the pool to remove

**Returns:** `Promise<boolean>` - True if pool was removed, false if not found

**Example:**
```typescript
const removed = await manager.removeResourcePool('database-connections');
console.log('Pool removed:', removed);
```

---

## 🔗 ENHANCED REFERENCE COUNTING

### **createAdvancedSharedResource<T>(factory, cleanup, name?)**

Creates an advanced shared resource with enhanced reference counting capabilities.

**Signature:**
```typescript
createAdvancedSharedResource<T>(
  factory: () => T,
  cleanup: (resource: T) => void,
  name?: string
): IAdvancedSharedResource<T>
```

**Parameters:**
- `factory: () => T` - Function to create the resource
- `cleanup: (resource: T) => void` - Function to cleanup the resource
- `name?: string` - Optional name for the resource

**Returns:** `IAdvancedSharedResource<T>` - Advanced shared resource wrapper

**Example:**
```typescript
const { resource, addRef, releaseRef } = manager.createAdvancedSharedResource(
  () => new ExpensiveResource(),
  (res) => res.dispose(),
  'expensive-resource'
);

// Add strong reference
const strongRef = addRef();

// Add weak reference
const weakRef = addRef(true);

// Release references
releaseRef(strongRef);
releaseRef(weakRef);
```

### **getAdvancedSharedResource<T>(name)**

Retrieves an existing advanced shared resource by name.

**Signature:**
```typescript
getAdvancedSharedResource<T>(name: string): IAdvancedSharedResource<T> | undefined
```

**Parameters:**
- `name: string` - Name of the shared resource

**Returns:** `IAdvancedSharedResource<T> | undefined` - The resource if found

---

## 📈 DYNAMIC SCALING

### **enableDynamicScaling(config)**

Enables dynamic resource scaling with the specified configuration.

**Signature:**
```typescript
enableDynamicScaling(config: IResourceScalingConfig): void
```

**Parameters:**
- `config: IResourceScalingConfig` - Scaling configuration

**Throws:**
- `ScalingError` - If scaling configuration is invalid

**Example:**
```typescript
manager.enableDynamicScaling({
  enabled: true,
  targetUtilization: 70,
  scaleUpThreshold: 85,
  scaleDownThreshold: 50,
  cooldownPeriod: 5000,
  maxScaleRate: 0.1,
  scalingPolicy: 'adaptive'
});
```

### **disableDynamicScaling()**

Disables dynamic resource scaling.

**Signature:**
```typescript
disableDynamicScaling(): void
```

### **getScalingMetrics()**

Returns current scaling metrics and status.

**Signature:**
```typescript
getScalingMetrics(): IScalingMetrics
```

**Returns:** `IScalingMetrics` - Current scaling metrics

---

## 📡 LIFECYCLE EVENTS

### **enableResourceLifecycleEvents(config)**

Enables resource lifecycle event emission with the specified configuration.

**Signature:**
```typescript
enableResourceLifecycleEvents(config: IResourceLifecycleConfig): void
```

**Parameters:**
- `config: IResourceLifecycleConfig` - Lifecycle events configuration

**Example:**
```typescript
manager.enableResourceLifecycleEvents({
  enableEvents: true,
  eventBufferSize: 100,
  emitInterval: 1000,
  enabledEvents: new Set(['created', 'accessed', 'cleanup', 'pooled']),
  eventHandlers: new Map([
    ['created', (event) => console.log('Resource created:', event)],
    ['pooled', (event) => console.log('Resource pooled:', event)]
  ])
});
```

### **disableResourceLifecycleEvents()**

Disables resource lifecycle event emission.

**Signature:**
```typescript
disableResourceLifecycleEvents(): void
```

### **emitLifecycleEvent(event)**

Manually emit a lifecycle event.

**Signature:**
```typescript
emitLifecycleEvent(event: IResourceLifecycleEvent): void
```

**Parameters:**
- `event: IResourceLifecycleEvent` - The event to emit

---

## 📊 MONITORING AND METRICS

### **getEnhancedResourceMetrics()**

Returns comprehensive resource metrics including enhanced features.

**Signature:**
```typescript
getEnhancedResourceMetrics(): IEnhancedResourceMetrics
```

**Returns:** `IEnhancedResourceMetrics` - Complete metrics object

**Example:**
```typescript
const metrics = manager.getEnhancedResourceMetrics();
console.log('Pool count:', metrics.poolMetrics.poolCount);
console.log('Events emitted:', metrics.eventMetrics.totalEventsEmitted);
```

### **isEnhancedHealthy()**

Checks the health status of the enhanced manager and all its features.

**Signature:**
```typescript
isEnhancedHealthy(): boolean
```

**Returns:** `boolean` - True if all enhanced features are healthy

### **getPoolMetrics(poolName)**

Returns metrics for a specific resource pool.

**Signature:**
```typescript
getPoolMetrics(poolName: string): IResourcePoolMetrics | undefined
```

**Parameters:**
- `poolName: string` - Name of the pool

**Returns:** `IResourcePoolMetrics | undefined` - Pool metrics if found

---

## 🔧 CONFIGURATION INTERFACES

### **IResourcePoolConfig**

Configuration interface for resource pools.

```typescript
interface IResourcePoolConfig {
  minSize: number;                    // Minimum pool size
  maxSize: number;                    // Maximum pool size
  idleTimeoutMs: number;             // Idle timeout in milliseconds
  validationInterval: number;         // Validation interval in milliseconds
  autoScale: boolean;                // Enable auto-scaling
  scalingPolicy: TScalingPolicy;     // Scaling policy
}
```

### **IResourceScalingConfig**

Configuration interface for dynamic scaling.

```typescript
interface IResourceScalingConfig {
  enabled: boolean;                   // Enable dynamic scaling
  targetUtilization: number;         // Target utilization percentage (0-100)
  scaleUpThreshold: number;          // Scale up threshold percentage
  scaleDownThreshold: number;        // Scale down threshold percentage
  cooldownPeriod: number;            // Cooldown period in milliseconds
  maxScaleRate: number;              // Maximum scale rate (0-1)
  scalingPolicy: TScalingPolicy;     // Scaling policy
}
```

### **IResourceLifecycleConfig**

Configuration interface for lifecycle events.

```typescript
interface IResourceLifecycleConfig {
  enableEvents: boolean;                           // Enable event emission
  eventBufferSize: number;                        // Event buffer size
  emitInterval: number;                           // Emit interval in milliseconds
  enabledEvents: Set<TLifecycleEventType>;       // Enabled event types
  eventHandlers: Map<TLifecycleEventType, Function>; // Event handlers
}
```

---

## 🏗️ RETURN TYPE INTERFACES

### **IResourcePool<T>**

Interface for resource pools.

```typescript
interface IResourcePool<T> {
  readonly name: string;
  readonly config: IResourcePoolConfig;
  borrowResource(): Promise<T>;
  returnResource(resource: T): void;
  getMetrics(): IResourcePoolMetrics;
  isHealthy(): boolean;
  shutdown(): Promise<void>;
}
```

### **IAdvancedSharedResource<T>**

Interface for advanced shared resources.

```typescript
interface IAdvancedSharedResource<T> {
  resource: T;
  addRef: (isWeak?: boolean) => string;
  releaseRef: (refId: string) => void;
  getRefCount: () => { strong: number; weak: number };
  getAccessPattern: () => IAccessPattern;
  isActive: () => boolean;
}
```

### **IEnhancedResourceMetrics**

Comprehensive metrics interface.

```typescript
interface IEnhancedResourceMetrics {
  // Base metrics (inherited)
  totalResources: number;
  activeIntervals: number;
  activeTimeouts: number;
  memoryUsageMB: number;
  
  // Enhanced metrics
  enhancementMetrics: {
    poolsCreated: number;
    advancedRefsCreated: number;
    scalingOperations: number;
    eventsEmitted: number;
  };
  
  poolMetrics: {
    poolCount: number;
    totalBorrows: number;
    totalReturns: number;
    averageUtilization: number;
  };
  
  referenceMetrics: {
    strongRefCount: number;
    weakRefCount: number;
    advancedRefCount: number;
    accessPatternCount: number;
  };
  
  eventMetrics: {
    totalEventsEmitted: number;
    eventBufferUtilization: number;
    averageEmitLatency: number;
    handlerErrorCount: number;
  };
}
```

---

## 🚨 ERROR TYPES

### **ResourcePoolError**

Error thrown during resource pool operations.

```typescript
class ResourcePoolError extends Error {
  constructor(message: string, public poolName: string) {
    super(message);
    this.name = 'ResourcePoolError';
  }
}
```

### **ScalingError**

Error thrown during scaling operations.

```typescript
class ScalingError extends Error {
  constructor(message: string, public operation: string) {
    super(message);
    this.name = 'ScalingError';
  }
}
```

### **ReferenceError**

Error thrown during reference counting operations.

```typescript
class ReferenceError extends Error {
  constructor(message: string, public resourceName: string) {
    super(message);
    this.name = 'ReferenceError';
  }
}
```

### **LifecycleEventError**

Error thrown during lifecycle event operations.

```typescript
class LifecycleEventError extends Error {
  constructor(message: string, public eventType: string) {
    super(message);
    this.name = 'LifecycleEventError';
  }
}
```

---

## 📡 EVENT TYPES

### **Lifecycle Events**

```typescript
type TLifecycleEventType = 
  | 'created'     // Resource created
  | 'accessed'    // Resource accessed
  | 'cleanup'     // Resource cleaned up
  | 'pooled'      // Resource added to pool
  | 'borrowed'    // Resource borrowed from pool
  | 'returned'    // Resource returned to pool
  | 'recycled'    // Resource recycled in pool
  | 'scaled'      // Pool scaled up/down
  | 'validated'   // Resource validated
  | 'expired';    // Resource expired
```

### **Event Objects**

```typescript
interface IResourceLifecycleEvent {
  type: TLifecycleEventType;
  resourceId: string;
  resourceName?: string;
  poolName?: string;
  timestamp: number;
  metadata?: Record<string, any>;
}

interface ILifecycleEventsBatch {
  events: IResourceLifecycleEvent[];
  batchSize: number;
  component: string;
  timestamp: number;
}
```

---

## 🔄 INHERITED METHODS

All methods from the base `MemorySafeResourceManager` class are available:

- `initialize(): Promise<void>`
- `shutdown(): Promise<void>`
- `createSafeInterval(callback, intervalMs, name): string`
- `createSafeTimeout(callback, timeoutMs, name): string`
- `createSharedResource<T>(factory, cleanup, name): ISharedResource<T>`
- `getResourceMetrics(): IResourceMetrics`
- `isHealthy(): boolean`
- `forceCleanup(): Promise<void>`

For complete base class documentation, see [MemorySafeResourceManager API](./memory-safe-resource-manager-api.md).

---

## 📚 USAGE EXAMPLES

### **Complete Example**

```typescript
import { MemorySafeResourceManagerEnhanced } from '@/shared/base/MemorySafeResourceManagerEnhanced';

// Initialize manager
const manager = new MemorySafeResourceManagerEnhanced({
  maxIntervals: 1000,
  maxTimeouts: 1000,
  maxCacheSize: 10000,
  cleanupIntervalMs: 60000
});

await manager.initialize();

// Enable all enhanced features
manager.enableDynamicScaling({
  enabled: true,
  targetUtilization: 70,
  scaleUpThreshold: 85,
  scaleDownThreshold: 50,
  cooldownPeriod: 5000,
  maxScaleRate: 0.1,
  scalingPolicy: 'adaptive'
});

manager.enableResourceLifecycleEvents({
  enableEvents: true,
  eventBufferSize: 100,
  emitInterval: 1000,
  enabledEvents: new Set(['created', 'pooled', 'borrowed', 'returned']),
  eventHandlers: new Map()
});

// Create resource pool
const dbPool = manager.createResourcePool(
  'database-connections',
  () => new DatabaseConnection(),
  (conn) => conn.close(),
  {
    minSize: 5,
    maxSize: 20,
    idleTimeoutMs: 30000,
    validationInterval: 5000,
    autoScale: true,
    scalingPolicy: 'adaptive'
  }
);

// Use the pool
const connection = await dbPool.borrowResource();
try {
  // Use connection
  await connection.query('SELECT * FROM users');
} finally {
  dbPool.returnResource(connection);
}

// Monitor metrics
const metrics = manager.getEnhancedResourceMetrics();
console.log('Pool utilization:', metrics.poolMetrics.averageUtilization);

// Cleanup
await manager.shutdown();
```

---

## 🔗 RELATED DOCUMENTATION

- [Component Documentation](../components/memory-safe-resource-manager-enhanced.md)
- [Resource Pool Management Guide](../guides/resource-pool-management.md)
- [Dynamic Scaling Guide](../guides/dynamic-scaling.md)
- [Lifecycle Events Guide](../guides/lifecycle-events.md)
- [Migration Guide](../guides/migration-enhanced.md)

---

## 📝 VERSION HISTORY

**v1.0.0 (2025-07-22)**
- Initial API documentation for MemorySafeResourceManagerEnhanced
- Complete method signatures and interfaces
- Comprehensive examples and usage patterns
- Error handling documentation
- Event system documentation

**Governance Status**: approved  
**Governance Compliance**: security-validated
