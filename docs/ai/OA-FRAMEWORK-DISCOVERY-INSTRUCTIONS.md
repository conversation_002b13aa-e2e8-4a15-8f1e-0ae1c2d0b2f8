# OA Framework Discovery Instructions for AI Assistants

**Document Type**: AI Discovery Instructions  
**Version**: 2.0.0  
**Created**: 2025-06-16 15:56:55 +03  
**Authority**: President & CEO, <PERSON><PERSON><PERSON><PERSON>tancy  
**Purpose**: Enable AI assistants to automatically discover and locate OA Framework components

---

## 🎯 **CRITICAL: READ THIS FIRST**

**Every AI assistant working on the OA Framework MUST follow this discovery sequence:**

### **Step 1: Read Configuration File**
```
ALWAYS read: .oa-framework-config.json (in project root)
```
This file contains ALL paths and locations for the entire OA Framework.

### **Step 2: Locate Governance Tracking Files**
```
ALWAYS check: docs/governance/tracking/
```
This directory contains all governance tracking and status files.

### **Step 3: Check System Status**
```
ALWAYS read: docs/governance/tracking/.oa-governance-gate-status.json
```
This file contains current system status and operational state.

---

## 🔍 **AUTOMATIC DISCOVERY PROTOCOL**

### **Primary Discovery Method**
1. **Read `.oa-framework-config.json`** - Single source of truth for all paths
2. **Use `frameworkPaths` section** - For directory navigation
3. **Use `trackingFiles` section** - For specific file locations
4. **Use `discoveryPatterns` section** - For pattern-based file finding

### **Governance Tracking Files Discovery**
```json
{
  "trackingFiles": {
    "governanceGateStatus": "docs/governance/tracking/.oa-governance-gate-status.json",
    "implementationProgress": "docs/governance/tracking/.oa-implementation-progress.json",
    "sessionLog": "docs/governance/tracking/.oa-session-log.jsonl",
    "governanceTracking": "docs/governance/tracking/.oa-governance-tracking.json",
    "governanceSession": "docs/governance/tracking/.oa-governance-session.json",
    "governanceCompliance": "docs/governance/tracking/.oa-governance-compliance.json"
  }
}
```

### **Directory Structure Discovery**
```json
{
  "frameworkPaths": {
    "governanceRoot": "docs/governance",
    "trackingDirectory": "docs/governance/tracking",
    "milestonesDirectory": "docs/governance/milestones",
    "templatesDirectory": "docs/governance/templates",
    "indexesDirectory": "docs/governance/indexes"
  }
}
```

---

## 🚀 **QUICK START DISCOVERY SEQUENCE**

### **For Any OA Framework Task:**
1. **Read** `.oa-framework-config.json` first
2. **Check** `docs/governance/tracking/.oa-governance-gate-status.json` for system status
3. **Use** config file paths for all file operations
4. **Never** hardcode paths - always use config file references

### **For Governance Tasks:**
1. **Read** `.oa-framework-config.json` for all paths
2. **Check** `docs/governance/tracking/` for current status
3. **Use** `milestoneStructure` section for milestone navigation
4. **Use** `governanceTemplates` section for template locations

### **For Implementation Tasks:**
1. **Read** `.oa-framework-config.json` for all paths
2. **Check** `docs/governance/tracking/.oa-implementation-progress.json` for progress
3. **Use** `coreFrameworkFiles` section for framework components
4. **Follow** governance gate status for implementation authorization

---

## 📁 **FILE LOCATION PATTERNS**

### **Tracking Files Pattern**
```
docs/governance/tracking/.oa-*.json*
```

### **Governance Files Pattern**
```
docs/governance/**/*.md
```

### **Milestone Pattern**
```
docs/governance/milestones/M*
```

### **Template Pattern**
```
docs/governance/templates/*.md
```

---

## 🎛️ **SYSTEM STATUS DISCOVERY**

### **Check System Status**
```
File: docs/governance/tracking/.oa-governance-gate-status.json
Key: systemHealth.overallHealth
```

### **Check Governance Status**
```
File: docs/governance/tracking/.oa-governance-session.json
Key: governance_workflow_state.governance_gate_status
```

### **Check Implementation Progress**
```
File: docs/governance/tracking/.oa-implementation-progress.json
Key: enhancedCurrentSession.milestone.current
```

---

## 🔧 **DISCOVERY TOOLS & COMMANDS**

### **Find All Tracking Files**
```bash
find docs/governance/tracking -name ".oa-*.json*"
```

### **List Governance Structure**
```bash
ls -la docs/governance/
```

### **Check Config File**
```bash
cat .oa-framework-config.json | jq '.trackingFiles'
```

---

## ⚡ **EFFICIENCY RULES**

### **DO THIS** ✅
- **ALWAYS** read `.oa-framework-config.json` first
- **USE** config file paths for all operations
- **CHECK** tracking directory for current status
- **FOLLOW** discovery sequence for consistency

### **DON'T DO THIS** ❌
- **NEVER** hardcode file paths
- **NEVER** guess file locations
- **NEVER** skip config file reading
- **NEVER** assume directory structure

---

## 🎯 **COMMON DISCOVERY SCENARIOS**

### **Scenario 1: "Find OA Framework governance tracking files"**
```
1. Read .oa-framework-config.json
2. Use trackingFiles section for exact paths
3. Check docs/governance/tracking/ directory
4. Use specific file paths from config
```

### **Scenario 2: "Check system status"**
```
1. Read .oa-framework-config.json
2. Go to docs/governance/tracking/.oa-governance-gate-status.json
3. Check systemHealth section
4. Check governanceGateStatus section
```

### **Scenario 3: "Find milestone governance"**
```
1. Read .oa-framework-config.json
2. Use milestoneStructure section for paths
3. Navigate to specific milestone directory
4. Check milestone governance artifacts
```

---

## 🔐 **AUTHORITY & COMPLIANCE**

### **Discovery Authority**
- **Primary Authority**: President & CEO, E.Z. Consultancy
- **Technical Authority**: Lead Soft Engineer
- **Implementation Authority**: AI Assistant (E.Z. Consultancy)

### **Compliance Requirements**
- **MUST** use config file for all path discovery
- **MUST** check governance gate status before implementation
- **MUST** follow authority validation requirements
- **MUST** maintain tracking file organization

---

## 📊 **DISCOVERY VALIDATION**

### **Validate Discovery Success**
1. **Config File Read**: ✅ `.oa-framework-config.json` loaded
2. **Tracking Directory Found**: ✅ `docs/governance/tracking/` accessible
3. **System Status Retrieved**: ✅ Governance gate status known
4. **File Paths Resolved**: ✅ All required paths available

### **Discovery Failure Recovery**
1. **Check** if `.oa-framework-config.json` exists in project root
2. **Verify** `docs/governance/tracking/` directory exists
3. **Confirm** tracking files are present and readable
4. **Escalate** to authority if discovery fails

---

## 🚀 **INTEGRATION WITH ORCHESTRATION DRIVER**

### **Enhanced Orchestration Driver Integration**
- **Version**: 6.2.1
- **Config Aware**: YES
- **Auto Discovery**: ENABLED
- **Smart Path Resolution**: ACTIVE

### **Discovery Performance**
- **Config File Caching**: ENABLED
- **Path Resolution Caching**: ENABLED
- **Discovery Time**: < 1 second
- **Memory Usage**: Optimized

---

**🏛️ Authority**: President & CEO, E.Z. Consultancy  
**📊 Status**: ACTIVE - Discovery Protocol Operational  
**🔐 Security**: Config File Protected  
**📈 Efficiency**: Maximum Discovery Speed Enabled  

---

*This discovery protocol ensures AI assistants can automatically locate and work with OA Framework components without manual guidance, saving time and preventing errors.* 