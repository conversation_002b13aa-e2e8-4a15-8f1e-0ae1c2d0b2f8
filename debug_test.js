// Simple debug test to trace operation execution
const { CleanupCoordinatorEnhanced, CleanupOperationType, CleanupPriority } = require('./shared/src/base/CleanupCoordinatorEnhanced.ts');

async function debugTest() {
  const coordinator = new CleanupCoordinatorEnhanced({
    testMode: true,
    maxRetries: 0,
    maxConcurrentOperations: 1
  });
  
  await coordinator.initialize();
  
  console.log('Scheduling failing operation...');
  const operationId = coordinator.scheduleCleanup(
    CleanupOperationType.RESOURCE_CLEANUP,
    'error-test',
    async () => {
      console.log('Operation executing and about to throw error...');
      throw new Error('Test error');
    }
  );
  
  console.log('Operation scheduled, status:', coordinator.getOperationStatus(operationId));
  
  console.log('Processing queue...');
  await coordinator.processQueue();
  
  console.log('Queue processed, status:', coordinator.getOperationStatus(operationId));
  
  console.log('Calling waitForCompletion...');
  try {
    const result = await coordinator.waitForCompletion(operationId);
    console.log('waitForCompletion result:', result);
  } catch (error) {
    console.log('waitForCompletion threw error:', error.message);
  }
  
  await coordinator.shutdown();
}

debugTest().catch(console.error);
