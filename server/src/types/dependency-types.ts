/**
 * @file dependency-types.ts
 * @filepath server/src/types/dependency-types.ts
 * @reference G-TSK-04.SUB-04.1.IMP-01.TYP-01
 * @component dependency-types
 * @template on-demand-creation-with-latest-standards
 * @tier T2
 * @context foundation-context
 * @category Core
 * @created 2025-06-30
 * @modified 2025-06-30
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level core-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-dependency-architecture
 * @governance-dcr DCR-foundation-001-dependency-management
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on foundation-context.CORE.dependency-injection
 * @enables all-contexts.DI.service-resolution
 * @related-contexts foundation-context, all-contexts
 * @governance-impact core-foundation
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type type-definitions
 * @lifecycle-stage implementation
 * @testing-status unit-tested
 * @deployment-ready true
 * @documentation docs/contexts/foundation-context/types/dependency-types.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 */

export const TYPES = {
  // Security Services
  CryptoManager: Symbol.for('CryptoManager'),
  SecurityManager: Symbol.for('SecurityManager'),
  AuthorizationManager: Symbol.for('AuthorizationManager'),
  
  // Core Services
  LoggingService: Symbol.for('LoggingService'),
  MonitoringService: Symbol.for('MonitoringService'),
  ConfigurationService: Symbol.for('ConfigurationService'),
  
  // Additional Services
  ValidationService: Symbol.for('ValidationService'),
  AuditService: Symbol.for('AuditService'),
  MetricsService: Symbol.for('MetricsService'),

  // Extended Security Services
  IntegrityValidator: Symbol.for('IntegrityValidator'),
  AuditLogger: Symbol.for('AuditLogger'),
  HashManager: Symbol.for('HashManager'),
  StorageManager: Symbol.for('StorageManager')
}; 