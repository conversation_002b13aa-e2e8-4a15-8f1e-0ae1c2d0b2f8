/**
 * @file storage-interfaces.ts
 * @filepath server/src/interfaces/storage/storage-interfaces.ts
 * @description Storage Management Interface Definitions
 * @created 2025-01-02
 * @modified 2025-01-02
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level interface-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-storage-architecture
 * @governance-dcr DCR-foundation-001-storage-development
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on foundation-context.INT.configuration-interfaces
 * @enables foundation-context.SERV.storage-manager
 * @related-contexts foundation-context, data-context
 * @governance-impact storage-interface-foundation
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type interface-definitions
 * @lifecycle-stage implementation
 * @testing-status interface-validated
 * @deployment-ready true
 * @monitoring-enabled false
 * @documentation docs/contexts/foundation-context/interfaces/storage-interfaces.md
 */

// Storage Configuration
export interface StorageConfig {
  type: 'memory' | 'file' | 'database' | 'persistent' | 'distributed';
  connectionString?: string;
  maxSize?: number;
  retentionDays?: number;
  compression?: boolean;
  encryption?: boolean;
  backupEnabled?: boolean;
  replicationFactor?: number;
}

// Storage Result
export interface StorageResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

// Storage Query
export interface StorageQuery {
  collection?: string;
  filters?: Record<string, any>;
  sort?: Record<string, 1 | -1>;
  limit?: number;
  offset?: number;
  projection?: string[];
}

// Storage Transaction
export interface StorageTransaction {
  id: string;
  operations: StorageOperation[];
  timestamp: Date;
  status: 'pending' | 'committed' | 'rolled_back';
}

// Storage Operation
export interface StorageOperation {
  type: 'create' | 'read' | 'update' | 'delete';
  collection: string;
  key?: string;
  data?: any;
  query?: StorageQuery;
}

// Storage Metrics
export interface StorageMetrics {
  totalSize: number;
  usedSize: number;
  availableSize: number;
  operationCount: number;
  averageResponseTime: number;
  errorRate: number;
  timestamp: Date;
}

// Storage Manager Interface
export interface IStorageManager {
  initialize(config: StorageConfig): Promise<void>;
  store(key: string, data: any, collection?: string): Promise<StorageResult>;
  retrieve(key: string, collection?: string): Promise<StorageResult>;
  query(query: StorageQuery): Promise<StorageResult>;
  update(key: string, data: any, collection?: string): Promise<StorageResult>;
  delete(key: string, collection?: string): Promise<StorageResult>;
  exists(key: string, collection?: string): Promise<boolean>;
  getMetrics(): Promise<StorageMetrics>;
  backup(): Promise<StorageResult>;
  restore(backupId: string): Promise<StorageResult>;
  cleanup(): Promise<StorageResult>;
} 