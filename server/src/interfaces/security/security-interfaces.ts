/**
 * @file security-interfaces.ts
 * @filepath server/src/interfaces/security/security-interfaces.ts
 * @reference G-TSK-04.SUB-04.1.IMP-01.INT-01
 * @component security-interfaces
 * @template on-demand-creation-with-latest-standards
 * @tier T2
 * @context foundation-context
 * @category Security
 * @created 2025-06-30
 * @modified 2025-06-30
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level security-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-security-architecture
 * @governance-dcr DCR-foundation-001-security-development
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on foundation-context.SERV.security-service
 * @enables authentication-context.AUTH.security-validation
 * @related-contexts foundation-context, authentication-context
 * @governance-impact security-foundation
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type interface-definitions
 * @lifecycle-stage implementation
 * @testing-status unit-tested
 * @deployment-ready true
 * @documentation docs/contexts/foundation-context/interfaces/security-interfaces.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 */

import { EncryptionAlgorithm, HashingAlgorithm } from './crypto-interfaces';

export interface ISecurityManager {
  initialize(): Promise<void>;
  validateSecurity(context: SecurityContext): Promise<SecurityValidationResult>;
  applySecurityPolicy(context: SecurityContext, policy: SecurityPolicy): Promise<void>;
  handleSecurityViolation(violation: SecurityViolation): Promise<void>;
  generateAuditRecord(context: SecurityContext): Promise<SecurityAuditRecord>;
  getSecurityMetrics(): Promise<SecurityMetrics>;
  generateDashboardData(): Promise<SecurityDashboardData>;
  exportSecurityData(format: SecurityExportFormat): Promise<SecurityExportResult>;
}

export interface ISecurityService extends ISecurityManager {}

export interface SecurityManagerConfig {
  encryptionAlgorithm: EncryptionAlgorithm;
  hashingAlgorithm: HashingAlgorithm;
  cacheTimeout: number;
  maxCacheSize: number;
  alertThreshold: number;
  monitoringInterval: number;
  auditRetentionDays: number;
}

export interface SecurityContext {
  id: string;
  timestamp: Date;
  source: string;
  type: string;
  metadata: Record<string, unknown>;
}

export interface SecurityPolicy {
  id: string;
  name: string;
  rules: SecurityRule[];
  metadata: Record<string, unknown>;
}

export interface SecurityRule {
  id: string;
  type: string;
  conditions: Record<string, unknown>;
  actions: Record<string, unknown>;
}

export interface SecurityViolation {
  id: string;
  timestamp: Date;
  context: SecurityContext;
  policy: SecurityPolicy;
  severity: string;
  details: Record<string, unknown>;
}

export interface SecurityValidationResult {
  valid: boolean;
  timestamp: Date;
  violations: SecurityViolation[];
  metadata?: Record<string, unknown>;
}

export interface SecurityAuditRecord {
  id: string;
  timestamp: Date;
  context: SecurityContext;
  action: string;
  result: Record<string, unknown>;
  signature?: string;
}

export interface SecurityMetrics {
  timestamp: Date;
  validationCount: number;
  violationCount: number;
  policyCompliance: number;
  riskScore: number;
  metrics: Record<string, number>;
}

export interface SecurityDashboardData {
  metrics: SecurityMetrics;
  alerts: SecurityViolation[];
  trends: Record<string, unknown>;
  recommendations: string[];
}

export enum SecurityExportFormat {
  JSON = 'json',
  CSV = 'csv',
  PDF = 'pdf'
}

export interface SecurityExportResult {
  format: SecurityExportFormat;
  data: unknown;
  metadata: Record<string, unknown>;
} 