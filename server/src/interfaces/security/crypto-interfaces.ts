/**
 * @file crypto-interfaces.ts
 * @filepath server/src/interfaces/security/crypto-interfaces.ts
 * @reference G-TSK-04.SUB-04.1.IMP-01.INT-02
 * @component crypto-interfaces
 * @template on-demand-creation-with-latest-standards
 * @tier T2
 * @context foundation-context
 * @category Security
 * @created 2025-06-30
 * @modified 2025-06-30
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level security-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-security-architecture
 * @governance-dcr DCR-foundation-001-security-development
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on foundation-context.SERV.crypto-service
 * @enables security-context.SEC.encryption-validation
 * @related-contexts foundation-context, security-context
 * @governance-impact security-foundation
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type interface-definitions
 * @lifecycle-stage implementation
 * @testing-status unit-tested
 * @deployment-ready true
 * @documentation docs/contexts/foundation-context/interfaces/crypto-interfaces.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 */

export interface ICryptoManager {
  initialize(config: CryptoConfig): Promise<void>;
  encrypt(data: unknown, key: string): Promise<string>;
  decrypt(data: string, key: string): Promise<unknown>;
  hash(data: string): Promise<string>;
  sign(data: unknown): Promise<DigitalSignature>;
  verify(data: unknown, signature: DigitalSignature): Promise<boolean>;
  generateKeyPair(): Promise<KeyPair>;
}

export enum EncryptionAlgorithm {
  AES256 = 'AES-256-GCM',
  AES192 = 'AES-192-GCM',
  AES128 = 'AES-128-GCM'
}

export enum HashingAlgorithm {
  SHA256 = 'SHA-256',
  SHA384 = 'SHA-384',
  SHA512 = 'SHA-512'
}

export interface KeyPair {
  publicKey: string;
  privateKey: string;
  algorithm: string;
  metadata: Record<string, unknown>;
}

export interface DigitalSignature {
  signature: string;
  algorithm: string;
  timestamp: Date;
  metadata: Record<string, unknown>;
}

export interface CryptoConfig {
  algorithm: EncryptionAlgorithm;
  hashAlgorithm: HashingAlgorithm;
  keySize?: number;
  options?: Record<string, unknown>;
} 