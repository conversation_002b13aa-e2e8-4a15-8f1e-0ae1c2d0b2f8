/**
 * @file audit-interfaces.ts
 * @filepath server/src/interfaces/security/audit-interfaces.ts
 * @description Audit Logging Interface Definitions
 * @created 2025-01-02
 * @modified 2025-01-02
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level interface-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-security-architecture
 * @governance-dcr DCR-foundation-001-security-development
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on foundation-context.INT.security-interfaces
 * @enables foundation-context.SERV.audit-logger
 * @related-contexts foundation-context, authentication-context
 * @governance-impact audit-interface-foundation
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type interface-definitions
 * @lifecycle-stage implementation
 * @testing-status interface-validated
 * @deployment-ready true
 * @monitoring-enabled false
 * @documentation docs/contexts/foundation-context/interfaces/audit-interfaces.md
 */

// Audit Log Entry
export interface AuditLogEntry {
  id: string;
  timestamp: Date;
  userId?: string;
  sessionId?: string;
  action: string;
  resource: string;
  result: 'success' | 'failure' | 'error';
  details: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

// Audit Context
export interface AuditContext {
  id: string;
  component: string;
  environment: string;
  correlationId?: string;
  metadata: Record<string, any>;
  timestamp: Date;
}

// Audit Policy
export interface AuditPolicy {
  id: string;
  name: string;
  description: string;
  rules: AuditRule[];
  retention: {
    days: number;
    archiveAfter: number;
  };
  enabled: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Audit Rule
export interface AuditRule {
  id: string;
  type: 'inclusion' | 'exclusion' | 'transformation';
  conditions: AuditCondition[];
  actions: AuditAction[];
}

// Audit Condition
export interface AuditCondition {
  field: string;
  operator: 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'regex';
  value: any;
}

// Audit Action
export interface AuditAction {
  type: 'log' | 'alert' | 'block' | 'transform';
  parameters: Record<string, any>;
}

// Audit Violation
export interface AuditViolation {
  id: string;
  entryId: string;
  policyId: string;
  ruleId: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  details: Record<string, any>;
  timestamp: Date;
  resolved: boolean;
}

// Audit Metrics
export interface AuditMetrics {
  totalEntries: number;
  entriesPerDay: number;
  violationCount: number;
  averageProcessingTime: number;
  storageUtilization: number;
  timestamp: Date;
  retentionCompliance: number;
}

// Audit Dashboard Data
export interface AuditDashboardData {
  metrics: AuditMetrics;
  recentEntries: AuditLogEntry[];
  recentViolations: AuditViolation[];
  activePolicies: AuditPolicy[];
  systemHealth: {
    status: 'healthy' | 'warning' | 'critical';
    uptime: number;
    lastUpdate: Date;
  };
  visualizations: {
    charts: any[];
    graphs: any[];
    tables: any[];
  };
}

// Export Formats
export enum AuditExportFormat {
  JSON = 'json',
  CSV = 'csv',
  XML = 'xml',
  PDF = 'pdf',
  SYSLOG = 'syslog'
}

// Export Result
export interface AuditExportResult {
  format: AuditExportFormat;
  data: string | Buffer;
  filename: string;
  size: number;
  timestamp: Date;
  checksum: string;
  entryCount: number;
}

// Audit Logger Configuration
export interface AuditLoggerConfig {
  storageType: 'memory' | 'file' | 'database' | 'persistent';
  cacheTimeout: number;
  maxCacheSize: number;
  alertThreshold: number;
  monitoringInterval: number;
  retentionDays: number;
}

// Main Audit Logger Interface
export interface IAuditLogger {
  initialize(): Promise<void>;
  logAuditEntry(entry: AuditLogEntry, context: AuditContext): Promise<void>;
  handleAuditViolation(violation: AuditViolation): Promise<void>;
  getAuditMetrics(): Promise<AuditMetrics>;
  generateDashboardData(): Promise<AuditDashboardData>;
  exportAuditData(format: AuditExportFormat): Promise<AuditExportResult>;
}

// Auditing Service Interface
export interface IAuditingService {
  initialize(): Promise<void>;
  logAuditEntry(entry: AuditLogEntry, context: AuditContext): Promise<void>;
  getAuditMetrics(): Promise<AuditMetrics>;
  generateDashboardData(): Promise<AuditDashboardData>;
  exportAuditData(format: AuditExportFormat): Promise<AuditExportResult>;
} 