/**
 * @file integrity-interfaces.ts
 * @filepath server/src/interfaces/security/integrity-interfaces.ts
 * @description Integrity Validation Interface Definitions
 * @created 2025-01-02
 * @modified 2025-01-02
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level interface-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-security-architecture
 * @governance-dcr DCR-foundation-001-security-development
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on foundation-context.INT.security-interfaces
 * @enables foundation-context.SERV.integrity-validator
 * @related-contexts foundation-context, authentication-context
 * @governance-impact integrity-interface-foundation
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type interface-definitions
 * @lifecycle-stage implementation
 * @testing-status interface-validated
 * @deployment-ready true
 * @monitoring-enabled false
 * @documentation docs/contexts/foundation-context/interfaces/integrity-interfaces.md
 */

// Integrity Context
export interface IntegrityContext {
  id: string;
  data: any;
  metadata: {
    source: string;
    timestamp: Date;
    version: string;
    checksum?: string;
  };
  validationRules: string[];
}

// Integrity Policy
export interface IntegrityPolicy {
  id: string;
  name: string;
  description: string;
  rules: IntegrityRule[];
  severity: 'low' | 'medium' | 'high' | 'critical';
  enabled: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Integrity Rule
export interface IntegrityRule {
  id: string;
  type: 'hash' | 'signature' | 'checksum' | 'structural';
  parameters: Record<string, any>;
  errorMessage: string;
}

// Integrity Violation
export interface IntegrityViolation {
  id: string;
  contextId: string;
  policyId: string;
  ruleId: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  details: Record<string, any>;
  timestamp: Date;
  resolved: boolean;
}

// Integrity Validation Result
export interface IntegrityValidationResult {
  valid: boolean;
  timestamp: Date;
  violations: IntegrityViolation[];
  score?: number;
  recommendations?: string[];
  metadata?: Record<string, any>;
}

// Integrity Metrics
export interface IntegrityMetrics {
  validationCount: number;
  violationCount: number;
  policyApplicationCount: number;
  averageResponseTime: number;
  successRate: number;
  timestamp: Date;
  integrityScore: number;
}

// Integrity Dashboard Data
export interface IntegrityDashboardData {
  metrics: IntegrityMetrics;
  recentViolations: IntegrityViolation[];
  activePolicies: IntegrityPolicy[];
  systemHealth: {
    status: 'healthy' | 'warning' | 'critical';
    uptime: number;
    lastUpdate: Date;
  };
  visualizations: {
    charts: any[];
    graphs: any[];
    tables: any[];
  };
}

// Export Formats
export enum IntegrityExportFormat {
  JSON = 'json',
  CSV = 'csv',
  XML = 'xml',
  PDF = 'pdf'
}

// Export Result
export interface IntegrityExportResult {
  format: IntegrityExportFormat;
  data: string | Buffer;
  filename: string;
  size: number;
  timestamp: Date;
  checksum: string;
}

// Integrity Validator Configuration
export interface IntegrityValidatorConfig {
  hashAlgorithm: any; // Will be replaced with proper enum
  cacheTimeout: number;
  maxCacheSize: number;
  alertThreshold: number;
  monitoringInterval: number;
  retentionDays: number;
}

// Main Integrity Validator Interface
export interface IIntegrityValidator {
  initialize(): Promise<void>;
  validateIntegrity(context: IntegrityContext): Promise<IntegrityValidationResult>;
  applyIntegrityPolicy(context: IntegrityContext, policy: IntegrityPolicy): Promise<void>;
  handleIntegrityViolation(violation: IntegrityViolation): Promise<void>;
  getIntegrityMetrics(): Promise<IntegrityMetrics>;
  generateDashboardData(): Promise<IntegrityDashboardData>;
  exportIntegrityData(format: IntegrityExportFormat): Promise<IntegrityExportResult>;
}

// Validation Service Interface
export interface IValidationService {
  initialize(): Promise<void>;
  validateIntegrity(context: IntegrityContext): Promise<IntegrityValidationResult>;
  getIntegrityMetrics(): Promise<IntegrityMetrics>;
  generateDashboardData(): Promise<IntegrityDashboardData>;
  exportIntegrityData(format: IntegrityExportFormat): Promise<IntegrityExportResult>;
} 