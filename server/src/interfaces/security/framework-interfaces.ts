/**
 * @file framework-interfaces.ts
 * @filepath server/src/interfaces/security/framework-interfaces.ts
 * @description Security Framework Interface Definitions
 * @created 2025-01-02
 * @modified 2025-01-02
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level interface-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-security-architecture
 * @governance-dcr DCR-foundation-001-security-development
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on foundation-context.INT.security-interfaces
 * @enables foundation-context.SERV.security-framework
 * @related-contexts foundation-context, authentication-context
 * @governance-impact security-interface-foundation
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type interface-definitions
 * @lifecycle-stage implementation
 * @testing-status interface-validated
 * @deployment-ready true
 * @monitoring-enabled false
 * @documentation docs/contexts/foundation-context/interfaces/framework-interfaces.md
 */

import { SecurityContext, SecurityPolicy, SecurityViolation } from './security-interfaces';

// Re-export for convenience
export { SecurityContext, SecurityPolicy, SecurityViolation };

// Framework Configuration
export interface SecurityFrameworkConfig {
  cacheTimeout: number;
  maxCacheSize: number;
  alertThreshold: number;
  monitoringInterval: number;
  retentionDays: number;
}

// Framework Metrics
export interface SecurityMetrics {
  validationCount: number;
  violationCount: number;
  policyApplicationCount: number;
  averageResponseTime: number;
  successRate: number;
  timestamp: Date;
}

// Dashboard Data
export interface SecurityDashboardData {
  metrics: SecurityMetrics;
  recentViolations: SecurityViolation[];
  activePolicies: SecurityPolicy[];
  systemHealth: {
    status: 'healthy' | 'warning' | 'critical';
    uptime: number;
    lastUpdate: Date;
  };
  visualizations: {
    charts: any[];
    graphs: any[];
    tables: any[];
  };
}

// Export Formats
export enum SecurityExportFormat {
  JSON = 'json',
  CSV = 'csv',
  XML = 'xml',
  PDF = 'pdf'
}

// Export Result
export interface SecurityExportResult {
  format: SecurityExportFormat;
  data: string | Buffer;
  filename: string;
  size: number;
  timestamp: Date;
  checksum: string;
}

// Main Framework Interface
export interface ISecurityFramework {
  initialize(): Promise<void>;
  validateSecurity(context: SecurityContext): Promise<SecurityValidationResult>;
  applySecurityPolicy(context: SecurityContext, policy: SecurityPolicy): Promise<void>;
  handleSecurityViolation(violation: SecurityViolation): Promise<void>;
  getSecurityMetrics(): Promise<SecurityMetrics>;
  generateDashboardData(): Promise<SecurityDashboardData>;
  exportSecurityData(format: SecurityExportFormat): Promise<SecurityExportResult>;
}

// Framework Service Interface
export interface IFrameworkService {
  initialize(): Promise<void>;
  getSecurityMetrics(): Promise<SecurityMetrics>;
  generateDashboardData(): Promise<SecurityDashboardData>;
  exportSecurityData(format: SecurityExportFormat): Promise<SecurityExportResult>;
}

// Security Validation Result (re-export for convenience)
export interface SecurityValidationResult {
  valid: boolean;
  timestamp: Date;
  violations: SecurityViolation[];
  score?: number;
  recommendations?: string[];
} 