/**
 * @file hash-interfaces.ts
 * @filepath server/src/interfaces/security/hash-interfaces.ts
 * @description Hash Management Interface Definitions
 * @created 2025-01-02
 * @modified 2025-01-02
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level interface-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-security-architecture
 * @governance-dcr DCR-foundation-001-security-development
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on foundation-context.INT.crypto-interfaces
 * @enables foundation-context.SERV.hash-manager
 * @related-contexts foundation-context, authentication-context
 * @governance-impact hash-interface-foundation
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type interface-definitions
 * @lifecycle-stage implementation
 * @testing-status interface-validated
 * @deployment-ready true
 * @monitoring-enabled false
 * @documentation docs/contexts/foundation-context/interfaces/hash-interfaces.md
 */

// Hash Algorithm Enumeration
export enum HashAlgorithm {
  MD5 = 'md5',
  SHA1 = 'sha1',
  SHA256 = 'sha256',
  SHA384 = 'sha384',
  SHA512 = 'sha512',
  BLAKE2B = 'blake2b',
  BLAKE2S = 'blake2s'
}

// Hash Configuration
export interface HashConfig {
  algorithm: HashAlgorithm;
  encoding?: 'hex' | 'base64' | 'binary';
  salt?: string;
  iterations?: number;
  keyLength?: number;
}

// Hash Result
export interface HashResult {
  hash: string;
  algorithm: HashAlgorithm;
  encoding: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

// Hash Verification Result
export interface HashVerificationResult {
  valid: boolean;
  algorithm: HashAlgorithm;
  timestamp: Date;
  details?: Record<string, any>;
}

// Hash Manager Interface
export interface IHashManager {
  initialize(config: HashConfig): Promise<void>;
  generateHash(data: string | Buffer): Promise<HashResult>;
  verifyHash(data: string | Buffer, expectedHash: string): Promise<HashVerificationResult>;
  compareHashes(hash1: string, hash2: string): boolean;
  getSupportedAlgorithms(): HashAlgorithm[];
} 