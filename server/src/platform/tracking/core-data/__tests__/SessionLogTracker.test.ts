/**
 * @file SessionLogTracker.test.ts
 * @description Comprehensive Jest tests for SessionLogTracker component
 * @reference T-TSK-01.2: Core Data Components Testing
 * @created 2025-01-07
 * @tier T2
 * @context foundation-context
 * @category Testing
 * 
 * Test Coverage:
 * - Session lifecycle tracking (create, update, terminate)
 * - Log entry formatting and validation logic
 * - Session correlation and cross-referencing capabilities
 * - Audit trail generation and data integrity
 * - Performance tests for high-volume session logging
 * - Session data persistence and recovery mechanisms
 * - Enterprise-grade error handling and TypeScript compliance
 */

import { SessionLogTracker, ISessionLogData } from '../SessionLogTracker';
import { BaseTrackingService } from '../base/BaseTrackingService';
import {
  TTrackingData,
  TValidationResult,
  TComponentStatus,
  TAuthorityLevel,
  TRealtimeCallback,
  TAuditResult
} from '../../../../../../shared/src/types/platform/tracking/tracking-types';
import { 
  TValidationError,
  TValidationWarning
} from '../../../../../../shared/src/types/platform/tracking/core/tracking-data-types';
import {
  TRealtimeData
} from '../../../../../../shared/src/types/platform/tracking/core/base-types';
import * as fs from 'fs';
import * as path from 'path';

// Mock file system operations
jest.mock('fs', () => ({
  existsSync: jest.fn(),
  mkdirSync: jest.fn(),
  appendFileSync: jest.fn(),
  writeFileSync: jest.fn(),
  readFileSync: jest.fn(),
  statSync: jest.fn(() => ({ size: 1024, mtime: new Date() })),
  readdirSync: jest.fn(() => []),
  renameSync: jest.fn(),
  unlinkSync: jest.fn(),
  createReadStream: jest.fn(),
  createWriteStream: jest.fn(),
  promises: {
    mkdir: jest.fn(),
    appendFile: jest.fn(),
    writeFile: jest.fn(),
    readFile: jest.fn()
  }
}));

jest.mock('path', () => ({
  join: jest.fn(),
  dirname: jest.fn(),
  resolve: jest.fn(),
  basename: jest.fn(),
  extname: jest.fn()
}));

const mockFs = fs as jest.Mocked<typeof fs>;
const mockPath = path as jest.Mocked<typeof path>;

// Mock timer functions
jest.useFakeTimers();

// Mock environment variables
process.env.NODE_ENV = 'test';

describe('SessionLogTracker', () => {
  let sessionLogTracker: SessionLogTracker;
  let mockTrackingData: TTrackingData;

  beforeEach(async () => {
    // Reset all mocks
    jest.clearAllMocks();
    jest.clearAllTimers();
    
    // Setup path mocks
    mockPath.join.mockImplementation((...args: string[]) => args.join('/'));
    mockPath.dirname.mockImplementation((p: string) => p.split('/').slice(0, -1).join('/') || '/');
    mockPath.resolve.mockImplementation((...args: string[]) => '/' + args.join('/'));
    mockPath.basename.mockImplementation((p: string) => p.split('/').pop() || '');
    mockPath.extname.mockImplementation((p: string) => {
      const parts = p.split('.');
      return parts.length > 1 ? '.' + parts.pop() : '';
    });
    
    // Setup fs mocks
    mockFs.existsSync.mockReturnValue(true);
    mockFs.mkdirSync.mockImplementation(() => undefined);
    mockFs.appendFileSync.mockImplementation(() => undefined);
    mockFs.writeFileSync.mockImplementation(() => undefined);
    mockFs.readFileSync.mockReturnValue('');
    mockFs.statSync.mockReturnValue({ size: 1024, mtime: new Date() } as any);
    mockFs.readdirSync.mockReturnValue([]);
    
    // Setup fs.promises mocks
    (mockFs as any).promises.mkdir.mockResolvedValue(undefined);
    (mockFs as any).promises.appendFile.mockResolvedValue(undefined);
    (mockFs as any).promises.writeFile.mockResolvedValue(undefined);
    (mockFs as any).promises.readFile.mockResolvedValue('');

    // Create fresh instance with test configuration
    sessionLogTracker = new SessionLogTracker();
    
    // Override security limits for testing
    (sessionLogTracker as any)._maxSessionsPerActor = 10;
    (sessionLogTracker as any)._maxEventsPerSession = 1000; // Higher limit for session
    (sessionLogTracker as any)._maxEventsPerMinute = 50; // Lower limit for rate testing
    (sessionLogTracker as any)._sessionTimeoutMs = 60 * 60 * 1000; // 1 hour
    (sessionLogTracker as any)._maxFailedAttempts = 10;
    (sessionLogTracker as any)._blacklistDurationMs = 1000; // 1 second for testing
    (sessionLogTracker as any)._maxEventSize = 2 * 1024 * 1024; // 2MB for testing
    
    // Initialize with test mode
    await sessionLogTracker.initialize();
    
    // Reset security state
    (sessionLogTracker as any)._blacklistedActors.clear();
    (sessionLogTracker as any)._violationAttempts.clear();
    (sessionLogTracker as any)._actorSessions.clear();
    (sessionLogTracker as any)._sessionEventCounts.clear();
    (sessionLogTracker as any)._sessionEventRates.clear();
    
    // Mock tracking data
    mockTrackingData = {
      componentId: 'test-component-001',
      status: 'in-progress' as TComponentStatus,
      timestamp: new Date().toISOString(),
      context: {
        contextId: 'foundation-context',
        milestone: 'M0',
        category: 'session-management',
        dependencies: [],
        dependents: []
      },
      metadata: {
        phase: 'session-tracking',
        progress: 50,
        priority: 'P1',
        tags: ['session', 'logging', 'tracking'],
        custom: {
          tier: 'T2',
          complexity: 'medium',
          estimatedDuration: 120,
          actualDuration: 60
        }
      },
      progress: {
        completion: 50,
        tasksCompleted: 5,
        totalTasks: 10,
        timeSpent: 60,
        estimatedTimeRemaining: 60,
        quality: {
          codeCoverage: 85,
          testCount: 25,
          bugCount: 2,
          qualityScore: 88,
          performanceScore: 92
        }
      },
      authority: {
        level: 'high' as TAuthorityLevel,
        validator: 'President & CEO, E.Z. Consultancy',
        validationStatus: 'validated',
        validatedAt: new Date().toISOString(),
        complianceScore: 95
      }
    };
  });

  afterEach(async () => {
    // Clean up
    await sessionLogTracker.shutdown();
    jest.clearAllTimers();
    jest.clearAllMocks();
  });

  // ============================================================================
  // INITIALIZATION AND SETUP TESTS
  // ============================================================================

  describe('Initialization', () => {
    it('should initialize successfully', async () => {
      await expect(sessionLogTracker.initialize()).resolves.not.toThrow();
      expect(sessionLogTracker.isReady()).toBe(true);
    });

    it('should extend BaseTrackingService', () => {
      expect(sessionLogTracker).toBeInstanceOf(BaseTrackingService);
    });

    it('should implement required service methods', () => {
      expect(typeof sessionLogTracker.track).toBe('function');
      expect(typeof sessionLogTracker.validate).toBe('function');
      expect(typeof sessionLogTracker.getMetrics).toBe('function');
      expect(typeof sessionLogTracker.shutdown).toBe('function');
    });

    it('should create log directories on initialization', async () => {
      // Create a fresh instance for this test
      const freshTracker = new SessionLogTracker();
      
      // Mock existsSync to return false for directories to trigger directory creation
      mockFs.existsSync.mockImplementation((path: fs.PathLike) => {
        const pathStr = path.toString();
        // Return false for directories to trigger creation
        if (pathStr.includes('docs/tracking') || pathStr.includes('archive')) {
          return false; // Directory doesn't exist
        }
        return true; // Files exist
      });
      
      await freshTracker.initialize();
      expect(mockFs.mkdirSync).toHaveBeenCalled();
      
      await freshTracker.shutdown();
    });

    it('should return correct service name', () => {
      expect((sessionLogTracker as any).getServiceName()).toBe('session-log-tracker');
    });
  });

  // ============================================================================
  // SESSION LIFECYCLE TRACKING TESTS
  // ============================================================================

  describe('Session Lifecycle Management', () => {
    beforeEach(async () => {
      await sessionLogTracker.initialize();
    });

    it('should create new session successfully', async () => {
      const sessionId = 'test-session-001';
      const actor = 'test-user';
      const sessionType = 'user';
      const metadata = { clientId: 'test-client', version: '1.0.0' };

      const sessionData = await sessionLogTracker.startSession(sessionId, actor, sessionType, metadata);

      expect(sessionData).toBeDefined();
      expect(sessionData.session.sessionId).toBe(sessionId);
      expect(sessionData.session.actor).toBe(actor);
      expect(sessionData.session.status).toBe('active');
      expect(sessionData.session.metadata.type).toBe(sessionType);
      expect(sessionData.session.metadata.custom).toEqual(metadata);
    });

    it('should update session with tracking data', async () => {
      const sessionId = 'test-session-002';
      await sessionLogTracker.startSession(sessionId, 'test-user');

      await sessionLogTracker.track(mockTrackingData);

      const sessions = await sessionLogTracker.getActiveSessions();
      expect(sessions.length).toBeGreaterThan(0);
    });

    it('should terminate session successfully', async () => {
      const sessionId = 'test-session-003';
      await sessionLogTracker.startSession(sessionId, 'test-user');

      await sessionLogTracker.endSession(sessionId, 'User logout');

      const sessionData = await sessionLogTracker.getSessionData(sessionId);
      expect(sessionData).toBeNull();
    });

    it('should calculate session duration on termination', async () => {
      const sessionId = 'test-session-004';
      await sessionLogTracker.startSession(sessionId, 'test-user');

      // Advance time by 5 minutes
      jest.advanceTimersByTime(5 * 60 * 1000);

      await sessionLogTracker.endSession(sessionId);

      const history = await sessionLogTracker.getSessionHistory(sessionId);
      const endEvent = history.find(event => event.eventType === 'SESSION_END');
      expect(endEvent).toBeDefined();
      expect(endEvent?.context?.duration).toBeGreaterThan(0);
    });

    it('should handle session not found errors', async () => {
      await expect(sessionLogTracker.endSession('non-existent-session'))
        .rejects.toThrow('Session not found: non-existent-session');
    });
  });

  // ============================================================================
  // LOG ENTRY FORMATTING AND VALIDATION TESTS
  // ============================================================================

  describe('Log Entry Management', () => {
    beforeEach(async () => {
      await sessionLogTracker.initialize();
    });

    it('should format log entries correctly', async () => {
      const sessionId = 'test-session-005';
      await sessionLogTracker.startSession(sessionId, 'test-user');

      await sessionLogTracker.logSessionEvent(
        sessionId,
        'info',
        'TEST_EVENT',
        'Test message',
        { key: 'value' },
        undefined
      );

      const history = await sessionLogTracker.getSessionHistory(sessionId);
      const logEvent = history.find(event => event.eventType === 'TEST_EVENT');

      expect(logEvent).toBeDefined();
      expect(logEvent?.sessionId).toBe(sessionId);
      expect(logEvent?.level).toBe('info');
      expect(logEvent?.message).toBe('Test message');
      expect(logEvent?.context).toEqual({ key: 'value' });
      expect(logEvent?.timestamp).toBeDefined();
    });

    it('should validate log entry data', async () => {
      const sessionId = 'test-session-006';
      await sessionLogTracker.startSession(sessionId, 'test-user');

      await sessionLogTracker.logSessionEvent(
        sessionId,
        'error',
        'ERROR_EVENT',
        'Error occurred',
        { errorCode: 'E001' },
        { code: 'E001', message: 'Test error', stack: 'Error stack trace' }
      );

      const history = await sessionLogTracker.getSessionHistory(sessionId);
      const errorEvent = history.find(event => event.eventType === 'ERROR_EVENT');

      expect(errorEvent?.error).toBeDefined();
      expect(errorEvent?.error?.code).toBe('E001');
      expect(errorEvent?.error?.message).toBe('Test error');
      expect(errorEvent?.error?.stack).toBe('Error stack trace');
    });

    it('should handle different log levels', async () => {
      const sessionId = 'test-session-007';
      await sessionLogTracker.startSession(sessionId, 'test-user');

      const logLevels: Array<'info' | 'warn' | 'error' | 'debug'> = ['info', 'warn', 'error', 'debug'];

      for (const level of logLevels) {
        await sessionLogTracker.logSessionEvent(
          sessionId,
          level,
          `${level.toUpperCase()}_EVENT`,
          `${level} message`
        );
      }

      const history = await sessionLogTracker.getSessionHistory(sessionId);
      const levelEvents = logLevels.map(level => 
        history.find(event => event.level === level)
      );

      levelEvents.forEach((event, index) => {
        expect(event).toBeDefined();
        expect(event?.level).toBe(logLevels[index]);
      });
    });

    it('should persist log entries to file system', async () => {
      const sessionId = 'test-session-008';
      await sessionLogTracker.startSession(sessionId, 'test-user');

      await sessionLogTracker.logSessionEvent(sessionId, 'info', 'PERSIST_TEST', 'Persistence test');

      expect(mockFs.appendFileSync).toHaveBeenCalled();
    });
  });

  // ============================================================================
  // SESSION CORRELATION AND CROSS-REFERENCING TESTS
  // ============================================================================

  describe('Session Correlation', () => {
    beforeEach(async () => {
      await sessionLogTracker.initialize();
    });

    it('should correlate related session events', async () => {
      const sessionId = 'test-session-009';
      const correlationId = 'correlation-001';
      
      await sessionLogTracker.startSession(sessionId, 'test-user');

      // Log correlated events
      await sessionLogTracker.logSessionEvent(
        sessionId,
        'info',
        'PROCESS_START',
        'Process started',
        { correlationId }
      );

      await sessionLogTracker.logSessionEvent(
        sessionId,
        'info',
        'PROCESS_COMPLETE',
        'Process completed',
        { correlationId }
      );

      const history = await sessionLogTracker.getSessionHistory(sessionId);
      const correlatedEvents = history.filter(event => 
        event.context?.correlationId === correlationId
      );

      expect(correlatedEvents).toHaveLength(2);
      expect(correlatedEvents[0].eventType).toBe('PROCESS_START');
      expect(correlatedEvents[1].eventType).toBe('PROCESS_COMPLETE');
    });

    it('should cross-reference sessions by actor', async () => {
      const actor = 'shared-actor';
      const sessionId1 = 'session-001';
      const sessionId2 = 'session-002';

      await sessionLogTracker.startSession(sessionId1, actor);
      await sessionLogTracker.startSession(sessionId2, actor);

      const activeSessions = await sessionLogTracker.getActiveSessions();
      const actorSessions = activeSessions.filter(session => 
        session.session.actor === actor
      );

      expect(actorSessions).toHaveLength(2);
      expect(actorSessions.map(s => s.session.sessionId)).toContain(sessionId1);
      expect(actorSessions.map(s => s.session.sessionId)).toContain(sessionId2);
    });

    it('should maintain session relationships', async () => {
      const parentSessionId = 'parent-session';
      const childSessionId = 'child-session';
      
      await sessionLogTracker.startSession(parentSessionId, 'parent-user');
      await sessionLogTracker.startSession(childSessionId, 'child-user', 'system', {
        parentSessionId: parentSessionId
      });

      const childSession = await sessionLogTracker.getSessionData(childSessionId);
      expect(childSession?.session.metadata.custom?.parentSessionId).toBe(parentSessionId);
    });
  });

  // ============================================================================
  // SESSION BOUNDARIES ENFORCEMENT TESTS
  // ============================================================================

  describe('Session Boundaries Enforcement', () => {
    beforeEach(async () => {
      await sessionLogTracker.initialize();
    });

    it('should enforce maximum sessions per actor limit', async () => {
      const actor = 'boundary-test-actor';
      const maxSessions = 5; // Defined in SessionLogTracker as _maxSessionsPerActor
      
      // Set the limit explicitly to match the test expectation
      (sessionLogTracker as any)._maxSessionsPerActor = maxSessions;

      // Create maximum allowed sessions
      for (let i = 0; i < maxSessions; i++) {
        await sessionLogTracker.startSession(`session-${i}`, actor);
      }

      // Attempt to create one more session
      await expect(sessionLogTracker.startSession('excess-session', actor))
        .rejects.toThrow('Maximum sessions per actor limit reached');

      const activeSessions = await sessionLogTracker.getActiveSessions();
      const actorSessions = activeSessions.filter(session => 
        session.session.actor === actor
      );

      expect(actorSessions).toHaveLength(maxSessions);
    });

    it('should enforce maximum events per session limit', async () => {
      const sessionId = 'event-limit-test';
      const maxEvents = 50; // Reduced for faster test execution
      
      // Set the limit explicitly to match the test expectation
      (sessionLogTracker as any)._maxEventsPerSession = maxEvents;
      // Set a very high rate limit to avoid hitting it during the test
      (sessionLogTracker as any)._maxEventsPerMinute = maxEvents * 2;
      
      await sessionLogTracker.startSession(sessionId, 'test-user');

      // Create maximum allowed events (minus 1 for SESSION_START)
      for (let i = 0; i < maxEvents - 1; i++) {
        await sessionLogTracker.logSessionEvent(
          sessionId,
          'info',
          'TEST_EVENT',
          `Event ${i}`
        );
      }

      // Attempt to create one more event
      await expect(sessionLogTracker.logSessionEvent(
        sessionId,
        'info',
        'EXCESS_EVENT',
        'This should fail'
      )).rejects.toThrow('Maximum events per session limit reached');

      const session = await sessionLogTracker.getSessionData(sessionId);
      expect(session?.session.performance.totalEvents).toBe(maxEvents);
    });

    it('should enforce session timeout', async () => {
      const sessionId = 'timeout-test';
      const timeoutMs = 30 * 60 * 1000; // 30 minutes, defined in SessionLogTracker
      
      // Set the timeout explicitly to match the test expectation
      (sessionLogTracker as any)._sessionTimeoutMs = timeoutMs;
      // Set high values for other limits to avoid hitting them during the test
      (sessionLogTracker as any)._maxEventsPerMinute = 1000;
      (sessionLogTracker as any)._maxEventsPerSession = 1000;

      await sessionLogTracker.startSession(sessionId, 'test-user');

      // Advance time beyond timeout
      jest.advanceTimersByTime(timeoutMs + 1000);

      // Attempt to log event to expired session
      await expect(sessionLogTracker.logSessionEvent(
        sessionId,
        'info',
        'TIMEOUT_TEST',
        'This should fail'
      )).rejects.toThrow('Session has expired');

      const sessionData = await sessionLogTracker.getSessionData(sessionId);
      expect(sessionData?.session.status).toBe('expired');
    });

    it('should enforce rate limiting for events', async () => {
      const sessionId = 'rate-limit-test';
      const maxEventsPerMinute = 10; // Reduced for faster test execution
      
      // Set the limit explicitly to match the test expectation
      (sessionLogTracker as any)._maxEventsPerMinute = maxEventsPerMinute;
      // Set a very high event count limit to avoid hitting it during the test
      (sessionLogTracker as any)._maxEventsPerSession = maxEventsPerMinute * 2;
      
      await sessionLogTracker.startSession(sessionId, 'test-user');

      // Create maximum allowed events within rate limit window (minus 1 for SESSION_START)
      for (let i = 0; i < maxEventsPerMinute - 1; i++) {
        await sessionLogTracker.logSessionEvent(
          sessionId,
          'info',
          'RATE_TEST',
          `Event ${i}`
        );
      }

      // Attempt to exceed rate limit
      await expect(sessionLogTracker.logSessionEvent(
        sessionId,
        'info',
        'RATE_EXCESS',
        'This should fail'
      )).rejects.toThrow('Event rate limit exceeded');
    });

    it('should blacklist actors after repeated violations', async () => {
      const actor = 'violation-test-actor';
      const maxFailedAttempts = 5; // Defined in SessionLogTracker
      
      // Set the limit explicitly to match the test expectation
      (sessionLogTracker as any)._maxFailedAttempts = maxFailedAttempts;
      // Set the max sessions per actor to 5 for this test
      (sessionLogTracker as any)._maxSessionsPerActor = 5;

      // Create and exceed session limit multiple times
      for (let i = 0; i < maxFailedAttempts; i++) {
        // Create max sessions
        for (let j = 0; j < 5; j++) {
          await sessionLogTracker.startSession(`session-${i}-${j}`, actor);
        }
        
        // Attempt to exceed limit
        try {
          await sessionLogTracker.startSession(`excess-${i}`, actor);
        } catch (error) {
          // Expected error
        }

        // End all sessions to prepare for next iteration
        const activeSessions = await sessionLogTracker.getActiveSessions();
        for (const session of activeSessions) {
          if (session.session.actor === actor) {
            await sessionLogTracker.endSession(session.session.sessionId);
          }
        }
      }

      // Verify actor is blacklisted
      await expect(sessionLogTracker.startSession('final-test', actor))
        .rejects.toThrow('Actor is blacklisted');
    });

    it('should enforce maximum event size limit', async () => {
      const sessionId = 'size-limit-test';
      const maxEventSize = 1024 * 1024; // 1MB, defined in SessionLogTracker
      
      // Set the limit explicitly to match the test expectation
      (sessionLogTracker as any)._maxEventSize = maxEventSize;
      
      await sessionLogTracker.startSession(sessionId, 'test-user');

      // Create large context data exceeding limit
      const largeContext = { data: 'x'.repeat(maxEventSize + 1000) };

      // Attempt to log oversized event
      await expect(sessionLogTracker.logSessionEvent(
        sessionId,
        'info',
        'SIZE_TEST',
        'Test message',
        largeContext
      )).rejects.toThrow(/Event size \d+ exceeds maximum allowed size \d+/);
    });
  });

  // ============================================================================
  // SESSION ANALYTICS AND METRICS TESTS
  // ============================================================================

  describe('Session Analytics and Metrics', () => {
    beforeEach(async () => {
      await sessionLogTracker.initialize();
    });

    it('should track basic session metrics', async () => {
      const sessionId = 'metrics-test-session';
      await sessionLogTracker.startSession(sessionId, 'test-user');

      // Generate some session activity
      for (let i = 0; i < 10; i++) {
        await sessionLogTracker.logSessionEvent(
          sessionId,
          'info',
          'METRICS_TEST',
          `Metrics test event ${i}`,
          { iteration: i }
        );
      }

      // Add some errors and warnings
      await sessionLogTracker.logSessionEvent(
        sessionId,
        'error',
        'ERROR_EVENT',
        'Test error',
        undefined,
        { code: 'E001', message: 'Test error' }
      );

      await sessionLogTracker.logSessionEvent(
        sessionId,
        'warn',
        'WARN_EVENT',
        'Test warning'
      );

      await sessionLogTracker.endSession(sessionId);

      const analytics = await sessionLogTracker.getSessionAnalytics();
      
      // Verify basic metrics
      expect(analytics.totalSessions).toBeGreaterThan(0);
      expect(analytics.totalEvents).toBeGreaterThan(0);
      expect(analytics.errorCount).toBeGreaterThan(0);
      expect(analytics.warningCount).toBeGreaterThan(0);
      expect(analytics.averageSessionDuration).toBeGreaterThanOrEqual(0); // May be 0 with fake timers
    });

    it('should track concurrent session metrics', async () => {
      // Start multiple concurrent sessions
      const sessions = ['concurrent-1', 'concurrent-2', 'concurrent-3'];
      for (const sessionId of sessions) {
        await sessionLogTracker.startSession(sessionId, 'test-user');
      }

      const analytics = await sessionLogTracker.getSessionAnalytics();
      
      // Verify concurrent session metrics
      expect(analytics.activeSessions).toBe(sessions.length);
      expect(analytics.peakConcurrentSessions).toBeGreaterThanOrEqual(sessions.length);

      // Cleanup
      for (const sessionId of sessions) {
        await sessionLogTracker.endSession(sessionId);
      }
    });

    it('should track session performance over time', async () => {
      const sessionId = 'performance-test-session';
      await sessionLogTracker.startSession(sessionId, 'test-user');

      // Generate events over time
      for (let i = 0; i < 5; i++) {
        await sessionLogTracker.logSessionEvent(
          sessionId,
          'info',
          'PERF_TEST',
          `Performance test event ${i}`
        );
        jest.advanceTimersByTime(1000); // Advance time by 1 second
      }

      await sessionLogTracker.endSession(sessionId);

      const analytics = await sessionLogTracker.getSessionAnalytics();
      
      // Verify performance metrics
      expect(analytics.totalEvents).toBeGreaterThan(0);
      expect(analytics.averageSessionDuration).toBeGreaterThanOrEqual(0); // May be 0 with fake timers
    });

    it('should track error and warning rates', async () => {
      const sessionId = 'error-rate-test';
      await sessionLogTracker.startSession(sessionId, 'test-user');

      // Generate mixed events
      await sessionLogTracker.logSessionEvent(sessionId, 'info', 'INFO_EVENT', 'Info message');
      await sessionLogTracker.logSessionEvent(sessionId, 'warn', 'WARN_EVENT', 'Warning message');
      await sessionLogTracker.logSessionEvent(sessionId, 'error', 'ERROR_EVENT', 'Error message');

      await sessionLogTracker.endSession(sessionId);

      const analytics = await sessionLogTracker.getSessionAnalytics();
      
      // Verify error and warning counts
      expect(analytics.errorCount).toBe(1);
      expect(analytics.warningCount).toBe(1);
      expect(analytics.totalEvents).toBeGreaterThanOrEqual(3); // Including session events (3 logged + session events)
    });

    it('should maintain accurate session counts', async () => {
      const initialAnalytics = await sessionLogTracker.getSessionAnalytics();
      const initialTotal = initialAnalytics.totalSessions;

      // Start and end multiple sessions
      const sessions = ['count-1', 'count-2', 'count-3'];
      for (const sessionId of sessions) {
        await sessionLogTracker.startSession(sessionId, 'test-user');
        await sessionLogTracker.endSession(sessionId);
      }

      const finalAnalytics = await sessionLogTracker.getSessionAnalytics();
      
      // Verify session counting
      expect(finalAnalytics.totalSessions).toBe(initialTotal + sessions.length);
      expect(finalAnalytics.activeSessions).toBe(0);
    });

    it('should track session duration accurately', async () => {
      const sessionId = 'duration-test';
      const duration = 5000; // 5 seconds

      await sessionLogTracker.startSession(sessionId, 'test-user');
      
      // Generate some activity and advance time
      await sessionLogTracker.logSessionEvent(sessionId, 'info', 'DURATION_TEST', 'Start');
      jest.advanceTimersByTime(duration);
      await sessionLogTracker.logSessionEvent(sessionId, 'info', 'DURATION_TEST', 'End');
      
      await sessionLogTracker.endSession(sessionId);

      const analytics = await sessionLogTracker.getSessionAnalytics();
      
      // Verify duration tracking
      expect(analytics.averageSessionDuration).toBeGreaterThanOrEqual(0); // May be 0 with fake timers
      expect(analytics.averageSessionDuration).toBeLessThanOrEqual(duration + 1000); // Allow some tolerance
    });
  });

  // ============================================================================
  // AUDIT TRAIL AND COMPLIANCE TESTS
  // ============================================================================

  describe('Audit Trail and Compliance', () => {
    beforeEach(async () => {
      await sessionLogTracker.initialize();
    });

    it('should generate audit trail with default options', async () => {
      const sessionId = 'audit-test-session';
      await sessionLogTracker.startSession(sessionId, 'test-user');

      // Generate some activity
      await sessionLogTracker.logSessionEvent(
        sessionId,
        'info',
        'AUDIT_TEST',
        'Test event for audit'
      );

      await sessionLogTracker.endSession(sessionId);

      const auditTrail = await sessionLogTracker.generateAuditTrail();

      // Verify audit trail structure
      expect(auditTrail.auditId).toBeDefined();
      expect(auditTrail.generatedAt).toBeDefined();
      expect(auditTrail.timeRange).toBeDefined();
      expect(auditTrail.sessionData).toBeDefined();
      expect(auditTrail.analytics).toBeDefined();
      expect(auditTrail.performanceMetrics).toBeDefined();
      expect(auditTrail.logFiles).toBeDefined();
      expect(auditTrail.details).toBeDefined();
    });

    it('should generate audit trail with custom date range', async () => {
      const startDate = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago
      const endDate = new Date();

      const auditTrail = await sessionLogTracker.generateAuditTrail({
        startDate,
        endDate,
        includeDetails: true
      });

      // Verify date range
      expect(new Date(auditTrail.timeRange.start)).toEqual(startDate);
      expect(new Date(auditTrail.timeRange.end)).toEqual(endDate);
    });

    it('should get audit history with default limit', async () => {
      const sessionId = 'history-audit-session';
      await sessionLogTracker.startSession(sessionId, 'test-user');

      // Generate multiple events
      for (let i = 0; i < 5; i++) {
        await sessionLogTracker.logSessionEvent(
          sessionId,
          'info',
          'HISTORY_TEST',
          `History test event ${i}`
        );
      }

      await sessionLogTracker.endSession(sessionId);

      const auditHistory = await sessionLogTracker.getAuditHistory();

      // Verify audit history
      expect(Array.isArray(auditHistory)).toBe(true);
      expect(auditHistory.length).toBeGreaterThan(0);
      expect(auditHistory[0].auditEntryId).toBeDefined();
      expect(auditHistory[0].sessionId).toBeDefined();
      expect(auditHistory[0].events).toBeDefined();
    });

    it('should get audit history with custom limit', async () => {
      const sessionId = 'limit-audit-session';
      await sessionLogTracker.startSession(sessionId, 'test-user');

      // Generate multiple events
      for (let i = 0; i < 10; i++) {
        await sessionLogTracker.logSessionEvent(
          sessionId,
          'info',
          'LIMIT_TEST',
          `Limit test event ${i}`
        );
      }

      await sessionLogTracker.endSession(sessionId);

      const limit = 5;
      const auditHistory = await sessionLogTracker.getAuditHistory(limit);

      // Verify limit is respected
      expect(auditHistory.length).toBeLessThanOrEqual(limit);
    });

    it('should export audit data in JSON format', async () => {
      const sessionId = 'json-export-session';
      await sessionLogTracker.startSession(sessionId, 'test-user');
      await sessionLogTracker.logSessionEvent(sessionId, 'info', 'EXPORT_TEST', 'Test event');
      await sessionLogTracker.endSession(sessionId);

      const jsonData = await sessionLogTracker.exportAuditData('json');

      // Verify JSON format
      expect(() => JSON.parse(jsonData)).not.toThrow();
      const parsedData = JSON.parse(jsonData);
      expect(parsedData.auditId).toBeDefined();
      expect(parsedData.sessionData).toBeDefined();
    });

    it('should export audit data in CSV format', async () => {
      const sessionId = 'csv-export-session';
      await sessionLogTracker.startSession(sessionId, 'test-user');
      await sessionLogTracker.logSessionEvent(sessionId, 'info', 'EXPORT_TEST', 'Test event');
      await sessionLogTracker.endSession(sessionId);

      const csvData = await sessionLogTracker.exportAuditData('csv');

      // Verify CSV format
      const lines = csvData.split('\n');
      expect(lines.length).toBeGreaterThan(1); // Header + at least one data row
      expect(lines[0]).toBe('Timestamp,Session ID,Event Type,Message,Actor');
    });

    it('should export audit data in XML format', async () => {
      const sessionId = 'xml-export-session';
      await sessionLogTracker.startSession(sessionId, 'test-user');
      await sessionLogTracker.logSessionEvent(sessionId, 'info', 'EXPORT_TEST', 'Test event');
      await sessionLogTracker.endSession(sessionId);

      const xmlData = await sessionLogTracker.exportAuditData('xml');

      // Verify XML format
      expect(xmlData).toMatch(/^<\?xml/);
      expect(xmlData).toContain('<sessionAuditTrail>');
      expect(xmlData).toContain('</sessionAuditTrail>');
    });

    it('should perform full compliance audit', async () => {
      const sessionId = 'compliance-test-session';
      await sessionLogTracker.startSession(sessionId, 'test-user');

      // Generate mixed activity for compliance testing
      await sessionLogTracker.logSessionEvent(sessionId, 'info', 'COMPLIANCE_TEST', 'Normal operation');
      await sessionLogTracker.logSessionEvent(sessionId, 'warn', 'COMPLIANCE_TEST', 'Minor issue');
      await sessionLogTracker.logSessionEvent(sessionId, 'error', 'COMPLIANCE_TEST', 'Major issue');

      await sessionLogTracker.endSession(sessionId);

      const auditResult = await sessionLogTracker.performComplianceAudit('full');

      // Verify audit result structure
      expect(auditResult.auditId).toBeDefined();
      expect(auditResult.timestamp).toBeDefined();
      expect(auditResult.auditType).toBe('compliance'); // 'full' maps to 'compliance' internally
      expect(auditResult.status).toMatch(/^(passed|failed|warning)$/);
      expect(auditResult.score).toBeGreaterThan(0);
      expect(auditResult.score).toBeLessThanOrEqual(100);
      expect(auditResult.findings).toBeDefined();
      expect(Array.isArray(auditResult.findings)).toBe(true);
      expect(auditResult.recommendations).toBeDefined();
      expect(auditResult.remediation).toBeDefined();
      expect(auditResult.nextAuditDate).toBeDefined();
    });

    it('should perform security-focused compliance audit', async () => {
      const sessionId = 'security-audit-session';
      await sessionLogTracker.startSession(sessionId, 'test-user');

      // Generate security-relevant activity
      await sessionLogTracker.logSessionEvent(
        sessionId,
        'warn',
        'SECURITY_TEST',
        'Suspicious activity detected'
      );

      await sessionLogTracker.endSession(sessionId);

      const auditResult = await sessionLogTracker.performComplianceAudit('security');

      // Verify security-specific audit results
      expect(auditResult.auditType).toBe('security');
      expect(auditResult.findings).toBeDefined();
      expect(auditResult.findings.some(f => f.type === 'violation' && f.severity === 'high')).toBe(true);
    });

    it('should perform governance-focused compliance audit', async () => {
      const sessionId = 'governance-audit-session';
      await sessionLogTracker.startSession(sessionId, 'test-user');

      // Generate governance-relevant activity
      await sessionLogTracker.logSessionEvent(
        sessionId,
        'info',
        'GOVERNANCE_TEST',
        'Policy check',
        {
          authority: {
            level: 'high',
            validator: 'President & CEO, E.Z. Consultancy',
            validationStatus: 'validated'
          }
        }
      );

      await sessionLogTracker.endSession(sessionId);

      const auditResult = await sessionLogTracker.performComplianceAudit('governance');

      // Verify governance-specific audit results
      expect(auditResult.auditType).toBe('governance');
      expect(auditResult.findings).toBeDefined();
      expect(auditResult.findings.some(f => f.type === 'compliance')).toBe(true);
    });

    it('should perform performance-focused compliance audit', async () => {
      const sessionId = 'performance-audit-session';
      await sessionLogTracker.startSession(sessionId, 'test-user');

      // Generate performance-relevant activity
      for (let i = 0; i < 10; i++) {
        await sessionLogTracker.logSessionEvent(
          sessionId,
          'info',
          'PERFORMANCE_TEST',
          `Performance test event ${i}`
        );
        jest.advanceTimersByTime(100); // Simulate time passing
      }

      await sessionLogTracker.endSession(sessionId);

      const auditResult = await sessionLogTracker.performComplianceAudit('performance');

      // Verify performance-specific audit results
      expect(auditResult.auditType).toBe('performance');
      expect(auditResult.findings).toBeDefined();
      expect(auditResult.findings.some(f => f.type === 'improvement')).toBe(true);
    });
  });

  // ============================================================================
  // PERFORMANCE TESTS FOR HIGH-VOLUME SESSION LOGGING
  // ============================================================================

  describe('Performance and Scalability', () => {
    beforeEach(async () => {
      await sessionLogTracker.initialize();
    });

    it('should handle high-volume session creation', async () => {
      const startTime = Date.now();
      const sessionCount = 100;
      const promises: Promise<any>[] = [];

      for (let i = 0; i < sessionCount; i++) {
        promises.push(sessionLogTracker.startSession(`bulk-session-${i}`, `user-${i}`));
      }

      await Promise.all(promises);

      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(duration).toBeLessThan(5000); // Should complete within 5 seconds
      
      const activeSessions = await sessionLogTracker.getActiveSessions();
      expect(activeSessions.length).toBe(sessionCount);
    });

    it('should handle concurrent session logging', async () => {
      const sessionId = 'concurrent-session-001';
      
      // Temporarily increase rate limit for this concurrent test
      (sessionLogTracker as any)._maxEventsPerMinute = 100;
      
      await sessionLogTracker.startSession(sessionId, 'concurrent-user');

      const logCount = 50;
      const promises: Promise<void>[] = [];

      for (let i = 0; i < logCount; i++) {
        promises.push(sessionLogTracker.logSessionEvent(
          sessionId,
          'info',
          'CONCURRENT_EVENT',
          `Concurrent message ${i}`,
          { index: i }
        ));
      }

      await Promise.all(promises);

      const history = await sessionLogTracker.getSessionHistory(sessionId);
      const concurrentEvents = history.filter(event => 
        event.eventType === 'CONCURRENT_EVENT'
      );

      expect(concurrentEvents.length).toBe(logCount);
      
      // Reset rate limit back to test configuration
      (sessionLogTracker as any)._maxEventsPerMinute = 50;
    });

    it('should maintain performance metrics', async () => {
      const sessionId = 'metrics-session-001';
      await sessionLogTracker.startSession(sessionId, 'metrics-user');

      // Generate some activity
      for (let i = 0; i < 10; i++) {
        await sessionLogTracker.logSessionEvent(sessionId, 'info', 'METRIC_EVENT', `Event ${i}`);
      }

      const metrics = await sessionLogTracker.getMetrics();
      expect(metrics).toBeDefined();
      expect(typeof metrics.service).toBe('string');
    });

    it('should handle memory usage efficiently', async () => {
      const sessionId = 'memory-session-001';
      // Override limits for this test to allow more events
      (sessionLogTracker as any)._maxEventsPerSession = 2000;
      (sessionLogTracker as any)._maxEventsPerMinute = 2000;
      
      await sessionLogTracker.startSession(sessionId, 'memory-user');

      // Log reasonable number of events (don't exceed new limits)
      for (let i = 0; i < 100; i++) {
        await sessionLogTracker.logSessionEvent(
          sessionId, 
          'info', 
          'MEMORY_TEST', 
          `Memory test event ${i}`,
          { data: new Array(10).fill(`data-${i}`) } // Smaller data array
        );
      }

      const sessionData = await sessionLogTracker.getSessionData(sessionId);
      expect(sessionData?.session.performance.totalEvents).toBe(101); // Including SESSION_START
    });
  });

  // ============================================================================
  // SESSION DATA PERSISTENCE AND RECOVERY TESTS
  // ============================================================================

  describe('Session Data Persistence and Recovery', () => {
    beforeEach(async () => {
      await sessionLogTracker.initialize();
    });

    it('should persist session events to log files', async () => {
      const sessionId = 'persistence-test';
      const actor = 'test-user';

      await sessionLogTracker.startSession(sessionId, actor);

      // Generate mixed-level events
      await sessionLogTracker.logSessionEvent(
        sessionId,
        'info',
        'PERSISTENCE_TEST',
        'Info message'
      );

      await sessionLogTracker.logSessionEvent(
        sessionId,
        'warn',
        'PERSISTENCE_TEST',
        'Warning message'
      );

      await sessionLogTracker.logSessionEvent(
        sessionId,
        'error',
        'PERSISTENCE_TEST',
        'Error message',
        undefined,
        { code: 'TEST_ERROR', message: 'Test error' }
      );

      await sessionLogTracker.endSession(sessionId);

      // Verify log files were written to
      expect(mockFs.appendFileSync).toHaveBeenCalled();
    });

    it('should calculate and persist session metrics', async () => {
      const sessionId = 'metrics-persistence-test';
      const actor = 'test-user';

      await sessionLogTracker.startSession(sessionId, actor);

      // Generate events with performance data
      for (let i = 0; i < 5; i++) {
        await sessionLogTracker.logSessionEvent(
          sessionId,
          'info',
          'METRICS_TEST',
          `Event ${i}`,
          {
            performance: {
              duration: 100,
              memoryUsage: 50,
              cpuUsage: 10
            }
          }
        );
      }

      // Add some errors and warnings
      await sessionLogTracker.logSessionEvent(
        sessionId,
        'error',
        'METRICS_TEST',
        'Error event',
        undefined,
        { code: 'TEST_ERROR', message: 'Test error' }
      );

      await sessionLogTracker.logSessionEvent(
        sessionId,
        'warn',
        'METRICS_TEST',
        'Warning event'
      );

      await sessionLogTracker.endSession(sessionId);

      // Get session data from history after ending
      const sessionHistory = await sessionLogTracker.getSessionHistory(sessionId);
      expect(sessionHistory).toBeDefined();
      expect(sessionHistory.length).toBeGreaterThanOrEqual(8); // SESSION_START + 5 info + 1 error + 1 warn + SESSION_END (may have additional events)
      
      // Check for various event types
      const startEvent = sessionHistory.find(e => e.eventType === 'SESSION_START');
      const endEvent = sessionHistory.find(e => e.eventType === 'SESSION_END');
      const infoEvents = sessionHistory.filter(e => e.level === 'info');
      const errorEvents = sessionHistory.filter(e => e.level === 'error');
      const warnEvents = sessionHistory.filter(e => e.level === 'warn');
      
      expect(startEvent).toBeDefined();
      expect(endEvent).toBeDefined();
      expect(infoEvents.length).toBeGreaterThanOrEqual(6); // 5 metrics + SESSION_START (may have additional events)
      expect(errorEvents.length).toBe(1);
      expect(warnEvents.length).toBe(1);
    });

    it('should handle file system errors gracefully', async () => {
      const sessionId = 'error-handling-test';
      const actor = 'test-user';

      // Start session first with normal file operations
      await sessionLogTracker.startSession(sessionId, actor);

      // Then make appendFileSync throw an error for subsequent operations
      mockFs.appendFileSync.mockImplementation(() => {
        throw new Error('Mock file system error');
      });

      // Attempt to log event with file system error - should throw
      await expect(sessionLogTracker.logSessionEvent(
        sessionId,
        'info',
        'ERROR_HANDLING',
        'Test message'
      )).rejects.toThrow('Mock file system error');

      // Reset the mock
      mockFs.appendFileSync.mockImplementation(() => undefined);
    });

    it('should save and recover analytics data', async () => {
      const sessionId = 'analytics-test';
      const actor = 'test-user';

      await sessionLogTracker.startSession(sessionId, actor);

      // Generate some activity
      for (let i = 0; i < 5; i++) {
        await sessionLogTracker.logSessionEvent(
          sessionId,
          'info',
          'ANALYTICS_TEST',
          `Event ${i}`
        );
      }

      await sessionLogTracker.endSession(sessionId);

      // Save analytics
      await (sessionLogTracker as any)._saveSessionAnalytics();

      // Verify analytics file was written
      expect(mockFs.writeFileSync).toHaveBeenCalled();
    });

    it('should handle service shutdown gracefully', async () => {
      const sessions = ['shutdown-1', 'shutdown-2', 'shutdown-3'];
      const actor = 'test-user';

      // Start multiple sessions
      for (const sessionId of sessions) {
        await sessionLogTracker.startSession(sessionId, actor);
        await sessionLogTracker.logSessionEvent(
          sessionId,
          'info',
          'SHUTDOWN_TEST',
          'Test event'
        );
      }

      // Perform shutdown
      await (sessionLogTracker as any).doShutdown();

      // Verify all sessions are no longer active
      for (const sessionId of sessions) {
        const sessionData = await sessionLogTracker.getSessionData(sessionId);
        expect(sessionData).toBeNull(); // Should be null since session was ended during shutdown
      }

      // Verify sessions were moved to history
      for (const sessionId of sessions) {
        const sessionHistory = await sessionLogTracker.getSessionHistory(sessionId);
        expect(sessionHistory.length).toBeGreaterThan(0);
        const endEvent = sessionHistory.find(e => e.eventType === 'SESSION_END');
        expect(endEvent).toBeDefined();
      }
    });

    it('should validate session data integrity', async () => {
      const sessionId = 'integrity-test';
      const actor = 'test-user';
      
      await sessionLogTracker.startSession(sessionId, actor);

      // Generate events without triggering timeout
      for (let i = 0; i < 5; i++) {
        await sessionLogTracker.logSessionEvent(
          sessionId,
          'info',
          'INTEGRITY_TEST',
          `Event ${i}`
        );
      }

      await sessionLogTracker.endSession(sessionId);

      // Perform validation
      const validationResult = await (sessionLogTracker as any).doValidate();

      // Should have warnings or be valid
      expect(validationResult.status).toMatch(/^(valid|invalid)$/);
    });
  });

  // ============================================================================
  // SESSION PERFORMANCE MONITORING TESTS
  // ============================================================================

  describe('Session Performance Monitoring', () => {
    beforeEach(async () => {
      await sessionLogTracker.initialize();
    });

    it('should track log write performance', async () => {
      const sessionId = 'write-perf-test';
      const actor = 'test-user';

      await sessionLogTracker.startSession(sessionId, actor);

      // Generate events to track write performance
      for (let i = 0; i < 10; i++) {
        await sessionLogTracker.logSessionEvent(
          sessionId,
          'info',
          'WRITE_PERF_TEST',
          `Event ${i}`,
          {
            performance: {
              duration: 100,
              memoryUsage: 50,
              cpuUsage: 10
            }
          }
        );
      }

      // Update performance metrics
      await (sessionLogTracker as any)._updateSessionPerformanceMetrics();

      // Verify performance metrics
      expect(sessionLogTracker['_performanceMonitor'].totalWrites).toBeGreaterThan(0);
      expect(sessionLogTracker['_performanceMonitor'].avgLogWriteTime).toBeGreaterThanOrEqual(0);
      expect(sessionLogTracker['_performanceMonitor'].failedWrites).toBe(0);
    });

    it('should track session-level performance metrics', async () => {
      const sessionId = 'session-perf-test';
      const actor = 'test-user';

      await sessionLogTracker.startSession(sessionId, actor);

      // Generate mixed-level events with performance data
      await sessionLogTracker.logSessionEvent(
        sessionId,
        'info',
        'PERF_TEST',
        'Fast operation',
        {
          performance: {
            duration: 50,
            memoryUsage: 25,
            cpuUsage: 5
          }
        }
      );

      await sessionLogTracker.logSessionEvent(
        sessionId,
        'warn',
        'PERF_TEST',
        'Slow operation',
        {
          performance: {
            duration: 150,
            memoryUsage: 75,
            cpuUsage: 15
          }
        }
      );

      await sessionLogTracker.logSessionEvent(
        sessionId,
        'error',
        'PERF_TEST',
        'Failed operation',
        {
          performance: {
            duration: 200,
            memoryUsage: 100,
            cpuUsage: 20
          }
        }
      );

      // Get session data and check performance metrics
      const sessionData = await sessionLogTracker.getSessionData(sessionId);
      expect(sessionData).toBeDefined();

      // Verify performance metrics
      expect(sessionData!.session.performance.totalEvents).toBe(4); // Including SESSION_START
      expect(sessionData!.session.performance.eventsByLevel).toEqual({
        info: 2, // SESSION_START + 1 perf test
        warn: 1,
        error: 1
      });
      
      // Average processing time should be calculated
      expect(sessionData!.session.performance.avgProcessingTime).toBe(0); // No duration data in events yet
      // Efficiency score is initially 100 and only recalculated when session ends
      expect(sessionData!.session.performance.efficiencyScore).toBeGreaterThanOrEqual(0);
    });

    it('should track quality metrics', async () => {
      const sessionId = 'quality-test';
      const actor = 'test-user';

      await sessionLogTracker.startSession(sessionId, actor);

      // Generate events with different quality implications
      await sessionLogTracker.logSessionEvent(
        sessionId,
        'info',
        'QUALITY_TEST',
        'Normal operation',
        {
          authority: {
            level: 'high',
            validator: 'President & CEO, E.Z. Consultancy',
            validationStatus: 'validated',
            complianceScore: 100
          }
        }
      );

      await sessionLogTracker.logSessionEvent(
        sessionId,
        'warn',
        'QUALITY_TEST',
        'Degraded operation',
        {
          authority: {
            level: 'medium',
            validator: 'System',
            validationStatus: 'pending',
            complianceScore: 75
          }
        }
      );

      await sessionLogTracker.logSessionEvent(
        sessionId,
        'error',
        'QUALITY_TEST',
        'Failed operation',
        {
          authority: {
            level: 'low',
            validator: 'System',
            validationStatus: 'failed',
            complianceScore: 0
          }
        }
      );

      // Get session data and check quality metrics
      const sessionData = await sessionLogTracker.getSessionData(sessionId);
      expect(sessionData).toBeDefined();

      // Verify quality metrics - error rate should be calculated based on total events
      const totalEvents = sessionData!.session.performance.totalEvents; // 4 events including SESSION_START
      expect(sessionData!.session.quality.errorRate).toBe((1 / totalEvents) * 100); // 1 error out of 4 events
      expect(sessionData!.session.quality.warningRate).toBe((1 / totalEvents) * 100); // 1 warning out of 4 events
      expect(sessionData!.session.quality.authorityValidationRate).toBe(100); // Default value
      // Compliance score is initially 100 and only recalculated when session ends
      expect(sessionData!.session.quality.complianceScore).toBeGreaterThanOrEqual(0);
    });

    it('should validate performance metrics', async () => {
      const sessionId = 'perf-validation-test';
      const actor = 'test-user';

      await sessionLogTracker.startSession(sessionId, actor);

      // Generate events with poor performance
      for (let i = 0; i < 5; i++) {
        await sessionLogTracker.logSessionEvent(
          sessionId,
          'info',
          'PERF_VALIDATION',
          `Event ${i}`,
          {
            performance: {
              duration: 1000, // Very slow
              memoryUsage: 500, // High memory usage
              cpuUsage: 90 // High CPU usage
            }
          }
        );
      }

      // Manually set slow write time to trigger validation warning
      (sessionLogTracker as any)._performanceMonitor.avgLogWriteTime = 150;

      // Perform validation
      const errors: TValidationError[] = [];
      const warnings: TValidationWarning[] = [];
      await (sessionLogTracker as any)._validatePerformanceMetrics(errors, warnings);

      // Verify validation results
      expect(warnings.length).toBeGreaterThan(0);
      expect(warnings.some(w => w.message.includes('Slow log write performance'))).toBe(true);
    });

    it('should handle performance monitoring errors gracefully', async () => {
      const sessionId = 'error-handling-test';
      const actor = 'test-user';

      await sessionLogTracker.startSession(sessionId, actor);

      // Mock performance monitoring to throw error
      const originalUpdateMetrics = sessionLogTracker['_updateSessionPerformanceMetrics'];
      sessionLogTracker['_updateSessionPerformanceMetrics'] = jest.fn().mockImplementation(() => {
        throw new Error('Mock performance monitoring error');
      });

      // Attempt to update metrics (should not throw)
      await expect(
        (sessionLogTracker as any)._startPerformanceMonitoring()
      ).resolves.not.toThrow();

      // Advance timers to trigger the performance monitoring interval
      jest.advanceTimersByTime(30000);

      // The error should be handled gracefully and not crash the system
      expect(sessionLogTracker.isReady()).toBe(true);

      // Restore original method
      sessionLogTracker['_updateSessionPerformanceMetrics'] = originalUpdateMetrics;
    });
  });

  // ============================================================================
  // REAL-TIME MONITORING AND SUBSCRIPTIONS TESTS
  // ============================================================================

  describe('Real-time Monitoring', () => {
    beforeEach(async () => {
      await sessionLogTracker.initialize();
      
      // Reset security state before each test
      (sessionLogTracker as any)._blacklistedActors.clear();
      (sessionLogTracker as any)._violationAttempts.clear();
      
      // Ensure no active sessions
      const activeSessions = await sessionLogTracker.getActiveSessions();
      for (const session of activeSessions) {
        await sessionLogTracker.endSession(session.session.sessionId);
      }
    });

    afterEach(async () => {
      // Cleanup after each test
      const activeSessions = await sessionLogTracker.getActiveSessions();
      for (const session of activeSessions) {
        await sessionLogTracker.endSession(session.session.sessionId);
      }
    });

    it('should support real-time event subscriptions', async () => {
      const sessionId = 'realtime-session-001';
      const actor = 'realtime-user';
      await sessionLogTracker.startSession(sessionId, actor);
      
      const mockCallback = jest.fn();
      const subscriptionId = await sessionLogTracker.subscribeToRealtimeEvents(sessionId, mockCallback);
      
      expect(subscriptionId).toBeDefined();
      expect(typeof subscriptionId).toBe('string');
      
      await sessionLogTracker.endSession(sessionId);
    });

    it('should notify subscribers of session events', async () => {
      const sessionId = 'notify-session-001';
      const actor = 'notify-user';
      await sessionLogTracker.startSession(sessionId, actor);
      
      const mockCallback = jest.fn();
      await sessionLogTracker.subscribeToRealtimeEvents(sessionId, mockCallback);
      
      // Log an event
      await sessionLogTracker.logSessionEvent(sessionId, 'info', 'NOTIFY_EVENT_1', 'First notification');
      
      // Verify notifications (should be synchronous now)
      expect(mockCallback).toHaveBeenCalledTimes(1);
      
      const firstCall = mockCallback.mock.calls[0][0];
      expect(firstCall).toMatchObject({
        sessionId: sessionId,
        timestamp: expect.any(String),
        actor: actor,
        eventCount: expect.any(Number),
        status: 'active',
        performance: expect.objectContaining({
          totalEvents: expect.any(Number),
          eventsByLevel: expect.any(Object),
          avgProcessingTime: expect.any(Number),
          peakMemoryUsage: expect.any(Number),
          efficiencyScore: expect.any(Number)
        }),
        quality: expect.objectContaining({
          errorRate: expect.any(Number),
          warningRate: expect.any(Number),
          complianceScore: expect.any(Number),
          authorityValidationRate: expect.any(Number)
        })
      });
      
      await sessionLogTracker.endSession(sessionId);
    });

    it('should handle multiple subscribers', async () => {
      const sessionId = 'multi-sub-session-001';
      const actor = 'multi-user';
      await sessionLogTracker.startSession(sessionId, actor);
      
      const callback1 = jest.fn();
      const callback2 = jest.fn();
      
      const sub1 = await sessionLogTracker.subscribeToRealtimeEvents(sessionId, callback1);
      const sub2 = await sessionLogTracker.subscribeToRealtimeEvents(sessionId, callback2);
      
      // Log an event
      await sessionLogTracker.logSessionEvent(sessionId, 'info', 'MULTI_SUB_EVENT', 'Test message');
      
      expect(sub1).not.toBe(sub2);
      expect(callback1).toHaveBeenCalled();
      expect(callback2).toHaveBeenCalled();
      
      await sessionLogTracker.endSession(sessionId);
    });

    it('should cleanup subscribers when session ends', async () => {
      const sessionId = 'cleanup-session-001';
      const actor = 'cleanup-user';
      await sessionLogTracker.startSession(sessionId, actor);
      
      const mockCallback = jest.fn();
      await sessionLogTracker.subscribeToRealtimeEvents(sessionId, mockCallback);
      
      // End session
      await sessionLogTracker.endSession(sessionId);
      
      // Try to start a new session with same ID and log event
      await sessionLogTracker.startSession(sessionId, actor);
      await sessionLogTracker.logSessionEvent(sessionId, 'info', 'POST_END_EVENT', 'Should not notify');
      
      // Original callback should not be called for new session
      expect(mockCallback).not.toHaveBeenCalled();
      
      await sessionLogTracker.endSession(sessionId);
    });

    it('should enforce maximum subscriber limit', async () => {
      const sessionId = 'limit-test-session';
      const actor = 'limit-test-user';
      await sessionLogTracker.startSession(sessionId, actor);
      
      // Override max subscribers for this test
      const maxSubscribers = 3;
      
      // Add maximum allowed subscribers
      const callbacks = Array.from({ length: maxSubscribers + 1 }, () => jest.fn());
      
      // Add subscribers up to the limit
      for (let i = 0; i < maxSubscribers; i++) {
        await sessionLogTracker.subscribeToRealtimeEvents(sessionId, callbacks[i]);
      }
      
      // Attempt to add one more subscriber - should fail due to per-session limit (10)
      // Since our test limit is lower than the actual limit, we need to add more subscribers
      for (let i = 0; i < 7; i++) { // Add 7 more to reach the actual limit of 10
        await sessionLogTracker.subscribeToRealtimeEvents(sessionId, jest.fn());
      }
      
      // Now this should fail
      await expect(
        sessionLogTracker.subscribeToRealtimeEvents(sessionId, callbacks[maxSubscribers])
      ).rejects.toThrow('Maximum subscribers per session limit reached');
      
      await sessionLogTracker.endSession(sessionId);
    });

    it('should handle failed subscriber notifications gracefully', async () => {
      const sessionId = 'error-handling-session';
      const actor = 'error-user';
      await sessionLogTracker.startSession(sessionId, actor);
      
      // Create a callback that will fail
      const failingCallback = jest.fn().mockImplementation(() => {
        throw new Error('Simulated callback failure');
      });
      
      // Create a successful callback
      const successCallback = jest.fn();
      
      await sessionLogTracker.subscribeToRealtimeEvents(sessionId, failingCallback);
      await sessionLogTracker.subscribeToRealtimeEvents(sessionId, successCallback);
      
      // Log an event - should not throw despite failing callback
      await sessionLogTracker.logSessionEvent(sessionId, 'info', 'ERROR_TEST', 'Testing error handling');
      
      // Successful callback should still be called
      expect(successCallback).toHaveBeenCalled();
      
      await sessionLogTracker.endSession(sessionId);
    });

    it('should maintain data integrity across notifications', async () => {
      const sessionId = 'data-integrity-session';
      const actor = 'integrity-user';
      await sessionLogTracker.startSession(sessionId, actor);
      
      const mockCallback = jest.fn();
      await sessionLogTracker.subscribeToRealtimeEvents(sessionId, mockCallback);
      
      // Log multiple events
      const testEvents = [
        { level: 'info', code: 'TEST_1', message: 'First test event' },
        { level: 'warn', code: 'TEST_2', message: 'Second test event' },
        { level: 'error', code: 'TEST_3', message: 'Third test event' }
      ];
      
      for (const event of testEvents) {
        await sessionLogTracker.logSessionEvent(
          sessionId,
          event.level as 'info' | 'warn' | 'error',
          event.code,
          event.message
        );
      }
      
      // Verify all notifications maintained data integrity
      const calls = mockCallback.mock.calls;
      expect(calls.length).toBe(testEvents.length);
      
      calls.forEach((call, index) => {
        const data = call[0];
        // Verify the notification structure matches TRealtimeData
        expect(data).toMatchObject({
          sessionId: sessionId,
          timestamp: expect.any(String),
          actor: actor,
          eventCount: expect.any(Number),
          status: 'active'
        });
        
        // Verify that the event count increases with each notification
        expect(data.eventCount).toBeGreaterThan(index + 1); // +1 for SESSION_START event
      });
      
      await sessionLogTracker.endSession(sessionId);
    });

    it('should enforce security boundaries for subscriptions', async () => {
      const sessionId = 'security-test-session';
      const actor = 'security-user';
      await sessionLogTracker.startSession(sessionId, actor);
      
      const mockCallback = jest.fn();
      await sessionLogTracker.subscribeToRealtimeEvents(sessionId, mockCallback);
      
      // Test memory limits
      const largePayload = Buffer.alloc(2 * 1024 * 1024).toString(); // 2MB payload
      
      // Attempt to log oversized event
      await expect(
        sessionLogTracker.logSessionEvent(sessionId, 'info', 'LARGE_EVENT', largePayload)
      ).rejects.toThrow(/Event size \d+ exceeds maximum allowed size \d+/);
      
      // Verify no notification for rejected event
      expect(mockCallback).not.toHaveBeenCalled();
      
      await sessionLogTracker.endSession(sessionId);
    });
  });

  // ============================================================================
  // ERROR HANDLING AND EDGE CASES TESTS
  // ============================================================================

  describe('Error Handling and Edge Cases', () => {
    beforeEach(async () => {
      await sessionLogTracker.initialize();
    });

    it('should handle invalid session IDs', async () => {
      await expect(sessionLogTracker.startSession('', 'user'))
        .rejects.toThrow();

      await expect(sessionLogTracker.logSessionEvent('invalid-session', 'info', 'TEST', 'message'))
        .rejects.toThrow('Session not found: invalid-session');
    });

    it('should handle file system errors gracefully', async () => {
      const sessionId = 'error-session-001';
      await sessionLogTracker.startSession(sessionId, 'error-user');

      // Mock file system error for subsequent operations
      mockFs.appendFileSync.mockImplementation(() => {
        throw new Error('File system error');
      });

      await expect(sessionLogTracker.logSessionEvent(sessionId, 'info', 'ERROR_TEST', 'Test'))
        .rejects.toThrow('File system error');
    });

    it('should validate input parameters', async () => {
      await expect(sessionLogTracker.startSession('test-session', ''))
        .rejects.toThrow();

      const sessionId = 'validation-session-001';
      await sessionLogTracker.startSession(sessionId, 'validation-user');

      // Test with invalid log level - should not throw (graceful handling)
      await expect(sessionLogTracker.logSessionEvent(sessionId, 'invalid' as any, 'TEST', 'message'))
        .resolves.not.toThrow();
    });

    it('should handle service shutdown gracefully', async () => {
      const sessionId = 'shutdown-session-001';
      await sessionLogTracker.startSession(sessionId, 'shutdown-user');

      await expect(sessionLogTracker.shutdown()).resolves.not.toThrow();
      expect(sessionLogTracker.isReady()).toBe(false);
    });
  });

  // ============================================================================
  // TYPESCRIPT COMPLIANCE AND TYPE SAFETY TESTS
  // ============================================================================

  describe('TypeScript Compliance', () => {
    beforeEach(async () => {
      await sessionLogTracker.initialize();
    });

    it('should maintain type safety for session data', async () => {
      const sessionId = 'type-session-001';
      const sessionData = await sessionLogTracker.startSession(sessionId, 'type-user');

      // Verify type structure
      expect(sessionData.session.sessionId).toBe(sessionId);
      expect(sessionData.session.status).toBe('active');
      expect(Array.isArray(sessionData.session.events)).toBe(true);
      expect(typeof sessionData.session.performance).toBe('object');
      expect(typeof sessionData.session.quality).toBe('object');
    });

    it('should enforce interface contracts', async () => {
      // Verify SessionLogTracker implements required interfaces
      expect(typeof sessionLogTracker.startSession).toBe('function');
      expect(typeof sessionLogTracker.endSession).toBe('function');
      expect(typeof sessionLogTracker.logSessionEvent).toBe('function');
      expect(typeof sessionLogTracker.getSessionData).toBe('function');
      expect(typeof sessionLogTracker.getActiveSessions).toBe('function');
      expect(typeof sessionLogTracker.getSessionHistory).toBe('function');
      expect(typeof sessionLogTracker.getSessionAnalytics).toBe('function');
      expect(typeof sessionLogTracker.subscribeToRealtimeEvents).toBe('function');
      expect(typeof sessionLogTracker.generateAuditTrail).toBe('function');
      expect(typeof sessionLogTracker.getAuditHistory).toBe('function');
      expect(typeof sessionLogTracker.exportAuditData).toBe('function');
      expect(typeof sessionLogTracker.performComplianceAudit).toBe('function');
    });

    it('should handle validation results correctly', async () => {
      const validationResult = await sessionLogTracker.validate();

      expect(validationResult).toBeDefined();
      expect(validationResult.status).toBeDefined();
      expect(Array.isArray(validationResult.errors)).toBe(true);
      expect(Array.isArray(validationResult.warnings)).toBe(true);
    });
  });

  describe('Session Security', () => {
    it('should allow valid session operations for test actors', async () => {
      const sessionId = 'test-session-001';
      const actor = 'test-actor-001';
      
      // Start session
      const session = await sessionLogTracker.startSession(sessionId, actor);
      expect(session).toBeDefined();
      expect(session.session.sessionId).toBe(sessionId);
      expect(session.session.actor).toBe(actor);
      
      // Log event
      await expect(sessionLogTracker.logSessionEvent(
        sessionId,
        'info',
        'test_event',
        'Test event'
      )).resolves.not.toThrow();
      
      // End session
      await expect(sessionLogTracker.endSession(sessionId)).resolves.not.toThrow();
    });

    it('should enforce session limits for non-test actors', async () => {
      const actor = 'real-actor-001';
      const sessions: ISessionLogData[] = [];
      
      // Try to create more than max allowed sessions
      const maxSessions = (sessionLogTracker as any)._maxSessionsPerActor;
      for (let i = 0; i < maxSessions; i++) {
        const sessionId = `session-${i}`;
        const session = await sessionLogTracker.startSession(sessionId, actor);
        sessions.push(session);
      }
      
      // Attempt to create one more session
      await expect(
        sessionLogTracker.startSession(`session-${maxSessions}`, actor)
      ).rejects.toThrow('Maximum sessions per actor limit reached');
    });

    it('should enforce event rate limits for non-test actors', async () => {
      const sessionId = 'rate-limit-session';
      const actor = 'real-actor-002';
      
      // Clear any existing state first
      (sessionLogTracker as any)._sessionEventRates.clear();
      (sessionLogTracker as any)._sessionEventCounts.clear();
      (sessionLogTracker as any)._activeSessions.clear();
      (sessionLogTracker as any)._actorSessions.clear();
      
      // Start session
      await sessionLogTracker.startSession(sessionId, actor);
      
      // Get the max events per minute limit
      const maxEventsPerMinute = (sessionLogTracker as any)._maxEventsPerMinute;

      // Log events up to the limit (accounting for the SESSION_START event already counted)
      for (let i = 0; i < maxEventsPerMinute - 1; i++) {
        await sessionLogTracker.logSessionEvent(
          sessionId,
          'info',
          'test_event',
          `Event ${i}`
        );
      }
      
      // Attempt to log one more event immediately - should trigger rate limit
      await expect(
        sessionLogTracker.logSessionEvent(
          sessionId,
          'info',
          'test_event',
          'Rate limit test'
        )
      ).rejects.toThrow('Event rate limit exceeded');
    });

    it('should handle invalid session operations', async () => {
      // Test invalid session ID
      await expect(
        sessionLogTracker.startSession('', 'test-actor')
      ).rejects.toThrow('Invalid session ID');
      
      // Test invalid actor
      await expect(
        sessionLogTracker.startSession('test-session', '')
      ).rejects.toThrow('Invalid actor');
      
      // Test non-existent session
      await expect(
        sessionLogTracker.logSessionEvent(
          'non-existent',
          'info',
          'test_event',
          'Test'
        )
      ).rejects.toThrow('Session not found');
    });

    it('should handle blacklisted actors', async () => {
      const actor = 'real-actor-003';
      const sessionId = 'blacklist-test';
      
      // Blacklist the actor
      (sessionLogTracker as any)._blacklistedActors.add(actor);
      
      // Attempt operations with blacklisted actor
      await expect(
        sessionLogTracker.startSession(sessionId, actor)
      ).rejects.toThrow('Actor is blacklisted');
    });
  });

  describe('Session Management', () => {
    it('should properly track active sessions', async () => {
      const actor = 'test-actor-002';
      const sessions: ISessionLogData[] = [];
      
      // Create multiple sessions
      for (let i = 0; i < 3; i++) {
        const sessionId = `test-session-${i}`;
        const session = await sessionLogTracker.startSession(sessionId, actor);
        sessions.push(session);
      }
      
      // Verify active sessions
      const activeSessions = await sessionLogTracker.getActiveSessions();
      expect(activeSessions.length).toBe(3);
      
      // End one session
      await sessionLogTracker.endSession(sessions[0].session.sessionId);
      
      // Verify updated active sessions
      const updatedSessions = await sessionLogTracker.getActiveSessions();
      expect(updatedSessions.length).toBe(2);
    });

    it('should properly handle session cleanup', async () => {
      const actor = 'test-actor-003';
      const sessionId = 'cleanup-test';
      
      // Start session
      await sessionLogTracker.startSession(sessionId, actor);
      
      // Add some events
      await sessionLogTracker.logSessionEvent(
        sessionId,
        'info',
        'test_event',
        'Test event 1'
      );
      await sessionLogTracker.logSessionEvent(
        sessionId,
        'warn',
        'test_event',
        'Test event 2'
      );
      
      // End session
      await sessionLogTracker.endSession(sessionId);
      
      // Verify cleanup
      expect((sessionLogTracker as any)._activeSessions.size).toBe(0);
      expect((sessionLogTracker as any)._sessionEventCounts.has(sessionId)).toBe(false);
      expect((sessionLogTracker as any)._sessionEventRates.has(sessionId)).toBe(false);
      expect((sessionLogTracker as any)._realtimeSubscribers.has(sessionId)).toBe(false);
    });
  });
});