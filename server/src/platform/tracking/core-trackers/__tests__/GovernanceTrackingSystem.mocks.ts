/**
 * @file Governance Tracking System Test Mocks and Utilities
 * @filepath server/src/platform/tracking/core-trackers/__tests__/GovernanceTrackingSystem.mocks.ts
 * @task-id T-TSK-02.SUB-02.1.IMP-03.TEST-MOCKS
 * @component tracking-governance-tracker-test-mocks
 * @reference foundation-context.SERVICE.005.TESTS.MOCKS
 * @template enterprise-test-mocking-standards
 * @tier T2
 * @context foundation-context
 * @category Foundation-Testing-Mocks
 * @created 2025-07-10
 * @modified 2025-07-10
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE TEST MOCKING (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-tracking-architecture-test-mocking
 * @governance-dcr DCR-foundation-001-tracking-development-test-mocking
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on server/src/platform/tracking/core-trackers/GovernanceTrackingSystem
 * @depends-on shared/src/types/platform/tracking/tracking-types
 * @enables enterprise-test-mocking-framework
 * @related-contexts foundation-context-test-mocking
 * @governance-impact test-reliability, mock-data-quality
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type governance-tracking-system-test-mocks
 * @lifecycle-stage test-support
 * @testing-framework jest
 * @mock-categories data-mocks, behavior-mocks, integration-mocks
 * @documentation docs/contexts/foundation-context/testing/governance-tracking-system-mocks.md
 */

import { jest } from '@jest/globals';
import { IGovernanceEvent } from '../GovernanceTrackingSystem';
import {
  TTrackingData,
  TRealtimeCallback,
  TAuthorityData,
  TTrackingConfig,
  TRealtimeData,
  TValidationResult,
  TGovernanceStatus,
  TAuditResult,
  TMetrics
} from '../../../../../../shared/src/types/platform/tracking/tracking-types';

/**
 * Mock Data Factories
 * 
 * Enterprise-grade mock data generation utilities for comprehensive testing
 */

/**
 * Creates a mock tracking configuration with enterprise defaults
 */
export function createMockTrackingConfig(): TTrackingConfig {
  return {
    service: {
      name: `test-governance-tracker-${Date.now()}`,
      version: '1.0.0',
      environment: 'development',
      timeout: 5000,
      retry: {
        maxAttempts: 3,
        delay: 100,
        backoffMultiplier: 2,
        maxDelay: 5000
      }
    },
    governance: {
      authority: 'Mock Authority',
      requiredCompliance: ['test-compliance'],
      auditFrequency: 24,
      violationReporting: true
    },
    performance: {
      metricsEnabled: true,
      metricsInterval: 1000,
      monitoringEnabled: true,
      alertThresholds: {
        errorRate: 5,
        responseTime: 1000,
        memoryUsage: 80,
        cpuUsage: 70
      }
    },
    logging: {
      level: 'info',
      format: 'json',
      filePath: undefined,
      rotation: false,
      maxFileSize: 10
    }
  };
}

/**
 * Creates a mock governance event with realistic data
 */
export function createMockGovernanceEvent(overrides?: Partial<IGovernanceEvent>): IGovernanceEvent {
  const baseEvent: IGovernanceEvent = {
    eventId: `evt-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    timestamp: new Date(),
    eventType: 'authority_validation',
    severity: 'info',
    source: 'MockTestSource',
    description: 'Mock governance event for testing',
    context: {
      milestone: 'M0-TEST',
      category: 'mock-testing',
      documents: ['mock-document.md'],
      affectedComponents: ['MockComponent'],
      metadata: {
        testEvent: true,
        mockId: Math.random().toString(36).substr(2, 9),
        timestamp: Date.now()
      }
    }
  };

  return { ...baseEvent, ...overrides };
}

/**
 * Creates mock authority data with comprehensive validation information
 */
export function createMockAuthorityData(overrides?: Partial<TAuthorityData>): TAuthorityData {
  const baseAuthority: TAuthorityData = {
    level: 'architectural-authority',
    validator: 'Mock Authority Validator',
    validationStatus: 'validated',
    validatedAt: new Date().toISOString(),
    complianceScore: 95
  };

  return { ...baseAuthority, ...overrides };
}

/**
 * Creates mock tracking data for testing track operations
 */
export function createMockTrackingData(overrides?: Partial<TTrackingData>): TTrackingData {
  const baseTrackingData: TTrackingData = {
    componentId: `mock-component-${Date.now()}`,
    status: 'in-progress',
    timestamp: new Date().toISOString(),
    metadata: {
      phase: 'testing',
      progress: 50,
      priority: 'P2',
      estimatedCompletion: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      assignee: 'test-user',
      tags: ['test', 'mock', 'governance'],
      custom: {
        testEvent: true,
        mockId: Math.random().toString(36).substr(2, 9),
        generatedAt: Date.now()
      }
    },
    context: {
      contextId: 'foundation-context',
      milestone: 'M0-TEST',
      category: 'mock-testing',
      dependencies: [],
      dependents: []
    },
    progress: {
      completion: 50,
      tasksCompleted: 5,
      totalTasks: 10,
      timeSpent: 120,
      estimatedTimeRemaining: 120,
      quality: {
        codeCoverage: 80,
        testCount: 25,
        bugCount: 2,
        qualityScore: 85,
        performanceScore: 90
      }
    },
    authority: {
      level: 'architectural-authority',
      validator: 'Mock Authority Validator',
      validationStatus: 'validated',
      validatedAt: new Date().toISOString(),
      complianceScore: 95
    }
  };

  return { ...baseTrackingData, ...overrides };
}

/**
 * Creates a mock realtime callback function for testing subscriptions
 */
export function createMockRealtimeCallback(): any {
  return jest.fn();
}

/**
 * Creates mock realtime data for testing notifications
 */
export function createMockRealtimeData(overrides?: Partial<TRealtimeData>): TRealtimeData {
  const baseRealtimeData: TRealtimeData = {
    sessionId: `session-${Date.now()}`,
    timestamp: new Date().toISOString(),
    actor: `user-${Math.random().toString(36).substr(2, 8)}`,
    eventCount: Math.floor(Math.random() * 100),
    status: 'active',
    performance: {
      totalEvents: Math.floor(Math.random() * 1000),
      eventsByLevel: {
        info: Math.floor(Math.random() * 500),
        warning: Math.floor(Math.random() * 100),
        error: Math.floor(Math.random() * 50)
      },
      avgProcessingTime: Math.random() * 100,
      peakMemoryUsage: Math.random() * 1000,
      efficiencyScore: Math.random() * 100
    },
    quality: {
      errorRate: Math.random() * 5,
      warningRate: Math.random() * 10,
      complianceScore: 80 + Math.random() * 20,
      authorityValidationRate: 90 + Math.random() * 10
    }
  };

  return { ...baseRealtimeData, ...overrides };
}

/**
 * Mock Data Generators for Complex Scenarios
 */

/**
 * Generates a series of related governance events for testing workflows
 */
export function createGovernanceEventSequence(count: number, baseContext?: any): IGovernanceEvent[] {
  const events: IGovernanceEvent[] = [];
  const baseTimestamp = Date.now();
  
  const eventTypes: Array<IGovernanceEvent['eventType']> = [
    'authority_validation',
    'compliance_check',
    'audit_trail',
    'violation_report',
    'governance_update'
  ];
  
  const severityLevels: Array<IGovernanceEvent['severity']> = ['info', 'warning', 'error', 'critical'];

  for (let i = 0; i < count; i++) {
    const event = createMockGovernanceEvent({
      eventId: `seq-evt-${i}-${baseTimestamp}`,
      timestamp: new Date(baseTimestamp + i * 1000), // 1 second apart
      eventType: eventTypes[i % eventTypes.length],
      severity: severityLevels[i % severityLevels.length],
      source: `SequenceSource${Math.floor(i / 5)}`, // Group every 5 events by source
      description: `Sequence event ${i + 1} of ${count}`,
      context: {
        milestone: baseContext?.milestone || 'M0-SEQUENCE',
        category: 'event-sequence',
        documents: [`sequence-doc-${i}.md`],
        affectedComponents: [`SequenceComponent${i % 3}`], // Cycle through 3 components
        metadata: {
          sequenceIndex: i,
          sequenceTotal: count,
          sequenceId: `seq-${baseTimestamp}`,
          ...baseContext?.metadata
        }
      }
    });

    events.push(event);
  }

  return events;
}

/**
 * Creates a comprehensive test scenario with multiple systems and events
 */
export function createIntegrationTestScenario(): IntegrationTestScenario {
  const scenarioId = `integration-${Date.now()}`;
  
  return {
    scenarioId,
    systems: [
      {
        systemId: 'primary-system',
        config: createMockTrackingConfig(),
        expectedEvents: 100,
        expectedCompliance: 'compliant'
      },
      {
        systemId: 'secondary-system',
        config: createMockTrackingConfig(),
        expectedEvents: 50,
        expectedCompliance: 'warning'
      }
    ],
    workflows: [
      {
        workflowId: 'authority-validation-workflow',
        steps: [
          { action: 'request-validation', expectedDuration: 100 },
          { action: 'perform-checks', expectedDuration: 200 },
          { action: 'approve-validation', expectedDuration: 150 },
          { action: 'audit-trail', expectedDuration: 50 }
        ],
        expectedOutcome: 'success'
      },
      {
        workflowId: 'compliance-monitoring-workflow',
        steps: [
          { action: 'start-monitoring', expectedDuration: 50 },
          { action: 'collect-metrics', expectedDuration: 300 },
          { action: 'analyze-compliance', expectedDuration: 200 },
          { action: 'generate-report', expectedDuration: 100 }
        ],
        expectedOutcome: 'report-generated'
      }
    ],
    testData: {
      authorities: Array.from({ length: 2 }, () => createMockAuthorityData()), // Reduced from 5
      events: createGovernanceEventSequence(5), // Reduced from 25
      trackingData: Array.from({ length: 5 }, () => createMockTrackingData()) // Reduced from 50
    },
    performanceTargets: {
      maxLatency: 1000, // ms
      minThroughput: 50, // events/sec
      maxMemoryUsage: 100 * 1024 * 1024, // 100MB
      maxErrorRate: 0.05 // 5%
    }
  };
}

/**
 * Creates performance test scenario with specific load characteristics
 */
export function createPerformanceTestScenario(): PerformanceTestScenario {
  return {
    scenarioId: `performance-${Date.now()}`,
    loadProfiles: [
      {
        name: 'low-load',
        eventsPerSecond: 10,
        duration: 30000, // 30 seconds
        concurrency: 1
      },
      {
        name: 'medium-load',
        eventsPerSecond: 50,
        duration: 60000, // 1 minute
        concurrency: 5
      },
      {
        name: 'high-load',
        eventsPerSecond: 100,
        duration: 30000, // 30 seconds
        concurrency: 10
      },
      {
        name: 'stress-load',
        eventsPerSecond: 200,
        duration: 15000, // 15 seconds
        concurrency: 20
      }
    ],
    memoryProfiles: [
      {
        name: 'tiny-events',
        eventSize: 256, // 256 bytes - very small for performance tests
        eventCount: 50
      },
      {
        name: 'small-events',
        eventSize: 512, // 512 bytes
        eventCount: 25
      },
      {
        name: 'medium-events',
        eventSize: 1024, // 1KB - reduced from 10KB
        eventCount: 10
      }
    ],
    performanceTargets: {
      maxAverageLatency: 50, // ms
      maxP95Latency: 200, // ms
      maxP99Latency: 500, // ms
      minThroughput: 75, // events/sec
      maxMemoryGrowth: 2.0, // 2x initial memory
      maxCpuUsage: 80 // 80%
    }
  };
}

/**
 * Mock Behavior Factories
 */

/**
 * Creates a latency mock that simulates network delays
 */
export function createLatencyMock<T extends (...args: any[]) => Promise<any>>(
  implementation: T,
  latencyMs: number = 100
): any {
  return jest.fn().mockImplementation(async (...args: any[]) => {
    await new Promise(resolve => setTimeout(resolve, latencyMs));
    return implementation(...args);
  });
}

/**
 * Creates a flakey mock that occasionally fails
 */
export function createFlakeyMock<T extends (...args: any[]) => any>(
  implementation: T,
  failureRate: number = 0.1,
  errorMessage: string = 'Simulated intermittent failure'
): any {
  return jest.fn().mockImplementation((...args: any[]) => {
    if (Math.random() < failureRate) {
      throw new Error(errorMessage);
    }
    return implementation(...args);
  });
}

/**
 * Creates a tracked mock function with call history
 */
export function createTrackedMock<T extends (...args: any[]) => any>(
  implementation: T
): any {
  const callHistory: Array<{ timestamp: number; args: any[]; result?: any; error?: Error }> = [];
  
  const mock = jest.fn().mockImplementation((...args: any[]) => {
    const callRecord: any = { timestamp: Date.now(), args };
    try {
      const result = implementation(...args);
      callRecord.result = result;
      callHistory.push(callRecord);
      return result;
    } catch (error) {
      callRecord.error = error as Error;
      callHistory.push(callRecord);
      throw error;
    }
  });

  // Add tracking methods
  (mock as any).getCallHistory = () => callHistory;
  (mock as any).getCallCount = () => callHistory.length;
  (mock as any).getLastCall = () => callHistory[callHistory.length - 1];
  (mock as any).getCallsInTimeRange = (startTime: number, endTime: number) =>
    callHistory.filter(call => call.timestamp >= startTime && call.timestamp <= endTime);

  return mock;
}

/**
 * Creates a mock governance system for testing
 */
export function createMockGovernanceSystem(): any {
  const events: IGovernanceEvent[] = [];
  const subscriptions = new Map<string, any>();

  // Memory management for performance tests
  const maxEvents = process.env.TEST_TYPE === 'performance' ? 10 : 100;

  return {
    logGovernanceEvent: jest.fn().mockImplementation(async (...args: any[]) => {
      const [eventType, severity, source, description, context] = args;
      const event: IGovernanceEvent = {
        eventId: `evt-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date(),
        eventType,
        severity,
        source,
        description,
        context
      };
      events.push(event);

      // Prevent memory accumulation - keep only recent events
      if (events.length > maxEvents) {
        events.shift();
      }

      // Notify subscribers
      Array.from(subscriptions.values()).forEach(callback => {
        try {
          callback(createMockRealtimeData());
        } catch (error) {
          // Ignore callback errors in mock
        }
      });

      return event;
    }),

    subscribeToGovernanceEvents: jest.fn().mockImplementation(async (callback: any) => {
      const subscriptionId = `sub-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      subscriptions.set(subscriptionId, callback);
      return subscriptionId;
    }),

    getGovernanceEventHistory: jest.fn().mockImplementation(async (...args: any[]) => {
      const [source, eventType] = args;
      return events.filter(event => {
        if (source && event.source !== source) return false;
        if (eventType && event.eventType !== eventType) return false;
        return true;
      });
    }),

    validateCompliance: jest.fn().mockImplementation(async () => ({
      validationId: `val-${Date.now()}`,
      componentId: 'mock-governance-system',
      timestamp: new Date(),
      executionTime: Math.random() * 100,
      status: 'valid' as const,
      overallScore: 95,
      checks: [],
      references: {
        componentId: 'mock-governance-system',
        internalReferences: [],
        externalReferences: [],
        circularReferences: [],
        missingReferences: [],
        redundantReferences: [],
        metadata: {
          totalReferences: 0,
          buildTimestamp: new Date(),
          analysisDepth: 1
        }
      },
      recommendations: [],
      warnings: [],
      errors: [],
      metadata: {
        validationMethod: 'mock',
        rulesApplied: 10,
        dependencyDepth: 1,
        cyclicDependencies: [],
        orphanReferences: []
      }
    })),

    getComplianceStatus: jest.fn().mockImplementation(async () => ({
      status: 'compliant' as const,
      lastCheck: new Date(),
      complianceScore: 95,
      violations: [],
      activeIssues: 0,
      resolvedIssues: 0,
      nextReview: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
    })),

    generateComplianceReport: jest.fn().mockImplementation(async () => ({
      auditId: `audit-${Date.now()}`,
      timestamp: new Date(),
      auditType: 'compliance' as const,
      status: 'passed' as const,
      score: 95,
      findings: [],
      recommendations: [],
      remediation: [],
      nextAuditDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
    })),

    getGovernanceMetrics: jest.fn().mockImplementation(async () => ({
      timestamp: new Date().toISOString(),
      service: 'mock-governance-system',
      performance: {
        queryExecutionTimes: [10, 20, 30],
        cacheOperationTimes: [5, 10, 15],
        memoryUtilization: [50, 60, 70],
        throughputMetrics: [100, 200, 300],
        errorRates: [0.1, 0.2, 0.3]
      },
      usage: {
        totalOperations: 1000,
        successfulOperations: 990,
        failedOperations: 10,
        activeUsers: 100,
        peakConcurrentUsers: 200
      },
      errors: {
        totalErrors: 10,
        errorRate: 1,
        errorsByType: {
          validation: 5,
          timeout: 3,
          unknown: 2
        },
        recentErrors: []
      },
      custom: {
        mockMetric: 100
      }
    })),

    _getEvents: () => events,
    _getSubscriptions: () => subscriptions,
    _reset: () => {
      events.length = 0;
      subscriptions.clear();
    }
  };
}

/**
 * Type Definitions for Test Interfaces
 */

export interface IntegrationTestScenario {
  scenarioId: string;
  systems: Array<{
    systemId: string;
    config: Partial<TTrackingConfig>;
    expectedEvents: number;
    expectedCompliance: 'compliant' | 'warning' | 'non-compliant';
  }>;
  workflows: Array<{
    workflowId: string;
    steps: Array<{
      action: string;
      expectedDuration: number;
    }>;
    expectedOutcome: string;
  }>;
  testData: {
    authorities: TAuthorityData[];
    events: IGovernanceEvent[];
    trackingData: TTrackingData[];
  };
  performanceTargets: {
    maxLatency: number;
    minThroughput: number;
    maxMemoryUsage: number;
    maxErrorRate: number;
  };
}

export interface PerformanceTestScenario {
  scenarioId: string;
  loadProfiles: Array<{
    name: string;
    eventsPerSecond: number;
    duration: number;
    concurrency: number;
  }>;
  memoryProfiles: Array<{
    name: string;
    eventSize: number;
    eventCount: number;
  }>;
  performanceTargets: {
    maxAverageLatency: number;
    maxP95Latency: number;
    maxP99Latency: number;
    minThroughput: number;
    maxMemoryGrowth: number;
    maxCpuUsage: number;
  };
}

export interface MockGovernanceSystem {
  logGovernanceEvent: any;
  subscribeToGovernanceEvents: any;
  getGovernanceEventHistory: any;
  validateCompliance: any;
  getComplianceStatus: any;
  generateComplianceReport: any;
  getGovernanceMetrics: any;
  _getEvents(): IGovernanceEvent[];
  _getSubscriptions(): Map<string, any>;
  _reset(): void;
}

/**
 * Test Utilities
 */

/**
 * Waits for a condition to be met within a timeout period
 */
export async function waitForCondition(
  condition: () => boolean | Promise<boolean>,
  timeoutMs: number = 5000,
  intervalMs: number = 100
): Promise<void> {
  const startTime = Date.now();
  
  while (Date.now() - startTime < timeoutMs) {
    if (await condition()) {
      return;
    }
    await new Promise(resolve => setTimeout(resolve, intervalMs));
  }
  
  throw new Error(`Condition not met within ${timeoutMs}ms`);
}

/**
 * Measures the execution time of an async function
 */
export async function measureExecutionTime<T>(
  operation: () => Promise<T>
): Promise<{ result: T; duration: number }> {
  const startTime = performance.now();
  const result = await operation();
  const endTime = performance.now();
  
  return {
    result,
    duration: endTime - startTime
  };
}

/**
 * Creates a test context with cleanup utilities
 */
export function createTestContext(): TestContext {
  const cleanupTasks: Array<() => Promise<void> | void> = [];
  
  return {
    addCleanupTask: (task: () => Promise<void> | void) => {
      cleanupTasks.push(task);
    },
    
    cleanup: async () => {
      for (const task of cleanupTasks.reverse()) {
        try {
          await task();
        } catch (error) {
          console.warn('Cleanup task failed:', error);
        }
      }
      cleanupTasks.length = 0;
    }
  };
}

export interface TestContext {
  addCleanupTask(task: () => Promise<void> | void): void;
  cleanup(): Promise<void>;
}

/**
 * Assertion Utilities
 */

/**
 * Asserts that an array is sorted by a specific property
 */
export function assertSortedBy<T>(
  array: T[],
  accessor: (item: T) => number | string | Date,
  order: 'asc' | 'desc' = 'asc'
): void {
  for (let i = 1; i < array.length; i++) {
    const prev = accessor(array[i - 1]);
    const curr = accessor(array[i]);
    
    if (order === 'asc') {
      if (prev > curr) {
        throw new Error(`Array is not sorted in ascending order at index ${i}`);
      }
    } else {
      if (prev < curr) {
        throw new Error(`Array is not sorted in descending order at index ${i}`);
      }
    }
  }
}

/**
 * Asserts that a value is within a specific range
 */
export function assertInRange(
  value: number,
  min: number,
  max: number,
  message?: string
): void {
  if (value < min || value > max) {
    throw new Error(message || `Value ${value} is not within range [${min}, ${max}]`);
  }
}

/**
 * Asserts that an operation completes within a time limit
 */
export async function assertCompletesWithin<T>(
  operation: () => Promise<T>,
  timeoutMs: number,
  message?: string
): Promise<T> {
  return new Promise<T>((resolve, reject) => {
    const timeout = setTimeout(() => {
      reject(new Error(message || `Operation did not complete within ${timeoutMs}ms`));
    }, timeoutMs);

    operation()
      .then(result => {
        clearTimeout(timeout);
        resolve(result);
      })
      .catch(error => {
        clearTimeout(timeout);
        reject(error);
      });
  });
}