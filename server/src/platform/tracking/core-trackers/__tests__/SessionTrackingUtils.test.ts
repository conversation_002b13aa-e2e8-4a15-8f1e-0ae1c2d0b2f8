/**
 * @file Session Tracking Utils Test Suite
 * @filepath server/src/platform/tracking/core-trackers/__tests__/SessionTrackingUtils.test.ts
 * @task-id T-REFACTOR-002.UTILS.TEST
 * @component session-tracking-utils-test
 * @reference foundation-context.SERVICE.003.UTILS.TEST
 * @template enterprise-grade-testing-with-latest-standards
 * @tier T2
 * @context foundation-context
 * @category Foundation-Testing
 * @created 2025-07-10
 * @modified 2025-07-10
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-tracking-architecture
 * @governance-dcr DCR-foundation-001-tracking-testing
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/types/platform/tracking/tracking-types
 * @tests server/src/platform/tracking/core-trackers/SessionTrackingUtils
 * @related-contexts foundation-context
 * @governance-impact framework-foundation, tracking-testing
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type session-tracking-utils-test
 * @lifecycle-stage testing
 * @testing-coverage minimum-80-percent
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/contexts/foundation-context/testing/session-tracking-utils-test.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 *   enterprise-testing-standards: true
 */

import { jest, describe, beforeEach, afterEach, it, expect } from '@jest/globals';
import { SessionTrackingUtils, ISessionData, ISessionAnalytics } from '../SessionTrackingUtils';
import { TValidationResult, TComplianceCheck } from '../../../../../../shared/src/types/platform/tracking/tracking-types';

/**
 * Enterprise-Grade Test Suite for Session Tracking Utils
 * 
 * Comprehensive testing covering all public methods, edge cases,
 * error conditions, and performance scenarios.
 */
describe('SessionTrackingUtils', () => {
  let sessionTrackingUtils: SessionTrackingUtils;
  let mockSessionData: ISessionData;
  let mockSessionAnalytics: ISessionAnalytics;
  let mockValidationResult: TValidationResult;

  // ============================================================================
  // TEST SETUP AND TEARDOWN
  // ============================================================================

  beforeEach(() => {
    // Initialize fresh instance for each test
    sessionTrackingUtils = new SessionTrackingUtils();
    
    // Create mock session data
    mockSessionData = createMockSessionData();
    
    // Create mock session analytics
    mockSessionAnalytics = createMockSessionAnalytics();
    
    // Create mock validation result
    mockValidationResult = createMockValidationResult();
    
    // Reset all timers and mocks
    jest.clearAllMocks();
    jest.clearAllTimers();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  // ============================================================================
  // CONSTRUCTOR AND INITIALIZATION TESTS
  // ============================================================================

  describe('Constructor and Initialization', () => {
    it('should create instance with correct version', () => {
      expect(sessionTrackingUtils).toBeInstanceOf(SessionTrackingUtils);
      // Accessing private property through type assertion for testing
      expect((sessionTrackingUtils as any)._version).toBe('2.0.0');
    });

    it('should initialize without throwing errors', () => {
      expect(() => new SessionTrackingUtils()).not.toThrow();
    });
  });

  // ============================================================================
  // ACTIVITY SCORE CALCULATION TESTS
  // ============================================================================

  describe('calculateActivityScore', () => {
    it('should calculate activity score correctly for active session', () => {
      const score = sessionTrackingUtils.calculateActivityScore(mockSessionData);
      
      expect(score).toBeGreaterThanOrEqual(0);
      expect(score).toBeLessThanOrEqual(100);
      expect(typeof score).toBe('number');
    });

    it('should handle session with no events', () => {
      const sessionWithNoEvents: ISessionData = {
        ...mockSessionData,
        events: [],
        analytics: {
          ...mockSessionData.analytics,
          totalEvents: 0,
          errorCount: 0
        }
      };

      const score = sessionTrackingUtils.calculateActivityScore(sessionWithNoEvents);
      expect(score).toBeGreaterThanOrEqual(0);
      expect(score).toBeLessThanOrEqual(100);
    });

    it('should handle session with high error rate', () => {
      const sessionWithHighErrors: ISessionData = {
        ...mockSessionData,
        analytics: {
          ...mockSessionData.analytics,
          totalEvents: 100,
          errorCount: 50 // 50% error rate
        }
      };

      const score = sessionTrackingUtils.calculateActivityScore(sessionWithHighErrors);
      expect(score).toBeLessThan(80); // Should be penalized for high error rate
    });

    it('should handle very old session', () => {
      const oldSession: ISessionData = {
        ...mockSessionData,
        lastActivity: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // 30 days ago
      };

      const score = sessionTrackingUtils.calculateActivityScore(oldSession);
      expect(score).toBeLessThan(50); // Should be heavily penalized for staleness
    });

    it('should handle calculation errors gracefully', () => {
      const invalidSession = null as any;
      
      // Mock console.error to verify error logging
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      
      const score = sessionTrackingUtils.calculateActivityScore(invalidSession);
      
      expect(score).toBe(0);
      expect(consoleSpy).toHaveBeenCalledWith('Failed to calculate activity score:', expect.any(Error));
      
      consoleSpy.mockRestore();
    });

    it('should return optimal score for ideal session', () => {
      const idealSession: ISessionData = {
        ...mockSessionData,
        startTime: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
        lastActivity: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
        analytics: {
          totalEvents: 100,
          errorCount: 0,
          warningCount: 5,
          duration: 2 * 60 * 60 * 1000, // 2 hours
          activityScore: 0 // Will be calculated
        }
      };

      const score = sessionTrackingUtils.calculateActivityScore(idealSession);
      expect(score).toBeGreaterThan(80);
    });
  });

  // ============================================================================
  // SESSION ANALYTICS UPDATE TESTS
  // ============================================================================

  describe('updateSessionAnalytics', () => {
    let activeSessions: Map<string, ISessionData>;

    beforeEach(() => {
      activeSessions = new Map();
      activeSessions.set('session1', mockSessionData);
      activeSessions.set('session2', {
        ...mockSessionData,
        sessionId: 'session2',
        endTime: new Date()
      });
    });

    it('should update analytics correctly', () => {
      const updatedAnalytics = sessionTrackingUtils.updateSessionAnalytics(
        mockSessionAnalytics,
        activeSessions
      );

      expect(updatedAnalytics.activeSessions).toBe(2);
      expect(updatedAnalytics.totalEvents).toBeGreaterThan(0);
      expect(updatedAnalytics.lastUpdated).toBeInstanceOf(Date);
    });

    it('should handle empty active sessions', () => {
      const emptyMap = new Map<string, ISessionData>();
      
      const updatedAnalytics = sessionTrackingUtils.updateSessionAnalytics(
        mockSessionAnalytics,
        emptyMap
      );

      expect(updatedAnalytics.activeSessions).toBe(0);
      expect(updatedAnalytics.totalEvents).toBe(0);
    });

    it('should calculate average duration correctly', () => {
      const sessionsWithEndTime = new Map<string, ISessionData>();
      
      const session1: ISessionData = {
        ...mockSessionData,
        sessionId: 'session1',
        startTime: new Date(Date.now() - 60000), // 1 minute duration
        endTime: new Date()
      };
      
      const session2: ISessionData = {
        ...mockSessionData,
        sessionId: 'session2',
        startTime: new Date(Date.now() - 120000), // 2 minute duration
        endTime: new Date()
      };

      sessionsWithEndTime.set('session1', session1);
      sessionsWithEndTime.set('session2', session2);

      const updatedAnalytics = sessionTrackingUtils.updateSessionAnalytics(
        mockSessionAnalytics,
        sessionsWithEndTime
      );

      expect(updatedAnalytics.averageSessionDuration).toBe(90000); // 1.5 minutes average
    });

    it('should handle update errors gracefully', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      
      // Pass invalid analytics object
      const result = sessionTrackingUtils.updateSessionAnalytics(
        null as any,
        activeSessions
      );

      expect(result).toBe(null);
      expect(consoleSpy).toHaveBeenCalledWith('Failed to update session analytics:', expect.any(Error));
      
      consoleSpy.mockRestore();
    });
  });

  // ============================================================================
  // SESSION VALIDATION TESTS
  // ============================================================================

  describe('validateActiveSessions', () => {
    let activeSessions: Map<string, ISessionData>;

    beforeEach(() => {
      activeSessions = new Map();
    });

    it('should validate normal active sessions', async () => {
      activeSessions.set('session1', mockSessionData);
      
      await sessionTrackingUtils.validateActiveSessions(activeSessions, mockValidationResult);
      
      expect(mockValidationResult.checks).toHaveLength(1);
      expect(mockValidationResult.checks[0].checkId).toBe('active-sessions-validation');
      expect(mockValidationResult.checks[0].status).toBe('passed');
    });

    it('should detect high session count', async () => {
      // Create 1001 sessions to trigger warning
      for (let i = 0; i < 1001; i++) {
        activeSessions.set(`session${i}`, {
          ...mockSessionData,
          sessionId: `session${i}`
        });
      }

      await sessionTrackingUtils.validateActiveSessions(activeSessions, mockValidationResult);
      
      expect(mockValidationResult.warnings).toContain('High number of active sessions detected');
    });

    it('should detect expired sessions', async () => {
      const expiredSession: ISessionData = {
        ...mockSessionData,
        lastActivity: new Date(Date.now() - 48 * 60 * 60 * 1000) // 48 hours ago
      };
      
      activeSessions.set('expired', expiredSession);

      await sessionTrackingUtils.validateActiveSessions(activeSessions, mockValidationResult);
      
      expect(mockValidationResult.warnings.some(w => w.includes('expired sessions found'))).toBe(true);
    });

    it('should detect stale sessions', async () => {
      const staleSession: ISessionData = {
        ...mockSessionData,
        lastActivity: new Date(Date.now() - 5 * 60 * 60 * 1000), // 5 hours ago
        status: 'active'
      };
      
      activeSessions.set('stale', staleSession);

      await sessionTrackingUtils.validateActiveSessions(activeSessions, mockValidationResult);
      
      expect(mockValidationResult.warnings.some(w => w.includes('stale sessions found'))).toBe(true);
    });

    it('should handle validation errors', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      
      // Pass invalid parameters
      await sessionTrackingUtils.validateActiveSessions(null as any, mockValidationResult);
      
      expect(mockValidationResult.errors).toContain('Active sessions validation failed');
      expect(consoleSpy).toHaveBeenCalled();
      
      consoleSpy.mockRestore();
    });
  });

  describe('validateSessionDataIntegrity', () => {
    let sessionHistory: Map<string, ISessionData>;

    beforeEach(() => {
      sessionHistory = new Map();
    });

    it('should validate correct session data', async () => {
      sessionHistory.set('session1', mockSessionData);
      
      await sessionTrackingUtils.validateSessionDataIntegrity(sessionHistory, mockValidationResult);
      
      const integrityCheck = mockValidationResult.checks.find(c => c.checkId === 'session-data-integrity');
      expect(integrityCheck).toBeDefined();
      expect(integrityCheck!.status).toBe('passed');
    });

    it('should detect structure integrity issues', async () => {
      const invalidSession: ISessionData = {
        ...mockSessionData,
        sessionId: '', // Invalid empty session ID
      };
      
      sessionHistory.set('invalid', invalidSession);

      await sessionTrackingUtils.validateSessionDataIntegrity(sessionHistory, mockValidationResult);
      
      const integrityCheck = mockValidationResult.checks.find(c => c.checkId === 'session-data-integrity');
      expect(integrityCheck!.status).not.toBe('passed');
    });

    it('should detect timeline integrity issues', async () => {
      const invalidTimelineSession: ISessionData = {
        ...mockSessionData,
        startTime: new Date(),
        endTime: new Date(Date.now() - 60000) // End time before start time
      };
      
      sessionHistory.set('timeline-invalid', invalidTimelineSession);

      await sessionTrackingUtils.validateSessionDataIntegrity(sessionHistory, mockValidationResult);
      
      expect(mockValidationResult.warnings.some(w => w.includes('integrity issues detected'))).toBe(true);
    });

    it('should handle empty session history', async () => {
      await sessionTrackingUtils.validateSessionDataIntegrity(sessionHistory, mockValidationResult);
      
      const integrityCheck = mockValidationResult.checks.find(c => c.checkId === 'session-data-integrity');
      expect(integrityCheck!.score).toBe(100);
    });
  });

  // ============================================================================
  // ID GENERATION TESTS
  // ============================================================================

  describe('ID Generation', () => {
    describe('generateSessionId', () => {
      it('should generate unique session IDs', () => {
        const id1 = sessionTrackingUtils.generateSessionId();
        const id2 = sessionTrackingUtils.generateSessionId();
        
        expect(id1).not.toBe(id2);
        expect(id1).toMatch(/^session_[a-z0-9]+_[a-z0-9]+$/);
        expect(id2).toMatch(/^session_[a-z0-9]+_[a-z0-9]+$/);
      });

      it('should generate IDs with correct format', () => {
        const id = sessionTrackingUtils.generateSessionId();
        const parts = id.split('_');
        
        expect(parts).toHaveLength(3);
        expect(parts[0]).toBe('session');
        expect(parts[1]).toMatch(/^[a-z0-9]+$/);
        expect(parts[2]).toMatch(/^[a-z0-9]+$/);
      });

      it('should generate multiple unique IDs', () => {
        const ids = new Set();
        for (let i = 0; i < 100; i++) {
          ids.add(sessionTrackingUtils.generateSessionId());
        }
        expect(ids.size).toBe(100);
      });
    });

    describe('generateEventId', () => {
      it('should generate unique event IDs', () => {
        const id1 = sessionTrackingUtils.generateEventId();
        const id2 = sessionTrackingUtils.generateEventId();
        
        expect(id1).not.toBe(id2);
        expect(id1).toMatch(/^event_[a-z0-9]+_[a-z0-9]+$/);
        expect(id2).toMatch(/^event_[a-z0-9]+_[a-z0-9]+$/);
      });

      it('should generate IDs with correct format', () => {
        const id = sessionTrackingUtils.generateEventId();
        const parts = id.split('_');
        
        expect(parts).toHaveLength(3);
        expect(parts[0]).toBe('event');
        expect(parts[1]).toMatch(/^[a-z0-9]+$/);
        expect(parts[2]).toMatch(/^[a-z0-9]+$/);
      });
    });
  });

  // ============================================================================
  // METADATA SANITIZATION TESTS
  // ============================================================================

  describe('sanitizeMetadata', () => {
    it('should redact sensitive information', () => {
      const metadata = {
        username: 'john_doe',
        password: 'secret123',
        apiKey: 'abc123',
        token: 'bearer_token',
        normalField: 'safe_value'
      };

      const sanitized = sessionTrackingUtils.sanitizeMetadata(metadata);

      expect(sanitized.username).toBe('john_doe');
      expect(sanitized.password).toBe('[REDACTED]');
      expect(sanitized.apiKey).toBe('[REDACTED]');
      expect(sanitized.token).toBe('[REDACTED]');
      expect(sanitized.normalField).toBe('safe_value');
    });

    it('should truncate long strings', () => {
      const longString = 'a'.repeat(1500);
      const metadata = {
        longField: longString,
        shortField: 'short'
      };

      const sanitized = sessionTrackingUtils.sanitizeMetadata(metadata);

      expect(sanitized.longField).toBe('a'.repeat(1000) + '...');
      expect(sanitized.shortField).toBe('short');
    });

    it('should handle nested objects safely', () => {
      const metadata = {
        user: {
          name: 'John',
          password: 'secret'
        },
        config: {
          timeout: 5000
        }
      };

      const sanitized = sessionTrackingUtils.sanitizeMetadata(metadata);

      expect(sanitized.user).toEqual({ name: 'John', password: 'secret' });
      expect(sanitized.config).toEqual({ timeout: 5000 });
    });

    it('should handle empty metadata', () => {
      const result = sessionTrackingUtils.sanitizeMetadata({});
      expect(result).toEqual({});
    });
  });

  // ============================================================================
  // DURATION FORMATTING TESTS
  // ============================================================================

  describe('formatDuration', () => {
    it('should format seconds correctly', () => {
      const start = new Date();
      const end = new Date(start.getTime() + 30000); // 30 seconds
      
      const formatted = sessionTrackingUtils.formatDuration(start, end);
      expect(formatted).toBe('30s');
    });

    it('should format minutes and seconds correctly', () => {
      const start = new Date();
      const end = new Date(start.getTime() + 90000); // 1 minute 30 seconds
      
      const formatted = sessionTrackingUtils.formatDuration(start, end);
      expect(formatted).toBe('1m 30s');
    });

    it('should format hours, minutes, and seconds correctly', () => {
      const start = new Date();
      const end = new Date(start.getTime() + 3690000); // 1 hour 1 minute 30 seconds
      
      const formatted = sessionTrackingUtils.formatDuration(start, end);
      expect(formatted).toBe('1h 1m 30s');
    });

    it('should format days, hours, and minutes correctly', () => {
      const start = new Date();
      const end = new Date(start.getTime() + 90060000); // 1 day 1 hour 1 minute
      
      const formatted = sessionTrackingUtils.formatDuration(start, end);
      expect(formatted).toBe('1d 1h 1m');
    });

    it('should use current time when end time not provided', () => {
      const start = new Date(Date.now() - 5000); // 5 seconds ago
      
      const formatted = sessionTrackingUtils.formatDuration(start);
      expect(formatted).toMatch(/^[0-9]+s$/);
    });
  });

  // ============================================================================
  // SESSION STATUS CHECKS TESTS
  // ============================================================================

  describe('Session Status Checks', () => {
    describe('isSessionExpired', () => {
      it('should detect expired session', () => {
        const expiredSession: ISessionData = {
          ...mockSessionData,
          lastActivity: new Date(Date.now() - 48 * 60 * 60 * 1000) // 48 hours ago
        };

        const isExpired = sessionTrackingUtils.isSessionExpired(expiredSession);
        expect(isExpired).toBe(true);
      });

      it('should detect non-expired session', () => {
        const activeSession: ISessionData = {
          ...mockSessionData,
          lastActivity: new Date(Date.now() - 60 * 60 * 1000) // 1 hour ago
        };

        const isExpired = sessionTrackingUtils.isSessionExpired(activeSession);
        expect(isExpired).toBe(false);
      });

      it('should respect custom timeout', () => {
        const session: ISessionData = {
          ...mockSessionData,
          lastActivity: new Date(Date.now() - 30 * 60 * 1000) // 30 minutes ago
        };

        const isExpired1 = sessionTrackingUtils.isSessionExpired(session, 60 * 60 * 1000); // 1 hour timeout
        const isExpired2 = sessionTrackingUtils.isSessionExpired(session, 15 * 60 * 1000); // 15 minute timeout

        expect(isExpired1).toBe(false);
        expect(isExpired2).toBe(true);
      });
    });

    describe('isSessionStale', () => {
      it('should detect stale active session', () => {
        const staleSession: ISessionData = {
          ...mockSessionData,
          lastActivity: new Date(Date.now() - 5 * 60 * 60 * 1000), // 5 hours ago
          status: 'active'
        };

        const isStale = sessionTrackingUtils.isSessionStale(staleSession);
        expect(isStale).toBe(true);
      });

      it('should not mark inactive session as stale', () => {
        const inactiveSession: ISessionData = {
          ...mockSessionData,
          lastActivity: new Date(Date.now() - 5 * 60 * 60 * 1000), // 5 hours ago
          status: 'inactive'
        };

        const isStale = sessionTrackingUtils.isSessionStale(inactiveSession);
        expect(isStale).toBe(false);
      });

      it('should respect custom stale threshold', () => {
        const session: ISessionData = {
          ...mockSessionData,
          lastActivity: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
          status: 'active'
        };

        const isStale1 = sessionTrackingUtils.isSessionStale(session, 3 * 60 * 60 * 1000); // 3 hour threshold
        const isStale2 = sessionTrackingUtils.isSessionStale(session, 1 * 60 * 60 * 1000); // 1 hour threshold

        expect(isStale1).toBe(false);
        expect(isStale2).toBe(true);
      });
    });
  });

  // ============================================================================
  // PERFORMANCE AND STRESS TESTS
  // ============================================================================

  describe('Performance Tests', () => {
    it('should handle large number of sessions efficiently', () => {
      const sessions = new Map<string, ISessionData>();
      
      // Create 1000 sessions
      for (let i = 0; i < 1000; i++) {
        sessions.set(`session${i}`, {
          ...mockSessionData,
          sessionId: `session${i}`
        });
      }

      const startTime = Date.now();
      sessionTrackingUtils.updateSessionAnalytics(mockSessionAnalytics, sessions);
      const endTime = Date.now();

      // Should complete within reasonable time (< 100ms)
      expect(endTime - startTime).toBeLessThan(100);
    });

    it('should handle large metadata objects efficiently', () => {
      const largeMetadata: Record<string, unknown> = {};
      
      for (let i = 0; i < 100; i++) {
        largeMetadata[`field${i}`] = `value${'x'.repeat(100)}`;
      }

      const startTime = Date.now();
      const sanitized = sessionTrackingUtils.sanitizeMetadata(largeMetadata);
      const endTime = Date.now();

      expect(Object.keys(sanitized)).toHaveLength(100);
      expect(endTime - startTime).toBeLessThan(50);
    });
  });

  // ============================================================================
  // EDGE CASES AND ERROR HANDLING TESTS
  // ============================================================================

  describe('Edge Cases and Error Handling', () => {
    it('should handle null/undefined session data gracefully', () => {
      expect(() => sessionTrackingUtils.calculateActivityScore(null as any)).not.toThrow();
      expect(() => sessionTrackingUtils.formatDuration(null as any)).not.toThrow();
      expect(() => sessionTrackingUtils.isSessionExpired(null as any)).not.toThrow();
    });

    it('should handle sessions with future timestamps', () => {
      const futureSession: ISessionData = {
        ...mockSessionData,
        startTime: new Date(Date.now() + 60000), // 1 minute in future
        lastActivity: new Date(Date.now() + 120000) // 2 minutes in future
      };

      expect(() => sessionTrackingUtils.calculateActivityScore(futureSession)).not.toThrow();
      expect(() => sessionTrackingUtils.isSessionExpired(futureSession)).not.toThrow();
    });

    it('should handle extremely old sessions', () => {
      const ancientSession: ISessionData = {
        ...mockSessionData,
        startTime: new Date('1970-01-01'),
        lastActivity: new Date('1970-01-02')
      };

      const score = sessionTrackingUtils.calculateActivityScore(ancientSession);
      expect(score).toBeGreaterThanOrEqual(0);
      expect(score).toBeLessThanOrEqual(100);
    });

    it('should handle sessions with invalid event timestamps', () => {
      const invalidEventSession: ISessionData = {
        ...mockSessionData,
        events: [
          {
            timestamp: new Date('invalid'),
            level: 'info',
            eventType: 'test',
            message: 'test'
          }
        ]
      };

      expect(() => sessionTrackingUtils.calculateActivityScore(invalidEventSession)).not.toThrow();
    });
  });

  // ============================================================================
  // INTEGRATION TESTS
  // ============================================================================

  describe('Integration Tests', () => {
    it('should work with complete session lifecycle', async () => {
      // Generate new session
      const sessionId = sessionTrackingUtils.generateSessionId();
      expect(sessionId).toMatch(/^session_/);

      // Create session data
      const sessionData: ISessionData = {
        sessionId,
        actor: 'test-user',
        sessionType: 'user',
        startTime: new Date(),
        lastActivity: new Date(),
        status: 'active',
        metadata: { source: 'test' },
        events: [],
        analytics: {
          totalEvents: 0,
          errorCount: 0,
          warningCount: 0,
          duration: 0,
          activityScore: 0
        }
      };

      // Calculate initial score
      const initialScore = sessionTrackingUtils.calculateActivityScore(sessionData);
      expect(initialScore).toBeGreaterThanOrEqual(0);

      // Update analytics
      const activeSessions = new Map([[sessionId, sessionData]]);
      const updatedAnalytics = sessionTrackingUtils.updateSessionAnalytics(
        mockSessionAnalytics,
        activeSessions
      );
      expect(updatedAnalytics.activeSessions).toBe(1);

      // Validate session
      const validationResult = createMockValidationResult();
      await sessionTrackingUtils.validateActiveSessions(activeSessions, validationResult);
      expect(validationResult.checks).toHaveLength(1);
    });

    it('should maintain data consistency across operations', () => {
      const sessions = new Map<string, ISessionData>();
      
      // Add multiple sessions
      for (let i = 0; i < 10; i++) {
        const sessionId = sessionTrackingUtils.generateSessionId();
        sessions.set(sessionId, {
          ...mockSessionData,
          sessionId,
          analytics: {
            ...mockSessionData.analytics,
            totalEvents: i * 10
          }
        });
      }

      // Update analytics
      const analytics = sessionTrackingUtils.updateSessionAnalytics(
        mockSessionAnalytics,
        sessions
      );

      // Verify consistency
      expect(analytics.activeSessions).toBe(10);
      expect(analytics.totalEvents).toBe(450); // Sum of 0+10+20+...+90
    });
  });

  // ============================================================================
  // HELPER FUNCTIONS FOR TESTS
  // ============================================================================

  /**
   * Create mock session data for testing
   */
  function createMockSessionData(): ISessionData {
    return {
      sessionId: 'test-session-123',
      actor: 'test-user',
      sessionType: 'user',
      startTime: new Date(Date.now() - 60 * 60 * 1000), // 1 hour ago
      lastActivity: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
      status: 'active',
      metadata: {
        source: 'test',
        version: '1.0.0'
      },
      events: [
        {
          timestamp: new Date(Date.now() - 30 * 60 * 1000),
          level: 'info',
          eventType: 'session.start',
          message: 'Session started'
        },
        {
          timestamp: new Date(Date.now() - 15 * 60 * 1000),
          level: 'info',
          eventType: 'user.action',
          message: 'User performed action'
        }
      ],
      analytics: {
        totalEvents: 2,
        errorCount: 0,
        warningCount: 0,
        duration: 55 * 60 * 1000, // 55 minutes
        activityScore: 85
      }
    };
  }

  /**
   * Create mock session analytics for testing
   */
  function createMockSessionAnalytics(): ISessionAnalytics {
    return {
      totalSessions: 100,
      activeSessions: 15,
      averageSessionDuration: 45 * 60 * 1000, // 45 minutes
      totalEvents: 1500,
      errorRate: 2.5,
      lastUpdated: new Date(Date.now() - 10 * 60 * 1000) // 10 minutes ago
    };
  }

  /**
   * Create mock validation result for testing
   */
  function createMockValidationResult(): TValidationResult {
    return {
      validationId: 'test-validation-123',
      componentId: 'SessionTrackingUtils.test',
      timestamp: new Date(),
      executionTime: 0,
      status: 'valid',
      overallScore: 100,
      checks: [],
      references: {
        componentId: 'SessionTrackingUtils.test',
        internalReferences: [],
        externalReferences: [],
        circularReferences: [],
        missingReferences: [],
        redundantReferences: [],
        metadata: {
          totalReferences: 0,
          buildTimestamp: new Date(),
          analysisDepth: 1
        }
      },
      recommendations: [],
      warnings: [],
      errors: [],
      metadata: {
        validationMethod: 'SessionTrackingUtils.test',
        rulesApplied: 1,
        dependencyDepth: 0,
        cyclicDependencies: [],
        orphanReferences: []
      }
    };
  }
});

/**
 * Additional Test Utilities for Integration Testing
 */
export class SessionTrackingTestUtils {
  /**
   * Create a batch of test sessions for performance testing
   */
  static createTestSessionBatch(count: number): Map<string, ISessionData> {
    const sessions = new Map<string, ISessionData>();
    const utils = new SessionTrackingUtils();

    for (let i = 0; i < count; i++) {
      const sessionId = utils.generateSessionId();
      const startTime = new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000);
      
      sessions.set(sessionId, {
        sessionId,
        actor: `test-user-${i}`,
        sessionType: i % 4 === 0 ? 'api' : 'user',
        startTime,
        lastActivity: new Date(startTime.getTime() + Math.random() * 60 * 60 * 1000),
        status: Math.random() > 0.1 ? 'active' : 'inactive',
        metadata: {
          testBatch: true,
          index: i
        },
        events: this.createRandomEvents(Math.floor(Math.random() * 20)),
        analytics: {
          totalEvents: Math.floor(Math.random() * 100),
          errorCount: Math.floor(Math.random() * 5),
          warningCount: Math.floor(Math.random() * 10),
          duration: Math.random() * 60 * 60 * 1000,
          activityScore: Math.floor(Math.random() * 100)
        }
      });
    }

    return sessions;
  }

  /**
   * Create random events for testing
   */
  private static createRandomEvents(count: number): ISessionData['events'] {
    const events: ISessionData['events'] = [];
    const eventTypes = ['user.action', 'system.event', 'error.occurred', 'warning.issued'];
    const levels: Array<'info' | 'warn' | 'error' | 'debug'> = ['info', 'warn', 'error', 'debug'];

    for (let i = 0; i < count; i++) {
      events.push({
        timestamp: new Date(Date.now() - Math.random() * 60 * 60 * 1000),
        level: levels[Math.floor(Math.random() * levels.length)],
        eventType: eventTypes[Math.floor(Math.random() * eventTypes.length)],
        message: `Test event ${i}`,
        context: {
          testEvent: true,
          index: i
        }
      });
    }

    return events;
  }
}