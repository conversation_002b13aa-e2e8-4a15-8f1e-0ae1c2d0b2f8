/**
 * @file Governance Tracking System Test Setup and Configuration
 * @filepath server/src/platform/tracking/core-trackers/__tests__/GovernanceTrackingSystem.test.setup.ts
 * @task-id T-TSK-02.SUB-02.1.IMP-03.TEST-SETUP
 * @component tracking-governance-tracker-test-setup
 * @reference foundation-context.SERVICE.005.TESTS.SETUP
 * @template enterprise-test-setup-standards
 * @tier T2
 * @context foundation-context
 * @category Foundation-Testing-Setup
 * @created 2025-07-10
 * @modified 2025-07-10
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE TEST SETUP (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-tracking-architecture-test-setup
 * @governance-dcr DCR-foundation-001-tracking-development-test-setup
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on server/src/platform/tracking/core-trackers/GovernanceTrackingSystem
 * @depends-on jest configuration
 * @enables enterprise-test-execution-framework
 * @related-contexts foundation-context-test-setup
 * @governance-impact test-reliability, test-environment-setup
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type governance-tracking-system-test-setup
 * @lifecycle-stage test-configuration
 * @testing-framework jest
 * @setup-categories environment, mocks, utilities, configuration
 * @documentation docs/contexts/foundation-context/testing/governance-tracking-system-test-setup.md
 */

import { jest } from '@jest/globals';

/**
 * Enterprise Test Environment Configuration
 * 
 * Comprehensive test setup for GovernanceTrackingSystem including:
 * - Global test configuration
 * - Mock implementations and factories
 * - Test utilities and helpers
 * - Performance monitoring setup
 * - Security testing configuration
 * - Memory management for tests
 * - Cross-browser compatibility setup
 * - CI/CD integration helpers
 */

/**
 * Global Test Configuration
 */
export const TEST_CONFIG = {
  // Test timeouts
  DEFAULT_TIMEOUT: 5000,
  INTEGRATION_TIMEOUT: 30000,
  PERFORMANCE_TIMEOUT: 60000,
  SECURITY_TIMEOUT: 45000,
  
  // Test environment
  NODE_ENV: 'test',
  LOG_LEVEL: 'silent',
  
  // Performance thresholds
  PERFORMANCE_THRESHOLDS: {
    MAX_LATENCY_MS: 100,
    MIN_THROUGHPUT_OPS_SEC: 50,
    MAX_MEMORY_MB: 100,
    MAX_CPU_PERCENT: 80
  },
  
  // Security testing
  SECURITY_CONFIG: {
    ENABLE_PENETRATION_TESTS: true,
    ENABLE_VULNERABILITY_SCANS: true,
    ENABLE_COMPLIANCE_CHECKS: true,
    MAX_INJECTION_ATTEMPTS: 50
  },
  
  // Test data configuration - environment aware
  TEST_DATA: {
    get MAX_EVENTS_PER_TEST() {
      // Drastically reduce for performance tests
      if (process.env.TEST_TYPE === 'performance') {
        return 50; // Maximum 50 events for performance tests
      }
      return 1000; // Reduced from 10000 for other tests
    },
    get MAX_METADATA_SIZE_KB() {
      // Very small metadata for performance tests
      if (process.env.TEST_TYPE === 'performance') {
        return 1; // 1KB max for performance tests
      }
      return 10; // Reduced from 100KB for other tests
    },
    get DEFAULT_USER_COUNT() {
      if (process.env.TEST_TYPE === 'performance') {
        return 5; // Minimal users for performance tests
      }
      return 25; // Reduced from 100 for other tests
    },
    get DEFAULT_COMPONENT_COUNT() {
      if (process.env.TEST_TYPE === 'performance') {
        return 3; // Minimal components for performance tests
      }
      return 10; // Reduced from 50 for other tests
    }
  }
};

/**
 * Jest Global Setup
 */
beforeAll(async () => {
  console.log('🚀 Starting GovernanceTrackingSystem Test Suite');
  
  // Setup global test environment
  setupGlobalTestEnvironment();
  
  // Initialize test monitoring
  initializeTestMonitoring();
  
  // Setup memory monitoring
  setupMemoryMonitoring();
  
  // Configure error handling
  setupGlobalErrorHandling();
});

afterAll(async () => {
  console.log('🏁 GovernanceTrackingSystem Test Suite Completed');
  
  // Cleanup global resources
  await cleanupGlobalResources();
  
  // Generate test reports
  await generateTestReports();
  
  // Cleanup memory
  if (global.gc) {
    global.gc();
  }
});

/**
 * Global Test Environment Setup
 */
function setupGlobalTestEnvironment(): void {
  // Set environment variables
  process.env.NODE_ENV = TEST_CONFIG.NODE_ENV;
  process.env.LOG_LEVEL = TEST_CONFIG.LOG_LEVEL;
  
  // Configure Jest
  jest.setTimeout(TEST_CONFIG.DEFAULT_TIMEOUT);
  
  // Setup console mocking for cleaner test output
  const originalConsole = global.console;
  global.console = {
    ...originalConsole,
    log: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    debug: jest.fn()
  };
  
  // Setup global test utilities
  setupGlobalTestUtilities();
}

/**
 * Global Test Utilities
 */
function setupGlobalTestUtilities(): void {
  // Add global test helpers
  (global as any).testUtils = {
    // Time manipulation utilities
    freezeTime: (date?: Date) => {
      const frozenDate = date || new Date('2025-07-10T12:00:00Z');
      jest.useFakeTimers();
      jest.setSystemTime(frozenDate);
      return frozenDate;
    },
    
    unfreezeTime: () => {
      jest.useRealTimers();
    },
    
    // Async utilities
    sleep: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),
    
    waitFor: async (condition: () => boolean, timeout: number = 5000) => {
      const startTime = Date.now();
      while (!condition() && Date.now() - startTime < timeout) {
        await (global as any).testUtils.sleep(100);
      }
      if (!condition()) {
        throw new Error(`Condition not met within ${timeout}ms`);
      }
    },
    
    // Performance utilities
    measurePerformance: async <T>(operation: () => Promise<T>): Promise<{ result: T; duration: number; memory: number }> => {
      const startTime = performance.now();
      const startMemory = process.memoryUsage().heapUsed;
      
      const result = await operation();
      
      const endTime = performance.now();
      const endMemory = process.memoryUsage().heapUsed;
      
      return {
        result,
        duration: endTime - startTime,
        memory: endMemory - startMemory
      };
    }
  };
}

/**
 * Test Monitoring Setup
 */
function initializeTestMonitoring(): void {
  // Determine limits based on test type
  const maxEntries = process.env.TEST_TYPE === 'performance' ? 10 : 100;

  (global as any).testMonitor = {
    testResults: [],
    performanceMetrics: [],
    memorySnapshots: [],

    recordTestResult: (testName: string, result: 'pass' | 'fail', duration: number) => {
      const monitor = (global as any).testMonitor;
      monitor.testResults.push({
        testName,
        result,
        duration,
        timestamp: Date.now()
      });

      // Prevent memory accumulation - keep only recent entries
      if (monitor.testResults.length > maxEntries) {
        monitor.testResults.shift();
      }
    },

    recordPerformanceMetric: (metricName: string, value: number, unit: string) => {
      const monitor = (global as any).testMonitor;
      monitor.performanceMetrics.push({
        metricName,
        value,
        unit,
        timestamp: Date.now()
      });

      // Prevent memory accumulation - keep only recent entries
      if (monitor.performanceMetrics.length > maxEntries) {
        monitor.performanceMetrics.shift();
      }
    },

    takeMemorySnapshot: (label: string) => {
      const memoryUsage = process.memoryUsage();
      const monitor = (global as any).testMonitor;
      monitor.memorySnapshots.push({
        label,
        ...memoryUsage,
        timestamp: Date.now()
      });

      // Prevent memory accumulation - keep only recent entries
      if (monitor.memorySnapshots.length > maxEntries) {
        monitor.memorySnapshots.shift();
      }
    },

    // Add cleanup method
    cleanup: () => {
      const monitor = (global as any).testMonitor;
      monitor.testResults.length = 0;
      monitor.performanceMetrics.length = 0;
      monitor.memorySnapshots.length = 0;
    }
  };
}

/**
 * Memory Monitoring Setup
 */
function setupMemoryMonitoring(): void {
  let memoryCheckInterval: NodeJS.Timeout;
  
  (global as any).memoryMonitor = {
    start: () => {
      memoryCheckInterval = setInterval(() => {
        const memoryUsage = process.memoryUsage();
        const memoryMB = memoryUsage.heapUsed / 1024 / 1024;
        
        if (memoryMB > TEST_CONFIG.PERFORMANCE_THRESHOLDS.MAX_MEMORY_MB) {
          console.warn(`⚠️ Memory usage high: ${memoryMB.toFixed(2)}MB`);
          
          // Force garbage collection if available
          if (global.gc) {
            global.gc();
          }
        }
        
        (global as any).testMonitor.recordPerformanceMetric('memory-usage', memoryMB, 'MB');
      }, 5000);
    },
    
    stop: () => {
      if (memoryCheckInterval) {
        clearInterval(memoryCheckInterval);
      }
    }
  };
  
  (global as any).memoryMonitor.start();
}

/**
 * Global Error Handling
 */
function setupGlobalErrorHandling(): void {
  const originalOnUnhandledRejection = process.listeners('unhandledRejection');
  const originalOnUncaughtException = process.listeners('uncaughtException');
  
  process.removeAllListeners('unhandledRejection');
  process.removeAllListeners('uncaughtException');
  
  process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
    
    // Record the error for test reporting
    (global as any).testMonitor?.recordTestResult('unhandled-rejection', 'fail', 0);
    
    // Call original handlers
    originalOnUnhandledRejection.forEach(handler => {
      if (typeof handler === 'function') {
        handler(reason, promise);
      }
    });
  });
  
  process.on('uncaughtException', (error) => {
    console.error('❌ Uncaught Exception:', error);
    
    // Record the error for test reporting
    (global as any).testMonitor?.recordTestResult('uncaught-exception', 'fail', 0);
    
    // Call original handlers
    originalOnUncaughtException.forEach(handler => {
      if (typeof handler === 'function') {
        // UncaughtException handlers expect (error, origin) parameters
        handler(error, 'uncaughtException');
      }
    });
  });
}

/**
 * Global Resource Cleanup
 */
async function cleanupGlobalResources(): Promise<void> {
  // Stop memory monitoring
  (global as any).memoryMonitor?.stop();

  // Cleanup test monitor data
  (global as any).testMonitor?.cleanup();

  // Clear all timers
  jest.clearAllTimers();
  jest.useRealTimers();

  // Clear all mocks
  jest.clearAllMocks();
  jest.restoreAllMocks();

  // Force garbage collection
  if (global.gc) {
    global.gc();
  }

  console.log('✅ Global resources cleaned up');
}

/**
 * Test Report Generation
 */
async function generateTestReports(): Promise<void> {
  const testMonitor = (global as any).testMonitor;
  
  if (!testMonitor) {
    return;
  }
  
  // Generate performance summary
  const performanceSummary = {
    totalTests: testMonitor.testResults.length,
    passedTests: testMonitor.testResults.filter((r: any) => r.result === 'pass').length,
    failedTests: testMonitor.testResults.filter((r: any) => r.result === 'fail').length,
    totalDuration: testMonitor.testResults.reduce((sum: number, r: any) => sum + r.duration, 0),
    averageDuration: testMonitor.testResults.length > 0 
      ? testMonitor.testResults.reduce((sum: number, r: any) => sum + r.duration, 0) / testMonitor.testResults.length 
      : 0,
    memorySnapshots: testMonitor.memorySnapshots.length,
    performanceMetrics: testMonitor.performanceMetrics.length
  };
  
  console.log('📊 Test Summary:', performanceSummary);
  
  // Check performance thresholds
  const memoryMetrics = testMonitor.performanceMetrics.filter((m: any) => m.metricName === 'memory-usage');
  const maxMemory = Math.max(...memoryMetrics.map((m: any) => m.value));
  
  if (maxMemory > TEST_CONFIG.PERFORMANCE_THRESHOLDS.MAX_MEMORY_MB) {
    console.warn(`⚠️ Memory threshold exceeded: ${maxMemory.toFixed(2)}MB (limit: ${TEST_CONFIG.PERFORMANCE_THRESHOLDS.MAX_MEMORY_MB}MB)`);
  }
  
  if (performanceSummary.averageDuration > TEST_CONFIG.PERFORMANCE_THRESHOLDS.MAX_LATENCY_MS) {
    console.warn(`⚠️ Latency threshold exceeded: ${performanceSummary.averageDuration.toFixed(2)}ms (limit: ${TEST_CONFIG.PERFORMANCE_THRESHOLDS.MAX_LATENCY_MS}ms)`);
  }
}

/**
 * Test Suite Specific Configurations
 */

/**
 * Unit Test Configuration
 */
export const UNIT_TEST_CONFIG = {
  timeout: TEST_CONFIG.DEFAULT_TIMEOUT,
  maxConcurrency: 10,
  enableCoverage: true,
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  }
};

/**
 * Integration Test Configuration
 */
export const INTEGRATION_TEST_CONFIG = {
  timeout: TEST_CONFIG.INTEGRATION_TIMEOUT,
  maxConcurrency: 5,
  enableCrossSystemTesting: true,
  enableWorkflowTesting: true,
  enableEndToEndValidation: true
};

/**
 * Performance Test Configuration
 */
export const PERFORMANCE_TEST_CONFIG = {
  timeout: TEST_CONFIG.PERFORMANCE_TIMEOUT,
  maxConcurrency: 1, // Sequential for accurate measurements
  enableBenchmarking: true,
  enableStressTesting: true,
  enableScalabilityTesting: true,
  thresholds: TEST_CONFIG.PERFORMANCE_THRESHOLDS
};

/**
 * Security Test Configuration
 */
export const SECURITY_TEST_CONFIG = {
  timeout: TEST_CONFIG.SECURITY_TIMEOUT,
  maxConcurrency: 3,
  enablePenetrationTesting: TEST_CONFIG.SECURITY_CONFIG.ENABLE_PENETRATION_TESTS,
  enableVulnerabilityScanning: TEST_CONFIG.SECURITY_CONFIG.ENABLE_VULNERABILITY_SCANS,
  enableComplianceTesting: TEST_CONFIG.SECURITY_CONFIG.ENABLE_COMPLIANCE_CHECKS,
  maxInjectionAttempts: TEST_CONFIG.SECURITY_CONFIG.MAX_INJECTION_ATTEMPTS
};

/**
 * Test Data Factories for Different Test Types
 */
export const TEST_DATA_FACTORIES = {
  // Unit test data
  unit: {
    createMinimalEvent: () => ({
      eventType: 'authority_validation' as const,
      severity: 'info' as const,
      source: 'UnitTest',
      description: 'Minimal unit test event',
      context: {
        milestone: 'M0-UNIT',
        category: 'unit-test',
        documents: [],
        affectedComponents: ['UnitTestComponent'],
        metadata: { unitTest: true }
      }
    }),
    
    createComplexEvent: () => ({
      eventType: 'compliance_check' as const,
      severity: 'warning' as const,
      source: 'UnitTestComplex',
      description: 'Complex unit test event with extensive metadata',
      context: {
        milestone: 'M0-UNIT-COMPLEX',
        category: 'unit-test-complex',
        documents: ['unit-test-doc.md', 'complex-test-doc.md'],
        affectedComponents: ['ComplexComponent1', 'ComplexComponent2'],
        metadata: {
          unitTest: true,
          complexity: 'high',
          nestedData: {
            level1: {
              level2: {
                level3: 'deep-nested-value'
              }
            }
          },
          arrayData: [1, 2, 3, 4, 5],
          booleanData: true,
          dateData: new Date().toISOString()
        }
      }
    })
  },
  
  // Integration test data
  integration: {
    createCrossSystemEvent: (systemId: string) => ({
      eventType: 'governance_update' as const,
      severity: 'info' as const,
      source: `IntegrationSystem${systemId}`,
      description: `Cross-system integration event from ${systemId}`,
      context: {
        milestone: 'M0-INTEGRATION',
        category: 'integration-test',
        documents: [`integration-${systemId}.md`],
        affectedComponents: [`IntegrationComponent${systemId}`],
        metadata: {
          integrationTest: true,
          systemId,
          crossSystemEvent: true,
          timestamp: Date.now()
        }
      }
    }),
    
    createWorkflowEvent: (workflowId: string, step: number) => ({
      eventType: 'audit_trail' as const,
      severity: 'info' as const,
      source: 'WorkflowEngine',
      description: `Workflow ${workflowId} step ${step}`,
      context: {
        milestone: 'M0-WORKFLOW',
        category: 'workflow-test',
        documents: [`workflow-${workflowId}.md`],
        affectedComponents: [`WorkflowComponent${workflowId}`],
        metadata: {
          integrationTest: true,
          workflowId,
          workflowStep: step,
          workflowEvent: true
        }
      }
    })
  },
  
  // Performance test data
  performance: {
    createLoadTestEvent: (index: number) => ({
      eventType: 'authority_validation' as const,
      severity: 'info' as const,
      source: 'LoadTestGenerator',
      description: `Load test event ${index}`,
      context: {
        milestone: 'M0-PERFORMANCE',
        category: 'load-test',
        documents: [],
        affectedComponents: ['LoadTestComponent'],
        metadata: {
          performanceTest: true,
          loadTestIndex: index,
          timestamp: Date.now()
        }
      }
    }),
    
    createStressTestEvent: (stressLevel: number) => ({
      eventType: 'violation_report' as const,
      severity: 'critical' as const,
      source: 'StressTestGenerator',
      description: `Stress test event at level ${stressLevel}`,
      context: {
        milestone: 'M0-STRESS',
        category: 'stress-test',
        documents: [`stress-test-${stressLevel}.md`],
        affectedComponents: ['StressTestComponent'],
        metadata: {
          performanceTest: true,
          stressTest: true,
          stressLevel,
          heavyData: 'x'.repeat(1000 * stressLevel) // Increasing data size
        }
      }
    })
  },
  
  // Security test data
  security: {
    createSecurityEvent: (threatType: string) => ({
      eventType: 'violation_report' as const,
      severity: 'critical' as const,
      source: 'SecurityTestSystem',
      description: `Security test: ${threatType}`,
      context: {
        milestone: 'M0-SECURITY',
        category: 'security-test',
        documents: ['security-test.md'],
        affectedComponents: ['SecurityTestComponent'],
        metadata: {
          securityTest: true,
          threatType,
          securityValidation: true,
          timestamp: Date.now()
        }
      }
    }),
    
    createPenetrationTestEvent: (attackVector: string) => ({
      eventType: 'violation_report' as const,
      severity: 'high' as const,
      source: 'PenetrationTestSystem',
      description: `Penetration test: ${attackVector}`,
      context: {
        milestone: 'M0-PENETRATION',
        category: 'penetration-test',
        documents: ['penetration-test.md'],
        affectedComponents: ['PenetrationTestComponent'],
        metadata: {
          securityTest: true,
          penetrationTest: true,
          attackVector,
          testingPhase: 'active'
        }
      }
    })
  }
};

/**
 * Custom Jest Matchers for GovernanceTrackingSystem
 */
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeValidGovernanceEvent(): R;
      toHaveValidAuthorityData(): R;
      toMeetPerformanceThreshold(threshold: number): R;
      toBeSecure(): R;
    }
  }
}

// Implement custom matchers
expect.extend({
  toBeValidGovernanceEvent(received: any) {
    const pass = received &&
      typeof received.eventId === 'string' &&
      received.timestamp instanceof Date &&
      ['authority_validation', 'compliance_check', 'audit_trail', 'violation_report', 'governance_update'].includes(received.eventType) &&
      ['info', 'warning', 'error', 'critical'].includes(received.severity) &&
      typeof received.source === 'string' &&
      typeof received.description === 'string' &&
      received.context &&
      typeof received.context.milestone === 'string' &&
      typeof received.context.category === 'string';

    return {
      message: () => pass 
        ? `expected ${JSON.stringify(received)} not to be a valid governance event`
        : `expected ${JSON.stringify(received)} to be a valid governance event`,
      pass
    };
  },

  toHaveValidAuthorityData(received: any) {
    const authority = received.authority;
    const pass = authority &&
      typeof authority.validatorId === 'string' &&
      typeof authority.validatorName === 'string' &&
      typeof authority.validationLevel === 'string' &&
      typeof authority.validationStatus === 'string' &&
      authority.validationTimestamp instanceof Date;

    return {
      message: () => pass
        ? `expected ${JSON.stringify(received)} not to have valid authority data`
        : `expected ${JSON.stringify(received)} to have valid authority data`,
      pass
    };
  },

  toMeetPerformanceThreshold(received: number, threshold: number) {
    const pass = received <= threshold;

    return {
      message: () => pass
        ? `expected ${received} not to meet performance threshold of ${threshold}`
        : `expected ${received} to meet performance threshold of ${threshold}`,
      pass
    };
  },

  toBeSecure(received: any) {
    // Basic security validation - check for common security issues
    const jsonString = JSON.stringify(received);
    const hasScriptTag = jsonString.includes('<script>');
    const hasSqlInjection = jsonString.includes('DROP TABLE') || jsonString.includes("'; --");
    const hasPathTraversal = jsonString.includes('../');
    
    const pass = !hasScriptTag && !hasSqlInjection && !hasPathTraversal;

    return {
      message: () => pass
        ? `expected ${JSON.stringify(received)} not to be secure`
        : `expected ${JSON.stringify(received)} to be secure (detected potential security issues)`,
      pass
    };
  }
});

/**
 * Export test configurations and utilities
 */
export default {
  setupFilesAfterEnv: [__filename],
  testEnvironment: 'node',
  collectCoverage: true,
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  testMatch: [
    '**/__tests__/**/*.test.ts',
    '**/*.test.ts'
  ],
  transform: {
    '^.+\\.ts$': 'ts-jest'
  },
  moduleFileExtensions: ['ts', 'js', 'json'],
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.test.ts',
    '!src/**/*.mock.ts',
    '!src/**/__tests__/**'
  ],
  coverageThreshold: UNIT_TEST_CONFIG.coverageThreshold
};