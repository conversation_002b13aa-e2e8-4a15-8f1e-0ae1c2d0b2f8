/**
 * Security Configuration System
 * 
 * Provides test-specific and environment-specific security configurations
 * to enable different security enforcement levels based on testing needs.
 */

export interface FloodProtectionConfig {
  enabled: boolean;
  threshold: number;
  window: number; // in milliseconds
}

export interface RateLimitingConfig {
  enabled: boolean;
  maxRequests: number;
  window: number; // in milliseconds
}

export interface InputSanitizationConfig {
  enabled: boolean;
  maxSize: number;
  truncateMarker: string;
}

export interface SecurityConfig {
  floodProtection: FloodProtectionConfig;
  rateLimiting: RateLimitingConfig;
  inputSanitization: InputSanitizationConfig;
  auditLogging: {
    enabled: boolean;
    logLevel: 'debug' | 'info' | 'warn' | 'error';
  };
  monitoring: {
    enabled: boolean;
    metricsCollection: boolean;
  };
}

/**
 * Predefined security profiles for different environments and test types
 */
export const SECURITY_PROFILES: Record<string, SecurityConfig> = {
  // Unit testing profile - minimal security for fast execution
  unit: {
    floodProtection: { 
      enabled: false, 
      threshold: 10000, 
      window: 60000 
    },
    rateLimiting: { 
      enabled: false, 
      maxRequests: 1000, 
      window: 60000 
    },
    inputSanitization: { 
      enabled: true, 
      maxSize: 50000, 
      truncateMarker: '[TRUNCATED]' 
    },
    auditLogging: { 
      enabled: false, 
      logLevel: 'error' 
    },
    monitoring: { 
      enabled: false, 
      metricsCollection: false 
    }
  },

  // Performance testing profile - security completely disabled for maximum throughput
  performance: {
    floodProtection: { 
      enabled: false, 
      threshold: Number.MAX_SAFE_INTEGER, 
      window: 1 
    },
    rateLimiting: { 
      enabled: false, 
      maxRequests: Number.MAX_SAFE_INTEGER, 
      window: 1 
    },
    inputSanitization: { 
      enabled: false, 
      maxSize: Number.MAX_SAFE_INTEGER, 
      truncateMarker: '' 
    },
    auditLogging: { 
      enabled: false, 
      logLevel: 'error' 
    },
    monitoring: { 
      enabled: false, 
      metricsCollection: false 
    }
  },

  // Integration testing profile - moderate security for realistic testing
  integration: {
    floodProtection: { 
      enabled: true, 
      threshold: 5000, 
      window: 60000 
    },
    rateLimiting: { 
      enabled: true, 
      maxRequests: 2000, 
      window: 60000 
    },
    inputSanitization: { 
      enabled: true, 
      maxSize: 100000, 
      truncateMarker: '[TRUNCATED]' 
    },
    auditLogging: { 
      enabled: true, 
      logLevel: 'warn' 
    },
    monitoring: { 
      enabled: true, 
      metricsCollection: true 
    }
  },

  // Security testing profile - aggressive security for vulnerability testing
  security: {
    floodProtection: { 
      enabled: true, 
      threshold: 50, 
      window: 30000 
    },
    rateLimiting: { 
      enabled: true, 
      maxRequests: 100, 
      window: 60000 
    },
    inputSanitization: { 
      enabled: true, 
      maxSize: 10000, 
      truncateMarker: '[TRUNCATED]' 
    },
    auditLogging: { 
      enabled: true, 
      logLevel: 'debug' 
    },
    monitoring: { 
      enabled: true, 
      metricsCollection: true 
    }
  },

  // Production profile - balanced security for real-world usage
  production: {
    floodProtection: { 
      enabled: true, 
      threshold: 1000, 
      window: 60000 
    },
    rateLimiting: { 
      enabled: true, 
      maxRequests: 500, 
      window: 60000 
    },
    inputSanitization: { 
      enabled: true, 
      maxSize: 50000, 
      truncateMarker: '[TRUNCATED]' 
    },
    auditLogging: { 
      enabled: true, 
      logLevel: 'info' 
    },
    monitoring: { 
      enabled: true, 
      metricsCollection: true 
    }
  }
};

/**
 * Get security configuration based on environment and test type
 */
export function getSecurityConfig(testType?: string): SecurityConfig {
  const isTestEnv = process.env.NODE_ENV === 'test';
  const envTestType = process.env.TEST_TYPE;
  
  // Determine the profile to use
  let profileName: string;
  
  if (isTestEnv) {
    profileName = testType || envTestType || 'unit';
  } else {
    profileName = process.env.SECURITY_PROFILE || 'production';
  }
  
  // Get the profile, defaulting to production for safety
  const profile = SECURITY_PROFILES[profileName] || SECURITY_PROFILES.production;
  
  return { ...profile }; // Return a copy to prevent mutations
}

/**
 * Validate security configuration
 */
export function validateSecurityConfig(config: SecurityConfig): boolean {
  try {
    // Basic validation
    if (!config.floodProtection || typeof config.floodProtection.enabled !== 'boolean') {
      return false;
    }
    
    if (!config.rateLimiting || typeof config.rateLimiting.enabled !== 'boolean') {
      return false;
    }
    
    if (!config.inputSanitization || typeof config.inputSanitization.enabled !== 'boolean') {
      return false;
    }
    
    // Validate numeric values
    if (config.floodProtection.threshold < 0 || config.floodProtection.window < 0) {
      return false;
    }
    
    if (config.rateLimiting.maxRequests < 0 || config.rateLimiting.window < 0) {
      return false;
    }
    
    if (config.inputSanitization.maxSize < 0) {
      return false;
    }
    
    return true;
  } catch (error) {
    return false;
  }
}
