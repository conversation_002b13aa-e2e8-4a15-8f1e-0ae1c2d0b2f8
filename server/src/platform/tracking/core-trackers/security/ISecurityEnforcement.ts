/**
 * Security Enforcement Interface
 * 
 * Defines the contract for security enforcement layers in the GovernanceTrackingSystem.
 * This abstraction allows for different security implementations based on environment
 * and testing requirements while maintaining consistent security functionality.
 */

export interface SecurityEvent {
  type: string;
  source: string;
  action: string;
  timestamp: Date;
  severity?: 'low' | 'medium' | 'high' | 'critical';
  description?: string;
  metadata?: Record<string, unknown>;
}

export interface SecurityTestMonitor {
  logSecurityEvent(event: SecurityEvent): void;
  getSecurityMetrics(): {
    totalEvents: number;
    blockedAttempts: number;
    eventsByType: Record<string, number>;
    eventsBySeverity: Record<string, number>;
  };
  resetSecurityCounters(): void;
}

/**
 * Core security enforcement interface
 */
export interface ISecurityEnforcement {
  /**
   * Enforce flood protection for a given source
   * @param source The source identifier to check
   * @returns Promise<boolean> true if allowed, throws SecurityViolationError if blocked
   */
  enforceFloodProtection(source: string): Promise<boolean>;

  /**
   * Enforce rate limiting for a given source
   * @param source The source identifier to check
   * @returns Promise<boolean> true if allowed, throws SecurityViolationError if blocked
   */
  enforceRateLimit(source: string): Promise<boolean>;

  /**
   * Sanitize input string to prevent XSS and other attacks
   * @param input The input string to sanitize
   * @returns Sanitized string
   */
  sanitizeInput(input: string): string;

  /**
   * Sanitize metadata object to prevent injection attacks
   * @param metadata The metadata object to sanitize
   * @returns Sanitized metadata object
   */
  sanitizeMetadata(metadata: Record<string, unknown>): Record<string, unknown>;

  /**
   * Log a security event
   * @param event The security event to log
   */
  logSecurityEvent(event: SecurityEvent): void;

  /**
   * Check if an error is a security-related error
   * @param error The error to check
   * @returns boolean true if it's a security error
   */
  isSecurityError(error: Error): boolean;

  /**
   * Get current security configuration
   * @returns Current security configuration
   */
  getSecurityConfig(): any;

  /**
   * Reset security counters (for testing)
   */
  resetSecurityCounters(): void;
}

/**
 * Security violation error class
 */
export class SecurityViolationError extends Error {
  constructor(
    message: string,
    public readonly securityEventType: string,
    public readonly source?: string
  ) {
    super(message);
    this.name = 'SecurityViolationError';
  }
}

/**
 * No-operation security layer for testing environments
 * where security enforcement should be bypassed
 */
export class NoOpSecurityLayer implements ISecurityEnforcement {
  async enforceFloodProtection(_source: string): Promise<boolean> {
    return true;
  }

  async enforceRateLimit(_source: string): Promise<boolean> {
    return true;
  }

  sanitizeInput(input: string): string {
    return input;
  }

  sanitizeMetadata(metadata: Record<string, unknown>): Record<string, unknown> {
    return metadata;
  }

  logSecurityEvent(_event: SecurityEvent): void {
    // No-op
  }

  isSecurityError(_error: Error): boolean {
    return false;
  }

  getSecurityConfig(): any {
    return {
      floodProtection: { enabled: false },
      rateLimiting: { enabled: false },
      inputSanitization: { enabled: false }
    };
  }

  resetSecurityCounters(): void {
    // No-op
  }
}
