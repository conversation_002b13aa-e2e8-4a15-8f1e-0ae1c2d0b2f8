/**
 * @file Smart Path Resolution System Test
 * @filepath server/src/platform/tracking/advanced-data/__tests__/SmartPathResolutionSystem.test.ts
 * @task-id T-TSK-01.SUB-01.2.IMP-01
 * @version 1.4.0
 * @template enterprise-test-suite-with-latest-standards
 * @description
 * Comprehensive, enterprise-grade test suite for the Smart Path Resolution System.
 * This suite validates all aspects of intelligent path resolution, including context
 * analysis, performance optimization, security compliance, analytics, and error handling.
 *
 * <AUTHOR> & CEO, E.Z. Consultancy"
 * @date 2025-07-09
 * @governance-adherence full-compliance
 */

import { SmartPathResolutionSystem } from '../SmartPathResolutionSystem';
import { BaseTrackingService } from '../../core-data/base/BaseTrackingService';
import { TPathResolutionConfig } from '../../../../../../shared/src/types/platform/tracking/tracking-types';

const testConfig: TPathResolutionConfig = {
  maxCacheSize: 500,
};

describe('SmartPathResolutionSystem Enterprise Test Suite', () => {
  let smartPathSystem: SmartPathResolutionSystem;

  afterEach(async () => {
    if (smartPathSystem && smartPathSystem.isReady()) {
      await smartPathSystem.shutdown();
    }
    jest.useRealTimers();
  });

  describe('Enterprise Instantiation & Configuration', () => {
    test('should inherit from BaseTrackingService', () => {
      smartPathSystem = new SmartPathResolutionSystem(testConfig);
      expect(smartPathSystem).toBeInstanceOf(SmartPathResolutionSystem);
      expect(smartPathSystem).toBeInstanceOf(BaseTrackingService);
    });

    test('should initialize with correct service name and version', async () => {
      smartPathSystem = new SmartPathResolutionSystem(testConfig);
      await smartPathSystem.initialize();
      expect((smartPathSystem as any).getServiceName()).toBe('smart-path-resolution-system');
      expect((smartPathSystem as any).getServiceVersion()).toBe('1.0.0');
      expect(smartPathSystem.isReady()).toBe(true);
    });

    test('should handle initialization failures gracefully', async () => {
      smartPathSystem = new SmartPathResolutionSystem(testConfig);
      const error = new Error('Infrastructure setup failed');
      (smartPathSystem as any).setupPathResolutionInfrastructure = jest.fn().mockRejectedValue(error);
      
      await expect(smartPathSystem.initialize()).rejects.toThrow(error);
      expect(smartPathSystem.isReady()).toBe(false);
    });
  });

  describe('Intelligent Path Resolution', () => {
    beforeEach(async () => {
      smartPathSystem = new SmartPathResolutionSystem(testConfig);
      await smartPathSystem.initialize();
    });

    test('should resolve a simple path for a foundation tracking component', async () => {
      const context = 'foundation';
      const componentType = 'tracking';
      const requirements = { type: 'core-data' };
      const result = await smartPathSystem.resolvePath(context, componentType, requirements);
      expect(result.resolvedPath).toBe('server/src/platform/tracking/core-data');
    });

    test('should resolve a complex path for an enterprise security component', async () => {
      const context = 'enterprise';
      const componentType = 'governance';
      const requirements = { type: 'security-management', complexity: 'high' };
      const result = await smartPathSystem.resolvePath(context, componentType, requirements);
      expect(result.resolvedPath).toBe('server/src/platform/governance/security-management');
    });

    test('should throw an error for invalid resolution requests', async () => {
      const context = 'unknown-context';
      const componentType = 'unknown-type';
      (smartPathSystem as any).analyzeContext = jest.fn().mockRejectedValue(new Error('Context analysis failed'));
      await expect(smartPathSystem.resolvePath(context, componentType, {})).rejects.toThrow('Context analysis failed');
    });
  });
  
  describe('Path Caching Mechanism', () => {
    beforeEach(async () => {
      smartPathSystem = new SmartPathResolutionSystem(testConfig);
      await smartPathSystem.initialize();
    });

    test('should use cache for subsequent identical requests', async () => {
      const resolutionSpy = jest.spyOn(smartPathSystem as any, 'performPathResolution');
      await smartPathSystem.resolvePath('foundation', 'tracking', { type: 'core-managers' });
      await smartPathSystem.resolvePath('foundation', 'tracking', { type: 'core-managers' });
      expect(resolutionSpy).toHaveBeenCalledTimes(1);
      resolutionSpy.mockRestore();
    });

    test('should respect cache TTL and re-resolve expired paths', async () => {
      jest.useFakeTimers();
      await smartPathSystem.resolvePath('user-experience', 'ui-component', { framework: 'react' });
      jest.advanceTimersByTime(3600 * 1000 + 1);
      const resolutionSpy = jest.spyOn(smartPathSystem as any, 'performPathResolution');
      await smartPathSystem.resolvePath('user-experience', 'ui-component', { framework: 'react' });
      expect(resolutionSpy).toHaveBeenCalledTimes(1);
      resolutionSpy.mockRestore();
    });

    test('should clear cache correctly', async () => {
      await smartPathSystem.resolvePath('foundation', 'tracking', { type: 'core-managers' });
      await smartPathSystem.clearCache();
      const resolutionSpy = jest.spyOn(smartPathSystem as any, 'performPathResolution');
      await smartPathSystem.resolvePath('foundation', 'tracking', { type: 'core-managers' });
      expect(resolutionSpy).toHaveBeenCalledTimes(1);
    });
  });

  describe('Context-Aware Optimization', () => {
    beforeEach(async () => {
      smartPathSystem = new SmartPathResolutionSystem(testConfig);
      await smartPathSystem.initialize();
    });

    test('should apply performance optimizations for foundation context', async () => {
      const result = await smartPathSystem.resolvePath('foundation', 'infrastructure', {});
      expect(result.optimizationLevel).toContain('performance-optimized');
    });

    test('should apply security optimizations for authentication context', async () => {
      const result = await smartPathSystem.resolvePath('authentication', 'governance', {});
      expect(result.optimizationLevel).toContain('security-optimized');
    });

    test('should generate warnings for low-confidence contexts', async () => {
      (smartPathSystem as any).analyzeContext = jest.fn().mockResolvedValue({
        score: 0.6,
        pathPrefix: 'server/src/platform',
        optimization: 'balanced',
        intelligentFactors: { performanceRequirements: 'medium', securityLevel: 'medium', scalabilityNeeds: 'medium' }
      });
      const result = await smartPathSystem.resolvePath('unknown-context', 'component', {});
      expect(result.validation.warnings).toContain('Low context confidence - consider manual review');
    });
  });

  describe('Enterprise Performance & Monitoring', () => {
    describe('Analytics Tracking', () => {
      test('should track path analytics correctly', async () => {
        smartPathSystem = new SmartPathResolutionSystem(testConfig);
        await smartPathSystem.initialize();
        
        // Directly mock the private method to avoid timer issues
        const updateAnalyticsSpy = jest.spyOn(smartPathSystem as any, 'updateAnalytics');
        
        await smartPathSystem.resolvePath('foundation', 'tracking', {});
        
        expect(updateAnalyticsSpy).toHaveBeenCalledWith('cache_miss', expect.any(Number));
        
        updateAnalyticsSpy.mockRestore();
      });
    });

    describe('Periodic Optimization', () => {
      test('should perform periodic optimization', async () => {
        jest.useFakeTimers();
        smartPathSystem = new SmartPathResolutionSystem(testConfig);
        await smartPathSystem.initialize();
        const optimizationSpy = jest.spyOn(smartPathSystem as any, 'performPeriodicOptimization');
        jest.advanceTimersByTime(300001);
        expect(optimizationSpy).toHaveBeenCalled();
      });
    });

    test('should return health status', async () => {
      smartPathSystem = new SmartPathResolutionSystem(testConfig);
      await smartPathSystem.initialize();
      const health = await smartPathSystem.getHealthStatus();
      expect(health.status).toBe('healthy');
    });
  });

  describe('Robustness & Error Handling', () => {
    test('should gracefully handle shutdown and cleanup', async () => {
      smartPathSystem = new SmartPathResolutionSystem(testConfig);
      await smartPathSystem.initialize();
      const cleanupSpy = jest.spyOn(smartPathSystem, 'cleanup');
      await smartPathSystem.shutdown();
      expect(cleanupSpy).toHaveBeenCalled();
    });
  });
}); 