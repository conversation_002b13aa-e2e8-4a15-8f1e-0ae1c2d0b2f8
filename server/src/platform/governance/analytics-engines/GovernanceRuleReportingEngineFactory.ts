/**
 * ============================================================================
 * AI CONTEXT: Rule Reporting Engine Factory - Advanced factory pattern implementation
 * Purpose: Singleton factory for managing reporting engine instances with pooling
 * Complexity: Moderate - Factory pattern with instance management and pooling
 * AI Navigation: 4 logical sections, factory management domain
 * ============================================================================
 */

import {
  GovernanceRuleReportingEngine,
  IReportingEngine,
  IReportingService,
  TReportingEngineData,
  TReportFormat,
  TReportOptions
} from './GovernanceRuleReportingEngine';
import { getTimerCoordinator } from '../../../../../shared/src/base/TimerCoordinationService';

import {
  TValidationResult,
  TAuthorityData,
  TMetrics,
  TPerformanceMetrics,
  TUsageMetrics,
  TErrorMetrics
} from '../../../../../shared/src/types/platform/tracking/tracking-types';

/**
 * @interface ICheck
 * @description Defines the structure for a health validation check.
 */
interface ICheck {
  checkId: string;
  description: string;
  status: 'failed' | 'passed' | 'warning';
  score: number;
  details: string;
}

/**
 * Factory configuration type
 */
export type TReportingEngineFactoryConfig = {
  maxInstances: number;
  instanceTimeout: number;
  enablePooling: boolean;
  poolSize: number;
  enableMonitoring: boolean;
  autoCleanup: boolean;
  cleanupInterval: number;
};

/**
 * Factory metrics type
 */
export type TReportingEngineFactoryMetrics = {
  instancesCreated: number;
  instancesDestroyed: number;
  activeInstances: number;
  pooledInstances: number;
  totalCreationTime: number;
  averageCreationTime: number;
  errorCount: number;
  lastCreated: Date | null;
  lastDestroyed: Date | null;
  factoryUptime: number;
};

/**
 * Default factory configuration
 */
const DEFAULT_FACTORY_CONFIG: TReportingEngineFactoryConfig = {
  maxInstances: 50, // Increased for high-volume reporting scenarios
  instanceTimeout: 3600000, // 1 hour
  enablePooling: true,
  poolSize: 10, // Increased pool size for better performance
  enableMonitoring: true,
  autoCleanup: true,
  cleanupInterval: 300000 // 5 minutes
};

/**
 * Governance Rule Reporting Engine Factory
 * Implements singleton pattern for managing reporting engine instances
 */
export class GovernanceRuleReportingEngineFactory {
  private static _instance: GovernanceRuleReportingEngineFactory | null = null;
  private static _initializationPromise: Promise<GovernanceRuleReportingEngineFactory> | null = null;

  private readonly _version = '1.0.0';
  private readonly _componentType = 'governance-rule-reporting-engine-factory';
  
  // Instance management
  private _instances: Map<string, IReportingEngine> = new Map();
  private _instancePool: IReportingEngine[] = [];
  private _instanceConfigs: Map<string, any> = new Map();
  private _instanceMetadata: Map<string, any> = new Map();
  
  // Factory configuration and state
  private _config: TReportingEngineFactoryConfig = DEFAULT_FACTORY_CONFIG;
  private _isInitialized = false;
  private _createdAt: Date = new Date();
  
  // Performance tracking
  private _instancesCreated = 0;
  private _instancesDestroyed = 0;
  private _totalCreationTime = 0;
  private _errorCount = 0;
  
  // ✅ TIMER COORDINATION: Timer management now handled by TimerCoordinationService

  /**
   * Private constructor for singleton pattern
   */
  private constructor() {
    // Private constructor
  }

  /**
   * Get singleton instance of the factory
   */
  public static async getInstance(): Promise<GovernanceRuleReportingEngineFactory> {
    if (GovernanceRuleReportingEngineFactory._instance) {
      return GovernanceRuleReportingEngineFactory._instance;
    }

    if (GovernanceRuleReportingEngineFactory._initializationPromise) {
      return GovernanceRuleReportingEngineFactory._initializationPromise;
    }

    GovernanceRuleReportingEngineFactory._initializationPromise = 
      GovernanceRuleReportingEngineFactory._createInstance();
    
    return GovernanceRuleReportingEngineFactory._initializationPromise;
  }

  /**
   * Create and initialize factory instance
   */
  private static async _createInstance(): Promise<GovernanceRuleReportingEngineFactory> {
    const factory = new GovernanceRuleReportingEngineFactory();
    await factory._initialize();
    GovernanceRuleReportingEngineFactory._instance = factory;
    return factory;
  }

  /**
   * Initialize the factory
   */
  private async _initialize(): Promise<void> {
    console.log('🏭 Initializing Governance Rule Reporting Engine Factory...');
    
    try {
      // Pre-populate instance pool if pooling is enabled
      if (this._config.enablePooling) {
        await this._populateInstancePool();
      }

      // Start cleanup timer if auto cleanup is enabled
      if (this._config.autoCleanup) {
        this._startCleanupTimer();
      }

      // Start monitoring if enabled
      if (this._config.enableMonitoring) {
        this._startMonitoring();
      }

      this._isInitialized = true;
      console.log('✅ Governance Rule Reporting Engine Factory initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize Governance Rule Reporting Engine Factory:', error);
      throw error;
    }
  }

  /**
   * Create a new reporting engine instance
   */
  public async createReportingEngine(config?: any): Promise<IReportingEngine> {
    if (!this._isInitialized) {
      throw new Error('Factory not initialized. Call getInstance() first.');
    }

    // Check instance limit first (including all active instances)
    if (this._instances.size >= this._config.maxInstances) {
      throw new Error(`Maximum instances limit reached: ${this._config.maxInstances}`);
    }

    const startTime = Date.now();
    let instance: IReportingEngine;
    let isFromPool = false;
    
    try {
      // Try to get instance from pool first
      if (this._config.enablePooling && this._instancePool.length > 0) {
        instance = this._instancePool.pop()!;
        isFromPool = true;
        console.log('♻️ Retrieved reporting engine from pool');
      } else {
        // Create new instance
        instance = new GovernanceRuleReportingEngine();
        await instance.initialize();
        console.log('🆕 Created new reporting engine instance');
      }

      // Generate instance ID and track the instance (whether pooled or new)
      const instanceId = this._generateInstanceId();
      this._instances.set(instanceId, instance);
      
      if (config) {
        this._instanceConfigs.set(instanceId, config);
      }

      this._instanceMetadata.set(instanceId, {
        createdAt: new Date(),
        lastAccessed: new Date(),
        accessCount: 0,
        fromPool: isFromPool
      });

      // Update metrics (only count newly created instances in creation metrics)
      if (!isFromPool) {
        this._instancesCreated++;
        this._totalCreationTime += (Date.now() - startTime);
      }

      console.log(`📊 Active instances: ${this._instances.size}, Pool size: ${this._instancePool.length}`);
      return instance;

    } catch (error) {
      this._errorCount++;
      console.error('❌ Failed to create reporting engine instance:', error);
      throw error;
    }
  }

  /**
   * Destroy a reporting engine instance
   */
  public async destroyReportingEngine(instance: IReportingEngine): Promise<void> {
    try {
      await instance.shutdown();

      // Find and remove from instances map (ES5 compatible)
      this._instances.forEach((storedInstance, instanceId) => {
        if (storedInstance === instance) {
          this._instances.delete(instanceId);
          this._instanceConfigs.delete(instanceId);
          this._instanceMetadata.delete(instanceId);
        }
      });

      // Remove from pool if present
      const poolIndex = this._instancePool.indexOf(instance);
      if (poolIndex !== -1) {
        this._instancePool.splice(poolIndex, 1);
      }

      this._instancesDestroyed++;
      console.log('🗑️ Reporting engine instance destroyed');

    } catch (error) {
      this._errorCount++;
      console.error('❌ Failed to destroy reporting engine instance:', error);
      throw error;
    }
  }

  /**
   * Return instance to pool for reuse
   */
  public async returnToPool(instance: IReportingEngine): Promise<void> {
    if (!this._config.enablePooling) {
      await this.destroyReportingEngine(instance);
      return;
    }

    // Find and remove from active instances tracking (ES5 compatible)
    let instanceId: string | null = null;
    this._instances.forEach((storedInstance, id) => {
      if (storedInstance === instance && instanceId === null) {
        instanceId = id;
        this._instances.delete(id);
        this._instanceConfigs.delete(id);
        this._instanceMetadata.delete(id);
      }
    });

    if (this._instancePool.length < this._config.poolSize) {
      this._instancePool.push(instance);
      console.log(`♻️ Returned reporting engine instance to pool (was ${instanceId})`);
    } else {
      // Pool is full, destroy the instance
      await instance.shutdown();
      this._instancesDestroyed++;
      console.log('🗑️ Pool full, destroyed reporting engine instance');
    }
  }

  /**
   * Get factory metrics
   */
  public getMetrics(): TReportingEngineFactoryMetrics {
    const uptime = Date.now() - this._createdAt.getTime();
    
    return {
      instancesCreated: this._instancesCreated,
      instancesDestroyed: this._instancesDestroyed,
      activeInstances: this._instances.size,
      pooledInstances: this._instancePool.length,
      totalCreationTime: this._totalCreationTime,
      averageCreationTime: this._instancesCreated > 0 ? this._totalCreationTime / this._instancesCreated : 0,
      errorCount: this._errorCount,
      lastCreated: this._instancesCreated > 0 ? new Date() : null,
      lastDestroyed: this._instancesDestroyed > 0 ? new Date() : null,
      factoryUptime: uptime
    };
  }

  /**
   * Get factory status
   */
  public getStatus(): {
    isInitialized: boolean;
    version: string;
    componentType: string;
    activeInstances: number;
    pooledInstances: number;
    config: TReportingEngineFactoryConfig;
  } {
    return {
      isInitialized: this._isInitialized,
      version: this._version,
      componentType: this._componentType,
      activeInstances: this._instances.size,
      pooledInstances: this._instancePool.length,
      config: this._config
    };
  }

  /**
   * Update factory configuration
   */
  public updateConfig(newConfig: Partial<TReportingEngineFactoryConfig>): void {
    this._config = { ...this._config, ...newConfig };
    console.log('⚙️ Factory configuration updated');
  }

  /**
   * Cleanup all instances and shutdown factory
   */
  public async shutdown(): Promise<void> {
    console.log('🛑 Shutting down Governance Rule Reporting Engine Factory...');

    try {
      // ✅ TIMER COORDINATION: Timer cleanup now handled automatically by TimerCoordinationService

      // Shutdown all active instances
      const shutdownPromises = Array.from(this._instances.values()).map(instance => 
        instance.shutdown().catch(error => 
          console.error('Error shutting down instance:', error)
        )
      );

      // Shutdown pooled instances
      const pooledShutdownPromises = this._instancePool.map(instance => 
        instance.shutdown().catch(error => 
          console.error('Error shutting down pooled instance:', error)
        )
      );

      await Promise.all([...shutdownPromises, ...pooledShutdownPromises]);

      // Clear all collections
      this._instances.clear();
      this._instancePool.length = 0;
      this._instanceConfigs.clear();
      this._instanceMetadata.clear();

      this._isInitialized = false;
      GovernanceRuleReportingEngineFactory._instance = null;
      GovernanceRuleReportingEngineFactory._initializationPromise = null;

      console.log('✅ Factory shutdown completed');
    } catch (error) {
      console.error('❌ Error during factory shutdown:', error);
      throw error;
    }
  }

  /**
   * Validate factory health
   */
  public async validateHealth(): Promise<TValidationResult> {
    const validationId = this._generateValidationId();
    const timestamp = new Date();
    const checks: ICheck[] = [];
    let overallScore = 100;

    // Check 1: Factory initialization
    if (!this._isInitialized) {
      checks.push({
        checkId: 'initialization',
        description: 'Factory initialization check',
        status: 'failed' as const,
        score: 0,
        details: 'Factory not initialized'
      });
      overallScore = 0;
    } else {
      checks.push({
        checkId: 'initialization',
        description: 'Factory initialization check',
        status: 'passed' as const,
        score: 100,
        details: 'Factory properly initialized'
      });
    }

    // Check instance limits
    const instanceUsage = (this._instances.size / this._config.maxInstances) * 100;
    if (instanceUsage > 90) {
      checks.push({
        checkId: 'instance-capacity',
        description: 'Instance capacity check',
        status: 'warning' as const,
        score: 70,
        details: `High instance usage: ${instanceUsage.toFixed(1)}%`
      });
      overallScore = Math.min(overallScore, 70);
    } else {
      checks.push({
        checkId: 'instance-capacity',
        description: 'Instance capacity check',
        status: 'passed' as const,
        score: 100,
        details: `Instance usage: ${instanceUsage.toFixed(1)}%`
      });
    }

    // Check error rate
    const errorRate = this._instancesCreated > 0 ? (this._errorCount / this._instancesCreated) * 100 : 0;
    if (errorRate > 10) {
      checks.push({
        checkId: 'error-rate',
        description: 'Error rate check',
        status: 'failed' as const,
        score: 0,
        details: `High error rate: ${errorRate.toFixed(1)}%`
      });
      overallScore = Math.min(overallScore, 50);
    } else {
      checks.push({
        checkId: 'error-rate',
        description: 'Error rate check',
        status: 'passed' as const,
        score: 100,
        details: `Error rate: ${errorRate.toFixed(1)}%`
      });
    }

    // Calculate overall score
    if (checks.length > 0) {
      overallScore = checks.reduce((sum, check) => sum + check.score, 0) / checks.length;
    }

    const status: 'valid' | 'invalid' = overallScore >= 90 ? 'valid' : 'invalid';

    return {
      validationId,
      componentId: this._componentType,
      timestamp: new Date(),
      executionTime: 0,
      status,
      overallScore,
      checks,
      references: {
        componentId: this._componentType,
        internalReferences: Array.from(this._instances.keys()),
        externalReferences: [],
        circularReferences: [],
        missingReferences: [],
        redundantReferences: [],
        metadata: {
          totalReferences: this._instances.size,
          buildTimestamp: this._createdAt,
          analysisDepth: 1
        }
      },
      recommendations: [],
      warnings: [],
      errors: [],
      metadata: {
        validationMethod: 'factory-health-check',
        rulesApplied: 3,
        dependencyDepth: 0,
        cyclicDependencies: [],
        orphanReferences: []
      }
    };
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  /**
   * Populate instance pool with initial instances
   */
  private async _populateInstancePool(): Promise<void> {
    console.log(`🏊 Populating instance pool with ${this._config.poolSize} instances...`);
    
    const poolPromises = Array.from({ length: this._config.poolSize }, async () => {
      try {
        const instance = new GovernanceRuleReportingEngine();
        await instance.initialize();
        return instance;
      } catch (error) {
        console.error('Failed to create pooled instance:', error);
        return null;
      }
    });

    const pooledInstances = (await Promise.all(poolPromises)).filter(Boolean) as IReportingEngine[];
    this._instancePool.push(...pooledInstances);
    
    console.log(`✅ Created ${pooledInstances.length} pooled instances`);
  }

  /**
   * Start cleanup timer for expired instances
   */
  private _startCleanupTimer(): void {
    const timerCoordinator = getTimerCoordinator();
    timerCoordinator.createCoordinatedInterval(
      () => {
        this._performCleanup();
      },
      this._config.cleanupInterval,
      'GovernanceRuleReportingEngineFactory',
      'cleanup'
    );
  }

  /**
   * Perform cleanup of expired instances
   */
  private _performCleanup(): void {
    const now = Date.now();
    const expiredInstances: string[] = [];

    // ES5 compatible iteration
    this._instanceMetadata.forEach((metadata, instanceId) => {
      const age = now - metadata.lastAccessed.getTime();
      if (age > this._config.instanceTimeout) {
        expiredInstances.push(instanceId);
      }
    });

    if (expiredInstances.length > 0) {
      console.log(`🧹 Cleaning up ${expiredInstances.length} expired instances`);
      
      for (const instanceId of expiredInstances) {
        const instance = this._instances.get(instanceId);
        if (instance) {
          this.destroyReportingEngine(instance).catch(error => 
            console.error(`Error cleaning up instance ${instanceId}:`, error)
          );
        }
      }
    }
  }

  /**
   * Start monitoring timer
   */
  private _startMonitoring(): void {
    const timerCoordinator = getTimerCoordinator();
    timerCoordinator.createCoordinatedInterval(
      () => {
        this._performMonitoring();
      },
      60000, // Monitor every minute
      'GovernanceRuleReportingEngineFactory',
      'monitoring'
    );
  }

  /**
   * Perform monitoring activities
   */
  private _performMonitoring(): void {
    const metrics = this.getMetrics();
    console.log('📊 Factory Metrics:', {
      activeInstances: metrics.activeInstances,
      pooledInstances: metrics.pooledInstances,
      totalCreated: metrics.instancesCreated,
      errorCount: metrics.errorCount,
      avgCreationTime: `${metrics.averageCreationTime.toFixed(2)}ms`
    });
  }

  /**
   * Generate unique instance ID
   */
  private _generateInstanceId(): string {
    return `reporting_engine_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate unique validation ID
   */
  private _generateValidationId(): string {
    return `validation_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Static method to create a single reporting engine instance
   * Convenience method for simple use cases
   */
  public static async createReportingEngine(config?: any): Promise<IReportingEngine> {
    const factory = await GovernanceRuleReportingEngineFactory.getInstance();
    return factory.createReportingEngine(config);
  }
}

/**
 * Export singleton instance for direct access
 * This function ensures we always get the current singleton instance,
 * even after shutdown/restart cycles
 */
export const reportingEngineFactory = () => GovernanceRuleReportingEngineFactory.getInstance(); 