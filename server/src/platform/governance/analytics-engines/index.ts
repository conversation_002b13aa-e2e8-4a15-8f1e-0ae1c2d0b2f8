/**
 * @file Analytics Engines Module Index
 * @filepath server/src/platform/governance/analytics-engines/index.ts
 * @task-id G-TSK-06.SUB-06.1.IMP-05
 * @component analytics-engines-module
 * @reference foundation-context.ANALYTICS.005
 * @template on-demand-creation-with-latest-standards
 * @tier T2
 * @context foundation-context
 * @category Analytics
 * @created 2025-07-01 05:12:30 +03
 * @modified 2025-07-03 13:42:57 +03
 * 
 * @description
 * Centralized exports for the Analytics Engines module providing
 * comprehensive analytics capabilities for governance rules.
 * Analytics domain: Analysis, optimization, insights generation.
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level module-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-tracking-architecture
 * @governance-dcr DCR-foundation-001-tracking-development
 * @governance-status active
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on server/src/platform/governance/analytics-engines/GovernanceRuleAnalyticsEngine
 * @depends-on server/src/platform/governance/analytics-engines/GovernanceRuleAnalyticsEngineFactory
 * @depends-on server/src/platform/governance/analytics-engines/GovernanceRuleOptimizationEngine
 * @depends-on server/src/platform/governance/analytics-engines/GovernanceRuleOptimizationEngineFactory
 * @depends-on server/src/platform/governance/analytics-engines/GovernanceRuleInsightsGenerator
 * @depends-on server/src/platform/governance/analytics-engines/GovernanceRuleInsightsGeneratorFactory
 * @enables server/src/platform/governance/automation-processing
 * @related-contexts foundation-context, governance-context
 * @governance-impact analytics-module, governance-analytics, compliance-reporting
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type module-index
 * @lifecycle-stage implementation
 * @testing-status comprehensive
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/contexts/foundation-context/analytics/analytics-engines-module.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 */

// ============================================================================
// CORE ANALYTICS ENGINE EXPORTS
// ============================================================================

export {
  GovernanceRuleAnalyticsEngine,
  IAnalyticsEngine,
  IAnalyticsService,
  TAnalyticsEngineData,
  TAnalyticsTimeRange,
  TAnalyticsPeriod,
  TInsightsOptions,
  TComplianceScope,
  TOptimizationStrategy,
  TRulePerformanceAnalysis,
  TRuleInsights,
  TUsagePatternAnalysis,
  TAnomalyDetectionResult,
  TComplianceAnalytics,
  TOptimizationResult,
  TAnalyticsData,
  TAnalyticsResult,
  TReportConfig,
  TAnalyticsReport,
  TAnalyticsMetrics,
  TAnalyticsTemplate,
  TAnalyticsDashboard
} from './GovernanceRuleAnalyticsEngine';

// ============================================================================
// ANALYTICS ENGINE FACTORY EXPORTS
// ============================================================================

export {
  GovernanceRuleAnalyticsEngineFactory,
  TAnalyticsEngineFactoryConfig,
  TAnalyticsEngineInstance,
  TFactoryStatistics,
  analyticsEngineFactory
} from './GovernanceRuleAnalyticsEngineFactory';

// ============================================================================
// PHASE 2: REPORTING ENGINE EXPORTS
// ============================================================================

export {
  GovernanceRuleReportingEngine,
  IReportingEngine,
  IReportingService,
  TReportingEngineData,
  TReportFormat,
  TReportOptions,
  TBatchReportOptions,
  TReportResult,
  TBatchReportResult,
  TScheduledReportResult,
  TCustomReportResult,
  TReportTemplate,
  TReportSchedule,
  TReportConfiguration,
  TReportDelivery,
  TReportingMetrics,
  TReportStatus,
  TBatchReportStatus,
  TScheduleStatus,
  TReportType,
  TTemplateCategory,
  TDeliveryMethod,
  TExportFormat
} from './GovernanceRuleReportingEngine';

// ============================================================================
// REPORTING ENGINE FACTORY EXPORTS
// ============================================================================

export {
  GovernanceRuleReportingEngineFactory,
  TReportingEngineFactoryConfig,
  TReportingEngineFactoryMetrics,
  reportingEngineFactory
} from './GovernanceRuleReportingEngineFactory';

// ============================================================================
// PHASE 3: OPTIMIZATION ENGINE EXPORTS
// ============================================================================

export {
  GovernanceRuleOptimizationEngine,
  IOptimizationEngine,
  IOptimizationService,
  TOptimizationOpportunity,
  TOptimizationReport,
  TOptimizationTestResult,
  TServiceHealth,
  TServiceStatus,
  TOptimizationServiceMetrics,
  TOptimizationEngineConfig,
  TOptimizationRecommendation,
  TOptimizationRisk
} from './GovernanceRuleOptimizationEngine';

// ============================================================================
// OPTIMIZATION ENGINE FACTORY EXPORTS
// ============================================================================

export {
  GovernanceRuleOptimizationEngineFactory,
  TOptimizationEngineFactoryConfig,
  TFactoryMetrics
} from './GovernanceRuleOptimizationEngineFactory';

// ============================================================================
// PHASE 4: INSIGHTS GENERATOR EXPORTS
// ============================================================================

export {
  GovernanceRuleInsightsGenerator,
  IInsightsGenerator,
  TTrendAnalysis,
  TInsightsReport,
  TPredictiveInsights,
  TComparativeInsights,
  TPerformanceInsights,
  TBusinessInsights,
  TInsightsServiceMetrics,
  TInsightsGeneratorConfig,
  TInsightRecommendation,
  TVisualizationData
} from './GovernanceRuleInsightsGenerator';

// ============================================================================
// INSIGHTS GENERATOR FACTORY EXPORTS
// ============================================================================

export {
  GovernanceRuleInsightsGeneratorFactory,
  TInsightsGeneratorFactoryConfig,
  TInsightsFactoryMetrics
} from './GovernanceRuleInsightsGeneratorFactory';



// ============================================================================
// MODULE METADATA
// ============================================================================

/**
 * Analytics Engines Module Information
 * Analytics domain: Analysis, optimization, insights generation
 */
export const ANALYTICS_ENGINES_MODULE = {
  name: 'Analytics Engines',
  version: '2.0.0',
  description: 'Analytics and optimization capabilities for governance rules',
  phases: {
    phase1: {
      name: 'Analytics Engine',
      status: 'completed',
      components: ['GovernanceRuleAnalyticsEngine', 'GovernanceRuleAnalyticsEngineFactory']
    },
    phase2: {
      name: 'Rule Reporting Engine', 
      status: 'completed',
      components: ['GovernanceRuleReportingEngine', 'GovernanceRuleReportingEngineFactory']
    },
    phase3: {
      name: 'Optimization Engine',
      status: 'completed',
      components: [
        'GovernanceRuleOptimizationEngine',
        'GovernanceRuleOptimizationEngineFactory'
      ]
    },
    phase4: {
      name: 'Insights Generator',
      status: 'completed',
      components: [
        'GovernanceRuleInsightsGenerator', 
        'GovernanceRuleInsightsGeneratorFactory'
      ]
    }
  },
  components: [
    'GovernanceRuleAnalyticsEngine',
    'GovernanceRuleAnalyticsEngineFactory',
    'GovernanceRuleReportingEngine',
    'GovernanceRuleReportingEngineFactory',
    'GovernanceRuleOptimizationEngine',
    'GovernanceRuleOptimizationEngineFactory',
    'GovernanceRuleInsightsGenerator',
    'GovernanceRuleInsightsGeneratorFactory'
  ],
  interfaces: [
    'IAnalyticsEngine',
    'IAnalyticsService',
    'IReportingEngine',
    'IReportingService',
    'IOptimizationEngine',
    'IOptimizationService',
    'IInsightsGenerator'
  ],
  capabilities: [
    'Rule Performance Analysis',
    'Insights Generation',
    'Usage Pattern Analysis',
    'Anomaly Detection',
    'Compliance Analytics',
    'Rule Optimization',
    'Analytics Reporting',
    'Factory Management',
    'Optimization Opportunities Analysis',
    'Predictive Analytics',
    'Business Intelligence',
    'Performance Insights',
    'Trend Analysis',
    'Data Visualization'
  ],
  domain: 'Analytics',
  scope: 'Data analysis, optimization, insights generation',
  authority: 'President & CEO, E.Z. Consultancy',
  created: '2025-07-01 05:12:30 +03',
  lastUpdated: '2025-07-03 13:42:57 +03',
  status: 'complete'
} as const; 