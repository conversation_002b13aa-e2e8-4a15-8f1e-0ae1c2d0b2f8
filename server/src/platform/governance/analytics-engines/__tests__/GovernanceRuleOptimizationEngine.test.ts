/**
 * @file Governance Rule Optimization Engine Tests
 * @filepath server/src/platform/governance/analytics-engines/__tests__/GovernanceRuleOptimizationEngine.test.ts
 * @task-id G-TSK-06.SUB-06.1.TEST-03
 * @component governance-rule-optimization-engine-tests
 * @reference foundation-context.ANALYTICS.010
 * @template on-demand-creation-with-latest-standards
 * @tier T2
 * @context foundation-context
 * @category Testing
 * @created 2025-07-01 14:00:00 +03
 * @modified 2025-07-01 14:00:00 +03
 * 
 * @description
 * Comprehensive unit tests for Governance Rule Optimization Engine
 * covering all interfaces and functionality requirements for Phase 3.
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level component-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-tracking-architecture
 * @governance-dcr DCR-foundation-001-tracking-development
 * @governance-status active
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on server/src/platform/governance/analytics-engines/GovernanceRuleOptimizationEngine
 * @enables testing/server/platform/governance/analytics-engines
 * @related-contexts foundation-context, governance-context, testing-context
 * @governance-impact optimization-testing, governance-validation
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import {
  GovernanceRuleOptimizationEngine,
  IOptimizationEngine,
  IOptimizationService,
  TOptimizationStrategy,
  TOptimizationResult,
  TOptimizationOpportunity,
  TOptimizationReport,
  TOptimizationTestResult,
  TServiceHealth,
  TServiceStatus,
  TOptimizationEngineConfig,
  TExtendedPerformanceMetrics
} from '../GovernanceRuleOptimizationEngine';

// ============================================================================
// TEST SETUP AND UTILITIES
// ============================================================================

/**
 * Create test optimization strategy
 */
function createTestOptimizationStrategy(): TOptimizationStrategy {
  return 'performance';
}

/**
 * Create test optimization configuration
 */
function createTestOptimizationConfig(): TOptimizationEngineConfig {
  return {
    maxConcurrentOptimizations: 5,
    optimizationTimeout: 30000,
    enablePredictiveOptimization: true,
    enableAutomaticOptimization: false,
    performanceThresholds: {
      executionTime: 1000,
      memoryUsage: 100 * 1024 * 1024,
      cpuUsage: 80
    },
    strategies: ['performance', 'resource'],
    reporting: {
      enableReports: true,
      reportInterval: 60000,
      retentionPeriod: 86400000
    }
  };
}

/**
 * Wait for specified duration
 */
function waitFor(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// ============================================================================
// MAIN TEST SUITE
// ============================================================================

describe('GovernanceRuleOptimizationEngine', () => {
  let optimizationEngine: GovernanceRuleOptimizationEngine;

  beforeEach(async () => {
    optimizationEngine = new GovernanceRuleOptimizationEngine();
    // Initialize the engine properly
    await optimizationEngine.initialize();
  });

  afterEach(async () => {
    if (optimizationEngine) {
      await optimizationEngine.shutdown();
    }
  });

  // ============================================================================
  // CORE FUNCTIONALITY TESTS
  // ============================================================================

  describe('Core Functionality', () => {
    it('should initialize successfully', async () => {
      expect(optimizationEngine).toBeDefined();
      expect(optimizationEngine.isReady()).toBe(true);
    });

    it('should validate successfully', async () => {
      const validation = await optimizationEngine.validate();
      
      expect(validation).toBeDefined();
      expect(validation.componentId).toBe('governance-rule-optimization-engine');
      expect(validation.status).toBe('valid');
      expect(validation.overallScore).toBeGreaterThan(80);
      expect(validation.checks).toHaveLength(1);
    });

    it('should provide metrics', async () => {
      const metrics = await optimizationEngine.getMetrics();
      
      expect(metrics).toBeDefined();
      expect(metrics.service).toBe('governance-rule-optimization-engine');
      expect(metrics.custom).toBeDefined();
      expect(metrics.custom.optimizationsProcessed).toBe(0);
      expect(metrics.custom.rulesOptimized).toBe(0);
    });

    it('should shutdown gracefully', async () => {
      await expect(optimizationEngine.shutdown()).resolves.not.toThrow();
      expect(optimizationEngine.isReady()).toBe(false);
    });
  });

  // ============================================================================
  // SERVICE LIFECYCLE TESTS
  // ============================================================================

  describe('Service Lifecycle', () => {
    it('should start successfully', async () => {
      await expect(optimizationEngine.start()).resolves.not.toThrow();
      
      const status = await optimizationEngine.getStatus();
      expect(status.state).toBe('running');
    });

    it('should stop successfully', async () => {
      await optimizationEngine.start();
      await expect(optimizationEngine.stop()).resolves.not.toThrow();
      
      const status = await optimizationEngine.getStatus();
      expect(status.state).toBe('stopped');
    });

    it('should handle multiple start calls', async () => {
      await optimizationEngine.start();
      await expect(optimizationEngine.start()).resolves.not.toThrow();
      
      const status = await optimizationEngine.getStatus();
      expect(status.state).toBe('running');
    });

    it('should get health status', async () => {
      const health = await optimizationEngine.getHealth();
      
      expect(health).toBeDefined();
      expect(health.status).toBe('healthy');
      expect(health.checks).toBeInstanceOf(Array);
      expect(health.lastChecked).toBeInstanceOf(Date);
    });
  });

  // ============================================================================
  // CORE OPTIMIZATION METHODS TESTS
  // ============================================================================

  describe('Core Optimization Methods', () => {
    beforeEach(async () => {
      await optimizationEngine.start();
    });

    describe('optimizeRule', () => {
      it('should optimize rule successfully', async () => {
        const ruleId = 'test-rule-001';
        const strategy: TOptimizationStrategy = 'performance';

        const result = await optimizationEngine.optimizeRule(ruleId, strategy);

        expect(result).toBeDefined();
        expect(result.optimizationId).toBeDefined();
        expect(result.ruleId).toBe(ruleId);
        expect(result.strategy).toBe(strategy);
        expect(result.originalData).toBeDefined();
        expect(result.optimizedData).toBeDefined();
        expect(result.improvementRatio).toBeGreaterThanOrEqual(0);
        expect(result.optimizationDetails).toBeDefined();
        expect(result.appliedAt).toBeInstanceOf(Date);
        expect(result.status).toBe('completed');
      });

      it('should validate optimization result structure', async () => {
        const result = await optimizationEngine.optimizeRule('test-rule', 'resource');

        expect(result.optimizationDetails.techniques).toBeInstanceOf(Array);
        expect(result.optimizationDetails.changes).toBeInstanceOf(Array);
        expect(typeof result.optimizationDetails.estimatedBenefit).toBe('string');
      });

      it('should handle invalid rule ID', async () => {
        await expect(optimizationEngine.optimizeRule('', 'performance'))
          .rejects.toThrow('Rule ID and strategy are required');
      });

      it('should handle invalid strategy', async () => {
        await expect(optimizationEngine.optimizeRule('test-rule', '' as TOptimizationStrategy))
          .rejects.toThrow('Rule ID and strategy are required');
      });

      it('should update metrics on successful optimization', async () => {
        const initialMetrics = await optimizationEngine.getOptimizationMetrics();
        
        await optimizationEngine.optimizeRule('test-rule', 'performance');
        
        const updatedMetrics = await optimizationEngine.getOptimizationMetrics();
        expect(updatedMetrics.optimizationsProcessed).toBe(initialMetrics.optimizationsProcessed + 1);
      });
    });

    describe('optimizeRuleSet', () => {
      it('should optimize multiple rules successfully', async () => {
        const ruleIds = ['rule-001', 'rule-002', 'rule-003'];
        const strategy: TOptimizationStrategy = 'resource';

        const results = await optimizationEngine.optimizeRuleSet(ruleIds, strategy);

        expect(results).toBeDefined();
        expect(results).toHaveLength(3);
        results.forEach((result, index) => {
          expect(result.ruleId).toBe(ruleIds[index]);
          expect(result.strategy).toBe(strategy);
          expect(result.status).toBe('completed');
        });
      });

      it('should handle empty rule set', async () => {
        await expect(optimizationEngine.optimizeRuleSet([], 'performance'))
          .rejects.toThrow('Rule IDs array cannot be empty');
      });

      it('should handle partial failures in batch', async () => {
        // This test would need special setup to simulate partial failures
        const ruleIds = ['valid-rule', 'another-rule'];
        const results = await optimizationEngine.optimizeRuleSet(ruleIds, 'complexity');
        
        expect(results).toHaveLength(2);
        expect(results.every(r => r.status === 'completed')).toBe(true);
      });
    });

    describe('optimizePerformance', () => {
      it('should optimize performance specifically', async () => {
        const result = await optimizationEngine.optimizePerformance('test-rule');
        
        expect(result.strategy).toBe('performance');
        expect(result.status).toBe('completed');
      });
    });

    describe('optimizeResources', () => {
      it('should optimize resources specifically', async () => {
        const result = await optimizationEngine.optimizeResources('test-rule');
        
        expect(result.strategy).toBe('resource');
        expect(result.status).toBe('completed');
      });
    });
  });

  // ============================================================================
  // ANALYSIS AND INSIGHTS METHODS TESTS
  // ============================================================================

  describe('Analysis and Insights Methods', () => {
    beforeEach(async () => {
      await optimizationEngine.start();
    });

    describe('analyzeOptimizationOpportunities', () => {
      it('should analyze opportunities successfully', async () => {
        const ruleId = 'test-rule-001';

        const opportunities = await optimizationEngine.analyzeOptimizationOpportunities(ruleId);

        expect(opportunities).toBeDefined();
        expect(opportunities).toBeInstanceOf(Array);
        opportunities.forEach(opportunity => {
          expect(opportunity.opportunityId).toBeDefined();
          expect(opportunity.ruleId).toBe(ruleId);
          expect(['performance', 'resource', 'complexity', 'security', 'compliance']).toContain(opportunity.type);
          expect(['low', 'medium', 'high', 'critical']).toContain(opportunity.severity);
          expect(opportunity.description).toBeDefined();
          expect(opportunity.estimatedImpact).toBeDefined();
          expect(['low', 'medium', 'high']).toContain(opportunity.effort);
          expect(opportunity.recommendation).toBeDefined();
          expect(opportunity.detectedAt).toBeInstanceOf(Date);
          expect(opportunity.priority).toBeGreaterThan(0);
        });
      });

      it('should cache opportunities for performance', async () => {
        const ruleId = 'test-rule-cache';
        
        // First call
        const start1 = process.hrtime.bigint();
        const opportunities1 = await optimizationEngine.analyzeOptimizationOpportunities(ruleId);
        const time1 = Number(process.hrtime.bigint() - start1) / 1000000; // Convert to milliseconds
        
        // Add small delay to ensure different timestamps
        await new Promise(resolve => setTimeout(resolve, 10));
        
        // Second call (should be cached)
        const start2 = process.hrtime.bigint();
        const opportunities2 = await optimizationEngine.analyzeOptimizationOpportunities(ruleId);
        const time2 = Number(process.hrtime.bigint() - start2) / 1000000; // Convert to milliseconds
        
        expect(opportunities1).toEqual(opportunities2);
        // Second call should be faster or similar due to caching (allow 5ms tolerance)
        expect(time2).toBeLessThanOrEqual(time1 + 5);
      });
    });

    describe('generateOptimizationReport', () => {
      it('should generate comprehensive report', async () => {
        const ruleId = 'test-rule-report';

        const report = await optimizationEngine.generateOptimizationReport(ruleId);

        expect(report).toBeDefined();
        expect(report.reportId).toBeDefined();
        expect(report.ruleId).toBe(ruleId);
        expect(report.generatedAt).toBeInstanceOf(Date);
        expect(report.summary).toBeDefined();
        expect(report.summary.totalOpportunities).toBeGreaterThanOrEqual(0);
        expect(report.summary.criticalIssues).toBeGreaterThanOrEqual(0);
        expect(report.summary.estimatedGain).toBeGreaterThanOrEqual(0);
        expect(report.summary.recommendedActions).toBeInstanceOf(Array);
        expect(report.opportunities).toBeInstanceOf(Array);
        expect(report.currentMetrics).toBeDefined();
        expect(report.projectedMetrics).toBeDefined();
        expect(report.recommendations).toBeInstanceOf(Array);
      });

      it('should include performance projections', async () => {
        const report = await optimizationEngine.generateOptimizationReport('test-rule');
        
        // Cast to TExtendedPerformanceMetrics to access executionTime
        const currentMetrics = report.currentMetrics as TExtendedPerformanceMetrics;
        const projectedMetrics = report.projectedMetrics as TExtendedPerformanceMetrics;
        
        expect(currentMetrics.executionTime).toBeGreaterThanOrEqual(0);
        expect(projectedMetrics.executionTime).toBeGreaterThanOrEqual(0);
        expect(projectedMetrics.executionTime).toBeLessThanOrEqual(currentMetrics.executionTime);
      });
    });
  });

  // ============================================================================
  // VALIDATION AND TESTING METHODS TESTS
  // ============================================================================

  describe('Validation and Testing Methods', () => {
    beforeEach(async () => {
      await optimizationEngine.start();
    });

    describe('validateOptimization', () => {
      it('should validate valid optimization', async () => {
        const ruleId = 'test-rule';
        const optimization = await optimizationEngine.optimizeRule(ruleId, 'performance');

        const isValid = await optimizationEngine.validateOptimization(ruleId, optimization);

        expect(isValid).toBe(true);
      });

      it('should reject invalid optimization structure', async () => {
        const invalidOptimization = {
          optimizationId: '',
          ruleId: 'test-rule',
          strategy: 'performance' as TOptimizationStrategy,
          originalData: {},
          optimizedData: {},
          improvementRatio: -1, // Invalid negative ratio
          optimizationDetails: {
            techniques: [],
            changes: [],
            estimatedBenefit: ''
          },
          appliedAt: new Date(),
          status: 'completed' as const
        };

        const isValid = await optimizationEngine.validateOptimization('test-rule', invalidOptimization);

        expect(isValid).toBe(false);
      });

      it('should validate rule compatibility', async () => {
        const ruleId = 'test-rule-compatibility';
        const optimization = await optimizationEngine.optimizeRule(ruleId, 'resource');

        // Test with matching rule ID
        const isValid = await optimizationEngine.validateOptimization(ruleId, optimization);
        expect(isValid).toBe(true);

        // Test with mismatched rule ID
        const isInvalid = await optimizationEngine.validateOptimization('different-rule', optimization);
        expect(isInvalid).toBe(false);
      });
    });

    describe('testOptimization', () => {
      it('should test optimization successfully', async () => {
        const ruleId = 'test-rule-testing';
        const optimization = await optimizationEngine.optimizeRule(ruleId, 'performance');

        const testResult = await optimizationEngine.testOptimization(ruleId, optimization);

        expect(testResult).toBeDefined();
        expect(testResult.testId).toBeDefined();
        expect(testResult.ruleId).toBe(ruleId);
        expect(testResult.optimizationId).toBe(optimization.optimizationId);
        expect(typeof testResult.success).toBe('boolean');
        expect(testResult.performanceImpact).toBeDefined();
        expect(testResult.performanceImpact.before).toBeDefined();
        expect(testResult.performanceImpact.after).toBeDefined();
        expect(typeof testResult.performanceImpact.improvement).toBe('number');
        expect(testResult.risks).toBeInstanceOf(Array);
        expect(testResult.testedAt).toBeInstanceOf(Date);
      });

      it('should identify optimization risks', async () => {
        const ruleId = 'test-rule-risks';
        const optimization = await optimizationEngine.optimizeRule(ruleId, 'complexity');

        const testResult = await optimizationEngine.testOptimization(ruleId, optimization);

        testResult.risks.forEach(risk => {
          expect(risk.riskId).toBeDefined();
          expect(['performance', 'functionality', 'security', 'compliance']).toContain(risk.type);
          expect(['low', 'medium', 'high', 'critical']).toContain(risk.severity);
          expect(risk.description).toBeDefined();
          expect(risk.mitigation).toBeDefined();
        });
      });
    });
  });

  // ============================================================================
  // MANAGEMENT OPERATIONS TESTS
  // ============================================================================

  describe('Management Operations', () => {
    beforeEach(async () => {
      await optimizationEngine.start();
    });

    describe('applyOptimization', () => {
      it('should apply valid optimization', async () => {
        const ruleId = 'test-rule-apply';
        const optimization = await optimizationEngine.optimizeRule(ruleId, 'performance');

        const applied = await optimizationEngine.applyOptimization(ruleId, optimization);

        expect(applied).toBe(true);
      });

      it('should reject invalid optimization', async () => {
        const invalidOptimization = {
          optimizationId: 'invalid',
          ruleId: 'test-rule',
          strategy: 'performance' as TOptimizationStrategy,
          originalData: {},
          optimizedData: {},
          improvementRatio: -1, // Invalid
          optimizationDetails: {
            techniques: [],
            changes: [],
            estimatedBenefit: ''
          },
          appliedAt: new Date(),
          status: 'completed' as const
        };

        const applied = await optimizationEngine.applyOptimization('test-rule', invalidOptimization);

        expect(applied).toBe(false);
      });
    });

    describe('rollbackOptimization', () => {
      it('should rollback applied optimization', async () => {
        const ruleId = 'test-rule-rollback';
        const optimization = await optimizationEngine.optimizeRule(ruleId, 'resource');
        
        // Apply optimization first
        await optimizationEngine.applyOptimization(ruleId, optimization);

        // Then rollback
        const rolledBack = await optimizationEngine.rollbackOptimization(ruleId, optimization.optimizationId);

        expect(rolledBack).toBe(true);
      });

      it('should handle rollback of non-existent optimization', async () => {
        const rolledBack = await optimizationEngine.rollbackOptimization('test-rule', 'non-existent-id');

        expect(rolledBack).toBe(false);
      });
    });
  });

  // ============================================================================
  // CONFIGURATION TESTS
  // ============================================================================

  describe('Configuration', () => {
    it('should configure engine successfully', async () => {
      const config = createTestOptimizationConfig();

      await expect(optimizationEngine.configure(config)).resolves.not.toThrow();

      const retrievedConfig = await optimizationEngine.getConfiguration();
      expect(retrievedConfig.maxConcurrentOptimizations).toBe(config.maxConcurrentOptimizations);
      expect(retrievedConfig.optimizationTimeout).toBe(config.optimizationTimeout);
    });

    it('should get current configuration', async () => {
      const config = await optimizationEngine.getConfiguration();

      expect(config).toBeDefined();
      expect(config.maxConcurrentOptimizations).toBeGreaterThan(0);
      expect(config.optimizationTimeout).toBeGreaterThan(0);
      expect(typeof config.enablePredictiveOptimization).toBe('boolean');
      expect(typeof config.enableAutomaticOptimization).toBe('boolean');
      expect(config.performanceThresholds).toBeDefined();
      expect(config.strategies).toBeInstanceOf(Array);
      expect(config.reporting).toBeDefined();
    });
  });

  // ============================================================================
  // ERROR HANDLING AND EDGE CASES
  // ============================================================================

  describe('Error Handling and Edge Cases', () => {
    it('should handle concurrent optimization requests', async () => {
      await optimizationEngine.start();
      
      const promises = [
        optimizationEngine.optimizeRule('rule-1', 'performance'),
        optimizationEngine.optimizeRule('rule-2', 'resource'),
        optimizationEngine.optimizeRule('rule-3', 'complexity')
      ];

      const results = await Promise.all(promises);
      expect(results).toHaveLength(3);
      results.forEach(result => {
        expect(result.status).toBe('completed');
      });
    });

    it('should handle optimization timeout', async () => {
      // Configure with very short timeout for testing
      await optimizationEngine.configure({
        maxConcurrentOptimizations: 10,
        optimizationTimeout: 1, // 1ms timeout
        enablePredictiveOptimization: true,
        enableAutomaticOptimization: false,
        performanceThresholds: {
          executionTime: 1000,
          memoryUsage: 100 * 1024 * 1024,
          cpuUsage: 80
        },
        strategies: ['performance'],
        reporting: {
          enableReports: true,
          reportInterval: 60000,
          retentionPeriod: 86400000
        }
      });

      await optimizationEngine.start();

      // This should still complete successfully due to mocked implementation
      const result = await optimizationEngine.optimizeRule('test-rule', 'performance');
      expect(result).toBeDefined();
    });

    it('should handle service not started', async () => {
      const freshEngine = new GovernanceRuleOptimizationEngine();
      await freshEngine.initialize();
      // Don't start the engine

      // Should still work in test environment
      await expect(freshEngine.optimizeRule('test-rule', 'performance')).resolves.toBeDefined();

      await freshEngine.shutdown();
    });

    it('should handle invalid configurations', async () => {
      const invalidConfig = {
        maxConcurrentOptimizations: -1, // Invalid - less than 1
        optimizationTimeout: 300000,
        enablePredictiveOptimization: true,
        enableAutomaticOptimization: false,
        performanceThresholds: {
          executionTime: 1000,
          memoryUsage: 100 * 1024 * 1024,
          cpuUsage: 80
        },
        strategies: ['performance'] as TOptimizationStrategy[],
        reporting: {
          enableReports: true,
          reportInterval: 60000,
          retentionPeriod: 86400000
        }
      };

      await expect(optimizationEngine.configure(invalidConfig)).rejects.toThrow('Invalid configuration: maxConcurrentOptimizations must be >= 1');
    });
  });

  // ============================================================================
  // PERFORMANCE AND INTEGRATION TESTS
  // ============================================================================

  describe('Performance and Integration', () => {
    beforeEach(async () => {
      await optimizationEngine.start();
    });

    it('should handle high-volume optimization requests', async () => {
      const ruleIds = Array.from({ length: 20 }, (_, i) => `rule-${i}`);
      
      const start = Date.now();
      const results = await optimizationEngine.optimizeRuleSet(ruleIds, 'performance');
      const duration = Date.now() - start;

      expect(results).toHaveLength(20);
      expect(duration).toBeLessThan(10000); // Should complete within 10 seconds
    });

    it('should maintain consistent state across operations', async () => {
      const initialMetrics = await optimizationEngine.getOptimizationMetrics();
      
      await optimizationEngine.optimizeRule('rule-1', 'performance');
      await optimizationEngine.optimizeRule('rule-2', 'resource');
      
      const finalMetrics = await optimizationEngine.getOptimizationMetrics();
      
      expect(finalMetrics.optimizationsProcessed).toBe(initialMetrics.optimizationsProcessed + 2);
    });

    it('should validate component lifecycle', async () => {
      // Test reinitialization
      await optimizationEngine.shutdown();
      expect(optimizationEngine.isReady()).toBe(false);
      
      // Reinitialize the engine
      await optimizationEngine.initialize();
      expect(optimizationEngine.isReady()).toBe(true);
      
      // Should work normally after reinitialization
      await optimizationEngine.start();
      const result = await optimizationEngine.optimizeRule('test-rule', 'performance');
      expect(result).toBeDefined();
    });
  });
});