/**
 * ============================================================================
 * AI CONTEXT: Rule Reporting Engine Factory Tests - Comprehensive factory testing
 * Purpose: Complete test suite for Phase 2 Rule Reporting Engine Factory
 * Complexity: Moderate - Factory pattern testing with instance management
 * AI Navigation: 5 logical sections, testing all factory capabilities
 * ============================================================================
 */

import { describe, beforeEach, afterEach, it, expect, jest } from '@jest/globals';
import {
  GovernanceRuleReportingEngineFactory,
  TReportingEngineFactoryConfig,
  TReportingEngineFactoryMetrics,
  reportingEngineFactory
} from '../GovernanceRuleReportingEngineFactory';

import {
  GovernanceRuleReportingEngine,
  IReportingEngine,
  IReportingService
} from '../GovernanceRuleReportingEngine';

import {
  TValidationResult
} from '../../../../../../shared/src/types/platform/tracking/tracking-types';

// ============================================================================
// TEST SETUP AND UTILITIES
// ============================================================================

/**
 * Create test factory configuration
 */
function createFactoryConfig(): TReportingEngineFactoryConfig {
  return {
    maxInstances: 10,
    instanceTimeout: 300000, // 5 minutes for testing
    enablePooling: true,
    poolSize: 3,
    enableMonitoring: true,
    autoCleanup: true,
    cleanupInterval: 60000 // 1 minute for testing
  };
}

/**
 * Wait for specified milliseconds
 */
function wait(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// ============================================================================
// MAIN TEST SUITE
// ============================================================================

describe('GovernanceRuleReportingEngineFactory', () => {
  let factory: GovernanceRuleReportingEngineFactory;

  // ============================================================================
  // SETUP AND TEARDOWN
  // ============================================================================

  beforeEach(async () => {
    factory = await GovernanceRuleReportingEngineFactory.getInstance();
  });

  afterEach(async () => {
    try {
      await factory.shutdown();
    } catch (error) {
      // Factory might already be shut down
    }
  });

  // ============================================================================
  // SINGLETON PATTERN TESTS
  // ============================================================================

  describe('Singleton Pattern', () => {
    it('should return the same instance on multiple getInstance calls', async () => {
      const instance1 = await GovernanceRuleReportingEngineFactory.getInstance();
      const instance2 = await GovernanceRuleReportingEngineFactory.getInstance();
      
      expect(instance1).toBe(instance2);
    });

    it('should initialize factory properly', async () => {
      const status = factory.getStatus();
      
      expect(status.isInitialized).toBe(true);
      expect(status.version).toBe('1.0.0');
      expect(status.componentType).toBe('governance-rule-reporting-engine-factory');
    });

    it('should have correct default configuration', () => {
      const status = factory.getStatus();
      
      expect(status.config.maxInstances).toBe(50);
      expect(status.config.enablePooling).toBe(true);
      expect(status.config.poolSize).toBe(10);
      expect(status.config.enableMonitoring).toBe(true);
      expect(status.config.autoCleanup).toBe(true);
    });
  });

  // ============================================================================
  // INSTANCE CREATION TESTS
  // ============================================================================

  describe('Instance Creation', () => {
    it('should create reporting engine instance successfully', async () => {
      const instance = await factory.createReportingEngine();
      
      expect(instance).toBeInstanceOf(GovernanceRuleReportingEngine);
      expect(instance.id).toBe('governance-rule-reporting-engine');
      expect(instance.authority).toBe('President & CEO, E.Z. Consultancy');
      expect(instance.isReady()).toBe(true);
      
      await factory.destroyReportingEngine(instance);
    });

    it('should create multiple instances with unique properties', async () => {
      const instance1 = await factory.createReportingEngine();
      const instance2 = await factory.createReportingEngine();
      
      expect(instance1).not.toBe(instance2);
      expect(instance1.isReady()).toBe(true);
      expect(instance2.isReady()).toBe(true);
      
      await factory.destroyReportingEngine(instance1);
      await factory.destroyReportingEngine(instance2);
    });

    it('should create instance with custom configuration', async () => {
      const config = { customProperty: 'test-value' };
      const instance = await factory.createReportingEngine(config);
      
      expect(instance).toBeDefined();
      expect(instance.isReady()).toBe(true);
      
      await factory.destroyReportingEngine(instance);
    });

    it('should respect maximum instance limit', async () => {
      // Update config to small limit for testing
      factory.updateConfig({ maxInstances: 2 });
      
      const instance1 = await factory.createReportingEngine();
      const instance2 = await factory.createReportingEngine();
      
      await expect(factory.createReportingEngine())
        .rejects.toThrow('Maximum instances limit reached: 2');
      
      await factory.destroyReportingEngine(instance1);
      await factory.destroyReportingEngine(instance2);
    });

    it('should track instance creation metrics', async () => {
      // Disable pooling to ensure we create a new instance instead of using pooled ones
      factory.updateConfig({ enablePooling: false });
      
      const initialMetrics = factory.getMetrics();
      
      const instance = await factory.createReportingEngine();
      
      const updatedMetrics = factory.getMetrics();
      
      expect(updatedMetrics.instancesCreated).toBe(initialMetrics.instancesCreated + 1);
      expect(updatedMetrics.activeInstances).toBe(initialMetrics.activeInstances + 1);
      // Since creation time might be very fast (sub-millisecond), check if it's at least not less than initial
      expect(updatedMetrics.totalCreationTime).toBeGreaterThanOrEqual(initialMetrics.totalCreationTime);
      
      await factory.destroyReportingEngine(instance);
    });
  });

  // ============================================================================
  // INSTANCE POOLING TESTS
  // ============================================================================

  describe('Instance Pooling', () => {
    it('should pre-populate instance pool on initialization', () => {
      const status = factory.getStatus();
      
      expect(status.pooledInstances).toBeGreaterThan(0);
      expect(status.pooledInstances).toBeLessThanOrEqual(status.config.poolSize);
    });

    it('should reuse pooled instances', async () => {
      // Test pool reuse with current factory state
      const initialMetrics = factory.getMetrics();
      const initialPooled = initialMetrics.pooledInstances;
      
      if (initialPooled > 0) {
        // Take an instance from the pool
        const instance = await factory.createReportingEngine();
        const afterTakingMetrics = factory.getMetrics();
        
        // Pool should have one less instance
        expect(afterTakingMetrics.pooledInstances).toBe(initialPooled - 1);
        
        // Return the instance to the pool
        await factory.returnToPool(instance);
        
        const afterReturningMetrics = factory.getMetrics();
        // Pool should be restored (within pool size limit)
        expect(afterReturningMetrics.pooledInstances).toBeGreaterThanOrEqual(initialPooled - 1);
        expect(afterReturningMetrics.pooledInstances).toBeLessThanOrEqual(factory.getStatus().config.poolSize);
      } else {
        // Skip test if no instances in pool
        console.log('Skipping pool reuse test - no instances in pool');
      }
    });

    it('should return instance to pool successfully', async () => {
      const instance = await factory.createReportingEngine();
      const initialPooled = factory.getMetrics().pooledInstances;
      
      await factory.returnToPool(instance);
      
      const finalPooled = factory.getMetrics().pooledInstances;
      expect(finalPooled).toBeGreaterThanOrEqual(initialPooled);
    });

    it('should destroy instance when pool is full', async () => {
      // Set small pool size
      factory.updateConfig({ poolSize: 1 });
      
      const instance1 = await factory.createReportingEngine();
      const instance2 = await factory.createReportingEngine();
      
      // Fill the pool
      await factory.returnToPool(instance1);
      
      const initialDestroyed = factory.getMetrics().instancesDestroyed;
      
      // This should destroy instance2 since pool is full
      await factory.returnToPool(instance2);
      
      const finalDestroyed = factory.getMetrics().instancesDestroyed;
      expect(finalDestroyed).toBeGreaterThan(initialDestroyed);
    });

    it('should handle pooling disabled gracefully', async () => {
      factory.updateConfig({ enablePooling: false });
      
      const instance = await factory.createReportingEngine();
      const initialDestroyed = factory.getMetrics().instancesDestroyed;
      
      await factory.returnToPool(instance);
      
      const finalDestroyed = factory.getMetrics().instancesDestroyed;
      expect(finalDestroyed).toBe(initialDestroyed + 1);
    });
  });

  // ============================================================================
  // INSTANCE DESTRUCTION TESTS
  // ============================================================================

  describe('Instance Destruction', () => {
    it('should destroy instance successfully', async () => {
      const instance = await factory.createReportingEngine();
      const initialMetrics = factory.getMetrics();
      
      await factory.destroyReportingEngine(instance);
      
      const updatedMetrics = factory.getMetrics();
      expect(updatedMetrics.instancesDestroyed).toBe(initialMetrics.instancesDestroyed + 1);
      expect(updatedMetrics.activeInstances).toBe(initialMetrics.activeInstances - 1);
    });

    it('should handle destruction errors gracefully', async () => {
      const instance = await factory.createReportingEngine();
      
      // Mock shutdown to throw error
      jest.spyOn(instance, 'shutdown').mockRejectedValue(new Error('Shutdown error'));
      
      const initialErrors = factory.getMetrics().errorCount;
      
      await expect(factory.destroyReportingEngine(instance))
        .rejects.toThrow('Shutdown error');
      
      const finalErrors = factory.getMetrics().errorCount;
      expect(finalErrors).toBeGreaterThan(initialErrors);
      
      // Restore original method
      jest.restoreAllMocks();
    });

    it('should remove instance from all tracking collections', async () => {
      const instance = await factory.createReportingEngine();
      const initialActive = factory.getMetrics().activeInstances;
      
      await factory.destroyReportingEngine(instance);
      
      const finalActive = factory.getMetrics().activeInstances;
      expect(finalActive).toBe(initialActive - 1);
    });
  });

  // ============================================================================
  // CONFIGURATION MANAGEMENT TESTS
  // ============================================================================

  describe('Configuration Management', () => {
    it('should update configuration successfully', () => {
      const newConfig: Partial<TReportingEngineFactoryConfig> = {
        maxInstances: 25,
        enablePooling: false,
        poolSize: 5
      };
      
      factory.updateConfig(newConfig);
      
      const status = factory.getStatus();
      expect(status.config.maxInstances).toBe(25);
      expect(status.config.enablePooling).toBe(false);
      expect(status.config.poolSize).toBe(5);
    });

    it('should maintain partial configuration updates', () => {
      const originalConfig = factory.getStatus().config;
      
      factory.updateConfig({ maxInstances: 100 });
      
      const updatedConfig = factory.getStatus().config;
      expect(updatedConfig.maxInstances).toBe(100);
      expect(updatedConfig.enablePooling).toBe(originalConfig.enablePooling);
      expect(updatedConfig.poolSize).toBe(originalConfig.poolSize);
    });
  });

  // ============================================================================
  // METRICS AND MONITORING TESTS
  // ============================================================================

  describe('Metrics and Monitoring', () => {
    it('should provide comprehensive metrics', () => {
      const metrics = factory.getMetrics();
      
      expect(metrics.instancesCreated).toBeDefined();
      expect(metrics.instancesDestroyed).toBeDefined();
      expect(metrics.activeInstances).toBeDefined();
      expect(metrics.pooledInstances).toBeDefined();
      expect(metrics.totalCreationTime).toBeDefined();
      expect(metrics.averageCreationTime).toBeDefined();
      expect(metrics.errorCount).toBeDefined();
      expect(metrics.factoryUptime).toBeDefined();
    });

    it('should calculate average creation time correctly', async () => {
      const initialMetrics = factory.getMetrics();
      
      const instance1 = await factory.createReportingEngine();
      const instance2 = await factory.createReportingEngine();
      
      const updatedMetrics = factory.getMetrics();
      
      if (updatedMetrics.instancesCreated > 0) {
        expect(updatedMetrics.averageCreationTime).toBe(
          updatedMetrics.totalCreationTime / updatedMetrics.instancesCreated
        );
      }
      
      await factory.destroyReportingEngine(instance1);
      await factory.destroyReportingEngine(instance2);
    });

    it('should track factory uptime', async () => {
      const metrics1 = factory.getMetrics();
      
      await wait(100); // Wait 100ms
      
      const metrics2 = factory.getMetrics();
      
      expect(metrics2.factoryUptime).toBeGreaterThan(metrics1.factoryUptime);
    });
  });

  // ============================================================================
  // HEALTH VALIDATION TESTS
  // ============================================================================

  describe('Health Validation', () => {
    it('should validate factory health successfully', async () => {
      const validation = await factory.validateHealth();
      
      expect(validation).toBeDefined();
      expect(validation.validationId).toBeDefined();
      expect(validation.componentId).toBe('governance-rule-reporting-engine-factory');
      expect(validation.status).toBe('valid');
      expect(validation.checks.length).toBeGreaterThan(0);
    });

    it('should detect initialization issues', async () => {
      // Create a fresh factory that might not be initialized
      const uninitializedFactory = new (GovernanceRuleReportingEngineFactory as any)();
      
      const validation = await uninitializedFactory.validateHealth();
      
      expect(validation.overallScore).toBe(0);
      expect(validation.status).toBe('invalid');
    });

    it('should detect high instance usage', async () => {
      // Set low limit and create instances near the limit
      factory.updateConfig({ maxInstances: 2 });
      
      const instance1 = await factory.createReportingEngine();
      const instance2 = await factory.createReportingEngine();
      
      const validation = await factory.validateHealth();
      
      // Should detect high usage (100%)
      const capacityCheck = validation.checks.find(c => c.checkId === 'instance-capacity');
      expect(capacityCheck).toBeDefined();
      
      await factory.destroyReportingEngine(instance1);
      await factory.destroyReportingEngine(instance2);
    });

    it('should validate error rates', async () => {
      const validation = await factory.validateHealth();
      
      const errorCheck = validation.checks.find(c => c.checkId === 'error-rate');
      expect(errorCheck).toBeDefined();
      expect(errorCheck?.status).toBe('passed');
    });
  });

  // ============================================================================
  // STATIC METHODS TESTS
  // ============================================================================

  describe('Static Methods', () => {
    it('should create instance using static method', async () => {
      const instance = await GovernanceRuleReportingEngineFactory.createReportingEngine();
      
      expect(instance).toBeInstanceOf(GovernanceRuleReportingEngine);
      expect(instance.isReady()).toBe(true);
      
      // Clean up
      const factoryInstance = await GovernanceRuleReportingEngineFactory.getInstance();
      await factoryInstance.destroyReportingEngine(instance);
    });

    it('should create instance with config using static method', async () => {
      const config = { testConfig: 'static-test' };
      const instance = await GovernanceRuleReportingEngineFactory.createReportingEngine(config);
      
      expect(instance).toBeDefined();
      expect(instance.isReady()).toBe(true);
      
      // Clean up
      const factoryInstance = await GovernanceRuleReportingEngineFactory.getInstance();
      await factoryInstance.destroyReportingEngine(instance);
    });
  });

  // ============================================================================
  // FACTORY SHUTDOWN TESTS
  // ============================================================================

  describe('Factory Shutdown', () => {
    it('should shutdown factory gracefully', async () => {
      const testFactory = await GovernanceRuleReportingEngineFactory.getInstance();
      const instance = await testFactory.createReportingEngine();
      
      await expect(testFactory.shutdown()).resolves.not.toThrow();
      
      const status = testFactory.getStatus();
      expect(status.isInitialized).toBe(false);
    });

    it('should shutdown all active instances', async () => {
      const testFactory = await GovernanceRuleReportingEngineFactory.getInstance();
      
      const instance1 = await testFactory.createReportingEngine();
      const instance2 = await testFactory.createReportingEngine();
      
      const metricsBeforeShutdown = testFactory.getMetrics();
      expect(metricsBeforeShutdown.activeInstances).toBe(2);
      
      await testFactory.shutdown();
      
      // Factory should be reset
      const status = testFactory.getStatus();
      expect(status.isInitialized).toBe(false);
    });

    it('should handle shutdown errors gracefully', async () => {
      const testFactory = await GovernanceRuleReportingEngineFactory.getInstance();
      const instance = await testFactory.createReportingEngine();
      
      // Mock instance shutdown to throw error
      jest.spyOn(instance, 'shutdown').mockRejectedValue(new Error('Instance shutdown error'));
      
      // Should not throw, should handle error gracefully
      await expect(testFactory.shutdown()).resolves.not.toThrow();
      
      jest.restoreAllMocks();
    });

    it('should clear all internal state on shutdown', async () => {
      const testFactory = await GovernanceRuleReportingEngineFactory.getInstance();
      
      await testFactory.createReportingEngine();
      await testFactory.createReportingEngine();
      
      await testFactory.shutdown();
      
      const metrics = testFactory.getMetrics();
      expect(metrics.activeInstances).toBe(0);
      expect(metrics.pooledInstances).toBe(0);
    });
  });

  // ============================================================================
  // CONCURRENT ACCESS TESTS
  // ============================================================================

  describe('Concurrent Access', () => {
    it('should handle concurrent instance creation', async () => {
      const createPromises = Array.from({ length: 5 }, () =>
        factory.createReportingEngine()
      );
      
      const instances = await Promise.all(createPromises);
      
      expect(instances).toHaveLength(5);
      instances.forEach(instance => {
        expect(instance).toBeInstanceOf(GovernanceRuleReportingEngine);
        expect(instance.isReady()).toBe(true);
      });
      
      // Clean up
      await Promise.all(instances.map(instance => 
        factory.destroyReportingEngine(instance)
      ));
    });

    it('should handle concurrent destruction', async () => {
      const instances = await Promise.all([
        factory.createReportingEngine(),
        factory.createReportingEngine(),
        factory.createReportingEngine()
      ]);
      
      const destructionPromises = instances.map(instance =>
        factory.destroyReportingEngine(instance)
      );
      
      await expect(Promise.all(destructionPromises)).resolves.not.toThrow();
    });

    it('should maintain thread safety during metrics access', async () => {
      const metricsPromises = Array.from({ length: 10 }, () =>
        factory.getMetrics()
      );
      
      const metricsResults = await Promise.all(metricsPromises);
      
      // All metrics should be consistent (same point in time)
      metricsResults.forEach(metrics => {
        expect(metrics.instancesCreated).toBeDefined();
        expect(metrics.activeInstances).toBeDefined();
      });
    });
  });

  // ============================================================================
  // EXPORTED FACTORY INSTANCE TESTS
  // ============================================================================

  describe('Exported Factory Instance', () => {
    it('should provide access to factory through exported instance', async () => {
      const exportedFactory = await reportingEngineFactory();
      
      expect(exportedFactory).toBeInstanceOf(GovernanceRuleReportingEngineFactory);
      expect(exportedFactory.getStatus().isInitialized).toBe(true);
    });

    it('should be the same instance as getInstance()', async () => {
      const exportedFactory = await reportingEngineFactory();
      const getInstanceFactory = await GovernanceRuleReportingEngineFactory.getInstance();
      
      expect(exportedFactory).toBe(getInstanceFactory);
    });
  });
}); 