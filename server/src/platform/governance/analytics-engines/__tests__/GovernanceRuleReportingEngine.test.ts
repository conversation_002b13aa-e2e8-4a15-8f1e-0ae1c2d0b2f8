/**
 * ============================================================================
 * AI CONTEXT: Rule Reporting Engine Tests - Comprehensive test coverage
 * Purpose: Complete test suite for Phase 2 Rule Reporting Engine implementation
 * Complexity: Complex - Full interface coverage and enterprise test patterns
 * AI Navigation: 6 logical sections, testing all reporting capabilities
 * ============================================================================
 */

import { describe, beforeEach, afterEach, it, expect, jest } from '@jest/globals';
import {
  GovernanceRuleReportingEngine,
  IReportingEngine,
  IReportingService,
  TReportFormat,
  TReportOptions,
  TBatchReportOptions,
  TReportResult,
  TBatchReportResult,
  TScheduledReportResult,
  TCustomReportResult,
  TReportTemplate,
  TReportSchedule,
  TReportConfiguration,
  TReportDelivery,
  TReportingMetrics,
  TExportFormat
} from '../GovernanceRuleReportingEngine';

import {
  TValidationResult,
  TMetrics
} from '../../../../../../shared/src/types/platform/tracking/tracking-types';

// ============================================================================
// TEST SETUP AND UTILITIES
// ============================================================================

/**
 * Create test report options
 */
function createReportOptions(): TReportOptions {
  return {
    includeMetrics: true,
    includeCharts: true,
    includeRawData: false,
    includeRecommendations: true,
    includeTrends: true,
    timeRange: {
      startDate: new Date('2025-01-01'),
      endDate: new Date('2025-12-31'),
      granularity: 'day'
    },
    aggregationLevel: 'detailed',
    filterCriteria: [],
    customizations: {},
    metadata: {}
  };
}

/**
 * Create test batch report options
 */
function createBatchReportOptions(): TBatchReportOptions {
  return {
    reportOptions: createReportOptions(),
    consolidationType: 'individual',
    parallelExecution: true,
    maxConcurrency: 5,
    deliveryMethod: 'immediate',
    notifications: [],
    metadata: {}
  };
}

/**
 * Create test report template
 */
function createReportTemplate(): TReportTemplate {
  return {
    templateId: 'test-template-001',
    name: 'Test Report Template',
    description: 'Template for testing purposes',
    category: 'standard',
    format: 'pdf',
    structure: {
      sections: ['header', 'content', 'footer']
    },
    parameters: [],
    styling: {
      theme: 'professional'
    },
    metadata: {},
    version: '1.0.0',
    createdAt: new Date(),
    updatedAt: new Date()
  };
}

/**
 * Create test report schedule
 */
function createReportSchedule(): TReportSchedule {
  return {
    scheduleId: 'test-schedule-001',
    name: 'Test Schedule',
    description: 'Scheduled test report',
    reportTemplate: createReportTemplate(),
    cronExpression: '0 0 * * *',
    timezone: 'UTC',
    enabled: true,
    nextExecution: new Date(),
    deliveryConfig: createReportDelivery(),
    metadata: {}
  };
}

/**
 * Create test report delivery config
 */
function createReportDelivery(): TReportDelivery {
  return {
    deliveryId: 'test-delivery-001',
    method: 'email',
    destination: {
      email: '<EMAIL>'
    },
    retryPolicy: {
      maxRetries: 3,
      retryDelay: 1000
    },
    metadata: {}
  };
}

/**
 * Create test report configuration
 */
function createReportConfiguration(): TReportConfiguration {
  return {
    configId: 'test-config-001',
    reportType: 'analytical',
    format: 'pdf',
    template: createReportTemplate(),
    dataSource: {
      type: 'governance-rules'
    },
    processing: {
      aggregation: 'detailed'
    },
    output: {
      compression: false
    },
    delivery: createReportDelivery(),
    metadata: {}
  };
}

// ============================================================================
// MAIN TEST SUITE
// ============================================================================

describe('GovernanceRuleReportingEngine', () => {
  let reportingEngine: GovernanceRuleReportingEngine;

  // ============================================================================
  // SETUP AND TEARDOWN
  // ============================================================================

  beforeEach(async () => {
    reportingEngine = new GovernanceRuleReportingEngine();
    await reportingEngine.initialize();
  });

  afterEach(async () => {
    await reportingEngine.shutdown();
  });

  // ============================================================================
  // CORE INITIALIZATION TESTS
  // ============================================================================

  describe('Core Initialization', () => {
    it('should initialize successfully', async () => {
      const engine = new GovernanceRuleReportingEngine();
      await expect(engine.initialize()).resolves.not.toThrow();
      await engine.shutdown();
    });

    it('should have correct service properties', () => {
      expect(reportingEngine.id).toBe('governance-rule-reporting-engine');
      expect(reportingEngine.authority).toBe('President & CEO, E.Z. Consultancy');
    });

    it('should be ready after initialization', () => {
      expect(reportingEngine.isReady()).toBe(true);
    });

    it('should validate successfully', async () => {
      const validation = await reportingEngine.validate();
      expect(validation.status).toBe('valid');
      expect(validation.overallScore).toBeGreaterThan(0);
    });

    it('should return metrics', async () => {
      const metrics = await reportingEngine.getMetrics();
      expect(metrics).toBeDefined();
      expect(metrics.service).toBe('governance-rule-reporting-engine');
      expect(metrics.custom).toBeDefined();
    });
  });

  // ============================================================================
  // IREPORTINGENGINE INTERFACE TESTS
  // ============================================================================

  describe('IReportingEngine Interface', () => {
    describe('generateRuleReport', () => {
      it('should generate PDF report successfully', async () => {
        const ruleId = 'test-rule-001';
        const format: TReportFormat = 'pdf';
        const options = createReportOptions();

        const result = await reportingEngine.generateRuleReport(ruleId, format, options);

        expect(result).toBeDefined();
        expect(result.reportId).toBeDefined();
        expect(result.ruleId).toBe(ruleId);
        expect(result.format).toBe(format);
        expect(result.status).toBe('completed');
        expect(result.data).toBeDefined();
        expect(result.metadata.size).toBeGreaterThan(0);
        expect(result.metadata.checksum).toBeDefined();
      });

      it('should generate CSV report successfully', async () => {
        const ruleId = 'test-rule-002';
        const format: TReportFormat = 'csv';
        const options = createReportOptions();

        const result = await reportingEngine.generateRuleReport(ruleId, format, options);

        expect(result.format).toBe(format);
        expect(result.status).toBe('completed');
        expect(typeof result.data).toBe('string');
      });

      it('should generate JSON report successfully', async () => {
        const ruleId = 'test-rule-003';
        const format: TReportFormat = 'json';
        const options = createReportOptions();

        const result = await reportingEngine.generateRuleReport(ruleId, format, options);

        expect(result.format).toBe(format);
        expect(result.status).toBe('completed');
        expect(typeof result.data).toBe('object');
      });

      it('should generate XML report successfully', async () => {
        const ruleId = 'test-rule-004';
        const format: TReportFormat = 'xml';
        const options = createReportOptions();

        const result = await reportingEngine.generateRuleReport(ruleId, format, options);

        expect(result.format).toBe(format);
        expect(result.status).toBe('completed');
        expect(typeof result.data).toBe('string');
        expect(result.data).toContain('<?xml');
      });

      it('should generate Excel report successfully', async () => {
        const ruleId = 'test-rule-005';
        const format: TReportFormat = 'excel';
        const options = createReportOptions();

        const result = await reportingEngine.generateRuleReport(ruleId, format, options);

        expect(result.format).toBe(format);
        expect(result.status).toBe('completed');
        expect(Buffer.isBuffer(result.data)).toBe(true);
      });

      it('should generate Markdown report successfully', async () => {
        const ruleId = 'test-rule-006';
        const format: TReportFormat = 'markdown';
        const options = createReportOptions();

        const result = await reportingEngine.generateRuleReport(ruleId, format, options);

        expect(result.format).toBe(format);
        expect(result.status).toBe('completed');
        expect(typeof result.data).toBe('string');
        expect(result.data).toContain('#');
      });

      it('should generate HTML report successfully', async () => {
        const ruleId = 'test-rule-007';
        const format: TReportFormat = 'html';
        const options = createReportOptions();

        const result = await reportingEngine.generateRuleReport(ruleId, format, options);

        expect(result.format).toBe(format);
        expect(result.status).toBe('completed');
        expect(typeof result.data).toBe('string');
        expect(result.data).toContain('<html');
      });

      it('should generate text report successfully', async () => {
        const ruleId = 'test-rule-008';
        const format: TReportFormat = 'txt';
        const options = createReportOptions();

        const result = await reportingEngine.generateRuleReport(ruleId, format, options);

        expect(result.format).toBe(format);
        expect(result.status).toBe('completed');
        expect(typeof result.data).toBe('string');
      });

      it('should throw error for unsupported format', async () => {
        const ruleId = 'test-rule-009';
        const format = 'unsupported' as TReportFormat;
        const options = createReportOptions();

        await expect(reportingEngine.generateRuleReport(ruleId, format, options))
          .rejects.toThrow('Unsupported report format: unsupported');
      });
    });

    describe('generateBatchReports', () => {
      it('should generate batch reports successfully', async () => {
        const ruleIds = ['rule-001', 'rule-002', 'rule-003'];
        const format: TReportFormat = 'json';
        const options = createBatchReportOptions();

        const result = await reportingEngine.generateBatchReports(ruleIds, format, options);

        expect(result).toBeDefined();
        expect(result.batchId).toBeDefined();
        expect(result.totalReports).toBe(3);
        expect(result.successfulReports).toBe(3);
        expect(result.failedReports).toBe(0);
        expect(result.reports).toHaveLength(3);
        expect(result.status).toBe('completed');
      });

      it('should handle partial batch success', async () => {
        // This would require mocking failures, for now we'll test the structure
        const ruleIds = ['rule-001'];
        const format: TReportFormat = 'pdf';
        const options = createBatchReportOptions();

        const result = await reportingEngine.generateBatchReports(ruleIds, format, options);

        expect(result.totalReports).toBe(1);
        expect(result.successfulReports + result.failedReports).toBe(result.totalReports);
      });
    });

    describe('generateScheduledReport', () => {
      it('should generate scheduled report successfully', async () => {
        const schedule = createReportSchedule();

        const result = await reportingEngine.generateScheduledReport(schedule);

        expect(result).toBeDefined();
        expect(result.scheduleId).toBe(schedule.scheduleId);
        expect(result.reportId).toBeDefined();
        expect(result.status).toBe('completed');
        expect(result.nextExecution).toBeDefined();
      });
    });

    describe('generateCustomReport', () => {
      it('should generate custom report from template', async () => {
        const template = createReportTemplate();
        const data = { testData: 'test-value' };

        const result = await reportingEngine.generateCustomReport(template, data);

        expect(result).toBeDefined();
        expect(result.reportId).toBeDefined();
        expect(result.templateId).toBe(template.templateId);
        expect(result.status).toBe('completed');
      });
    });

    describe('exportReportData', () => {
      it('should export report as JSON', async () => {
        // First generate a report
        const reportResult = await reportingEngine.generateRuleReport(
          'test-rule-export', 'json', createReportOptions()
        );

        const exportResult = await reportingEngine.exportReportData(
          reportResult.reportId, 'json'
        );

        expect(exportResult).toBeDefined();
        expect(exportResult.exportId).toBeDefined();
        expect(exportResult.reportId).toBe(reportResult.reportId);
        expect(exportResult.format).toBe('json');
        expect(exportResult.data).toBeDefined();
      });

      it('should export report as CSV', async () => {
        const reportResult = await reportingEngine.generateRuleReport(
          'test-rule-export-csv', 'json', createReportOptions()
        );

        const exportResult = await reportingEngine.exportReportData(
          reportResult.reportId, 'csv'
        );

        expect(exportResult.format).toBe('csv');
        expect(typeof exportResult.data).toBe('string');
      });

      it('should throw error for non-existent report', async () => {
        await expect(reportingEngine.exportReportData('non-existent', 'json'))
          .rejects.toThrow('Report not found: non-existent');
      });
    });

    describe('validateReportConfiguration', () => {
      it('should validate report configuration successfully', async () => {
        const config = createReportConfiguration();

        const validation = await reportingEngine.validateReportConfiguration(config);

        expect(validation).toBeDefined();
        expect(validation.status).toBe('valid');
        expect(validation.overallScore).toBe(100);
        expect(validation.checks).toHaveLength(1);
      });
    });
  });

  // ============================================================================
  // IREPORTINGSERVICE INTERFACE TESTS
  // ============================================================================

  describe('IReportingService Interface', () => {
    describe('processReportingData', () => {
      it('should process reporting data successfully', async () => {
        const data = { testData: 'test-value' };

        const result = await reportingEngine.processReportingData(data);

        expect(result).toBeDefined();
        expect(result.processed).toBe(true);
        expect(result.timestamp).toBeDefined();
      });
    });

    describe('generateReport', () => {
      it('should generate report from configuration', async () => {
        const config = { reportType: 'test', format: 'json' };

        const result = await reportingEngine.generateReport(config);

        expect(result).toBeDefined();
        expect(result.reportId).toBeDefined();
        expect(result.status).toBe('completed');
      });
    });

    describe('getReportingMetrics', () => {
      it('should return comprehensive reporting metrics', async () => {
        const metrics = await reportingEngine.getReportingMetrics();

        expect(metrics).toBeDefined();
        expect(metrics.totalReports).toBeDefined();
        expect(metrics.successfulReports).toBeDefined();
        expect(metrics.failedReports).toBeDefined();
        expect(metrics.averageGenerationTime).toBeDefined();
        expect(metrics.reportsByFormat).toBeDefined();
        expect(metrics.reportsByStatus).toBeDefined();
        expect(metrics.lastUpdated).toBeDefined();
      });
    });

    describe('cacheReportResults', () => {
      it('should cache report results successfully', async () => {
        const reportId = 'test-cache-report';
        const data = { testData: 'cached-value' };

        await expect(reportingEngine.cacheReportResults(reportId, data))
          .resolves.not.toThrow();
      });
    });

    describe('deliverReport', () => {
      it('should deliver report successfully', async () => {
        const reportId = 'test-delivery-report';
        const delivery = createReportDelivery();

        const result = await reportingEngine.deliverReport(reportId, delivery);

        expect(result).toBeDefined();
        expect(result.deliveryId).toBeDefined();
        expect(result.reportId).toBe(reportId);
        expect(result.status).toBe('delivered');
        expect(result.method).toBe(delivery.method);
      });
    });
  });

  // ============================================================================
  // PERFORMANCE AND RELIABILITY TESTS
  // ============================================================================

  describe('Performance and Reliability', () => {
    it('should handle multiple concurrent report generations', async () => {
      const reportPromises = Array.from({ length: 5 }, (_, i) =>
        reportingEngine.generateRuleReport(
          `concurrent-rule-${i}`,
          'json',
          createReportOptions()
        )
      );

      const results = await Promise.all(reportPromises);

      expect(results).toHaveLength(5);
      results.forEach((result, index) => {
        expect(result.ruleId).toBe(`concurrent-rule-${index}`);
        expect(result.status).toBe('completed');
      });
    });

    it('should generate unique report IDs', async () => {
      const results = await Promise.all([
        reportingEngine.generateRuleReport('rule-1', 'json', createReportOptions()),
        reportingEngine.generateRuleReport('rule-2', 'json', createReportOptions()),
        reportingEngine.generateRuleReport('rule-3', 'json', createReportOptions())
      ]);

      const reportIds = results.map(r => r.reportId);
      const uniqueIds = new Set(reportIds);
      expect(uniqueIds.size).toBe(reportIds.length);
    });

    it('should handle large batch reports efficiently', async () => {
      const ruleIds = Array.from({ length: 20 }, (_, i) => `batch-rule-${i}`);
      const options = createBatchReportOptions();

      const startTime = Date.now();
      const result = await reportingEngine.generateBatchReports(ruleIds, 'json', options);
      const endTime = Date.now();

      expect(result.totalReports).toBe(20);
      expect(result.successfulReports).toBe(20);
      expect(endTime - startTime).toBeLessThan(10000); // Should complete within 10 seconds
    });
  });

  // ============================================================================
  // ERROR HANDLING TESTS
  // ============================================================================

  describe('Error Handling', () => {
    it('should handle invalid report format gracefully', async () => {
      await expect(reportingEngine.generateRuleReport(
        'test-rule',
        'invalid' as TReportFormat,
        createReportOptions()
      )).rejects.toThrow();
    });

    it('should handle empty rule ID', async () => {
      await expect(reportingEngine.generateRuleReport(
        '',
        'json',
        createReportOptions()
      )).rejects.toThrow('Rule ID is required for report generation');
    });

    it('should handle null options gracefully', async () => {
      await expect(reportingEngine.generateRuleReport(
        'test-rule',
        'json',
        null as any
      )).rejects.toThrow();
    });
  });

  // ============================================================================
  // INTEGRATION TESTS
  // ============================================================================

  describe('Integration Tests', () => {
    it('should integrate metrics tracking with report generation', async () => {
      const initialMetrics = await reportingEngine.getReportingMetrics();
      
      await reportingEngine.generateRuleReport('integration-rule', 'json', createReportOptions());
      
      const updatedMetrics = await reportingEngine.getReportingMetrics();
      
      expect(updatedMetrics.totalReports).toBe(initialMetrics.totalReports + 1);
      expect(updatedMetrics.successfulReports).toBe(initialMetrics.successfulReports + 1);
      expect(updatedMetrics.reportsByFormat.json).toBe(initialMetrics.reportsByFormat.json + 1);
    });

    it('should maintain report cache consistency', async () => {
      const reportId = 'cache-consistency-test';
      const cacheData = { test: 'cache-data' };

      await reportingEngine.cacheReportResults(reportId, cacheData);
      
      // Generate a report and verify caching doesn't interfere
      const report = await reportingEngine.generateRuleReport(
        'cache-test-rule',
        'json',
        createReportOptions()
      );

      expect(report).toBeDefined();
      expect(report.status).toBe('completed');
    });

    it('should properly sequence scheduled reports', async () => {
      const schedule = createReportSchedule();
      
      const result = await reportingEngine.generateScheduledReport(schedule);
      
      expect(result.executedAt).toBeDefined();
      expect(result.nextExecution).toBeDefined();
      if (result.nextExecution) {
        expect(result.nextExecution.getTime()).toBeGreaterThan(result.executedAt.getTime());
      }
    });
  });

  // ============================================================================
  // BASETRACKINGSERVICE COMPLIANCE TESTS
  // ============================================================================

  describe('BaseTrackingService Compliance', () => {
    it('should implement required abstract methods', () => {
      expect(typeof (reportingEngine as any).getServiceName).toBe('function');
      expect(typeof (reportingEngine as any).getServiceVersion).toBe('function');
      expect(typeof (reportingEngine as any).doInitialize).toBe('function');
      expect(typeof (reportingEngine as any).doTrack).toBe('function');
      expect(typeof (reportingEngine as any).doValidate).toBe('function');
    });

    it('should return correct service name and version', () => {
      expect((reportingEngine as any).getServiceName()).toBe('governance-rule-reporting-engine');
      expect((reportingEngine as any).getServiceVersion()).toBe('1.0.0');
    });

    it('should handle tracking data', async () => {
      const trackingData = { operation: 'test-tracking' };
      
      await expect((reportingEngine as any).doTrack(trackingData))
        .resolves.not.toThrow();
    });

    it('should validate service state', async () => {
      const validation = await (reportingEngine as any).doValidate();
      
      expect(validation).toBeDefined();
      expect(validation.validationId).toBeDefined();
      expect(validation.componentId).toBe('governance-rule-reporting-engine');
      expect(validation.status).toBe('valid');
    });
  });

  // ============================================================================
  // CLEANUP AND SHUTDOWN TESTS
  // ============================================================================

  describe('Cleanup and Shutdown', () => {
    it('should shutdown gracefully', async () => {
      const engine = new GovernanceRuleReportingEngine();
      await engine.initialize();
      
      await expect(engine.shutdown()).resolves.not.toThrow();
    });

    it('should handle shutdown with active reports', async () => {
      const engine = new GovernanceRuleReportingEngine();
      await engine.initialize();
      
      // Start a report generation
      const reportPromise = engine.generateRuleReport(
        'shutdown-test-rule',
        'json',
        createReportOptions()
      );
      
      // Shutdown while report is being generated
      await Promise.all([
        reportPromise,
        engine.shutdown()
      ]);
      
      // Should not throw
    });

    it('should clear internal state on shutdown', async () => {
      const engine = new GovernanceRuleReportingEngine();
      await engine.initialize();
      
      // Generate some reports
      await engine.generateRuleReport('cleanup-rule-1', 'json', createReportOptions());
      await engine.generateRuleReport('cleanup-rule-2', 'json', createReportOptions());
      
      const metricsBeforeShutdown = await engine.getReportingMetrics();
      expect(metricsBeforeShutdown.totalReports).toBeGreaterThan(0);
      
      await engine.shutdown();
      
      // Verify the engine is no longer ready
      expect(engine.isReady()).toBe(false);
    });
  });
});

// ============================================================================
// TYPE CHECKING TESTS
// ============================================================================

describe('Type Checking and Interface Compliance', () => {
  let reportingEngine: GovernanceRuleReportingEngine;

  beforeEach(async () => {
    reportingEngine = new GovernanceRuleReportingEngine();
    await reportingEngine.initialize();
  });

  afterEach(async () => {
    await reportingEngine.shutdown();
  });

  it('should implement IReportingEngine interface', () => {
    const engine: IReportingEngine = reportingEngine;
    
    expect(typeof engine.generateRuleReport).toBe('function');
    expect(typeof engine.generateBatchReports).toBe('function');
    expect(typeof engine.generateScheduledReport).toBe('function');
    expect(typeof engine.generateCustomReport).toBe('function');
    expect(typeof engine.exportReportData).toBe('function');
    expect(typeof engine.validateReportConfiguration).toBe('function');
  });

  it('should implement IReportingService interface', () => {
    const service: IReportingService = reportingEngine;
    
    expect(typeof service.processReportingData).toBe('function');
    expect(typeof service.generateReport).toBe('function');
    expect(typeof service.getReportingMetrics).toBe('function');
    expect(typeof service.cacheReportResults).toBe('function');
    expect(typeof service.deliverReport).toBe('function');
  });

  it('should have correct type definitions for return types', async () => {
    const reportResult = await reportingEngine.generateRuleReport(
      'type-test-rule',
      'json',
      createReportOptions()
    );

    // Verify TReportResult structure
    expect(typeof reportResult.reportId).toBe('string');
    expect(typeof reportResult.ruleId).toBe('string');
    expect(typeof reportResult.format).toBe('string');
    expect(reportResult.generatedAt).toBeInstanceOf(Date);
    expect(typeof reportResult.status).toBe('string');
    expect(reportResult.metadata).toBeDefined();
    expect(typeof reportResult.metadata.size).toBe('number');
    expect(typeof reportResult.metadata.checksum).toBe('string');
  });
}); 