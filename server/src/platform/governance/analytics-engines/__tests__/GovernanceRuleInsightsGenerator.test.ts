/**
 * @file Governance Rule Insights Generator Tests
 * @filepath server/src/platform/governance/analytics-engines/__tests__/GovernanceRuleInsightsGenerator.test.ts
 * @task-id G-TSK-06.SUB-06.1.TEST-05
 * @component governance-rule-insights-generator-tests
 * @reference foundation-context.ANALYTICS.012
 * @template on-demand-creation-with-latest-standards
 * @tier T2
 * @context foundation-context
 * @category Testing
 * @created 2025-07-01 14:00:00 +03
 * @modified 2025-07-01 14:00:00 +03
 * 
 * @description
 * Comprehensive unit tests for Governance Rule Insights Generator
 * covering all interfaces and functionality requirements for Phase 3 insights.
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level component-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-tracking-architecture
 * @governance-dcr DCR-foundation-001-tracking-development
 * @governance-status active
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on server/src/platform/governance/analytics-engines/GovernanceRuleInsightsGenerator
 * @enables testing/server/platform/governance/analytics-engines
 * @related-contexts foundation-context, governance-context, testing-context
 * @governance-impact insights-testing, governance-validation
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import {
  GovernanceRuleInsightsGenerator,
  IInsightsGenerator,
  IAnalyticsService,
  TInsightsOptions,
  TAnalyticsTimeRange,
  TAnalyticsPeriod,
  TRuleInsights,
  TUsagePatternAnalysis,
  TAnomalyDetectionResult,
  TTrendAnalysis,
  TInsightsReport,
  TPredictiveInsights,
  TComparativeInsights,
  TPerformanceInsights,
  TBusinessInsights,
  TServiceHealth,
  TServiceStatus,
  TInsightsGeneratorConfig
} from '../GovernanceRuleInsightsGenerator';

// ============================================================================
// TEST SETUP AND UTILITIES
// ============================================================================

/**
 * Create test insights options
 */
function createTestInsightsOptions(): TInsightsOptions {
  return {
    depth: 'standard',
    includePerformance: true,
    includeUsage: true,
    includeCompliance: true,
    includePredictions: true,
    confidenceThreshold: 0.8
  };
}

/**
 * Create test time range
 */
function createTestTimeRange(): TAnalyticsTimeRange {
  const now = new Date();
  const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
  
  return {
    startTime: oneWeekAgo,
    endTime: now
  };
}

/**
 * Create test insights generator configuration
 */
function createTestInsightsConfig(): TInsightsGeneratorConfig {
  return {
    maxConcurrentAnalyses: 3,
    analysisTimeout: 30000,
    enablePredictiveAnalysis: true,
    enableAnomalyDetection: true,
    confidenceThreshold: 0.75,
    dataRetentionPeriod: 86400000,
    insightDepth: 'comprehensive',
    visualization: {
      enableCharts: true,
      chartTypes: ['line', 'bar', 'pie'],
      interactivity: true
    }
  };
}

/**
 * Wait for specified duration
 */
function waitFor(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// ============================================================================
// MAIN TEST SUITE
// ============================================================================

describe('GovernanceRuleInsightsGenerator', () => {
  let insightsGenerator: GovernanceRuleInsightsGenerator;

  beforeEach(async () => {
    insightsGenerator = new GovernanceRuleInsightsGenerator();
    await insightsGenerator.initialize();
  });

  afterEach(async () => {
    if (insightsGenerator) {
      await insightsGenerator.shutdown();
    }
  });

  // ============================================================================
  // CORE FUNCTIONALITY TESTS
  // ============================================================================

  describe('Core Functionality', () => {
    it('should initialize successfully', async () => {
      expect(insightsGenerator).toBeDefined();
      expect(insightsGenerator).toBeInstanceOf(GovernanceRuleInsightsGenerator);
      expect(insightsGenerator.isReady()).toBe(true);
    });

    it('should validate successfully', async () => {
      const validation = await insightsGenerator.validate();
      
      expect(validation).toBeDefined();
      expect(validation.componentId).toBe('governance-rule-insights-generator');
      expect(validation.status).toBe('valid');
      expect(validation.overallScore).toBeGreaterThan(80);
      expect(validation.checks).toHaveLength(0);
    });

    it('should provide metrics', async () => {
      const metrics = await insightsGenerator.getMetrics();
      
      expect(metrics).toBeDefined();
      expect(metrics.service).toBe('governance-rule-insights-generator');
      expect(metrics.custom).toBeDefined();
      expect(metrics.custom.insightsGenerated).toBeDefined();
      expect(metrics.custom.rulesAnalyzed).toBeDefined();
    });

    it('should shutdown gracefully', async () => {
      await expect(insightsGenerator.shutdown()).resolves.not.toThrow();
    });
  });

  // ============================================================================
  // SERVICE LIFECYCLE TESTS
  // ============================================================================

  describe('Service Lifecycle', () => {
    it('should start successfully', async () => {
      await expect(insightsGenerator.start()).resolves.not.toThrow();
      
      const status = await insightsGenerator.getStatus();
      expect(status.state).toBe('running');
    });

    it('should stop successfully', async () => {
      await insightsGenerator.start();
      await expect(insightsGenerator.stop()).resolves.not.toThrow();
      
      const status = await insightsGenerator.getStatus();
      expect(status.state).toBe('stopped');
    });

    it('should handle multiple start calls', async () => {
      await insightsGenerator.start();
      await expect(insightsGenerator.start()).resolves.not.toThrow();
      
      const status = await insightsGenerator.getStatus();
      expect(status.state).toBe('running');
    });

    it('should get health status', async () => {
      const health = await insightsGenerator.getHealth();
      
      expect(health).toBeDefined();
      expect(health.status).toBe('healthy');
      expect(health.checks).toBeInstanceOf(Array);
      expect(health.checks).toHaveLength(3);
      expect(health.lastChecked).toBeInstanceOf(Date);
    });
  });

  // ============================================================================
  // CORE INSIGHTS GENERATION TESTS
  // ============================================================================

  describe('Core Insights Generation', () => {
    beforeEach(async () => {
      await insightsGenerator.start();
    });

    describe('generateRuleInsights', () => {
      it('should generate rule insights successfully', async () => {
        const ruleId = 'test-rule-001';
        const options = createTestInsightsOptions();

        const insights = await insightsGenerator.generateRuleInsights(ruleId, options);

        expect(insights).toBeDefined();
        expect(insights.insightId).toBeDefined();
        expect(insights.ruleId).toBe(ruleId);
        expect(insights.generatedAt).toBeInstanceOf(Date);
        expect(insights.keyInsights).toBeInstanceOf(Array);
        expect(insights.patterns).toBeDefined();
        expect(insights.patterns.usage).toBeInstanceOf(Array);
        expect(insights.patterns.performance).toBeInstanceOf(Array);
        expect(insights.patterns.errors).toBeInstanceOf(Array);
        expect(insights.trends).toBeDefined();
        expect(['stable', 'improving', 'degrading']).toContain(insights.trends.short);
        expect(['stable', 'improving', 'degrading']).toContain(insights.trends.medium);
        expect(['stable', 'improving', 'degrading', 'positive']).toContain(insights.trends.long);
        expect(insights.anomalies).toBeInstanceOf(Array);
        expect(insights.recommendations).toBeInstanceOf(Array);
        expect(insights.confidence).toBeGreaterThanOrEqual(0);
        expect(insights.confidence).toBeLessThanOrEqual(1);
        expect(insights.metadata).toBeDefined();
        expect(insights.metadata.analysisDepth).toBeDefined();
        expect(insights.metadata.dataQuality).toBeDefined();
        expect(insights.metadata.sourceCoverage).toBeGreaterThanOrEqual(0);
      });

      it('should handle empty rule ID', async () => {
        await expect(insightsGenerator.generateRuleInsights('', createTestInsightsOptions()))
          .rejects.toThrow('Rule ID is required');
      });

      it('should cache insights for performance', async () => {
        const ruleId = 'test-rule-cache';
        const options = createTestInsightsOptions();
        
        // First call
        const start1 = Date.now();
        const insights1 = await insightsGenerator.generateRuleInsights(ruleId, options);
        const time1 = Date.now() - start1;
        
        // Second call (should be cached)
        const start2 = Date.now();
        const insights2 = await insightsGenerator.generateRuleInsights(ruleId, options);
        const time2 = Date.now() - start2;
        
        expect(insights1).toEqual(insights2);
        expect(time2).toBeLessThan(time1);
      });

      it('should update metrics on successful generation', async () => {
        const initialMetrics = await insightsGenerator.getMetrics();
        
        await insightsGenerator.generateRuleInsights('test-rule', createTestInsightsOptions());
        
        const updatedMetrics = await insightsGenerator.getMetrics();
        expect(updatedMetrics.custom.insightsGenerated).toBe(initialMetrics.custom.insightsGenerated + 1);
      });
    });

    describe('generateRuleSetInsights', () => {
      it('should generate insights for multiple rules', async () => {
        const ruleIds = ['rule-001', 'rule-002', 'rule-003'];
        const options = createTestInsightsOptions();

        const insights = await insightsGenerator.generateRuleSetInsights(ruleIds, options);

        expect(insights).toBeDefined();
        expect(insights).toHaveLength(3);
        insights.forEach((insight, index) => {
          expect(insight.ruleId).toBe(ruleIds[index]);
          expect(insight.insightId).toBeDefined();
        });
      });

      it('should handle empty rule set', async () => {
        const insights = await insightsGenerator.generateRuleSetInsights([], createTestInsightsOptions());
        
        expect(insights).toBeInstanceOf(Array);
        expect(insights).toHaveLength(0);
      });

      it('should handle partial failures gracefully', async () => {
        // In test environment, all should succeed
        const ruleIds = ['valid-rule-1', 'valid-rule-2'];
        const insights = await insightsGenerator.generateRuleSetInsights(ruleIds, createTestInsightsOptions());
        
        expect(insights).toHaveLength(2);
      });
    });

    describe('analyzeUsagePatterns', () => {
      it('should analyze usage patterns successfully', async () => {
        const ruleId = 'test-rule-patterns';
        const timeRange = createTestTimeRange();

        const analysis = await insightsGenerator.analyzeUsagePatterns(ruleId, timeRange);

        expect(analysis).toBeDefined();
        expect(analysis.analysisId).toBeDefined();
        expect(analysis.ruleId).toBe(ruleId);
        expect(analysis.timeRange).toEqual(timeRange);
        expect(analysis.patterns).toBeInstanceOf(Array);
        analysis.patterns.forEach(pattern => {
          expect(pattern.patternType).toBeDefined();
          expect(pattern.description).toBeDefined();
          expect(pattern.frequency).toBeDefined();
          expect(pattern.confidence).toBeGreaterThanOrEqual(0);
          expect(pattern.confidence).toBeLessThanOrEqual(1);
          expect(pattern.impact).toBeDefined();
          expect(pattern.examples).toBeInstanceOf(Array);
        });
        expect(analysis.summary).toBeDefined();
        expect(analysis.summary.totalPatterns).toBeGreaterThanOrEqual(0);
        expect(analysis.summary.significantPatterns).toBeGreaterThanOrEqual(0);
        expect(analysis.summary.recommendations).toBeInstanceOf(Array);
        expect(analysis.generatedAt).toBeInstanceOf(Date);
      });

      it('should cache patterns analysis', async () => {
        const ruleId = 'test-rule-patterns-cache';
        const timeRange = createTestTimeRange();
        
        const analysis1 = await insightsGenerator.analyzeUsagePatterns(ruleId, timeRange);
        const analysis2 = await insightsGenerator.analyzeUsagePatterns(ruleId, timeRange);
        
        expect(analysis1).toEqual(analysis2);
      });
    });

    describe('detectAnomalies', () => {
      it('should detect anomalies successfully', async () => {
        const ruleId = 'test-rule-anomalies';
        const timeRange = createTestTimeRange();

        const detection = await insightsGenerator.detectAnomalies(ruleId, timeRange);

        expect(detection).toBeDefined();
        expect(detection.detectionId).toBeDefined();
        expect(detection.ruleId).toBe(ruleId);
        expect(detection.timeRange).toEqual(timeRange);
        expect(detection.anomalies).toBeInstanceOf(Array);
        expect(detection.summary).toBeDefined();
        expect(detection.summary.totalAnomalies).toBeGreaterThanOrEqual(0);
        expect(detection.summary.severity).toBeDefined();
        expect(detection.summary.recommendation).toBeDefined();
        expect(detection.detectionMethod).toBe('statistical');
        expect(detection.confidence).toBeGreaterThanOrEqual(0);
        expect(detection.confidence).toBeLessThanOrEqual(1);
        expect(detection.detectedAt).toBeInstanceOf(Date);
      });

      it('should cache anomaly detection results', async () => {
        const ruleId = 'test-rule-anomalies-cache';
        const timeRange = createTestTimeRange();
        
        const detection1 = await insightsGenerator.detectAnomalies(ruleId, timeRange);
        const detection2 = await insightsGenerator.detectAnomalies(ruleId, timeRange);
        
        expect(detection1).toEqual(detection2);
      });
    });

    describe('generateTrendAnalysis', () => {
      it('should generate trend analysis successfully', async () => {
        const ruleId = 'test-rule-trends';
        const period: TAnalyticsPeriod = 'monthly';

        const trends = await insightsGenerator.generateTrendAnalysis(ruleId, period);

        expect(trends).toBeDefined();
        expect(trends.trendId).toBeDefined();
        expect(trends.ruleId).toBe(ruleId);
        expect(trends.period).toBe(period);
        expect(trends.trends).toBeInstanceOf(Array);
        trends.trends.forEach(trend => {
          expect(trend.metric).toBeDefined();
          expect(['increasing', 'decreasing', 'stable', 'volatile']).toContain(trend.direction);
          expect(typeof trend.magnitude).toBe('number');
          expect(trend.confidence).toBeGreaterThanOrEqual(0);
          expect(trend.confidence).toBeLessThanOrEqual(1);
          expect(trend.forecast).toBeInstanceOf(Array);
        });
        expect(trends.summary).toBeDefined();
        expect(['positive', 'negative', 'stable']).toContain(trends.summary.overallTrend);
        expect(trends.summary.keyFindings).toBeInstanceOf(Array);
        expect(trends.summary.recommendations).toBeInstanceOf(Array);
        expect(trends.generatedAt).toBeInstanceOf(Date);
      });

      it('should handle different periods', async () => {
        const periods: TAnalyticsPeriod[] = ['hourly', 'daily', 'weekly', 'monthly', 'quarterly', 'yearly'];
        
        for (const period of periods) {
          const trends = await insightsGenerator.generateTrendAnalysis('test-rule', period);
          expect(trends.period).toBe(period);
        }
      });
    });
  });

  // ============================================================================
  // ADVANCED INSIGHTS METHODS TESTS
  // ============================================================================

  describe('Advanced Insights Methods', () => {
    beforeEach(async () => {
      await insightsGenerator.start();
    });

    describe('generateInsightsReport', () => {
      it('should generate comprehensive insights report', async () => {
        const ruleId = 'test-rule-report';
        const options = createTestInsightsOptions();

        const report = await insightsGenerator.generateInsightsReport(ruleId, options);

        expect(report).toBeDefined();
        expect(report.reportId).toBeDefined();
        expect(report.ruleId).toBe(ruleId);
        expect(report.generatedAt).toBeInstanceOf(Date);
        expect(report.executiveSummary).toBeDefined();
        expect(report.executiveSummary.keyInsights).toBeInstanceOf(Array);
        expect(report.executiveSummary.criticalFindings).toBeInstanceOf(Array);
        expect(report.executiveSummary.actionItems).toBeInstanceOf(Array);
        expect(['excellent', 'good', 'fair', 'poor']).toContain(report.executiveSummary.overallHealth);
        expect(report.detailedInsights).toBeDefined();
        expect(report.trends).toBeDefined();
        expect(report.anomalies).toBeDefined();
        expect(report.patterns).toBeDefined();
        expect(report.recommendations).toBeInstanceOf(Array);
        expect(report.visualizations).toBeInstanceOf(Array);
      });

      it('should cache reports for performance', async () => {
        const ruleId = 'test-rule-report-cache';
        const options = createTestInsightsOptions();
        
        const report1 = await insightsGenerator.generateInsightsReport(ruleId, options);
        
        // The report should be cached by reportId, not by input parameters
        // So we can't directly test caching this way, but we can verify structure
        expect(report1.reportId).toBeDefined();
      });
    });

    describe('generatePredictiveInsights', () => {
      it('should generate predictive insights', async () => {
        const ruleId = 'test-rule-predictive';
        const horizon = 30; // 30 days

        const predictions = await insightsGenerator.generatePredictiveInsights(ruleId, horizon);

        expect(predictions).toBeDefined();
        expect(predictions.predictionId).toBeDefined();
        expect(predictions.ruleId).toBe(ruleId);
        expect(predictions.horizon).toBe(horizon);
        expect(predictions.predictions).toBeInstanceOf(Array);
        predictions.predictions.forEach(prediction => {
          expect(prediction.metric).toBeDefined();
          expect(typeof prediction.currentValue).toBe('number');
          expect(typeof prediction.predictedValue).toBe('number');
          expect(prediction.confidence).toBeGreaterThanOrEqual(0);
          expect(prediction.confidence).toBeLessThanOrEqual(1);
          expect(prediction.factors).toBeInstanceOf(Array);
        });
        expect(predictions.scenarios).toBeInstanceOf(Array);
        predictions.scenarios.forEach(scenario => {
          expect(scenario.name).toBeDefined();
          expect(scenario.probability).toBeGreaterThanOrEqual(0);
          expect(scenario.probability).toBeLessThanOrEqual(1);
          expect(['low', 'medium', 'high']).toContain(scenario.impact);
          expect(scenario.description).toBeDefined();
        });
        expect(predictions.recommendations).toBeInstanceOf(Array);
        expect(predictions.generatedAt).toBeInstanceOf(Date);
      });

      it('should handle different prediction horizons', async () => {
        const horizons = [7, 14, 30, 90];
        
        for (const horizon of horizons) {
          const predictions = await insightsGenerator.generatePredictiveInsights('test-rule', horizon);
          expect(predictions.horizon).toBe(horizon);
        }
      });
    });

    describe('generateComparativeInsights', () => {
      it('should generate comparative insights', async () => {
        const ruleIds = ['rule-001', 'rule-002', 'rule-003'];
        const metrics = ['performance', 'reliability', 'efficiency'];

        const comparison = await insightsGenerator.generateComparativeInsights(ruleIds, metrics);

        expect(comparison).toBeDefined();
        expect(comparison.comparisonId).toBeDefined();
        expect(comparison.ruleIds).toEqual(ruleIds);
        expect(comparison.metrics).toEqual(metrics);
        expect(comparison.comparisons).toBeInstanceOf(Array);
        expect(comparison.correlations).toBeInstanceOf(Array);
        expect(comparison.summary).toBeInstanceOf(Array);
        expect(comparison.generatedAt).toBeInstanceOf(Date);
      });

      it('should handle empty rule sets', async () => {
        const comparison = await insightsGenerator.generateComparativeInsights([], ['performance']);
        
        expect(comparison.ruleIds).toEqual([]);
        expect(comparison.comparisons).toBeInstanceOf(Array);
      });
    });

    describe('generatePerformanceInsights', () => {
      it('should generate performance insights', async () => {
        const ruleId = 'test-rule-performance';

        const insights = await insightsGenerator.generatePerformanceInsights(ruleId);

        expect(insights).toBeDefined();
        expect(insights.insightId).toBeDefined();
        expect(insights.ruleId).toBe(ruleId);
        expect(insights.performanceMetrics).toBeDefined();
        expect(insights.performanceMetrics.efficiency).toBeGreaterThanOrEqual(0);
        expect(insights.performanceMetrics.efficiency).toBeLessThanOrEqual(1);
        expect(insights.performanceMetrics.reliability).toBeGreaterThanOrEqual(0);
        expect(insights.performanceMetrics.reliability).toBeLessThanOrEqual(1);
        expect(insights.performanceMetrics.scalability).toBeGreaterThanOrEqual(0);
        expect(insights.performanceMetrics.scalability).toBeLessThanOrEqual(1);
        expect(insights.performanceMetrics.maintainability).toBeGreaterThanOrEqual(0);
        expect(insights.performanceMetrics.maintainability).toBeLessThanOrEqual(1);
        expect(insights.bottlenecks).toBeInstanceOf(Array);
        insights.bottlenecks.forEach(bottleneck => {
          expect(bottleneck.area).toBeDefined();
          expect(['critical', 'high', 'medium', 'low']).toContain(bottleneck.severity);
          expect(bottleneck.impact).toBeDefined();
          expect(bottleneck.recommendation).toBeDefined();
        });
        expect(insights.optimizationPotential).toBeDefined();
        expect(insights.benchmarks).toBeDefined();
        expect(insights.generatedAt).toBeInstanceOf(Date);
      });
    });

    describe('generateBusinessInsights', () => {
      it('should generate business insights', async () => {
        const ruleId = 'test-rule-business';

        const insights = await insightsGenerator.generateBusinessInsights(ruleId);

        expect(insights).toBeDefined();
        expect(insights.insightId).toBeDefined();
        expect(insights.ruleId).toBe(ruleId);
        expect(insights.businessImpact).toBeDefined();
        expect(typeof insights.businessImpact.riskReduction).toBe('number');
        expect(typeof insights.businessImpact.complianceImprovement).toBe('number');
        expect(typeof insights.businessImpact.operationalEfficiency).toBe('number');
        expect(typeof insights.businessImpact.costSavings).toBe('number');
        expect(insights.stakeholderValue).toBeInstanceOf(Array);
        insights.stakeholderValue.forEach(value => {
          expect(value.stakeholder).toBeDefined();
          expect(value.value).toBeDefined();
          expect(typeof value.quantification).toBe('number');
        });
        expect(insights.roi).toBeDefined();
        expect(typeof insights.roi.investment).toBe('number');
        expect(typeof insights.roi.return).toBe('number');
        expect(typeof insights.roi.paybackPeriod).toBe('number');
        expect(typeof insights.roi.netValue).toBe('number');
        expect(insights.strategicAlignment).toBeDefined();
        expect(insights.strategicAlignment.score).toBeGreaterThanOrEqual(0);
        expect(insights.strategicAlignment.score).toBeLessThanOrEqual(1);
        expect(insights.strategicAlignment.factors).toBeInstanceOf(Array);
        expect(insights.strategicAlignment.gaps).toBeInstanceOf(Array);
        expect(insights.generatedAt).toBeInstanceOf(Date);
      });
    });
  });

  // ============================================================================
  // CONFIGURATION TESTS
  // ============================================================================

  describe('Configuration', () => {
    it('should configure generator successfully', async () => {
      const config = createTestInsightsConfig();

      await expect(insightsGenerator.configure(config)).resolves.not.toThrow();

      const retrievedConfig = await insightsGenerator.getConfiguration();
      expect(retrievedConfig.maxConcurrentAnalyses).toBe(config.maxConcurrentAnalyses);
      expect(retrievedConfig.analysisTimeout).toBe(config.analysisTimeout);
      expect(retrievedConfig.enablePredictiveAnalysis).toBe(config.enablePredictiveAnalysis);
      expect(retrievedConfig.enableAnomalyDetection).toBe(config.enableAnomalyDetection);
      expect(retrievedConfig.confidenceThreshold).toBe(config.confidenceThreshold);
      expect(retrievedConfig.dataRetentionPeriod).toBe(config.dataRetentionPeriod);
      expect(retrievedConfig.insightDepth).toBe(config.insightDepth);
      expect(retrievedConfig.visualization).toBe(config.visualization);
    });

    it('should get current configuration', async () => {
      const config = await insightsGenerator.getConfiguration();

      expect(config).toBeDefined();
      expect(config.maxConcurrentAnalyses).toBeGreaterThan(0);
      expect(config.analysisTimeout).toBeGreaterThan(0);
      expect(typeof config.enablePredictiveAnalysis).toBe('boolean');
      expect(typeof config.enableAnomalyDetection).toBe('boolean');
      expect(config.confidenceThreshold).toBeGreaterThanOrEqual(0);
      expect(config.confidenceThreshold).toBeLessThanOrEqual(1);
      expect(config.dataRetentionPeriod).toBeGreaterThan(0);
      expect(['basic', 'standard', 'comprehensive', 'deep']).toContain(config.insightDepth);
      expect(config.visualization).toBeDefined();
    });

    it('should validate configuration parameters', async () => {
      const invalidConfig = {
        ...createTestInsightsConfig(),
        maxConcurrentAnalyses: 0, // Invalid
        confidenceThreshold: 1.5 // Invalid (> 1)
      };

      await expect(insightsGenerator.configure(invalidConfig)).rejects.toThrow();
    });
  });

  // ============================================================================
  // ERROR HANDLING AND EDGE CASES
  // ============================================================================

  describe('Error Handling and Edge Cases', () => {
    beforeEach(async () => {
      await insightsGenerator.start();
    });

    it('should handle concurrent analysis requests', async () => {
      const promises = [
        insightsGenerator.generateRuleInsights('rule-1', createTestInsightsOptions()),
        insightsGenerator.generateRuleInsights('rule-2', createTestInsightsOptions()),
        insightsGenerator.generateRuleInsights('rule-3', createTestInsightsOptions())
      ];

      const results = await Promise.all(promises);
      expect(results).toHaveLength(3);
      results.forEach(result => {
        expect(result.insightId).toBeDefined();
      });
    });

    it('should handle service not started', async () => {
      const freshGenerator = new GovernanceRuleInsightsGenerator();
      await freshGenerator.initialize();
      // Don't start the generator

      // Should still work in test environment
      await expect(freshGenerator.generateRuleInsights('test-rule', createTestInsightsOptions()))
        .resolves.toBeDefined();

      await freshGenerator.shutdown();
    });

    it('should handle invalid time ranges', async () => {
      const invalidTimeRange = {
        startTime: new Date(),
        endTime: new Date(Date.now() - 24 * 60 * 60 * 1000) // End before start
      };

      // Should still process but may return limited data
      const result = await insightsGenerator.analyzeUsagePatterns('test-rule', invalidTimeRange);
      expect(result).toBeDefined();
    });

    it('should handle extreme confidence thresholds', async () => {
      const extremeOptions: TInsightsOptions = {
        ...createTestInsightsOptions(),
        confidenceThreshold: 0.99 // Very high threshold
      };

      const insights = await insightsGenerator.generateRuleInsights('test-rule', extremeOptions);
      expect(insights.confidence).toBeLessThanOrEqual(extremeOptions.confidenceThreshold!);
    });

    it('should handle different insight depths', async () => {
      const depths: TInsightsOptions['depth'][] = ['basic', 'standard', 'comprehensive', 'deep'];
      
      for (const depth of depths) {
        const options = { ...createTestInsightsOptions(), depth };
        const insights = await insightsGenerator.generateRuleInsights('test-rule', options);
        expect(insights.metadata.analysisDepth).toBe(depth);
      }
    });
  });

  // ============================================================================
  // PERFORMANCE AND INTEGRATION TESTS
  // ============================================================================

  describe('Performance and Integration', () => {
    beforeEach(async () => {
      await insightsGenerator.start();
    });

    it('should handle high-volume insights generation', async () => {
      const ruleIds = Array.from({ length: 10 }, (_, i) => `rule-${i}`);
      
      const start = Date.now();
      const insights = await insightsGenerator.generateRuleSetInsights(ruleIds, createTestInsightsOptions());
      const duration = Date.now() - start;

      expect(insights).toHaveLength(10);
      expect(duration).toBeLessThan(5000); // Should complete within 5 seconds
    });

    it('should maintain consistent state across operations', async () => {
      const initialMetrics = await insightsGenerator.getMetrics();
      
      await insightsGenerator.generateRuleInsights('rule-1', createTestInsightsOptions());
      await insightsGenerator.generateTrendAnalysis('rule-2', 'weekly');
      
      const finalMetrics = await insightsGenerator.getMetrics();
      
      expect(finalMetrics.custom.insightsGenerated).toBe(initialMetrics.custom.insightsGenerated + 1);
      expect(finalMetrics.custom.trendsIdentified).toBeGreaterThan(initialMetrics.custom.trendsIdentified);
    });

    it('should validate component lifecycle', async () => {
      // Test reinitialization
      await insightsGenerator.shutdown();
      expect(insightsGenerator.isReady()).toBe(false);
      
      await insightsGenerator.initialize();
      expect(insightsGenerator.isReady()).toBe(true);
      
      // Should work normally after reinitialization
      await insightsGenerator.start();
      const result = await insightsGenerator.generateRuleInsights('test-rule', createTestInsightsOptions());
      expect(result).toBeDefined();
    });

    it('should handle mixed analysis types efficiently', async () => {
      const operations = [
        insightsGenerator.generateRuleInsights('rule-1', createTestInsightsOptions()),
        insightsGenerator.analyzeUsagePatterns('rule-2', createTestTimeRange()),
        insightsGenerator.detectAnomalies('rule-3', createTestTimeRange()),
        insightsGenerator.generateTrendAnalysis('rule-4', 'monthly'),
        insightsGenerator.generatePredictiveInsights('rule-5', 30)
      ];

      const results = await Promise.all(operations);
      expect(results).toHaveLength(5);
      results.forEach(result => {
        expect(result).toBeDefined();
      });
    });

    it('should cache effectively across different analysis types', async () => {
      const ruleId = 'cache-test-rule';
      const timeRange = createTestTimeRange();
      
      // Generate multiple analyses for the same rule
      await insightsGenerator.analyzeUsagePatterns(ruleId, timeRange);
      await insightsGenerator.detectAnomalies(ruleId, timeRange);
      
      // Subsequent calls should benefit from caching
      const start = Date.now();
      await insightsGenerator.analyzeUsagePatterns(ruleId, timeRange);
      const duration = Date.now() - start;
      
      expect(duration).toBeLessThan(50); // Should be very fast due to caching
    });
  });
});