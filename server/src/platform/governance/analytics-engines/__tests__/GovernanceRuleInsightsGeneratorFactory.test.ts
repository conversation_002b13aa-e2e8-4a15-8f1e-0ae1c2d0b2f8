/**
 * @file Governance Rule Insights Generator Factory Tests
 * @filepath server/src/platform/governance/analytics-engines/__tests__/GovernanceRuleInsightsGeneratorFactory.test.ts
 * @task-id G-TSK-06.SUB-06.1.TEST-06
 * @component governance-rule-insights-generator-factory-tests
 * @reference foundation-context.ANALYTICS.013
 * @template on-demand-creation-with-latest-standards
 * @tier T2
 * @context foundation-context
 * @category Testing
 * @created 2025-07-01 14:00:00 +03
 * @modified 2025-07-01 14:00:00 +03
 * 
 * @description
 * Comprehensive unit tests for Governance Rule Insights Generator Factory
 * covering factory functionality, instance management, and lifecycle for Phase 3 insights.
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level component-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-tracking-architecture
 * @governance-dcr DCR-foundation-001-tracking-development
 * @governance-status active
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on server/src/platform/governance/analytics-engines/GovernanceRuleInsightsGeneratorFactory
 * @enables testing/server/platform/governance/analytics-engines
 * @related-contexts foundation-context, governance-context, testing-context
 * @governance-impact insights-testing, factory-validation
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import {
  GovernanceRuleInsightsGeneratorFactory,
  TInsightsGeneratorFactoryConfig,
  TInsightsFactoryMetrics
} from '../GovernanceRuleInsightsGeneratorFactory';

import {
  GovernanceRuleInsightsGenerator,
  IAnalyticsService,
  TInsightsGeneratorConfig
} from '../GovernanceRuleInsightsGenerator';

// ============================================================================
// TEST SETUP AND UTILITIES
// ============================================================================

/**
 * Create test factory configuration
 */
function createTestFactoryConfig(): TInsightsGeneratorFactoryConfig {
  return {
    maxInstances: 6,
    instanceTimeout: 30000,
    enablePooling: true,
    poolSize: 2,
    enableMonitoring: true,
    autoCleanup: true,
    cleanupInterval: 1000, // Minimum 1000ms
    insightsCaching: true,
    cacheRetentionPeriod: 60000 // Minimum 60000ms (1 minute)
  };
}

/**
 * Create test insights generator configuration
 */
function createTestInsightsConfig(): TInsightsGeneratorConfig {
  return {
    maxConcurrentAnalyses: 3,
    analysisTimeout: 30000,
    enablePredictiveAnalysis: true,
    enableAnomalyDetection: true,
    confidenceThreshold: 0.75,
    dataRetentionPeriod: 86400000,
    insightDepth: 'comprehensive',
    visualization: {
      enableCharts: true,
      chartTypes: ['line', 'bar', 'pie'],
      interactivity: true
    }
  };
}

/**
 * Wait for specified duration
 */
function waitFor(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// ============================================================================
// MAIN TEST SUITE
// ============================================================================

describe('GovernanceRuleInsightsGeneratorFactory', () => {
  let factory: GovernanceRuleInsightsGeneratorFactory;

  beforeEach(async () => {
    factory = await GovernanceRuleInsightsGeneratorFactory.getInstance();
  });

  afterEach(async () => {
    if (factory) {
      await factory.shutdown();
      // Reset singleton for next test
      (GovernanceRuleInsightsGeneratorFactory as any)._instance = null;
      (GovernanceRuleInsightsGeneratorFactory as any)._initializationPromise = null;
    }
  });

  // ============================================================================
  // SINGLETON AND INITIALIZATION TESTS
  // ============================================================================

  describe('Singleton and Initialization', () => {
    it('should create singleton instance', async () => {
      expect(factory).toBeDefined();
      expect(factory).toBeInstanceOf(GovernanceRuleInsightsGeneratorFactory);
    });

    it('should return same instance on multiple calls', async () => {
      const factory1 = await GovernanceRuleInsightsGeneratorFactory.getInstance();
      const factory2 = await GovernanceRuleInsightsGeneratorFactory.getInstance();
      
      expect(factory1).toBe(factory2);
    });

    it('should initialize with default configuration', async () => {
      const config = factory.getConfiguration();
      
      expect(config).toBeDefined();
      expect(config.maxInstances).toBeGreaterThan(0);
      expect(config.instanceTimeout).toBeGreaterThan(0);
      expect(typeof config.enablePooling).toBe('boolean');
      expect(config.poolSize).toBeGreaterThanOrEqual(0);
      expect(typeof config.enableMonitoring).toBe('boolean');
      expect(typeof config.autoCleanup).toBe('boolean');
      expect(config.cleanupInterval).toBeGreaterThan(0);
      expect(typeof config.insightsCaching).toBe('boolean');
      expect(config.cacheRetentionPeriod).toBeGreaterThan(0);
    });

    it('should handle concurrent initialization calls', async () => {
      // Reset singleton
      (GovernanceRuleInsightsGeneratorFactory as any)._instance = null;
      (GovernanceRuleInsightsGeneratorFactory as any)._initializationPromise = null;
      
      const promises = [
        GovernanceRuleInsightsGeneratorFactory.getInstance(),
        GovernanceRuleInsightsGeneratorFactory.getInstance(),
        GovernanceRuleInsightsGeneratorFactory.getInstance()
      ];
      
      const instances = await Promise.all(promises);
      
      expect(instances[0]).toBe(instances[1]);
      expect(instances[1]).toBe(instances[2]);
    });
  });

  // ============================================================================
  // INSTANCE CREATION AND MANAGEMENT TESTS
  // ============================================================================

  describe('Instance Creation and Management', () => {
    it('should create insights generator instance', async () => {
      const generator = await factory.createInsightsGenerator();
      
      expect(generator).toBeDefined();
      expect(generator).toBeInstanceOf(GovernanceRuleInsightsGenerator);
      
      await factory.releaseInsightsGenerator(generator);
    });

    it('should create instance with custom configuration', async () => {
      const config = createTestInsightsConfig();
      const generator = await factory.createInsightsGenerator(config);
      
      expect(generator).toBeDefined();
      expect(generator).toBeInstanceOf(GovernanceRuleInsightsGenerator);
      
      await factory.releaseInsightsGenerator(generator);
    });

    it('should reuse instance from pool when possible', async () => {
      await factory.configure({ enablePooling: true, poolSize: 3 });
      
      // Create and release a generator to populate the pool
      const generator1 = await factory.createInsightsGenerator();
      await factory.releaseInsightsGenerator(generator1);
      
      // Create another generator - should reuse from pool
      const generator2 = await factory.createInsightsGenerator();
      
      expect(generator2).toBeDefined();
      await factory.releaseInsightsGenerator(generator2);
    });

    it('should create new instance when pool is empty', async () => {
      await factory.configure({ enablePooling: true, poolSize: 0 });
      
      const generator = await factory.createInsightsGenerator();
      
      expect(generator).toBeDefined();
      await factory.releaseInsightsGenerator(generator);
    });

    it('should enforce maximum instances limit', async () => {
      await factory.configure({ maxInstances: 2, enablePooling: false, poolSize: 0 });
      
      const generator1 = await factory.createInsightsGenerator();
      const generator2 = await factory.createInsightsGenerator();
      
      await expect(factory.createInsightsGenerator())
        .rejects.toThrow('Maximum instances limit reached: 2');
      
      await factory.releaseInsightsGenerator(generator1);
      await factory.releaseInsightsGenerator(generator2);
    });

    it('should get active instances', async () => {
      const generator1 = await factory.createInsightsGenerator();
      const generator2 = await factory.createInsightsGenerator();
      
      const activeInstances = factory.getActiveInstances();
      
      expect(activeInstances).toHaveLength(2);
      expect(activeInstances).toContain(generator1);
      expect(activeInstances).toContain(generator2);
      
      await factory.releaseInsightsGenerator(generator1);
      await factory.releaseInsightsGenerator(generator2);
    });
  });

  // ============================================================================
  // INSTANCE RELEASE AND CLEANUP TESTS
  // ============================================================================

  describe('Instance Release and Cleanup', () => {
    it('should release instance and return to pool', async () => {
      // Clear existing pool first
      await factory.configure({ enablePooling: false });
      await factory.configure({ enablePooling: true, poolSize: 3 });
      
      const initialMetrics = factory.getMetrics();
      const generator = await factory.createInsightsGenerator();
      
      // The generator should be taken from the pool, so active instances increase
      // but pooled instances decrease
      const afterCreateMetrics = factory.getMetrics();
      expect(afterCreateMetrics.pooledInstances).toBe(initialMetrics.pooledInstances - 1);
      
      await factory.releaseInsightsGenerator(generator);
      
      const finalMetrics = factory.getMetrics();
      // When released, it should go back to the pool
      expect(finalMetrics.pooledInstances).toBe(initialMetrics.pooledInstances);
    });

    it('should shutdown instance when pool is full', async () => {
      await factory.configure({ enablePooling: true, poolSize: 1 });
      
      // Fill the pool
      const generator1 = await factory.createInsightsGenerator();
      await factory.releaseInsightsGenerator(generator1);
      
      // This should exceed pool capacity and be destroyed
      const generator2 = await factory.createInsightsGenerator();
      await factory.releaseInsightsGenerator(generator2);
      
      const metrics = factory.getMetrics();
      expect(metrics.pooledInstances).toBeLessThanOrEqual(1);
    });

    it('should handle release of non-existent instance gracefully', async () => {
      const unknownGenerator = new GovernanceRuleInsightsGenerator();
      await unknownGenerator.initialize();
      
      await expect(factory.releaseInsightsGenerator(unknownGenerator))
        .rejects.toThrow('Instance not found in factory registry');
      
      await unknownGenerator.shutdown();
    });

    it('should shutdown all instances when factory shuts down', async () => {
      const generator1 = await factory.createInsightsGenerator();
      const generator2 = await factory.createInsightsGenerator();
      
      await factory.shutdown();
      
      const metrics = factory.getMetrics();
      expect(metrics.activeInstances).toBe(0);
      expect(metrics.pooledInstances).toBe(0);
    });

    it('should update instance metrics on release', async () => {
      const generator = await factory.createInsightsGenerator();
      const initialMetrics = factory.getMetrics();
      
      // Simulate some insights generation by getting metrics from the instance
      const instanceMetrics = await generator.getMetrics();
      
      await factory.releaseInsightsGenerator(generator);
      
      const finalMetrics = factory.getMetrics();
      expect(finalMetrics.totalInsightsGenerated).toBeGreaterThanOrEqual(initialMetrics.totalInsightsGenerated);
    });
  });

  // ============================================================================
  // CONFIGURATION VALIDATION TESTS
  // ============================================================================

  describe('Configuration Validation', () => {
    it('should validate valid configuration', async () => {
      const validConfig = createTestFactoryConfig();
      
      await expect(factory.configure(validConfig)).resolves.not.toThrow();
      
      const config = factory.getConfiguration();
      expect(config.maxInstances).toBe(validConfig.maxInstances);
      expect(config.enablePooling).toBe(validConfig.enablePooling);
      expect(config.insightsCaching).toBe(validConfig.insightsCaching);
    });

    it('should reject configuration with invalid maxInstances', async () => {
      const invalidConfig = {
        ...createTestFactoryConfig(),
        maxInstances: 0
      };
      
      await expect(factory.configure(invalidConfig))
        .rejects.toThrow('maxInstances must be >= 1');
    });

    it('should reject configuration with invalid poolSize', async () => {
      const invalidConfig = {
        ...createTestFactoryConfig(),
        poolSize: -1
      };
      
      await expect(factory.configure(invalidConfig))
        .rejects.toThrow('poolSize must be >= 0');
    });

    it('should reject configuration where poolSize exceeds maxInstances', async () => {
      const invalidConfig = {
        ...createTestFactoryConfig(),
        maxInstances: 2,
        poolSize: 5
      };
      
      await expect(factory.configure(invalidConfig))
        .rejects.toThrow('poolSize cannot exceed maxInstances');
    });

    it('should reject configuration with invalid timeout values', async () => {
      const invalidConfig = {
        ...createTestFactoryConfig(),
        instanceTimeout: -1
      };
      
      await expect(factory.configure(invalidConfig))
        .rejects.toThrow('instanceTimeout must be >= 0');
    });

    it('should reject configuration with invalid cleanup interval', async () => {
      const invalidConfig = {
        ...createTestFactoryConfig(),
        cleanupInterval: 500 // Less than 1000ms
      };
      
      await expect(factory.configure(invalidConfig))
        .rejects.toThrow('cleanupInterval must be >= 1000ms');
    });

    it('should reject configuration with invalid cache retention period', async () => {
      const invalidConfig = {
        ...createTestFactoryConfig(),
        cacheRetentionPeriod: 30000 // Less than 60000ms (1 minute)
      };
      
      await expect(factory.configure(invalidConfig))
        .rejects.toThrow('cacheRetentionPeriod must be >= 60000ms (1 minute)');
    });

    it('should update partial configuration', async () => {
      const originalConfig = factory.getConfiguration();
      
      await factory.configure({ maxInstances: 20, insightsCaching: false });
      
      const updatedConfig = factory.getConfiguration();
      expect(updatedConfig.maxInstances).toBe(20);
      expect(updatedConfig.insightsCaching).toBe(false);
      expect(updatedConfig.enablePooling).toBe(originalConfig.enablePooling);
    });

    it('should handle cache retention period changes', async () => {
      const originalConfig = factory.getConfiguration();
      
      await factory.configure({ 
        cacheRetentionPeriod: 120000, // 2 minutes
        insightsCaching: true 
      });
      
      const updatedConfig = factory.getConfiguration();
      expect(updatedConfig.cacheRetentionPeriod).toBe(120000);
      expect(updatedConfig.insightsCaching).toBe(true);
    });
  });

  // ============================================================================
  // FACTORY METRICS TESTS
  // ============================================================================

  describe('Factory Metrics', () => {
    it('should provide comprehensive metrics', async () => {
      const metrics = factory.getMetrics();
      
      expect(metrics).toBeDefined();
      expect(metrics.instancesCreated).toBeGreaterThanOrEqual(0);
      expect(metrics.instancesDestroyed).toBeGreaterThanOrEqual(0);
      expect(metrics.activeInstances).toBeGreaterThanOrEqual(0);
      expect(metrics.pooledInstances).toBeGreaterThanOrEqual(0);
      expect(metrics.factoryUptime).toBeGreaterThanOrEqual(0);
      expect(metrics.averageCreationTime).toBeGreaterThanOrEqual(0);
      expect(metrics.errorCount).toBeGreaterThanOrEqual(0);
      expect(metrics.totalInsightsGenerated).toBeGreaterThanOrEqual(0);
      expect(metrics.averageInsightGenerationTime).toBeGreaterThanOrEqual(0);
    });

    it('should update metrics on instance creation', async () => {
      const initialMetrics = factory.getMetrics();
      
      const generator = await factory.createInsightsGenerator();
      
      const updatedMetrics = factory.getMetrics();
      expect(updatedMetrics.instancesCreated).toBe(initialMetrics.instancesCreated + 1);
      expect(updatedMetrics.activeInstances).toBe(initialMetrics.activeInstances + 1);
      
      await factory.releaseInsightsGenerator(generator);
    });

    it('should update metrics on instance release', async () => {
      const generator = await factory.createInsightsGenerator();
      const midMetrics = factory.getMetrics();
      
      await factory.releaseInsightsGenerator(generator);
      
      const finalMetrics = factory.getMetrics();
      expect(finalMetrics.activeInstances).toBe(midMetrics.activeInstances - 1);
    });

    it('should track factory uptime', async () => {
      const metrics1 = factory.getMetrics();
      await waitFor(100);
      const metrics2 = factory.getMetrics();
      
      expect(metrics2.factoryUptime).toBeGreaterThan(metrics1.factoryUptime);
    });

    it('should track insights generation metrics', async () => {
      const generator = await factory.createInsightsGenerator();
      const initialMetrics = factory.getMetrics();
      
      // Simulate generating insights by starting the generator
      await generator.start();
      
      // Generate some insights to affect metrics
      await generator.generateRuleInsights('test-rule', {
        depth: 'basic',
        includePerformance: true,
        includeUsage: true,
        includeCompliance: true,
        includePredictions: false,
        confidenceThreshold: 0.8
      });
      
      await factory.releaseInsightsGenerator(generator);
      
      const finalMetrics = factory.getMetrics();
      // Metrics should be updated based on instance usage
      expect(finalMetrics).toBeDefined();
    });
  });

  // ============================================================================
  // POOLING FUNCTIONALITY TESTS
  // ============================================================================

  describe('Pooling Functionality', () => {
    it('should respect pool size limits', async () => {
      await factory.configure({ 
        enablePooling: true, 
        maxInstances: 5,
        poolSize: 2 
      });
      
      // Create and release multiple generators
      const generators = [];
      for (let i = 0; i < 4; i++) {
        const generator = await factory.createInsightsGenerator();
        generators.push(generator);
      }
      
      // Release all generators
      for (const generator of generators) {
        await factory.releaseInsightsGenerator(generator);
      }
      
      const metrics = factory.getMetrics();
      expect(metrics.pooledInstances).toBeLessThanOrEqual(2);
    });

    it('should handle pooling disabled scenario', async () => {
      await factory.configure({ enablePooling: false });
      
      const generator = await factory.createInsightsGenerator();
      const metricsBeforeRelease = factory.getMetrics();
      
      await factory.releaseInsightsGenerator(generator);
      
      const metricsAfterRelease = factory.getMetrics();
      expect(metricsAfterRelease.pooledInstances).toBe(0);
      expect(metricsAfterRelease.instancesDestroyed).toBe(metricsBeforeRelease.instancesDestroyed + 1);
    });

    it('should handle pool configuration with caching enabled', async () => {
      // Clear existing pool first  
      await factory.configure({ enablePooling: false });
      await factory.configure({ 
        enablePooling: true, 
        poolSize: 3,
        insightsCaching: true,
        cacheRetentionPeriod: 60000
      });
      
      const initialMetrics = factory.getMetrics();
      const generator = await factory.createInsightsGenerator();
      await factory.releaseInsightsGenerator(generator);
      
      // Pool should have the same number as initially (taken and returned)
      expect(factory.getMetrics().pooledInstances).toBe(initialMetrics.pooledInstances);
    });

    it('should reset pooled instances correctly on shutdown', async () => {
      // Clear existing pool first
      await factory.configure({ enablePooling: false });
      await factory.configure({ enablePooling: true, poolSize: 3 });
      
      const initialMetrics = factory.getMetrics();
      const generator = await factory.createInsightsGenerator();
      await factory.releaseInsightsGenerator(generator);
      
      // Pool should have the same number as initially (taken and returned)
      expect(factory.getMetrics().pooledInstances).toBe(initialMetrics.pooledInstances);
      
      await factory.shutdown();
      
      // After shutdown, create new instance to verify reset
      const newFactory = await GovernanceRuleInsightsGeneratorFactory.getInstance();
      expect(newFactory.getMetrics().pooledInstances).toBeGreaterThanOrEqual(0);
      
      await newFactory.shutdown();
    });
  });

  // ============================================================================
  // CACHING FUNCTIONALITY TESTS
  // ============================================================================

  describe('Caching Functionality', () => {
    it('should handle caching enabled configuration', async () => {
      await factory.configure({ 
        insightsCaching: true,
        cacheRetentionPeriod: 120000 // 2 minutes
      });
      
      const config = factory.getConfiguration();
      expect(config.insightsCaching).toBe(true);
      expect(config.cacheRetentionPeriod).toBe(120000);
    });

    it('should handle caching disabled configuration', async () => {
      await factory.configure({ 
        insightsCaching: false
      });
      
      const config = factory.getConfiguration();
      expect(config.insightsCaching).toBe(false);
    });

    it('should restart cache cleanup timer on configuration change', async () => {
      await factory.configure({ 
        insightsCaching: true,
        cacheRetentionPeriod: 60000
      });
      
      // Change cache retention period
      await factory.configure({ 
        cacheRetentionPeriod: 90000
      });
      
      const config = factory.getConfiguration();
      expect(config.cacheRetentionPeriod).toBe(90000);
    });
  });

  // ============================================================================
  // CLEANUP AND MAINTENANCE TESTS
  // ============================================================================

  describe('Cleanup and Maintenance', () => {
    it('should perform periodic cleanup when enabled', async () => {
      await factory.configure({ 
        autoCleanup: true, 
        cleanupInterval: 1000, // Minimum 1000ms
        instanceTimeout: 5000 // 5 seconds timeout
      });
      
      const generator = await factory.createInsightsGenerator();
      
      // Wait for cleanup cycle
      await waitFor(100);
      
      // Instance should still be active (hasn't timed out yet in our test environment)
      const metrics = factory.getMetrics();
      expect(metrics.activeInstances).toBeGreaterThanOrEqual(0);
      
      await factory.releaseInsightsGenerator(generator);
    });

    it('should handle cleanup configuration changes', async () => {
      await factory.configure({ 
        autoCleanup: true,
        cleanupInterval: 1000
      });
      
      // Change cleanup interval
      await factory.configure({ cleanupInterval: 2000 });
      
      // Should not throw errors
      await waitFor(50);
      
      const metrics = factory.getMetrics();
      expect(metrics).toBeDefined();
    });

    it('should handle cache cleanup when caching enabled', async () => {
      await factory.configure({ 
        insightsCaching: true,
        cacheRetentionPeriod: 60000 // Minimum 60000ms (1 minute)
      });
      
      // Wait for cache cleanup cycle
      await waitFor(100);
      
      // Should not throw errors
      const metrics = factory.getMetrics();
      expect(metrics.errorCount).toBe(0);
    });

    it('should cleanup expired instances', async () => {
      await factory.configure({ 
        autoCleanup: true,
        instanceTimeout: 5000, // 5 seconds timeout
        cleanupInterval: 1000 // Minimum 1000ms
      });
      
      const generator = await factory.createInsightsGenerator();
      
      // In test environment, instances don't actually expire
      // This test verifies the cleanup mechanism runs without errors
      await waitFor(100);
      
      const metrics = factory.getMetrics();
      expect(metrics.errorCount).toBe(0);
      
      await factory.releaseInsightsGenerator(generator);
    });
  });

  // ============================================================================
  // ERROR HANDLING AND EDGE CASES
  // ============================================================================

  describe('Error Handling and Edge Cases', () => {
    it('should handle factory reinitialization properly', async () => {
      const initialConfig = factory.getConfiguration();
      
      await factory.shutdown();
      
      // Get new factory instance
      const newFactory = await GovernanceRuleInsightsGeneratorFactory.getInstance();
      const newConfig = newFactory.getConfiguration();
      
      expect(newConfig.maxInstances).toBe(initialConfig.maxInstances);
      
      await newFactory.shutdown();
    });

    it('should handle rapid instance creation and release', async () => {
      const promises = [];
      
      // Rapidly create and release instances
      for (let i = 0; i < 10; i++) {
        promises.push(
          factory.createInsightsGenerator().then(generator => 
            factory.releaseInsightsGenerator(generator)
          )
        );
      }
      
      await Promise.all(promises);
      
      const metrics = factory.getMetrics();
      expect(metrics.errorCount).toBe(0);
    });

    it('should maintain consistency under concurrent access', async () => {
      await factory.configure({ maxInstances: 10 });
      
      const createPromises = Array.from({ length: 5 }, () => 
        factory.createInsightsGenerator()
      );
      
      const generators = await Promise.all(createPromises);
      const config = factory.getConfiguration();
      const metrics = factory.getMetrics();
      
      expect(generators).toHaveLength(5);
      expect(config).toBeDefined();
      expect(metrics).toBeDefined();
      expect(metrics.activeInstances).toBe(5);
      
      // Release all generators
      const releasePromises = generators.map(generator => 
        factory.releaseInsightsGenerator(generator)
      );
      
      await Promise.all(releasePromises);
    });

    it('should handle shutdown with active instances gracefully', async () => {
      const generator1 = await factory.createInsightsGenerator();
      const generator2 = await factory.createInsightsGenerator();
      
      // Shutdown should clean up all instances
      await factory.shutdown();
      
      const metrics = factory.getMetrics();
      expect(metrics.activeInstances).toBe(0);
    });

    it('should handle repeated shutdown calls', async () => {
      await factory.shutdown();
      await expect(factory.shutdown()).resolves.not.toThrow();
    });

    it('should handle configuration validation errors gracefully', async () => {
      const initialErrorCount = factory.getMetrics().errorCount;
      
      try {
        await factory.configure({ 
          maxInstances: -5,
          poolSize: -2,
          cacheRetentionPeriod: 100
        });
      } catch (error) {
        // Expected to throw
      }
      
      const finalErrorCount = factory.getMetrics().errorCount;
      expect(finalErrorCount).toBeGreaterThanOrEqual(initialErrorCount);
    });
  });

  // ============================================================================
  // STATIC HELPER METHODS TESTS
  // ============================================================================

  describe('Static Helper Methods', () => {
    it('should create insights generator using static method', async () => {
      const generator = await GovernanceRuleInsightsGeneratorFactory.createInsightsGenerator();
      
      expect(generator).toBeDefined();
      expect(generator).toBeInstanceOf(GovernanceRuleInsightsGenerator);
      
      await GovernanceRuleInsightsGeneratorFactory.releaseInsightsGenerator(generator);
    });

    it('should create insights generator with config using static method', async () => {
      const config = createTestInsightsConfig();
      const generator = await GovernanceRuleInsightsGeneratorFactory.createInsightsGenerator(config);
      
      expect(generator).toBeDefined();
      
      await GovernanceRuleInsightsGeneratorFactory.releaseInsightsGenerator(generator);
    });

    it('should release insights generator using static method', async () => {
      const generator = await GovernanceRuleInsightsGeneratorFactory.createInsightsGenerator();
      
      await expect(
        GovernanceRuleInsightsGeneratorFactory.releaseInsightsGenerator(generator)
      ).resolves.not.toThrow();
    });

    it('should handle static methods with factory lifecycle', async () => {
      // Create using static method
      const generator = await GovernanceRuleInsightsGeneratorFactory.createInsightsGenerator();
      
      // Get instance to check state
      const factoryInstance = await GovernanceRuleInsightsGeneratorFactory.getInstance();
      const metrics = factoryInstance.getMetrics();
      
      expect(metrics.activeInstances).toBeGreaterThan(0);
      
      // Release using static method
      await GovernanceRuleInsightsGeneratorFactory.releaseInsightsGenerator(generator);
    });
  });
});