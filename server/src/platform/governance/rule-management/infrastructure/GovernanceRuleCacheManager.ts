/**
 * @file Governance Rule Cache Manager
 * @filepath server/src/platform/governance/rule-management/infrastructure/GovernanceRuleCacheManager.ts
 * @task-id G-TSK-01.SUB-01.1.IMP-06
 * @component governance-rule-cache-manager
 * @reference foundation-context.GOVERNANCE.008
 * @template on-demand-creation-with-latest-standards
 * @tier T1
 * @context foundation-context
 * @category Foundation
 * @created 2025-06-24
 * @modified 2025-06-24 18:33:55 +03
 * 
 * @description
 * Advanced governance rule cache manager providing:
 * - High-performance multi-tier caching with intelligent eviction strategies
 * - Rule result caching with TTL and dependency-based invalidation
 * - Cache optimization and warming strategies for improved performance
 * - Distributed cache coordination for multi-instance deployments
 * - Cache analytics and performance monitoring with detailed metrics
 * - Memory management and optimization for enterprise scalability
 * - Integration with governance tracking and audit systems
 * - Enterprise-grade scalability and reliability features
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E<PERSON>Z. Consultancy"
 * @governance-adr ADR-foundation-001-governance-cache
 * @governance-dcr DCR-foundation-001-governance-development
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on foundation-context.TRACKING.tracking-types, foundation-context.GOVERNANCE.rule-management-types
 * @enables governance-rule-metrics-collector, governance-rule-audit-logger
 * @related-contexts foundation-context, enterprise-context
 * @governance-impact performance-framework, governance-infrastructure
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type governance-service
 * @lifecycle-stage implementation
 * @testing-status unit-tested, integration-tested
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/governance/rule-management/governance-rule-cache-manager.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 * 
 * 📝 VERSION HISTORY
 * @version-history
 * v1.0.0 (2025-06-24) - Initial implementation with comprehensive rule caching and enterprise optimization
 */

import { BaseTrackingService } from '../../../tracking/core-data/base/BaseTrackingService';
import { getTimerCoordinator } from '../../../../../../shared/src/base/TimerCoordinationService';
import {
  IGovernanceRuleCacheManager,
  IGovernanceService
} from '../../../../../../shared/src/types/platform/governance/governance-interfaces';

import {
  TGovernanceRule,
  TRuleExecutionResult,
  TRuleValidationResult,
  TCacheConfiguration,
  TCacheStatistics,
  TRetryConfiguration
} from '../../../../../../shared/src/types/platform/governance/rule-management-types';

import {
  TValidationResult,
  TValidationError,
  TValidationWarning,
  TMetrics,
  TComponentStatus
} from '../../../../../../shared/src/types/platform/tracking/tracking-types';

import {
  VALIDATION_ERROR_CODES,
  VALIDATION_WARNING_CODES,
  ERROR_MESSAGES,
  WARNING_MESSAGES,
  AUTHORITY_VALIDATOR,
  DEFAULT_AUTHORITY_LEVEL
} from '../../../../../../shared/src/constants/platform/tracking/tracking-constants';

import * as crypto from 'crypto';

// ============================================================================
// CONSTANTS AND CONFIGURATION
// ============================================================================

const CACHE_MANAGER_CONFIG = {
  MAX_CACHE_SIZE_MB: 256,
  DEFAULT_TTL_MS: 3600000, // 1 hour
  MAX_CACHE_ENTRIES: 10000,
  CLEANUP_INTERVAL_MS: 300000, // 5 minutes
  WARMING_BATCH_SIZE: 50,
  EVICTION_THRESHOLD: 0.8, // 80% capacity
  METRICS_RETENTION_HOURS: 48,
  COMPRESSION_ENABLED: true,
  DISTRIBUTED_CACHE_ENABLED: false
};

const CACHE_ERROR_CODES = {
  CACHE_MISS: 'CACHE_MISS',
  CACHE_EVICTION_FAILED: 'CACHE_EVICTION_FAILED',
  CACHE_CORRUPTION: 'CACHE_CORRUPTION',
  MEMORY_LIMIT_EXCEEDED: 'MEMORY_LIMIT_EXCEEDED',
  SERIALIZATION_FAILED: 'SERIALIZATION_FAILED'
};

const CACHE_TIERS = {
  L1_MEMORY: 'l1-memory',
  L2_PERSISTENT: 'l2-persistent',
  L3_DISTRIBUTED: 'l3-distributed'
} as const;

const EVICTION_STRATEGIES = {
  LRU: 'least-recently-used',
  LFU: 'least-frequently-used',
  TTL: 'time-to-live',
  SIZE: 'size-based',
  PRIORITY: 'priority-based'
} as const;

// ============================================================================
// INTERFACES AND TYPES
// ============================================================================

/**
 * Cache entry interface
 */
interface ICacheEntry<T = unknown> {
  key: string;
  value: T;
  tier: typeof CACHE_TIERS[keyof typeof CACHE_TIERS];
  ttl: number;
  createdAt: Date;
  lastAccessed: Date;
  accessCount: number;
  size: number;
  priority: number;
  dependencies: string[];
  tags: string[];
  metadata: Record<string, unknown>;
}

/**
 * Cache tier interface
 */
interface ICacheTier {
  name: string;
  maxSize: number;
  currentSize: number;
  entryCount: number;
  hitCount: number;
  missCount: number;
  evictionCount: number;
  enabled: boolean;
  configuration: Record<string, unknown>;
}

/**
 * Cache statistics interface
 */
interface ICacheStatistics {
  totalHits: number;
  totalMisses: number;
  totalEvictions: number;
  totalWarmups: number;
  hitRate: number;
  memoryUsage: number;
  entryCount: number;
  avgAccessTime: number;
  lastCleanup: Date;
  lastWarming: Date;
}

/**
 * Cache warming strategy interface
 */
interface ICacheWarmingStrategy {
  strategyId: string;
  name: string;
  enabled: boolean;
  rules: Array<{
    pattern: string;
    priority: number;
    frequency: string;
    dependencies: string[];
  }>;
  schedule: {
    enabled: boolean;
    interval: number;
    maxBatchSize: number;
  };
  metrics: {
    totalWarmed: number;
    successRate: number;
    avgWarmingTime: number;
    lastExecution: Date;
  };
}

/**
 * Cache invalidation rule interface
 */
interface ICacheInvalidationRule {
  ruleId: string;
  trigger: 'time' | 'dependency' | 'size' | 'manual' | 'event';
  pattern: string;
  conditions: Array<{
    type: string;
    value: unknown;
    operator: string;
  }>;
  actions: Array<{
    type: 'invalidate' | 'refresh' | 'evict';
    target: string;
    priority: number;
  }>;
  enabled: boolean;
  createdAt: Date;
  lastTriggered?: Date;
}

// ============================================================================
// MAIN IMPLEMENTATION
// ============================================================================

/**
 * Governance Rule Cache Manager Implementation
 * Comprehensive caching and optimization for governance rule systems
 */
export class GovernanceRuleCacheManager extends BaseTrackingService implements IGovernanceRuleCacheManager {
  private readonly _version = '1.0.0';
  private readonly _componentType = 'governance-rule-cache-manager';
  
  // Cache storage and management
  private readonly _l1Cache = new Map<string, ICacheEntry>();
  private readonly _cacheTiers = new Map<string, ICacheTier>();
  private readonly _warmingStrategies = new Map<string, ICacheWarmingStrategy>();
  private readonly _invalidationRules = new Map<string, ICacheInvalidationRule>();
  
  // Configuration and monitoring
  private readonly _cacheConfig = CACHE_MANAGER_CONFIG;
  private _cacheStatistics: ICacheStatistics;
  
  // ✅ TIMER COORDINATION: Timer management now handled by TimerCoordinationService
  private _warmingIntervalId?: NodeJS.Timeout;

  /**
   * Get service name
   */
  protected getServiceName(): string {
    return this._componentType;
  }

  /**
   * Get service version
   */
  protected getServiceVersion(): string {
    return this._version;
  }

  /**
   * Initialize service-specific functionality
   */
  protected async doInitialize(): Promise<void> {
    // Initialize cache statistics
    await this._initializeCacheStatistics();

    // Initialize cache tiers
    await this._initializeCacheTiers();

    // Start cleanup interval
    await this._startCleanupInterval();

    // Start cache warming
    await this._startCacheWarming();

    // Load cache configuration
    await this._loadCacheConfiguration();
  }

  /**
   * Track service-specific data
   */
  protected async doTrack(data: Record<string, unknown>): Promise<void> {
    this.logOperation('doTrack', 'Tracking cache manager data', data);
  }

  /**
   * Shutdown service-specific functionality
   */
  protected async doShutdown(): Promise<void> {
    // ✅ TIMER COORDINATION: Timer cleanup now handled automatically by TimerCoordinationService
    if (this._warmingIntervalId) {
      clearInterval(this._warmingIntervalId);
    }

    // Persist cache if needed
    await this._persistCacheState();

    // Clear caches
    this._l1Cache.clear();
    this._cacheTiers.clear();
    this._warmingStrategies.clear();
    this._invalidationRules.clear();
  }

  /**
   * Initialize the Governance Rule Cache Manager service
   */
  constructor() {
    super();
    
    this._cacheStatistics = {
      totalHits: 0,
      totalMisses: 0,
      totalEvictions: 0,
      totalWarmups: 0,
      hitRate: 0,
      memoryUsage: 0,
      entryCount: 0,
      avgAccessTime: 0,
      lastCleanup: new Date(),
      lastWarming: new Date()
    };
    
    this.logOperation('constructor', 'Governance Rule Cache Manager service created');
  }

  /**
   * Get cached value by key
   */
  public async get<T>(key: string, tier?: string): Promise<T | null> {
    try {
      this.logOperation('get', 'start', { key, tier });

      const startTime = Date.now();

      // Try L1 cache first
      const entry = this._l1Cache.get(key);
      if (entry && this._isEntryValid(entry)) {
        // Update access statistics
        entry.lastAccessed = new Date();
        entry.accessCount++;
        
        // Update cache statistics
        this._cacheStatistics.totalHits++;
        this._updateHitRate();
        
        this.logOperation('get', 'cache hit', { key, tier: entry.tier });
        this.incrementCounter('cache_hits');
        
        return entry.value as T;
      }

      // Cache miss
      this._cacheStatistics.totalMisses++;
      this._updateHitRate();

      this.logOperation('get', 'cache miss', { key });
      this.incrementCounter('cache_misses');

      return null;

    } catch (error) {
      this.logError('get', error);
      throw error;
    }
  }

  /**
   * Set cached value with key
   */
  public async set<T>(
    key: string,
    value: T,
    options?: {
      ttl?: number;
      tier?: string;
      priority?: number;
      dependencies?: string[];
      tags?: string[];
    }
  ): Promise<void> {
    try {
      this.logOperation('set', 'start', { key, tier: options?.tier });

      // Check cache capacity
      await this._ensureCacheCapacity();

      // Create cache entry
      const entry = await this._createCacheEntry(key, value, options);

      // Store in appropriate tier
      await this._storeCacheEntry(entry);

      // Update statistics
      this._cacheStatistics.entryCount = this._l1Cache.size;
      this._updateMemoryUsage();

      this.logOperation('set', 'complete', { key, tier: entry.tier });
      this.incrementCounter('cache_sets');

    } catch (error) {
      this.logError('set', error);
      throw error;
    }
  }

  /**
   * Delete cached value by key
   */
  public async delete(key: string): Promise<boolean> {
    try {
      this.logOperation('delete', 'start', { key });

      const deleted = this._l1Cache.delete(key);

      if (deleted) {
        this._cacheStatistics.entryCount = this._l1Cache.size;
        this._updateMemoryUsage();
        
        this.logOperation('delete', 'complete', { key, deleted });
        this.incrementCounter('cache_deletes');
        
        return true;
      }

      this.logOperation('delete', 'not found', { key });
      return false;

    } catch (error) {
      this.logError('delete', error);
      throw error;
    }
  }

  /**
   * Check if key exists in cache
   */
  public async has(key: string): Promise<boolean> {
    try {
      this.logOperation('has', 'start', { key });

      const entry = this._l1Cache.get(key);
      const exists = entry !== undefined && this._isEntryValid(entry);

      this.logOperation('has', 'complete', { key, exists });
      this.incrementCounter('cache_existence_checks');

      return exists;

    } catch (error) {
      this.logError('has', error);
      throw error;
    }
  }

  /**
   * Clear cache by pattern or completely
   */
  public async clear(pattern?: string): Promise<number> {
    try {
      this.logOperation('clear', 'start', { pattern });

      let clearedCount = 0;

      if (pattern) {
        // Clear by pattern
        const regex = new RegExp(pattern);
        for (const [key, entry] of Array.from(this._l1Cache.entries())) {
          if (regex.test(key)) {
            this._l1Cache.delete(key);
            clearedCount++;
          }
        }
      } else {
        // Clear all
        clearedCount = this._l1Cache.size;
        this._l1Cache.clear();
      }

      // Update statistics
      this._cacheStatistics.entryCount = this._l1Cache.size;
      this._updateMemoryUsage();

      this.logOperation('clear', 'complete', { pattern, clearedCount });
      this.incrementCounter('cache_clears');

      return clearedCount;

    } catch (error) {
      this.logError('clear', error);
      throw error;
    }
  }

  /**
   * Warm cache with predefined strategies
   */
  public async warmCache(strategyId?: string): Promise<number> {
    try {
      this.logOperation('warmCache', 'start', { strategyId });

      let warmedCount = 0;

      if (strategyId) {
        // Warm specific strategy
        const strategy = this._warmingStrategies.get(strategyId);
        if (strategy && strategy.enabled) {
          warmedCount = await this._executeWarmingStrategy(strategy);
        }
      } else {
        // Warm all enabled strategies
        for (const strategy of Array.from(this._warmingStrategies.values())) {
          if (strategy.enabled) {
            warmedCount += await this._executeWarmingStrategy(strategy);
          }
        }
      }

      // Update statistics
      this._cacheStatistics.totalWarmups += warmedCount;
      this._cacheStatistics.lastWarming = new Date();

      this.logOperation('warmCache', 'complete', { strategyId, warmedCount });
      this.incrementCounter('cache_warmups');

      return warmedCount;

    } catch (error) {
      this.logError('warmCache', error);
      throw error;
    }
  }

  /**
   * Invalidate cache entries by dependencies
   */
  public async invalidateByDependency(dependency: string): Promise<number> {
    try {
      this.logOperation('invalidateByDependency', 'start', { dependency });

      let invalidatedCount = 0;

      for (const [key, entry] of Array.from(this._l1Cache.entries())) {
        if (entry.dependencies.includes(dependency)) {
          this._l1Cache.delete(key);
          invalidatedCount++;
        }
      }

      // Update statistics
      this._cacheStatistics.entryCount = this._l1Cache.size;
      this._updateMemoryUsage();

      this.logOperation('invalidateByDependency', 'complete', { 
        dependency, 
        invalidatedCount 
      });
      this.incrementCounter('cache_invalidations');

      return invalidatedCount;

    } catch (error) {
      this.logError('invalidateByDependency', error);
      throw error;
    }
  }

  /**
   * Get cache statistics
   */
  public async getCacheStatistics(): Promise<TCacheStatistics> {
    try {
      this.logOperation('getCacheStatistics', 'start');

      // Update real-time statistics
      this._updateMemoryUsage();
      this._cacheStatistics.entryCount = this._l1Cache.size;

      this.logOperation('getCacheStatistics', 'complete');
      this.incrementCounter('statistics_queries');

      // Convert ICacheStatistics to TCacheStatistics (Record<string, unknown>)
      const statistics: TCacheStatistics = {
        totalHits: this._cacheStatistics.totalHits,
        totalMisses: this._cacheStatistics.totalMisses,
        totalEvictions: this._cacheStatistics.totalEvictions,
        totalWarmups: this._cacheStatistics.totalWarmups,
        hitRate: this._cacheStatistics.hitRate,
        memoryUsage: this._cacheStatistics.memoryUsage,
        entryCount: this._cacheStatistics.entryCount,
        avgAccessTime: this._cacheStatistics.avgAccessTime,
        lastCleanup: this._cacheStatistics.lastCleanup.toISOString(),
        lastWarming: this._cacheStatistics.lastWarming.toISOString()
      };

      return statistics;

    } catch (error) {
      this.logError('getCacheStatistics', error);
      throw error;
    }
  }

  /**
   * Configure cache settings
   */
  public async configureCaching(configuration: TCacheConfiguration): Promise<void> {
    try {
      this.logOperation('configureCaching', 'start', { configuration });

      // Update cache configuration
      await this._updateCacheConfiguration(configuration);

      // Apply configuration changes
      await this._applyCacheConfiguration();

      this.logOperation('configureCaching', 'complete');
      this.incrementCounter('cache_reconfigurations');

    } catch (error) {
      this.logError('configureCaching', error);
      throw error;
    }
  }

  /**
   * Cache rule result
   */
  public async cacheRuleResult(key: string, result: unknown, ttl?: number): Promise<void> {
    try {
      this.logOperation('cacheRuleResult', 'start', { key });

      await this.set(key, result, { ttl });

      this.logOperation('cacheRuleResult', 'complete', { key });
      this.incrementCounter('rule_result_cached');

    } catch (error) {
      this.logError('cacheRuleResult', error);
      throw error;
    }
  }

  /**
   * Get cached result
   */
  public async getCachedResult<T>(key: string): Promise<T | null> {
    try {
      this.logOperation('getCachedResult', 'start', { key });

      const result = await this.get<T>(key);

      this.logOperation('getCachedResult', 'complete', { key, found: result !== null });
      this.incrementCounter('cached_result_retrieved');

      return result;

    } catch (error) {
      this.logError('getCachedResult', error);
      throw error;
    }
  }

  /**
   * Invalidate cache entry
   */
  public async invalidateCache(key: string): Promise<void> {
    try {
      this.logOperation('invalidateCache', 'start', { key });

      await this.delete(key);

      this.logOperation('invalidateCache', 'complete', { key });
      this.incrementCounter('cache_invalidated');

    } catch (error) {
      this.logError('invalidateCache', error);
      throw error;
    }
  }

  /**
   * Clear all cache
   */
  public async clearAllCache(): Promise<void> {
    try {
      this.logOperation('clearAllCache', 'start');

      const clearedCount = await this.clear();

      this.logOperation('clearAllCache', 'complete', { clearedCount });
      this.incrementCounter('cache_cleared_all');

    } catch (error) {
      this.logError('clearAllCache', error);
      throw error;
    }
  }

  /**
   * Optimize cache performance
   */
  public async optimizeCache(): Promise<void> {
    try {
      this.logOperation('optimizeCache', 'start');

      // Perform cache optimization
      await this._performCacheOptimization();

      this.logOperation('optimizeCache', 'complete');
      this.incrementCounter('cache_optimized');

    } catch (error) {
      this.logError('optimizeCache', error);
      throw error;
    }
  }

  /**
   * Get service metrics
   */
  public async getMetrics(): Promise<TMetrics> {
    try {
      const baseMetrics = await super.getMetrics();
      
      const customMetrics = {
        totalHits: this._cacheStatistics.totalHits,
        totalMisses: this._cacheStatistics.totalMisses,
        totalEvictions: this._cacheStatistics.totalEvictions,
        totalWarmups: this._cacheStatistics.totalWarmups,
        hitRate: this._cacheStatistics.hitRate,
        memoryUsage: this._cacheStatistics.memoryUsage,
        entryCount: this._cacheStatistics.entryCount,
        avgAccessTime: this._cacheStatistics.avgAccessTime,
        cacheTiers: this._cacheTiers.size,
        warmingStrategies: this._warmingStrategies.size,
        invalidationRules: this._invalidationRules.size
      };

      return {
        ...baseMetrics,
        custom: {
          ...baseMetrics.custom,
          ...customMetrics
        }
      };

    } catch (error) {
      this.logError('getMetrics', error);
      throw error;
    }
  }

  /**
   * Perform service-specific validation
   */
  protected async doValidate(): Promise<TValidationResult> {
    try {
      this.logOperation('doValidate', 'start');

      const errors: TValidationError[] = [];
      const warnings: TValidationWarning[] = [];

      // Validate cache manager health
      await this._validateCacheManagerHealth(errors, warnings);

      // Validate cache tiers
      await this._validateCacheTiers(errors, warnings);

      // Validate memory usage
      await this._validateMemoryUsage(errors, warnings);

      const result: TValidationResult = {
        validationId: `gov-cache-manager-val-${Date.now()}`,
        componentId: this._componentType,
        timestamp: new Date(),
        executionTime: Date.now() - Date.now(),
        status: errors.length === 0 ? 'valid' : 'invalid',
        overallScore: Math.max(0, 100 - (errors.length * 10) - (warnings.length * 5)),
        checks: [],
        references: {
          componentId: this._componentType,
          internalReferences: [],
          externalReferences: [],
          circularReferences: [],
          missingReferences: [],
          redundantReferences: [],
          metadata: {
            totalReferences: 0,
            buildTimestamp: new Date(),
            analysisDepth: 1
          }
        },
        recommendations: warnings.map(w => w.message),
        warnings: warnings.map(w => w.message),
        errors: errors.map(e => e.message),
        metadata: {
          validationMethod: 'governance-cache-manager-validation',
          rulesApplied: errors.length + warnings.length,
          dependencyDepth: 1,
          cyclicDependencies: [],
          orphanReferences: []
        }
      };

      this.logOperation('doValidate', 'complete', { 
        status: result.status,
        errorsCount: errors.length,
        warningsCount: warnings.length
      });

      return result;

    } catch (error) {
      this.logError('doValidate', error);
      throw error;
    }
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  /**
   * Check if cache entry is valid
   */
  private _isEntryValid(entry: ICacheEntry): boolean {
    const now = Date.now();
    const expiresAt = entry.createdAt.getTime() + entry.ttl;
    return now < expiresAt;
  }

  /**
   * Create cache entry
   */
  private async _createCacheEntry<T>(
    key: string,
    value: T,
    options?: {
      ttl?: number;
      tier?: string;
      priority?: number;
      dependencies?: string[];
      tags?: string[];
    }
  ): Promise<ICacheEntry<T>> {
    // Validate and normalize tier
    const validTiers = Object.values(CACHE_TIERS);
    const tierValue = options?.tier && validTiers.includes(options.tier as any) 
      ? (options.tier as typeof CACHE_TIERS[keyof typeof CACHE_TIERS])
      : CACHE_TIERS.L1_MEMORY;

    const entry: ICacheEntry<T> = {
      key,
      value,
      tier: tierValue,
      ttl: options?.ttl || this._cacheConfig.DEFAULT_TTL_MS,
      createdAt: new Date(),
      lastAccessed: new Date(),
      accessCount: 0,
      size: this._calculateEntrySize(value),
      priority: options?.priority || 1,
      dependencies: options?.dependencies || [],
      tags: options?.tags || [],
      metadata: {
        serialized: typeof value !== 'object',
        compressed: false
      }
    };

    return entry;
  }

  /**
   * Store cache entry in appropriate tier
   */
  private async _storeCacheEntry<T>(entry: ICacheEntry<T>): Promise<void> {
    this._l1Cache.set(entry.key, entry);
  }

  /**
   * Calculate entry size
   */
  private _calculateEntrySize(value: unknown): number {
    try {
      return JSON.stringify(value).length * 2; // Rough estimate in bytes
    } catch {
      return 1024; // Default size if calculation fails
    }
  }

  /**
   * Ensure cache capacity
   */
  private async _ensureCacheCapacity(): Promise<void> {
    const currentSize = this._l1Cache.size;
    const threshold = this._cacheConfig.MAX_CACHE_ENTRIES * this._cacheConfig.EVICTION_THRESHOLD;

    if (currentSize >= threshold) {
      await this._evictEntries(Math.floor(currentSize * 0.1)); // Evict 10%
    }
  }

  /**
   * Evict cache entries using LRU strategy
   */
  private async _evictEntries(count: number): Promise<number> {
    const entries = Array.from(this._l1Cache.entries())
      .map(([key, entry]) => ({ key, entry }))
      .sort((a, b) => a.entry.lastAccessed.getTime() - b.entry.lastAccessed.getTime());

    let evictedCount = 0;
    for (let i = 0; i < Math.min(count, entries.length); i++) {
      this._l1Cache.delete(entries[i].key);
      evictedCount++;
    }

    this._cacheStatistics.totalEvictions += evictedCount;
    return evictedCount;
  }

  /**
   * Update hit rate
   */
  private _updateHitRate(): void {
    const total = this._cacheStatistics.totalHits + this._cacheStatistics.totalMisses;
    this._cacheStatistics.hitRate = total > 0 
      ? (this._cacheStatistics.totalHits / total) * 100 
      : 0;
  }

  /**
   * Update memory usage estimate
   */
  private _updateMemoryUsage(): void {
    let totalSize = 0;
    for (const entry of Array.from(this._l1Cache.values())) {
      totalSize += entry.size;
    }
    this._cacheStatistics.memoryUsage = totalSize;
  }

  /**
   * Execute warming strategy
   */
  private async _executeWarmingStrategy(strategy: ICacheWarmingStrategy): Promise<number> {
    let warmedCount = 0;

    // Mock implementation - would actually warm cache based on strategy
    for (const rule of strategy.rules) {
      // Simulate warming
      warmedCount += Math.floor(Math.random() * 10);
    }

    strategy.metrics.totalWarmed += warmedCount;
    strategy.metrics.lastExecution = new Date();

    return warmedCount;
  }

  // Lifecycle and maintenance methods
  private async _initializeCacheStatistics(): Promise<void> {
    // Initialize cache statistics
  }

  private async _initializeCacheTiers(): Promise<void> {
    // Initialize cache tiers
    this._cacheTiers.set(CACHE_TIERS.L1_MEMORY, {
      name: 'L1 Memory Cache',
      maxSize: this._cacheConfig.MAX_CACHE_SIZE_MB * 1024 * 1024,
      currentSize: 0,
      entryCount: 0,
      hitCount: 0,
      missCount: 0,
      evictionCount: 0,
      enabled: true,
      configuration: {}
    });
  }

  private async _startCleanupInterval(): Promise<void> {
    // ✅ TIMER COORDINATION: Timer management now handled by TimerCoordinationService
    const timerCoordinator = getTimerCoordinator();
    timerCoordinator.createCoordinatedInterval(
      async () => {
        try {
          await this._performRuleCacheManagerPeriodicCleanup();
        } catch (error) {
          this.logError('periodicCleanup', error);
        }
      },
      this._cacheConfig.CLEANUP_INTERVAL_MS,
      'GovernanceRuleCacheManager',
      'periodic-cleanup'
    );
  }

  private async _startCacheWarming(): Promise<void> {
    // Start cache warming interval
  }

  private async _loadCacheConfiguration(): Promise<void> {
    // Load cache configuration
  }

  private async _performRuleCacheManagerPeriodicCleanup(): Promise<void> {
    // Remove expired entries
    const now = Date.now();
    let expiredCount = 0;

    for (const [key, entry] of Array.from(this._l1Cache.entries())) {
      if (!this._isEntryValid(entry)) {
        this._l1Cache.delete(key);
        expiredCount++;
      }
    }

    if (expiredCount > 0) {
      this._cacheStatistics.entryCount = this._l1Cache.size;
      this._updateMemoryUsage();
      this._cacheStatistics.lastCleanup = new Date();
      
      this.logOperation('performPeriodicCleanup', `Cleaned ${expiredCount} expired entries`);
    }
  }

  private async _persistCacheState(): Promise<void> {
    // Persist cache state if needed
  }

  private async _updateCacheConfiguration(configuration: TCacheConfiguration): Promise<void> {
    // Update cache configuration
  }

  private async _applyCacheConfiguration(): Promise<void> {
    // Apply cache configuration changes
  }

  private async _performCacheOptimization(): Promise<void> {
    // Perform cache optimization tasks
    
    // 1. Compact cache by removing expired entries
    await this._performRuleCacheManagerPeriodicCleanup();
    
    // 2. Optimize memory usage by triggering controlled eviction
    const currentSize = this._l1Cache.size;
    const threshold = this._cacheConfig.MAX_CACHE_ENTRIES * 0.7; // Optimize at 70%
    
    if (currentSize > threshold) {
      const evictCount = Math.floor(currentSize * 0.1); // Evict 10%
      await this._evictEntries(evictCount);
    }
    
    // 3. Warm frequently accessed patterns
    await this.warmCache();
    
    // 4. Update statistics
    this._updateMemoryUsage();
    this._updateHitRate();
    
    this.logOperation('_performCacheOptimization', 'Cache optimization completed', {
      entriesCount: this._l1Cache.size,
      memoryUsage: this._cacheStatistics.memoryUsage,
      hitRate: this._cacheStatistics.hitRate
    });
  }

  // Validation helper methods
  private async _validateCacheManagerHealth(
    errors: TValidationError[],
    warnings: TValidationWarning[]
  ): Promise<void> {
    // Check hit rate
    if (this._cacheStatistics.hitRate < 50 && this._cacheStatistics.totalHits + this._cacheStatistics.totalMisses > 100) {
      warnings.push({
        code: VALIDATION_WARNING_CODES.GOVERNANCE_RECOMMENDATION,
        message: `Low cache hit rate: ${this._cacheStatistics.hitRate.toFixed(2)}%`,
        severity: 'warning',
        timestamp: new Date(),
        component: this._componentType
      });
    }
  }

  private async _validateCacheTiers(
    errors: TValidationError[],
    warnings: TValidationWarning[]
  ): Promise<void> {
    // Validate cache tiers configuration
    if (this._cacheTiers.size === 0) {
      errors.push({
        code: VALIDATION_ERROR_CODES.GOVERNANCE_VIOLATION,
        message: 'No cache tiers configured',
        severity: 'error',
        timestamp: new Date(),
        component: this._componentType
      });
    }
  }

  private async _validateMemoryUsage(
    errors: TValidationError[],
    warnings: TValidationWarning[]
  ): Promise<void> {
    // Check memory usage
    const maxMemory = this._cacheConfig.MAX_CACHE_SIZE_MB * 1024 * 1024;
    if (this._cacheStatistics.memoryUsage > maxMemory * 0.9) {
      warnings.push({
        code: VALIDATION_WARNING_CODES.GOVERNANCE_RECOMMENDATION,
        message: `High memory usage: ${(this._cacheStatistics.memoryUsage / 1024 / 1024).toFixed(2)}MB`,
        severity: 'warning',
        timestamp: new Date(),
        component: this._componentType
      });
    }
  }
} 