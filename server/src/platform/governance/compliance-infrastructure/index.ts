/**
 * @file Compliance Infrastructure Index
 * @filepath server/src/platform/governance/compliance-infrastructure/index.ts
 * @reference G-SUB-04.2
 * @component compliance-infrastructure-index
 * @template on-demand-creation-with-latest-standards
 * @tier T1
 * @context foundation-context
 * @category Foundation
 * @created 2025-06-29
 * @modified 2025-06-29 13:12:03 +03
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-governance-architecture
 * @governance-dcr DCR-foundation-001-governance-development
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @enables compliance-infrastructure, governance-framework
 * @related-contexts foundation-context, enterprise-context
 * @governance-impact framework-foundation, compliance-infrastructure
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type module-index
 * @lifecycle-stage implementation
 * @testing-status validated
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/governance/compliance/compliance-infrastructure.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 */

/**
 * Compliance Infrastructure Components Index
 * G-SUB-04.2 Implementation
 */

import { GovernanceRuleComplianceChecker } from './GovernanceRuleComplianceChecker';
import { GovernanceRuleComplianceFramework } from './GovernanceRuleComplianceFramework';
import { GovernanceRuleQualityFramework } from './GovernanceRuleQualityFramework';

export { GovernanceRuleComplianceChecker } from './GovernanceRuleComplianceChecker';
export { GovernanceRuleComplianceFramework } from './GovernanceRuleComplianceFramework';
export { GovernanceRuleQualityFramework } from './GovernanceRuleQualityFramework';

// ============================================================================
// CONVENIENCE EXPORTS
// ============================================================================

/**
 * All compliance infrastructure components
 */
export const COMPLIANCE_INFRASTRUCTURE_COMPONENTS = {
  GovernanceRuleComplianceChecker,
  GovernanceRuleComplianceFramework,
  GovernanceRuleQualityFramework
} as const;

/**
 * Component metadata for compliance infrastructure
 */
export const COMPLIANCE_INFRASTRUCTURE_METADATA = {
  subsystem: 'G-SUB-04.2',
  name: 'Compliance Infrastructure',
  version: '1.0.0',
  components: [
    {
      id: 'G-TSK-04.SUB-04.2.IMP-01',
      name: 'governance-rule-compliance-checker',
      class: 'GovernanceRuleComplianceChecker',
      implements: ['IComplianceChecker', 'IComplianceService'],
      inheritance: 'BaseTrackingService'
    },
    {
      id: 'G-TSK-04.SUB-04.2.IMP-02',
      name: 'governance-rule-compliance-framework',
      class: 'GovernanceRuleComplianceFramework',
      implements: ['IComplianceFramework', 'IFrameworkService'],
      inheritance: 'BaseTrackingService'
    },
    {
      id: 'G-TSK-04.SUB-04.2.IMP-03',
      name: 'governance-rule-quality-framework',
      class: 'GovernanceRuleQualityFramework',
      implements: ['IQualityFramework', 'IQualityService'],
      inheritance: 'BaseTrackingService'
    }
  ],
  authority: 'President & CEO, E.Z. Consultancy',
  governance: 'authority-validated',
  deployment: 'production-ready'
} as const; 