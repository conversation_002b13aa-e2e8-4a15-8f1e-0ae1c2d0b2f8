/**
 * @file GovernanceRuleSecurityPolicy Tests
 * @filepath server/src/platform/governance/management-configuration/__tests__/GovernanceRuleSecurityPolicy.test.ts
 * @reference G-TSK-07.SUB-07.2.TEST-03
 * @template templates/contexts/foundation-context/governance/security-test.template
 * @tier T2
 * @context foundation-context
 * @category Foundation
 * @created 2025-01-27
 * @modified 2025-07-04
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-009-template-security-architecture
 * @governance-dcr DCR-foundation-008-template-security-standards
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @tests server/src/platform/governance/management-configuration/GovernanceRuleSecurityPolicy
 * @depends-on shared/src/types/platform/governance/security-types
 * @related-contexts foundation-context, governance-context, security-context
 * @governance-impact framework-foundation, security-policy-testing
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type governance-security-policy-test
 * @lifecycle-stage testing
 * @testing-status comprehensive
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/contexts/foundation-context/governance/GovernanceRuleSecurityPolicy-tests.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 */

import { GovernanceRuleSecurityPolicy } from '../GovernanceRuleSecurityPolicy';

describe('GovernanceRuleSecurityPolicy', () => {
  let securityPolicy: GovernanceRuleSecurityPolicy;

  beforeEach(() => {
    securityPolicy = new GovernanceRuleSecurityPolicy();
  });

  afterEach(() => {
    securityPolicy.shutdown();
  });

  describe('Service Lifecycle', () => {
    test('should initialize successfully', async () => {
      await expect(securityPolicy.initialize()).resolves.not.toThrow();
    });

    test('should shutdown gracefully', async () => {
      await securityPolicy.initialize();
      await expect(securityPolicy.shutdown()).resolves.not.toThrow();
    });
  });

  describe('Security Configuration', () => {
    test('should get security configuration', () => {
      const config = securityPolicy.getSecurityConfig();
      expect(config).toBeDefined();
      expect(typeof config).toBe('object');
    });

    test('should update security configuration', async () => {
      const updates = {
        contentSecurityPolicy: true,
        securityHeaders: {
          'X-Frame-Options': 'DENY',
          'X-Content-Type-Options': 'nosniff'
        }
      };
      
      await expect(securityPolicy.updateSecurityConfig(updates)).resolves.not.toThrow();
    });
  });

  describe('XSS Protection', () => {
    test('should enforce XSS protection on content', async () => {
      const maliciousContent = '<script>alert("XSS")</script><div>Safe content</div>';
      const context = {
        templateId: 'test-template',
        securityLevel: 'standard' as const,
        allowedHelpers: ['if', 'each'],
        blockedHelpers: ['eval'],
        allowedVariables: ['user', 'data'],
        blockedVariables: ['system'],
        sanitizationRules: [],
        validationRules: [],
        cspDirectives: {},
        trustedSources: []
      };
      
      const sanitizedContent = await securityPolicy.enforceXSSProtection(maliciousContent, context);
      
      expect(sanitizedContent).toBeDefined();
      expect(typeof sanitizedContent).toBe('string');
      expect(sanitizedContent).not.toContain('<script>');
      expect(sanitizedContent).toContain('Safe content');
    });

    test('should preserve safe content during XSS protection', async () => {
      const safeContent = '<div><p>This is safe content</p></div>';
      const context = {
        templateId: 'test-template',
        securityLevel: 'standard' as const,
        allowedHelpers: ['if', 'each'],
        blockedHelpers: ['eval'],
        allowedVariables: ['user', 'data'],
        blockedVariables: ['system'],
        sanitizationRules: [],
        validationRules: [],
        cspDirectives: {},
        trustedSources: []
      };
      
      const sanitizedContent = await securityPolicy.enforceXSSProtection(safeContent, context);
      
      expect(sanitizedContent).toBeDefined();
      expect(sanitizedContent).toContain('This is safe content');
    });
  });

  describe('Content Security Policy', () => {
    test('should generate security headers for template context', async () => {
      const headers = await securityPolicy.generateSecurityHeaders('template');
      
      expect(headers).toBeDefined();
      expect(typeof headers).toBe('object');
      expect(Object.keys(headers).length).toBeGreaterThan(0);
    });

    test('should generate security headers for API context', async () => {
      const headers = await securityPolicy.generateSecurityHeaders('api');
      
      expect(headers).toBeDefined();
      expect(typeof headers).toBe('object');
    });

    test('should generate security headers for UI context', async () => {
      const headers = await securityPolicy.generateSecurityHeaders('ui');
      
      expect(headers).toBeDefined();
      expect(typeof headers).toBe('object');
    });
  });

  describe('Security Compliance', () => {
    test('should validate security compliance', async () => {
      const isCompliant = await securityPolicy.validateSecurityCompliance();
      expect(typeof isCompliant).toBe('boolean');
    });

    test('should generate security report', async () => {
      const report = await securityPolicy.generateSecurityReport();
      expect(report).toBeDefined();
      expect(typeof report).toBe('object');
    });
  });

  describe('Security Audit', () => {
    test('should audit security events', async () => {
      const event = {
        logId: 'test-log-id',
        timestamp: new Date(),
        eventType: 'violation' as const,
        action: 'policy_enforcement',
        resource: 'security-policy',
        result: 'success' as const,
        details: { test: true },
        riskLevel: 'medium' as const
      };
      
      await expect(securityPolicy.auditSecurityEvent(event)).resolves.not.toThrow();
    });
  });

  describe('Error Handling', () => {
    test('should handle null content gracefully', async () => {
      const context = {
        templateId: 'test-template',
        securityLevel: 'standard' as const,
        allowedHelpers: [],
        blockedHelpers: [],
        allowedVariables: [],
        blockedVariables: [],
        sanitizationRules: [],
        validationRules: [],
        cspDirectives: {},
        trustedSources: []
      };
      
      const result = await securityPolicy.enforceXSSProtection(null as any, context);
      
      expect(result).toBeDefined();
      expect(typeof result).toBe('string');
    });

    test('should handle empty context gracefully', async () => {
      const context = {
        templateId: '',
        securityLevel: 'standard' as const,
        allowedHelpers: [],
        blockedHelpers: [],
        allowedVariables: [],
        blockedVariables: [],
        sanitizationRules: [],
        validationRules: [],
        cspDirectives: {},
        trustedSources: []
      };
      
      const result = await securityPolicy.enforceXSSProtection('test content', context);
      
      expect(result).toBeDefined();
      expect(typeof result).toBe('string');
    });
  });

  describe('Performance Tests', () => {
    test('should enforce XSS protection efficiently', async () => {
      const content = '<div>Test content</div>'.repeat(100);
      const context = {
        templateId: 'test-template',
        securityLevel: 'standard' as const,
        allowedHelpers: [],
        blockedHelpers: [],
        allowedVariables: [],
        blockedVariables: [],
        sanitizationRules: [],
        validationRules: [],
        cspDirectives: {},
        trustedSources: []
      };
      
      const startTime = Date.now();
      const result = await securityPolicy.enforceXSSProtection(content, context);
      const endTime = Date.now();
      
      expect(endTime - startTime).toBeLessThan(1000); // Should complete within 1 second
      expect(result).toBeDefined();
    });
  });
}); 