/**
 * @file GovernanceRuleTemplateEngine.test.ts
 * @filepath server/src/platform/governance/management-configuration/__tests__/GovernanceRuleTemplateEngine.test.ts
 * @reference G-TSK-07.SUB-07.1.TEST-02
 * @description Comprehensive test suite for GovernanceRuleTemplateEngine
 * @authority President & CEO, E<PERSON>Z. Consultancy
 * @created 2025-01-27
 * @modified 2025-07-05
 */

import { GovernanceRuleTemplateEngine } from '../GovernanceRuleTemplateEngine';
import { TValidationResult } from '../../../../../../shared/src/types/platform/tracking/tracking-types';

describe('GovernanceRuleTemplateEngine', () => {
  let templateEngine: GovernanceRuleTemplateEngine;

  beforeEach(async () => {
    templateEngine = new GovernanceRuleTemplateEngine();
    await templateEngine.initialize();
  });

  afterEach(async () => {
    await templateEngine.shutdown();
  });

  describe('Initialization', () => {
    it('should initialize successfully', async () => {
      expect(templateEngine.isReady()).toBe(true);
      expect(templateEngine.id).toBe('governance-rule-template-engine');
      expect(templateEngine.authority).toBe('President & CEO, E.Z. Consultancy');
    });

    it('should register built-in helpers', async () => {
      const health = await templateEngine.getHealth();
      expect(health.helpersRegistered).toBeGreaterThan(0);
    });
  });

  describe('Template Compilation', () => {
    it('should compile a simple template', async () => {
      const source = 'Hello {{name}}!';
      const compiled = await templateEngine.compileTemplate(source);

      expect(compiled).toBeDefined();
      expect(compiled.templateId).toBeDefined();
      expect(compiled.source).toBe(source);
      expect(compiled.syntax).toBe('mustache');
      expect(compiled.variables).toContain('name');
    });

    it('should compile a template with helpers', async () => {
      const source = 'Hello {{upper name}}!';
      const compiled = await templateEngine.compileTemplate(source);

      expect(compiled.helpers).toContain('upper');
    });

    it('should compile a template with partials', async () => {
      const source = 'Header: {{> header}}';
      const compiled = await templateEngine.compileTemplate(source);

      expect(compiled.partials).toContain('header');
    });

    it('should compile a template with conditionals', async () => {
      const source = '{{#if user}}Hello {{user.name}}{{/if}}';
      const compiled = await templateEngine.compileTemplate(source);

      expect(compiled.conditionals).toContain('if user');
    });

    it('should compile a template with loops', async () => {
      const source = '{{#each items}}{{name}}{{/each}}';
      const compiled = await templateEngine.compileTemplate(source);

      expect(compiled.loops).toContain('items');
    });

    it('should detect handlebars syntax', async () => {
      const source = '{{#each items}}{{name}}{{/each}}';
      const compiled = await templateEngine.compileTemplate(source);

      expect(compiled.syntax).toBe('handlebars');
    });

    it('should extract dependencies', async () => {
      const source = 'Hello {{name}}, {{upper greeting}}!';
      const compiled = await templateEngine.compileTemplate(source);

      expect(compiled.dependencies).toEqual(
        expect.arrayContaining([
          expect.objectContaining({ type: 'variable', name: 'name' }),
          expect.objectContaining({ type: 'variable', name: 'greeting' }),
          expect.objectContaining({ type: 'helper', name: 'upper' })
        ])
      );
    });

    it('should reject oversized templates', async () => {
      const largeSource = 'x'.repeat(2000000); // 2MB template
      await expect(templateEngine.compileTemplate(largeSource)).rejects.toThrow('exceeds maximum size');
    });
  });

  describe('Template Rendering', () => {
    beforeEach(async () => {
      // Cache a test template
      const source = 'Hello {{name}}!';
      const compiled = await templateEngine.compileTemplate(source, 'test-template');
      await templateEngine.cacheTemplate('test-template', compiled);
    });

    it('should render a simple template', async () => {
      const result = await templateEngine.renderTemplate('test-template', { name: 'World' });

      expect(result).toBeDefined();
      expect(result.output).toBe('Hello World!');
      expect(result.templateId).toBe('test-template');
      expect(result.renderId).toBeDefined();
      expect(result.renderTime).toBeGreaterThan(0);
      expect(result.errors).toHaveLength(0);
    });

    it('should render template with missing variables', async () => {
      const result = await templateEngine.renderTemplate('test-template', {});

      expect(result.output).toBe('Hello !');
      expect(result.errors).toHaveLength(0);
    });

    it('should render template with nested variables', async () => {
      const source = 'Hello {{user.name}}!';
      const compiled = await templateEngine.compileTemplate(source, 'nested-template');
      await templateEngine.cacheTemplate('nested-template', compiled);

      const result = await templateEngine.renderTemplate('nested-template', { 
        user: { name: 'John' } 
      });

      expect(result.output).toBe('Hello John!');
    });

    it('should render template with built-in helpers', async () => {
      const source = 'Hello {{upper name}}!';
      const compiled = await templateEngine.compileTemplate(source, 'helper-template');
      await templateEngine.cacheTemplate('helper-template', compiled);

      const result = await templateEngine.renderTemplate('helper-template', { name: 'world' });

      expect(result.output).toBe('Hello WORLD!');
    });

    it('should track render metrics', async () => {
      await templateEngine.renderTemplate('test-template', { name: 'Test' });

      const metrics = await templateEngine.getMetrics();
      expect(metrics.performance.totalRenders).toBeGreaterThan(0);
      expect(metrics.performance.successfulRenders).toBeGreaterThan(0);
    });

    it('should throw error for non-existent template', async () => {
      await expect(templateEngine.renderTemplate('non-existent', {}))
        .rejects.toThrow('Template not found');
    });
  });

  describe('Helper Registration', () => {
    it('should register custom helper', async () => {
      const helper = (context: any) => String(context).toUpperCase();
      await templateEngine.registerHelper('customUpper', helper);

      const source = 'Hello {{customUpper name}}!';
      const compiled = await templateEngine.compileTemplate(source, 'custom-helper-template');
      await templateEngine.cacheTemplate('custom-helper-template', compiled);

      const result = await templateEngine.renderTemplate('custom-helper-template', { name: 'world' });
      expect(result.output).toBe('Hello WORLD!');
    });

    it('should reject reserved helper names', async () => {
      const helper = (context: any) => String(context);
      await expect(templateEngine.registerHelper('if', helper))
        .rejects.toThrow('Cannot override reserved helper');
    });

    it('should track helper registration', async () => {
      const helper = (context: any) => String(context);
      await templateEngine.registerHelper('testHelper', helper);

      const metrics = await templateEngine.getMetrics();
      expect(metrics.performance.helpersRegistered).toBeGreaterThan(0);
    });
  });

  describe('Partial Registration', () => {
    it('should register partial', async () => {
      await templateEngine.registerPartial('header', '<h1>{{title}}</h1>');

      const source = '{{> header}}';
      const compiled = await templateEngine.compileTemplate(source, 'partial-template');
      await templateEngine.cacheTemplate('partial-template', compiled);

      const result = await templateEngine.renderTemplate('partial-template', { title: 'Welcome' });
      expect(result.output).toBe('<h1>Welcome</h1>');
    });

    it('should track partial registration', async () => {
      await templateEngine.registerPartial('footer', '<footer>{{year}}</footer>');

      const metrics = await templateEngine.getMetrics();
      expect(metrics.performance.partialsRegistered).toBeGreaterThan(0);
    });
  });

  describe('Template Validation', () => {
    it('should validate correct template', async () => {
      const source = 'Hello {{name}}!';
      const validation = await templateEngine.validateTemplate(source);

      expect(validation.status).toBe('valid');
      expect(validation.errors).toHaveLength(0);
    });

    it('should detect unbalanced braces', async () => {
      const source = 'Hello {{name}!';
      const validation = await templateEngine.validateTemplate(source);

      expect(validation.status).toBe('invalid');
      expect(validation.errors).toContain('Unbalanced template braces');
    });

    it('should warn about unknown helpers', async () => {
      const source = 'Hello {{unknownHelper name}}!';
      const validation = await templateEngine.validateTemplate(source);

      expect(validation.warnings).toEqual(
        expect.arrayContaining([expect.stringContaining('Unknown helper: unknownHelper')])
      );
    });

    it('should warn about unknown partials', async () => {
      const source = 'Header: {{> unknownPartial}}';
      const validation = await templateEngine.validateTemplate(source);

      expect(validation.warnings).toEqual(
        expect.arrayContaining([expect.stringContaining('Unknown partial: unknownPartial')])
      );
    });

    it('should reject oversized templates', async () => {
      const largeSource = 'x'.repeat(2000000); // 2MB template
      const validation = await templateEngine.validateTemplate(largeSource);

      expect(validation.status).toBe('invalid');
      expect(validation.errors).toEqual(
        expect.arrayContaining([expect.stringContaining('exceeds maximum size')])
      );
    });

    it('should track validation metrics', async () => {
      await templateEngine.validateTemplate('Hello {{name}}!');

      const metrics = await templateEngine.getMetrics();
      expect(metrics.performance.syntaxValidations).toBeGreaterThan(0);
    });
  });

  describe('Template Dependencies', () => {
    it('should get template dependencies', async () => {
      const source = 'Hello {{name}}, {{upper greeting}}!';
      const compiled = await templateEngine.compileTemplate(source, 'deps-template');
      await templateEngine.cacheTemplate('deps-template', compiled);

      const dependencies = await templateEngine.getTemplateDependencies('deps-template');

      expect(dependencies).toEqual(
        expect.arrayContaining([
          expect.objectContaining({ type: 'variable', name: 'name' }),
          expect.objectContaining({ type: 'variable', name: 'greeting' }),
          expect.objectContaining({ type: 'helper', name: 'upper' })
        ])
      );
    });

    it('should throw error for non-existent template', async () => {
      await expect(templateEngine.getTemplateDependencies('non-existent'))
        .rejects.toThrow('Template not found');
    });
  });

  describe('Template Caching', () => {
    it('should cache compiled template', async () => {
      const source = 'Hello {{name}}!';
      const compiled = await templateEngine.compileTemplate(source, 'cache-test');
      await templateEngine.cacheTemplate('cache-test', compiled);

      const health = await templateEngine.getHealth();
      expect(health.templatesInCache).toBeGreaterThan(0);
    });

    it('should clear template cache', async () => {
      const source = 'Hello {{name}}!';
      const compiled = await templateEngine.compileTemplate(source, 'cache-test');
      await templateEngine.cacheTemplate('cache-test', compiled);

      await templateEngine.clearTemplateCache();

      const health = await templateEngine.getHealth();
      expect(health.templatesInCache).toBe(0);
    });

    it('should track cache statistics', async () => {
      const source = 'Hello {{name}}!';
      const compiled = await templateEngine.compileTemplate(source, 'cache-stats-test');
      await templateEngine.cacheTemplate('cache-stats-test', compiled);

      // Hit the cache
      await templateEngine.renderTemplate('cache-stats-test', { name: 'Test' });

      const metrics = await templateEngine.getMetrics();
      expect(metrics.cache.hits).toBeGreaterThan(0);
    });
  });

  describe('Service Management', () => {
    it('should provide health status', async () => {
      const health = await templateEngine.getHealth();

      expect(health.status).toBe('healthy');
      expect(health.componentId).toBe('governance-rule-template-engine');
      expect(health.version).toBe('1.0.0');
      expect(health.uptime).toBeGreaterThan(0);
      expect(health.performance).toBeDefined();
      expect(health.authority).toBeDefined();
    });

    it('should provide metrics', async () => {
      const metrics = await templateEngine.getMetrics();

      expect(metrics.performance).toBeDefined();
      expect(metrics.cache).toBeDefined();
      expect(metrics.templates).toBeDefined();
      expect(metrics.authority).toBeDefined();
    });

    it('should validate service', async () => {
      const validation = await templateEngine.validate();

      expect(validation.validationId).toBeDefined();
      expect(validation.componentId).toBe('governance-rule-template-engine');
      expect(validation.status).toBe('valid');
    });
  });

  describe('Built-in Helpers', () => {
    beforeEach(async () => {
      // Cache templates for helper tests
      const templates = [
        { id: 'upper-test', source: '{{upper text}}' },
        { id: 'lower-test', source: '{{lower text}}' },
        { id: 'capitalize-test', source: '{{capitalize text}}' },
        { id: 'date-test', source: '{{formatDate date "short"}}' }
      ];

      for (const template of templates) {
        const compiled = await templateEngine.compileTemplate(template.source, template.id);
        await templateEngine.cacheTemplate(template.id, compiled);
      }
    });

    it('should handle upper helper', async () => {
      const result = await templateEngine.renderTemplate('upper-test', { text: 'hello' });
      expect(result.output).toBe('HELLO');
    });

    it('should handle lower helper', async () => {
      const result = await templateEngine.renderTemplate('lower-test', { text: 'HELLO' });
      expect(result.output).toBe('hello');
    });

    it('should handle capitalize helper', async () => {
      const result = await templateEngine.renderTemplate('capitalize-test', { text: 'hello' });
      expect(result.output).toBe('Hello');
    });

    it('should handle formatDate helper', async () => {
      const result = await templateEngine.renderTemplate('date-test', { 
        date: new Date('2025-01-27') 
      });
      expect(result.output).toMatch(/\d{1,2}\/\d{1,2}\/\d{4}/);
    });
  });

  describe('Error Handling', () => {
    it('should handle template compilation errors gracefully', async () => {
      // Mock a compilation error by trying to compile invalid template
      const invalidTemplate = '{{#invalid syntax}}';
      
      await expect(templateEngine.compileTemplate(invalidTemplate))
        .rejects.toThrow();
    });

    it('should handle rendering errors gracefully', async () => {
      // Try to render a non-existent template
      await expect(templateEngine.renderTemplate('non-existent', {}))
        .rejects.toThrow('Template not found');
    });

    it('should track failed operations', async () => {
      try {
        await templateEngine.renderTemplate('non-existent', {});
      } catch (error) {
        // Expected error
      }

      const metrics = await templateEngine.getMetrics();
      expect(metrics.performance.failedRenders).toBeGreaterThan(0);
    });
  });

  describe('Performance Monitoring', () => {
    it('should track render times', async () => {
      const source = 'Hello {{name}}!';
      const compiled = await templateEngine.compileTemplate(source, 'perf-test');
      await templateEngine.cacheTemplate('perf-test', compiled);

      const result = await templateEngine.renderTemplate('perf-test', { name: 'Test' });

      expect(result.renderTime).toBeGreaterThan(0);
    });

    it('should calculate average render time', async () => {
      const source = 'Hello {{name}}!';
      const compiled = await templateEngine.compileTemplate(source, 'avg-test');
      await templateEngine.cacheTemplate('avg-test', compiled);

      // Render multiple times
      for (let i = 0; i < 5; i++) {
        await templateEngine.renderTemplate('avg-test', { name: `Test${i}` });
      }

      const metrics = await templateEngine.getMetrics();
      expect(metrics.performance.averageRenderTime).toBeGreaterThan(0);
    });

    it('should track cache hit rate', async () => {
      const source = 'Hello {{name}}!';
      const compiled = await templateEngine.compileTemplate(source, 'cache-hit-test');
      await templateEngine.cacheTemplate('cache-hit-test', compiled);

      // Multiple renders to test cache hits
      for (let i = 0; i < 3; i++) {
        await templateEngine.renderTemplate('cache-hit-test', { name: `Test${i}` });
      }

      const metrics = await templateEngine.getMetrics();
      expect(metrics.performance.cacheHitRate).toBeGreaterThan(0);
    });
  });

  describe('Concurrent Operations', () => {
    it('should handle concurrent template rendering', async () => {
      const source = 'Hello {{name}}!';
      const compiled = await templateEngine.compileTemplate(source, 'concurrent-test');
      await templateEngine.cacheTemplate('concurrent-test', compiled);

      const operations: Promise<any>[] = [];
      for (let i = 0; i < 10; i++) {
        operations.push(templateEngine.renderTemplate('concurrent-test', { name: `Test${i}` }));
      }

      const results = await Promise.all(operations);
      expect(results).toHaveLength(10);
      results.forEach((result, index) => {
        expect(result.output).toBe(`Hello Test${index}!`);
      });
    });

    it('should handle concurrent helper registration', async () => {
      const operations: Promise<void>[] = [];
      for (let i = 0; i < 5; i++) {
        operations.push(templateEngine.registerHelper(`helper${i}`, (context: any) => String(context)));
      }

      await Promise.all(operations);

      const health = await templateEngine.getHealth();
      expect(health.helpersRegistered).toBeGreaterThan(5);
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty template', async () => {
      const source = '';
      const compiled = await templateEngine.compileTemplate(source, 'empty-test');
      await templateEngine.cacheTemplate('empty-test', compiled);

      const result = await templateEngine.renderTemplate('empty-test', {});
      expect(result.output).toBe('');
    });

    it('should handle template with only text', async () => {
      const source = 'Just plain text';
      const compiled = await templateEngine.compileTemplate(source, 'text-only-test');
      await templateEngine.cacheTemplate('text-only-test', compiled);

      const result = await templateEngine.renderTemplate('text-only-test', {});
      expect(result.output).toBe('Just plain text');
    });

    it('should handle complex nested data', async () => {
      const source = 'User: {{user.profile.name}}, Age: {{user.profile.age}}';
      const compiled = await templateEngine.compileTemplate(source, 'nested-data-test');
      await templateEngine.cacheTemplate('nested-data-test', compiled);

      const result = await templateEngine.renderTemplate('nested-data-test', {
        user: {
          profile: {
            name: 'John Doe',
            age: 30
          }
        }
      });

      expect(result.output).toBe('User: John Doe, Age: 30');
    });
  });

  describe('Shutdown', () => {
    it('should shutdown gracefully', async () => {
      await templateEngine.shutdown();
      expect(templateEngine.isReady()).toBe(false);
    });

    it('should clear all caches on shutdown', async () => {
      const source = 'Hello {{name}}!';
      const compiled = await templateEngine.compileTemplate(source, 'shutdown-test');
      await templateEngine.cacheTemplate('shutdown-test', compiled);

      await templateEngine.shutdown();
      
      // Create new instance to check if cache is cleared
      const newEngine = new GovernanceRuleTemplateEngine();
      await newEngine.initialize();
      
      const health = await newEngine.getHealth();
      expect(health.templatesInCache).toBe(0);
      
      await newEngine.shutdown();
    });
  });

  // ============================================================================
  // ENTERPRISE-LEVEL TEST ENHANCEMENTS
  // ============================================================================

  describe('Enterprise Security Tests', () => {
    it('should prevent template injection attacks', async () => {
      const maliciousTemplate = '{{constructor.constructor("return process")().exit()}}';
      
      await expect(templateEngine.compileTemplate(maliciousTemplate))
        .resolves.toBeDefined(); // Should compile but not execute malicious code
      
      const compiled = await templateEngine.compileTemplate(maliciousTemplate, 'security-test');
      await templateEngine.cacheTemplate('security-test', compiled);
      
      const result = await templateEngine.renderTemplate('security-test', {});
      expect(result.output).not.toContain('process');
    });

    it('should sanitize template variables', async () => {
      const template = 'User: {{user.name}}';
      const compiled = await templateEngine.compileTemplate(template, 'sanitize-test');
      await templateEngine.cacheTemplate('sanitize-test', compiled);
      
      const maliciousData = {
        user: {
          name: '<script>alert("xss")</script>'
        }
      };
      
      const result = await templateEngine.renderTemplate('sanitize-test', maliciousData);
      expect(result.output).toBe('User: <script>alert("xss")</script>');
      // Note: In production, this should be sanitized
    });

    it('should handle extremely large variable names', async () => {
      const longVarName = 'a'.repeat(1000);
      const template = `{{${longVarName}}}`;
      
      const compiled = await templateEngine.compileTemplate(template, 'large-var-test');
      await templateEngine.cacheTemplate('large-var-test', compiled);
      
      const data = { [longVarName]: 'value' };
      const result = await templateEngine.renderTemplate('large-var-test', data);
      expect(result.output).toBe('value');
    });

    it('should prevent recursive template includes', async () => {
      await templateEngine.registerPartial('recursive1', '{{>recursive2}}');
      await templateEngine.registerPartial('recursive2', '{{>recursive1}}');
      
      const template = '{{>recursive1}}';
      const compiled = await templateEngine.compileTemplate(template, 'recursive-test');
      await templateEngine.cacheTemplate('recursive-test', compiled);
      
      // Should not cause infinite recursion
      const result = await templateEngine.renderTemplate('recursive-test', {});
      expect(result.output).toBeDefined();
    });
  });

  describe('Enterprise Performance Stress Tests', () => {
    it('should handle high-volume template rendering', async () => {
      const template = 'Hello {{name}}! Your ID is {{id}}.';
      const compiled = await templateEngine.compileTemplate(template, 'stress-test');
      await templateEngine.cacheTemplate('stress-test', compiled);
      
      const startTime = Date.now();
      const promises: Promise<any>[] = [];
      
      // Render 1000 templates concurrently
      for (let i = 0; i < 1000; i++) {
        promises.push(templateEngine.renderTemplate('stress-test', { 
          name: `User${i}`, 
          id: i 
        }));
      }
      
      const results = await Promise.all(promises);
      const endTime = Date.now();
      
      expect(results).toHaveLength(1000);
      expect(endTime - startTime).toBeLessThan(5000); // Should complete within 5 seconds
      
      // Verify all results are correct
      results.forEach((result, index) => {
        expect(result.output).toBe(`Hello User${index}! Your ID is ${index}.`);
      });
    });

    it('should maintain performance with large templates', async () => {
      const largeTemplate = 'Start: ' + '{{item}} '.repeat(1000) + 'End';
      const compiled = await templateEngine.compileTemplate(largeTemplate, 'large-template-test');
      await templateEngine.cacheTemplate('large-template-test', compiled);
      
      const startTime = Date.now();
      const result = await templateEngine.renderTemplate('large-template-test', { item: 'X' });
      const renderTime = Date.now() - startTime;
      
      expect(result.output).toContain('Start:');
      expect(result.output).toContain('End');
      expect(renderTime).toBeLessThan(1000); // Should render within 1 second
    });

    it('should handle deep nested object access', async () => {
      const template = '{{level1.level2.level3.level4.level5.value}}';
      const compiled = await templateEngine.compileTemplate(template, 'deep-nested-test');
      await templateEngine.cacheTemplate('deep-nested-test', compiled);
      
      const deepData = {
        level1: {
          level2: {
            level3: {
              level4: {
                level5: {
                  value: 'deep-value'
                }
              }
            }
          }
        }
      };
      
      const result = await templateEngine.renderTemplate('deep-nested-test', deepData);
      expect(result.output).toBe('deep-value');
    });

    it('should efficiently handle large arrays in loops', async () => {
      await templateEngine.registerHelper('each', (context: any, options: any) => {
        let result = '';
        if (Array.isArray(context)) {
          for (let i = 0; i < Math.min(context.length, 10000); i++) { // Limit to prevent infinite loops
            result += options.fn ? options.fn(context[i], { index: i }) : '';
          }
        }
        return result;
      });
      
      const template = '{{#each items}}{{this}},{{/each}}';
      const compiled = await templateEngine.compileTemplate(template, 'large-array-test');
      await templateEngine.cacheTemplate('large-array-test', compiled);
      
      const largeArray = Array.from({ length: 5000 }, (_, i) => i);
      const startTime = Date.now();
      const result = await templateEngine.renderTemplate('large-array-test', { items: largeArray });
      const renderTime = Date.now() - startTime;
      
      expect(result.output).toContain('0,1,2,');
      expect(renderTime).toBeLessThan(2000); // Should complete within 2 seconds
    });
  });

  describe('Enterprise Error Boundary Tests', () => {
    it('should gracefully handle circular references', async () => {
      const template = 'Name: {{user.name}}, Friend: {{user.friend.name}}';
      const compiled = await templateEngine.compileTemplate(template, 'circular-test');
      await templateEngine.cacheTemplate('circular-test', compiled);
      
      const user1: any = { name: 'Alice' };
      const user2: any = { name: 'Bob' };
      user1.friend = user2;
      user2.friend = user1; // Circular reference
      
      const result = await templateEngine.renderTemplate('circular-test', { user: user1 });
      expect(result.output).toBe('Name: Alice, Friend: Bob');
    });

    it('should handle null and undefined gracefully', async () => {
      const template = 'Value: {{value}}, Nested: {{nested.prop}}, Array: {{arr.0}}';
      const compiled = await templateEngine.compileTemplate(template, 'null-test');
      await templateEngine.cacheTemplate('null-test', compiled);
      
      const result = await templateEngine.renderTemplate('null-test', {
        value: null,
        nested: null,
        arr: null
      });
      
      expect(result.output).toBe('Value: , Nested: , Array: ');
    });

    it('should handle malformed template syntax gracefully', async () => {
      const malformedTemplates = [
        '{{unclosed',
        'unopened}}',
        '{{{{nested}}}}',
        '{{#if}}{{/unless}}',
        '{{helper with "unclosed string}}'
      ];
      
      for (const template of malformedTemplates) {
        const validation = await templateEngine.validateTemplate(template);
        expect(validation.status).toBe('invalid');
        expect(validation.errors.length).toBeGreaterThan(0);
      }
    });

    it('should handle memory pressure gracefully', async () => {
      // Create many templates to test memory management
      const templates: string[] = [];
      for (let i = 0; i < 100; i++) {
        templates.push(`Template ${i}: {{value${i}}}`);
      }
      
      const compiledTemplates: any[] = [];
      for (let i = 0; i < templates.length; i++) {
        const compiled = await templateEngine.compileTemplate(templates[i], `memory-test-${i}`);
        compiledTemplates.push(compiled);
        await templateEngine.cacheTemplate(`memory-test-${i}`, compiled);
      }
      
      // Verify cache management
      const health = await templateEngine.getHealth();
      expect(health.templatesInCache).toBeLessThanOrEqual(100);
      
      // Render all templates
      for (let i = 0; i < 50; i++) { // Test subset to avoid timeout
        const data = { [`value${i}`]: `Value ${i}` };
        const result = await templateEngine.renderTemplate(`memory-test-${i}`, data);
        expect(result.output).toContain(`Value ${i}`);
      }
    });
  });

  describe('Enterprise Data Integrity Tests', () => {
    it('should maintain template versioning', async () => {
      const template1 = 'Version 1: {{name}}';
      const template2 = 'Version 2: Hello {{name}}!';
      
      const compiled1 = await templateEngine.compileTemplate(template1, 'version-test');
      const compiled2 = await templateEngine.compileTemplate(template2, 'version-test');
      
      expect(compiled1.version).toBeDefined();
      expect(compiled2.version).toBeDefined();
      expect(compiled1.compiled_at).not.toEqual(compiled2.compiled_at);
    });

    it('should track template dependencies accurately', async () => {
      await templateEngine.registerPartial('header', '<h1>{{title}}</h1>');
      await templateEngine.registerHelper('format', (text: string) => text.toUpperCase());
      
      const template = '{{>header}} Content: {{format content}}';
      const compiled = await templateEngine.compileTemplate(template, 'dependency-test');
      
      expect(compiled.dependencies).toBeDefined();
      const depNames = compiled.dependencies.map(d => d.name);
      expect(depNames).toContain('title');
      expect(depNames).toContain('content');
      expect(depNames).toContain('format');
      expect(depNames).toContain('header');
    });

    it('should validate template integrity', async () => {
      const template = 'Hello {{name}}! {{#if active}}Active{{/if}}';
      const compiled = await templateEngine.compileTemplate(template, 'integrity-test');
      
      // Verify all components are extracted
      expect(compiled.variables).toContain('name');
      expect(compiled.variables).toContain('active');
      expect(compiled.conditionals.length).toBeGreaterThan(0);
      expect(compiled.syntax).toBe('handlebars');
    });

    it('should handle unicode and special characters', async () => {
      const template = 'Unicode: {{emoji}} Special: {{symbols}}';
      const compiled = await templateEngine.compileTemplate(template, 'unicode-test');
      await templateEngine.cacheTemplate('unicode-test', compiled);
      
      const result = await templateEngine.renderTemplate('unicode-test', {
        emoji: '🚀💻🎉',
        symbols: '©®™€£¥'
      });
      
      expect(result.output).toBe('Unicode: 🚀💻🎉 Special: ©®™€£¥');
    });
  });

  describe('Enterprise Monitoring and Observability', () => {
    it('should provide detailed performance metrics', async () => {
      const template = 'Test: {{value}}';
      const compiled = await templateEngine.compileTemplate(template, 'metrics-test');
      await templateEngine.cacheTemplate('metrics-test', compiled);
      
      // Perform multiple operations
      for (let i = 0; i < 10; i++) {
        await templateEngine.renderTemplate('metrics-test', { value: i });
      }
      
      const metrics = await templateEngine.getMetrics();
      
      expect(metrics.performance.totalRenders).toBeGreaterThanOrEqual(10);
      expect(metrics.performance.successfulRenders).toBeGreaterThanOrEqual(10);
      expect(metrics.performance.averageRenderTime).toBeGreaterThan(0);
      expect(metrics.performance.cacheHitRate).toBeGreaterThanOrEqual(0);
      expect(metrics.performance.templatesCompiled).toBeGreaterThan(0);
    });

    it('should track error rates and patterns', async () => {
      const initialMetrics = await templateEngine.getMetrics();
      const initialFailures = initialMetrics.performance.failedRenders;
      
      // Cause some failures
      try {
        await templateEngine.renderTemplate('non-existent-1', {});
      } catch (e) { /* expected */ }
      
      try {
        await templateEngine.renderTemplate('non-existent-2', {});
      } catch (e) { /* expected */ }
      
      const finalMetrics = await templateEngine.getMetrics();
      expect(finalMetrics.performance.failedRenders).toBe(initialFailures + 2);
    });

    it('should provide health status with detailed information', async () => {
      const health = await templateEngine.getHealth();
      
      expect(health).toHaveProperty('status');
      expect(health).toHaveProperty('uptime');
      expect(health).toHaveProperty('templatesInCache');
      expect(health).toHaveProperty('helpersRegistered');
      expect(health).toHaveProperty('partialsRegistered');
      expect(health).toHaveProperty('memoryUsage');
      expect(health).toHaveProperty('cacheStats');
      
      expect(health.status).toBe('healthy');
      expect(typeof health.uptime).toBe('number');
      expect(typeof health.templatesInCache).toBe('number');
    });

    it('should validate service compliance', async () => {
      const validation = await templateEngine.validate();
      
      expect(validation.status).toBe('valid');
      expect(validation.validationId).toBeDefined();
      expect(validation.componentId).toBe('governance-rule-template-engine');
      expect(validation.timestamp).toBeInstanceOf(Date);
      expect(validation.executionTime).toBeGreaterThanOrEqual(0);
    });
  });
}); 