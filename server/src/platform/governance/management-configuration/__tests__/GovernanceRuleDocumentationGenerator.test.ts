/**
 * @file GovernanceRuleDocumentationGenerator.test.ts
 * @filepath server/src/platform/governance/management-configuration/__tests__/GovernanceRuleDocumentationGenerator.test.ts
 * @reference G-TSK-07.SUB-07.1.TEST-03
 * @tier T2
 * @context foundation-context
 * @category Foundation
 * @created 2025-01-27
 * @modified 2025-01-27 18:45:00 UTC
 */

import { GovernanceRuleDocumentationGenerator } from '../GovernanceRuleDocumentationGenerator';
import { GovernanceRuleConfigurationManager } from '../GovernanceRuleConfigurationManager';
import { GovernanceRuleTemplateEngine } from '../GovernanceRuleTemplateEngine';

describe('GovernanceRuleDocumentationGenerator', () => {
  let documentationGenerator: GovernanceRuleDocumentationGenerator;
  let mockConfigurationManager: jest.Mocked<GovernanceRuleConfigurationManager>;
  let mockTemplateEngine: jest.Mocked<GovernanceRuleTemplateEngine>;

  beforeEach(() => {
    // Create mocks
    mockConfigurationManager = {
      getConfiguration: jest.fn(),
      setConfiguration: jest.fn(),
      validateConfiguration: jest.fn(),
      getServiceName: jest.fn().mockReturnValue('GovernanceRuleConfigurationManager'),
      getServiceVersion: jest.fn().mockReturnValue('1.0.0'),
      initialize: jest.fn(),
      track: jest.fn(),
      validate: jest.fn(),
      shutdown: jest.fn()
    } as any;

    mockTemplateEngine = {
      processTemplate: jest.fn(),
      getServiceName: jest.fn().mockReturnValue('GovernanceRuleTemplateEngine'),
      getServiceVersion: jest.fn().mockReturnValue('1.0.0'),
      initialize: jest.fn(),
      track: jest.fn(),
      validate: jest.fn(),
      shutdown: jest.fn()
    } as any;

    documentationGenerator = new GovernanceRuleDocumentationGenerator(
      mockConfigurationManager,
      mockTemplateEngine
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Constructor and Initialization', () => {
    it('should initialize with configuration manager and template engine', () => {
      expect(documentationGenerator).toBeDefined();
      expect(documentationGenerator).toBeInstanceOf(GovernanceRuleDocumentationGenerator);
    });

    it('should handle null configuration manager', () => {
      expect(() => new GovernanceRuleDocumentationGenerator(null as any, mockTemplateEngine))
        .toThrow();
    });

    it('should handle null template engine', () => {
      expect(() => new GovernanceRuleDocumentationGenerator(mockConfigurationManager, null as any))
        .toThrow();
    });

    it('should initialize with proper service metadata', () => {
      expect(documentationGenerator).toBeDefined();
      // Additional initialization checks can be added here
    });
  });

  describe('generateDocumentation', () => {
    const mockContext = {
      id: 'test-context-001',
      rules: [
        {
          id: 'test-rule-001',
          name: 'Test Governance Rule',
          type: 'security',
          description: 'Test rule for security governance',
          priority: 'High',
          conditions: ['condition1', 'condition2'],
          actions: ['action1', 'action2'],
          dependencies: ['dependency1']
        }
      ],
      metadata: {
        title: 'Test Documentation Context',
        description: 'Test context for documentation generation',
        version: '1.0.0',
        authority: 'E.Z. Consultancy',
        created: '2025-01-27T18:45:00Z',
        features: ['feature1', 'feature2'],
        environmentVariables: {
          TEST_VAR: {
            description: 'Test environment variable',
            default: 'test-value',
            required: true
          }
        },
        apiEndpoints: [
          {
            method: 'GET',
            path: '/api/test',
            description: 'Test API endpoint',
            parameters: [
              {
                name: 'testParam',
                type: 'string',
                description: 'Test parameter'
              }
            ],
            example: { test: 'example' }
          }
        ],
        complianceLevel: 'Enterprise'
      },
      securityLevel: 'High',
      authorityLevel: 'architectural-authority'
    };

    it('should generate documentation with default options', async () => {
      const result = await documentationGenerator.generateDocumentation(mockContext);

      expect(result).toBeDefined();
      expect(result.id).toBeDefined();
      expect(result.context).toBe(mockContext.id);
      expect(result.format).toBe('markdown');
      expect(result.content).toBeDefined();
      expect(result.metadata).toBeDefined();
      expect(result.generatedAt).toBeDefined();
      expect(result.version).toBe('1.0.0');
      expect(result.auditTrail).toBeDefined();
    });

    it('should generate documentation with custom options', async () => {
      const options = {
        format: 'html' as const,
        includeTableOfContents: true,
        includeSections: {
          overview: true,
          rules: true,
          configuration: false,
          compliance: true,
          api: false,
          troubleshooting: false,
          appendices: false
        }
      };

      const result = await documentationGenerator.generateDocumentation(mockContext, options);

      expect(result).toBeDefined();
      expect(result.format).toBe('html');
      expect(result.content).toContain('<html');
      expect(result.content).toContain('<body>');
    });

    it('should generate documentation with all sections enabled', async () => {
      const options = {
        format: 'markdown' as const,
        includeSections: {
          overview: true,
          rules: true,
          configuration: true,
          compliance: true,
          api: true,
          troubleshooting: true,
          appendices: true
        }
      };

      const result = await documentationGenerator.generateDocumentation(mockContext, options);

      expect(result.content).toContain('# Overview');
      expect(result.content).toContain('# Governance Rules');
      expect(result.content).toContain('# Configuration');
      expect(result.content).toContain('# Compliance');
      expect(result.content).toContain('# API Documentation');
      expect(result.content).toContain('# Troubleshooting');
      expect(result.content).toContain('# Appendices');
    });

    it('should handle context with multiple rules of different types', async () => {
      const multiRuleContext = {
        ...mockContext,
        rules: [
          mockContext.rules[0],
          {
            id: 'test-rule-002',
            name: 'Compliance Rule',
            type: 'compliance',
            priority: 'Medium',
            conditions: ['compliance-condition'],
            actions: ['compliance-action']
          },
          {
            id: 'test-rule-003',
            name: 'Performance Rule',
            type: 'performance',
            priority: 'Low',
            conditions: ['performance-condition'],
            actions: ['performance-action']
          }
        ]
      };

      const result = await documentationGenerator.generateDocumentation(multiRuleContext);

      expect(result.content).toContain('Test Governance Rule');
      expect(result.content).toContain('Compliance Rule');
      expect(result.content).toContain('Performance Rule');
      expect(result.content).toContain('security Rules');
      expect(result.content).toContain('compliance Rules');
      expect(result.content).toContain('performance Rules');
      expect(result.metadata.rulesCount).toBe(3);
    });

    it('should handle context with no rules gracefully', async () => {
      const noRulesContext = {
        ...mockContext,
        rules: []
      };

      const result = await documentationGenerator.generateDocumentation(noRulesContext);

      expect(result).toBeDefined();
      expect(result.content).toContain('Governance Rules');
      expect(result.metadata.rulesCount).toBe(0);
    });

    it('should handle context with minimal metadata', async () => {
      const minimalContext = {
        id: 'minimal-context',
        rules: [
          {
            id: 'minimal-rule',
            name: 'Minimal Rule',
            type: 'basic'
          }
        ],
        metadata: {
          title: 'Minimal Context'
        }
      };

      const result = await documentationGenerator.generateDocumentation(minimalContext);

      expect(result).toBeDefined();
      expect(result.content).toContain('Minimal Context');
      expect(result.content).toContain('Minimal Rule');
    });

    it('should validate context before generation', async () => {
      const invalidContext = {
        ...mockContext,
        id: '', // Invalid ID
      };

      await expect(documentationGenerator.generateDocumentation(invalidContext))
        .rejects.toThrow('Invalid context ID');
    });

    it('should validate rules array', async () => {
      const invalidContext = {
        ...mockContext,
        rules: null as any
      };

      await expect(documentationGenerator.generateDocumentation(invalidContext))
        .rejects.toThrow('Invalid rules array');
    });

    it('should validate metadata object', async () => {
      const invalidContext = {
        ...mockContext,
        metadata: null as any
      };

      await expect(documentationGenerator.generateDocumentation(invalidContext))
        .rejects.toThrow('Invalid context metadata');
    });

    it('should validate individual rule structure', async () => {
      const invalidContext = {
        ...mockContext,
        rules: [
          {
            id: '', // Invalid rule
            name: '',
            type: ''
          }
        ]
      };

      await expect(documentationGenerator.generateDocumentation(invalidContext))
        .rejects.toThrow('Invalid rule structure');
    });

    it('should handle context with special characters in names', async () => {
      const specialCharContext = {
        ...mockContext,
        id: 'test-context-with-special-chars-@#$%',
        rules: [
          {
            id: 'rule-with-special-chars-!@#',
            name: 'Rule with Special Characters: @#$%^&*()',
            type: 'security',
            description: 'Rule with special characters & symbols'
          }
        ],
        metadata: {
          title: 'Context with Special Characters: @#$%^&*()',
          description: 'Test context with special characters & symbols'
        }
      };

      const result = await documentationGenerator.generateDocumentation(specialCharContext);

      expect(result).toBeDefined();
      expect(result.content).toContain('Rule with Special Characters');
      expect(result.content).toContain('Context with Special Characters');
    });

    it('should handle large context with many rules', async () => {
      const largeContext = {
        ...mockContext,
        rules: Array.from({ length: 50 }, (_, i) => ({
          id: `rule-${i + 1}`,
          name: `Rule ${i + 1}`,
          type: i % 3 === 0 ? 'security' : i % 3 === 1 ? 'compliance' : 'performance',
          priority: i % 2 === 0 ? 'High' : 'Medium',
          conditions: [`condition-${i + 1}`],
          actions: [`action-${i + 1}`]
        }))
      };

      const result = await documentationGenerator.generateDocumentation(largeContext);

      expect(result).toBeDefined();
      expect(result.metadata.rulesCount).toBe(50);
      expect(result.content).toContain('Rule 1');
      expect(result.content).toContain('Rule 50');
    });

    it('should handle context with unicode characters', async () => {
      const unicodeContext = {
        ...mockContext,
        id: 'unicode-context-测试',
        rules: [
          {
            id: 'unicode-rule-测试',
            name: 'Unicode Rule: 测试规则 🚀',
            type: 'security',
            description: 'Rule with unicode characters: 中文测试 🔒'
          }
        ],
        metadata: {
          title: 'Unicode Context: 测试上下文 🌍',
          description: 'Context with unicode: 中文描述 ✨'
        }
      };

      const result = await documentationGenerator.generateDocumentation(unicodeContext);

      expect(result).toBeDefined();
      expect(result.content).toContain('Unicode Rule: 测试规则 🚀');
      expect(result.content).toContain('Unicode Context: 测试上下文 🌍');
    });
  });

  describe('generateBatchDocumentation', () => {
    const mockContext = {
      id: 'test-context-001',
      rules: [
        {
          id: 'test-rule-001',
          name: 'Test Governance Rule',
          type: 'security'
        }
      ],
      metadata: {
        title: 'Test Documentation Context',
        description: 'Test context for documentation generation',
        version: '1.0.0',
        authority: 'E.Z. Consultancy'
      }
    };

    it('should generate documentation for multiple contexts', async () => {
      const contexts = [
        mockContext,
        {
          ...mockContext,
          id: 'test-context-002',
          metadata: {
            ...mockContext.metadata,
            title: 'Second Test Context'
          }
        }
      ];

      const results = await documentationGenerator.generateBatchDocumentation(contexts);

      expect(results).toHaveLength(2);
      expect(results[0].context).toBe('test-context-001');
      expect(results[1].context).toBe('test-context-002');
    });

    it('should handle empty contexts array', async () => {
      const results = await documentationGenerator.generateBatchDocumentation([]);

      expect(results).toHaveLength(0);
    });

    it('should handle batch generation with custom options', async () => {
      const contexts = [mockContext];
      const options = {
        format: 'json' as const,
        concurrency: 2
      };

      const results = await documentationGenerator.generateBatchDocumentation(contexts, options);

      expect(results).toHaveLength(1);
      expect(results[0].format).toBe('json');
    });

    it('should handle large batch with controlled concurrency', async () => {
      const contexts = Array.from({ length: 10 }, (_, i) => ({
        ...mockContext,
        id: `batch-context-${i + 1}`,
        metadata: {
          ...mockContext.metadata,
          title: `Batch Context ${i + 1}`
        }
      }));

      const options = {
        concurrency: 3
      };

      const results = await documentationGenerator.generateBatchDocumentation(contexts, options);

      expect(results).toHaveLength(10);
      results.forEach((result, index) => {
        expect(result.context).toBe(`batch-context-${index + 1}`);
      });
    });

    it('should handle batch with mixed valid and invalid contexts', async () => {
      const contexts = [
        mockContext,
        {
          ...mockContext,
          id: '', // Invalid context
          metadata: {
            ...mockContext.metadata,
            title: 'Invalid Context'
          }
        }
      ];

      await expect(documentationGenerator.generateBatchDocumentation(contexts))
        .rejects.toThrow('Invalid context ID');
    });

    it('should handle batch generation with different formats', async () => {
      const contexts = [
        mockContext,
        {
          ...mockContext,
          id: 'test-context-002'
        }
      ];

      const options = {
        format: 'html' as const
      };

      const results = await documentationGenerator.generateBatchDocumentation(contexts, options);

      expect(results).toHaveLength(2);
      results.forEach(result => {
        expect(result.format).toBe('html');
        expect(result.content).toContain('<html');
      });
    });
  });

  describe('Documentation Content Generation', () => {
    const mockContext = {
      id: 'test-context-001',
      rules: [
        {
          id: 'test-rule-001',
          name: 'Test Governance Rule',
          type: 'security',
          priority: 'High',
          conditions: ['condition1'],
          actions: ['action1'],
          dependencies: ['dependency1']
        }
      ],
      metadata: {
        title: 'Test Documentation Context',
        description: 'Test context for documentation generation',
        version: '1.0.0',
        authority: 'E.Z. Consultancy',
        features: ['feature1', 'feature2'],
        environmentVariables: {
          TEST_VAR: {
            description: 'Test environment variable',
            default: 'test-value',
            required: true
          }
        },
        apiEndpoints: [
          {
            method: 'GET',
            path: '/api/test',
            description: 'Test API endpoint',
            parameters: [
              {
                name: 'testParam',
                type: 'string',
                description: 'Test parameter'
              }
            ]
          }
        ]
      }
    };

    it('should generate overview section with context information', async () => {
      const result = await documentationGenerator.generateDocumentation(mockContext);

      expect(result.content).toContain('Test Documentation Context');
      expect(result.content).toContain('Context Information');
      expect(result.content).toContain('test-context-001');
      expect(result.content).toContain('E.Z. Consultancy');
    });

    it('should generate rules section with rule details', async () => {
      const result = await documentationGenerator.generateDocumentation(mockContext);

      expect(result.content).toContain('Governance Rules');
      expect(result.content).toContain('Test Governance Rule');
      expect(result.content).toContain('security');
      expect(result.content).toContain('High');
      expect(result.content).toContain('condition1');
      expect(result.content).toContain('action1');
      expect(result.content).toContain('dependency1');
    });

    it('should generate configuration section with settings', async () => {
      const result = await documentationGenerator.generateDocumentation(mockContext);

      expect(result.content).toContain('Configuration');
      expect(result.content).toContain('Configuration Settings');
      expect(result.content).toContain('Environment Variables');
      expect(result.content).toContain('TEST_VAR');
    });

    it('should generate compliance section with authority information', async () => {
      const result = await documentationGenerator.generateDocumentation(mockContext);

      expect(result.content).toContain('Compliance');
      expect(result.content).toContain('Authority Compliance');
      expect(result.content).toContain('E.Z. Consultancy');
    });

    it('should generate API section with endpoint documentation', async () => {
      const result = await documentationGenerator.generateDocumentation(mockContext);

      expect(result.content).toContain('API Documentation');
      expect(result.content).toContain('GET /api/test');
      expect(result.content).toContain('testParam');
    });

    it('should generate troubleshooting section with common issues', async () => {
      const result = await documentationGenerator.generateDocumentation(mockContext);

      expect(result.content).toContain('Troubleshooting');
      expect(result.content).toContain('Common Issues');
      expect(result.content).toContain('Configuration Issues');
      expect(result.content).toContain('Diagnostic Procedures');
    });

    it('should generate appendices section with references', async () => {
      const result = await documentationGenerator.generateDocumentation(mockContext);

      expect(result.content).toContain('Appendices');
      expect(result.content).toContain('Glossary');
      expect(result.content).toContain('References');
      expect(result.content).toContain('ADR');
      expect(result.content).toContain('DCR');
    });

    it('should handle context without optional metadata fields', async () => {
      const minimalContext = {
        id: 'minimal-context',
        rules: [
          {
            id: 'minimal-rule',
            name: 'Minimal Rule',
            type: 'basic'
          }
        ],
        metadata: {
          title: 'Minimal Context'
        }
      };

      const result = await documentationGenerator.generateDocumentation(minimalContext);

      expect(result.content).toContain('Minimal Context');
      expect(result.content).toContain('Minimal Rule');
      expect(result.content).toContain('No API endpoints documented');
    });

    it('should handle rules without optional fields', async () => {
      const contextWithMinimalRules = {
        ...mockContext,
        rules: [
          {
            id: 'minimal-rule',
            name: 'Minimal Rule',
            type: 'basic'
          }
        ]
      };

      const result = await documentationGenerator.generateDocumentation(contextWithMinimalRules);

      expect(result.content).toContain('Minimal Rule');
      expect(result.content).toContain('basic');
    });
  });

  describe('Format-Specific Generation', () => {
    const mockContext = {
      id: 'test-context-001',
      rules: [
        {
          id: 'test-rule-001',
          name: 'Test Governance Rule',
          type: 'security'
        }
      ],
      metadata: {
        title: 'Test Documentation Context',
        version: '1.0.0',
        authority: 'E.Z. Consultancy'
      }
    };

    it('should generate valid Markdown format', async () => {
      const options = { format: 'markdown' as const };
      const result = await documentationGenerator.generateDocumentation(mockContext, options);

      expect(result.content).toContain('# Table of Contents');
      expect(result.content).toContain('## Context Information');
      expect(result.content).toContain('### Test Governance Rule');
      expect(result.content).toContain('| Property | Value |');
    });

    it('should generate valid HTML format', async () => {
      const options = { format: 'html' as const };
      const result = await documentationGenerator.generateDocumentation(mockContext, options);

      expect(result.content).toContain('<!DOCTYPE html>');
      expect(result.content).toContain('<html lang="en">');
      expect(result.content).toContain('<head>');
      expect(result.content).toContain('<body>');
      expect(result.content).toContain('</html>');
    });

    it('should generate valid JSON format', async () => {
      const options = { format: 'json' as const };
      const result = await documentationGenerator.generateDocumentation(mockContext, options);

      expect(() => JSON.parse(result.content)).not.toThrow();
      const jsonContent = JSON.parse(result.content);
      expect(jsonContent.metadata).toBeDefined();
      expect(jsonContent.sections).toBeDefined();
    });

    it('should generate PDF-ready HTML format', async () => {
      const options = { format: 'pdf' as const };
      const result = await documentationGenerator.generateDocumentation(mockContext, options);

      expect(result.content).toContain('<!DOCTYPE html>');
      expect(result.content).toContain('@media print');
      expect(result.content).toContain('page-break-before: always');
    });

    it('should throw error for unsupported format', async () => {
      const options = { format: 'unsupported' as any };

      await expect(documentationGenerator.generateDocumentation(mockContext, options))
        .rejects.toThrow('Unsupported format: unsupported');
    });

    it('should handle table of contents inclusion/exclusion', async () => {
      const withTocOptions = { includeTableOfContents: true };
      const withoutTocOptions = { includeTableOfContents: false };

      const withTocResult = await documentationGenerator.generateDocumentation(mockContext, withTocOptions);
      const withoutTocResult = await documentationGenerator.generateDocumentation(mockContext, withoutTocOptions);

      expect(withTocResult.content).toContain('# Table of Contents');
      expect(withoutTocResult.content).not.toContain('# Table of Contents');
    });
  });

  describe('Error Handling', () => {
    it('should handle missing context gracefully', async () => {
      await expect(documentationGenerator.generateDocumentation(null as any))
        .rejects.toThrow();
    });

    it('should handle undefined context gracefully', async () => {
      await expect(documentationGenerator.generateDocumentation(undefined as any))
        .rejects.toThrow();
    });

    it('should handle invalid security level', async () => {
      const invalidContext = {
        id: 'test-context-001',
        rules: [
          {
            id: 'test-rule-001',
            name: 'Test Rule',
            type: 'security'
          }
        ],
        metadata: {
          title: 'Test Context',
          version: '1.0.0'
        },
        securityLevel: 'InvalidLevel'
      };

      await expect(documentationGenerator.generateDocumentation(invalidContext))
        .rejects.toThrow('Invalid security level: InvalidLevel');
    });

    it('should handle context with invalid JSON in metadata', async () => {
      const contextWithInvalidJson = {
        id: 'test-context-001',
        rules: [
          {
            id: 'test-rule-001',
            name: 'Test Rule',
            type: 'security'
          }
        ],
        metadata: {
          title: 'Test Context',
          version: '1.0.0',
          invalidField: { circular: null as any }
        }
      };

      // Create circular reference
      contextWithInvalidJson.metadata.invalidField.circular = contextWithInvalidJson.metadata.invalidField;

      const result = await documentationGenerator.generateDocumentation(contextWithInvalidJson);
      expect(result).toBeDefined();
    });

    it('should handle extremely long content gracefully', async () => {
      const longContext = {
        id: 'long-context',
        rules: [
          {
            id: 'long-rule',
            name: 'A'.repeat(1000),
            type: 'security',
            description: 'B'.repeat(5000)
          }
        ],
        metadata: {
          title: 'C'.repeat(1000),
          description: 'D'.repeat(10000)
        }
      };

      const result = await documentationGenerator.generateDocumentation(longContext);
      expect(result).toBeDefined();
      expect(result.content.length).toBeGreaterThan(0);
    });

    it('should handle context with empty strings', async () => {
      const emptyStringContext = {
        id: 'empty-string-context',
        rules: [
          {
            id: 'empty-rule',
            name: '',
            type: 'security',
            description: ''
          }
        ],
        metadata: {
          title: '',
          description: ''
        }
      };

      const result = await documentationGenerator.generateDocumentation(emptyStringContext);
      expect(result).toBeDefined();
    });
  });

  describe('Metadata Generation', () => {
    const mockContext = {
      id: 'test-context-001',
      rules: [
        {
          id: 'test-rule-001',
          name: 'Test Rule',
          type: 'security'
        }
      ],
      metadata: {
        title: 'Test Context',
        version: '1.0.0',
        authority: 'E.Z. Consultancy',
        complianceLevel: 'Enterprise'
      },
      securityLevel: 'High'
    };

    it('should generate comprehensive metadata', async () => {
      const result = await documentationGenerator.generateDocumentation(mockContext);

      expect(result.metadata).toBeDefined();
      expect(result.metadata.contextId).toBe(mockContext.id);
      expect(result.metadata.generatedAt).toBeDefined();
      expect(result.metadata.format).toBe('markdown');
      expect(result.metadata.version).toBe('1.0.0');
      expect(result.metadata.authority).toBe('E.Z. Consultancy');
      expect(result.metadata.complianceLevel).toBe('Enterprise');
      expect(result.metadata.securityLevel).toBe('High');
      expect(result.metadata.rulesCount).toBe(1);
      expect(result.metadata.validationStatus).toBe('Validated');
    });

    it('should include audit trail in metadata', async () => {
      const result = await documentationGenerator.generateDocumentation(mockContext);

      expect(result.metadata.auditTrail).toBeDefined();
      expect(Array.isArray(result.metadata.auditTrail)).toBe(true);
    });

    it('should handle metadata with default values', async () => {
      const minimalContext = {
        id: 'minimal-context',
        rules: [
          {
            id: 'minimal-rule',
            name: 'Minimal Rule',
            type: 'basic'
          }
        ],
        metadata: {
          title: 'Minimal Context'
        }
      };

      const result = await documentationGenerator.generateDocumentation(minimalContext);

      expect(result.metadata.authority).toBe('E.Z. Consultancy');
      expect(result.metadata.complianceLevel).toBe('Enterprise');
      expect(result.metadata.securityLevel).toBe('Standard');
    });

    it('should track generation performance in metadata', async () => {
      const result = await documentationGenerator.generateDocumentation(mockContext);

      expect(result.metadata.generatedAt).toBeDefined();
      expect(new Date(result.metadata.generatedAt).getTime()).toBeGreaterThan(0);
    });
  });

  describe('Performance and Scalability', () => {
    it('should handle generation within reasonable time limits', async () => {
      const mockContext = {
        id: 'performance-test',
        rules: [
          {
            id: 'test-rule',
            name: 'Test Rule',
            type: 'security'
          }
        ],
        metadata: {
          title: 'Performance Test Context'
        }
      };

      const startTime = Date.now();
      const result = await documentationGenerator.generateDocumentation(mockContext);
      const endTime = Date.now();

      expect(result).toBeDefined();
      expect(endTime - startTime).toBeLessThan(5000); // Should complete within 5 seconds
    });

    it('should handle concurrent generation requests', async () => {
      const mockContext = {
        id: 'concurrent-test',
        rules: [
          {
            id: 'test-rule',
            name: 'Test Rule',
            type: 'security'
          }
        ],
        metadata: {
          title: 'Concurrent Test Context'
        }
      };

      const promises = Array.from({ length: 5 }, (_, i) => 
        documentationGenerator.generateDocumentation({
          ...mockContext,
          id: `concurrent-test-${i}`
        })
      );

      const results = await Promise.all(promises);

      expect(results).toHaveLength(5);
      results.forEach((result, index) => {
        expect(result).toBeDefined();
        expect(result.context).toBe(`concurrent-test-${index}`);
      });
    });

    it('should handle memory efficiently with large contexts', async () => {
      const largeContext = {
        id: 'large-context',
        rules: Array.from({ length: 100 }, (_, i) => ({
          id: `large-rule-${i}`,
          name: `Large Rule ${i}`,
          type: 'security',
          description: `Description for large rule ${i}`.repeat(10)
        })),
        metadata: {
          title: 'Large Context',
          description: 'Large context for memory testing'.repeat(100)
        }
      };

      const result = await documentationGenerator.generateDocumentation(largeContext);

      expect(result).toBeDefined();
      expect(result.metadata.rulesCount).toBe(100);
    });
  });

  describe('Security and Validation', () => {
    it('should validate security levels correctly', async () => {
      const securityLevels = ['Low', 'Medium', 'High', 'Critical'];

      for (const level of securityLevels) {
        const context = {
          id: `security-test-${level}`,
          rules: [
            {
              id: 'security-rule',
              name: 'Security Rule',
              type: 'security'
            }
          ],
          metadata: {
            title: 'Security Test Context'
          },
          securityLevel: level
        };

        const result = await documentationGenerator.generateDocumentation(context);
        expect(result).toBeDefined();
        expect(result.metadata.securityLevel).toBe(level);
      }
    });

    it('should handle authority validation correctly', async () => {
      const context = {
        id: 'authority-test',
        rules: [
          {
            id: 'authority-rule',
            name: 'Authority Rule',
            type: 'security'
          }
        ],
        metadata: {
          title: 'Authority Test Context'
        },
        authorityLevel: 'architectural-authority'
      };

      const result = await documentationGenerator.generateDocumentation(context);
      expect(result).toBeDefined();
    });

    it('should sanitize potentially dangerous content', async () => {
      const dangerousContext = {
        id: 'dangerous-context',
        rules: [
          {
            id: 'dangerous-rule',
            name: '<script>alert("xss")</script>',
            type: 'security',
            description: 'javascript:alert("xss")'
          }
        ],
        metadata: {
          title: '<img src="x" onerror="alert(1)">',
          description: 'onload="alert(1)"'
        }
      };

      const result = await documentationGenerator.generateDocumentation(dangerousContext);
      expect(result).toBeDefined();
      // Content should be sanitized and not contain dangerous scripts
      expect(result.content).not.toContain('<script>');
      expect(result.content).not.toContain('javascript:');
      expect(result.content).not.toContain('onerror=');
    });
  });

  describe('Integration and Compatibility', () => {
    it('should maintain backward compatibility with older context formats', async () => {
      const legacyContext = {
        id: 'legacy-context',
        rules: [
          {
            id: 'legacy-rule',
            name: 'Legacy Rule',
            type: 'security'
          }
        ],
        metadata: {
          title: 'Legacy Context'
          // Missing newer fields
        }
      };

      const result = await documentationGenerator.generateDocumentation(legacyContext);
      expect(result).toBeDefined();
      expect(result.content).toContain('Legacy Context');
    });

    it('should handle cross-platform line endings', async () => {
      const context = {
        id: 'line-ending-test',
        rules: [
          {
            id: 'line-ending-rule',
            name: 'Line Ending Rule',
            type: 'security',
            description: 'Line 1\nLine 2\r\nLine 3\rLine 4'
          }
        ],
        metadata: {
          title: 'Line Ending Test',
          description: 'Test\nwith\r\ndifferent\rline endings'
        }
      };

      const result = await documentationGenerator.generateDocumentation(context);
      expect(result).toBeDefined();
      expect(result.content).toContain('Line Ending Rule');
    });

    it('should handle different character encodings', async () => {
      const encodingContext = {
        id: 'encoding-test',
        rules: [
          {
            id: 'encoding-rule',
            name: 'Encoding Rule: café naïve résumé',
            type: 'security',
            description: 'Special chars: àáâãäåæçèéêë'
          }
        ],
        metadata: {
          title: 'Encoding Test: ñoño piñata',
          description: 'More special chars: ìíîïðñòóôõö'
        }
      };

      const result = await documentationGenerator.generateDocumentation(encodingContext);
      expect(result).toBeDefined();
      expect(result.content).toContain('café naïve résumé');
      expect(result.content).toContain('ñoño piñata');
    });
  });
}); 