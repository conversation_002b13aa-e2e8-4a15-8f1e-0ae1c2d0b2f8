/**
 * @file GovernanceRuleEnvironmentManager Tests
 * @filepath server/src/platform/governance/management-configuration/__tests__/GovernanceRuleEnvironmentManager.test.ts
 * @reference G-TSK-07.SUB-07.1.IMP-04
 * @tier T2
 * @context foundation-context
 * @category Foundation
 * @created 2025-07-05
 * @modified 2025-07-05
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-007-management-administration-architecture
 * @governance-dcr DCR-foundation-006-security-integration-standards
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🧪 ENTERPRISE TEST COVERAGE (v2.1)
 * @test-coverage 100%
 * @test-type unit, integration, performance, security
 * @test-framework jest
 * @test-environment node
 * @test-isolation true
 * @test-parallelization true
 * @test-reporting comprehensive
 * @test-monitoring enabled
 * @test-quality enterprise-grade
 */

import { GovernanceRuleEnvironmentManager } from '../GovernanceRuleEnvironmentManager';
import {
  IEnvironmentConfiguration,
  IEnvironmentFilter,
  IDeploymentOptions,
  IEnvironmentStatus,
  IEnvironmentMetrics,
} from '../../../../../../shared/src/interfaces/governance/management-configuration/governance-rule-environment-manager';
import {
  TValidationResult,
  TMetrics,
} from '../../../../../../shared/src/types/platform/tracking/tracking-types';

// ============================================================================
// TEST SETUP & CONFIGURATION
// ============================================================================

describe('GovernanceRuleEnvironmentManager', () => {
  let manager: GovernanceRuleEnvironmentManager;
  let mockEnvironmentConfig: IEnvironmentConfiguration;

  beforeEach(async () => {
    manager = new GovernanceRuleEnvironmentManager();
    await manager.initialize();

    // Mock environment configuration
    mockEnvironmentConfig = {
      id: 'test-env-001',
      name: 'Test Environment',
      description: 'Test environment for unit testing',
      type: 'testing',
      status: 'creating',
      version: '1.0.0',
      configuration: {
        resources: {
          cpu: {
            limit: 2000,
            request: 1000,
            architecture: 'x86_64',
          },
          memory: {
            limit: 4096,
            request: 2048,
            type: 'standard',
          },
          storage: {
            limit: 100,
            type: 'ssd',
            class: 'standard',
          },
          network: {
            bandwidth: 1000,
            type: 'standard',
          },
        },
        network: {
          vpc: {
            id: 'vpc-test-001',
            cidr: '10.0.0.0/16',
            dns: {
              servers: ['*******', '*******'],
              searchDomains: ['test.local'],
            },
          },
          subnets: {
            public: ['********/24'],
            private: ['********/24'],
          },
          securityGroups: {
            ingress: [{
              type: 'allow',
              protocol: 'tcp',
              ports: { from: 80, to: 80 },
              source: '0.0.0.0/0',
              description: 'HTTP access',
            }],
            egress: [{
              type: 'allow',
              protocol: 'tcp',
              ports: { from: 443, to: 443 },
              source: '0.0.0.0/0',
              description: 'HTTPS access',
            }],
          },
        },
        security: {
          authentication: {
            type: 'oauth',
            provider: 'auth0',
            config: { clientId: 'test-client' },
          },
          authorization: {
            type: 'rbac',
            policies: { admin: ['*'] },
          },
          encryption: {
            atRest: {
              enabled: true,
              algorithm: 'AES-256',
              keyManagement: 'managed',
            },
            inTransit: {
              enabled: true,
              tlsVersion: '1.3',
            },
          },
        },
        monitoring: {
          metrics: {
            enabled: true,
            interval: 30,
            retention: 7,
            exporters: ['prometheus'],
          },
          logging: {
            enabled: true,
            level: 'info',
            retention: 30,
            destinations: ['elasticsearch'],
          },
          alerting: {
            enabled: true,
            destinations: ['slack'],
            rules: [{
              name: 'high-cpu',
              condition: 'cpu > 80',
              severity: 'warning',
              threshold: 80,
              duration: 300,
              labels: { team: 'ops' },
            }],
          },
        },
      },
      variables: {
        NODE_ENV: 'test',
        API_URL: 'https://api.test.com',
      },
      secrets: {
        API_KEY: 'test-key-123',
        DB_PASSWORD: 'test-password',
      },
      tags: {
        environment: 'test',
        team: 'platform',
        project: 'oa-framework',
      },
      metadata: {
        createdAt: new Date(),
        modifiedAt: new Date(),
        createdBy: 'test-user',
        modifiedBy: 'test-user',
        owner: 'platform-team',
        authority: {
          level: 'architectural-authority',
          validator: 'President & CEO, E.Z. Consultancy',
          validationStatus: 'validated',
          validatedAt: new Date().toISOString(),
          complianceScore: 100,
        },
      },
    };
  });

  afterEach(async () => {
    await manager.shutdown();
  });

  // ============================================================================
  // INITIALIZATION TESTS
  // ============================================================================

  describe('Initialization', () => {
    it('should initialize successfully', async () => {
      const newManager = new GovernanceRuleEnvironmentManager();
      await expect(newManager.initialize()).resolves.not.toThrow();
      await newManager.shutdown();
    });

    it('should have correct component identification', () => {
      expect(manager.id).toBe('governance-rule-environment-manager');
      expect(manager.authority).toBe('President & CEO, E.Z. Consultancy');
      expect(manager.isReady()).toBe(true);
    });

    it('should validate service state after initialization', async () => {
      const validation = await manager.validate();
      expect(validation.status).toBe('valid');
      expect(validation.overallScore).toBe(100);
      expect(validation.errors).toHaveLength(0);
    });
  });

  // ============================================================================
  // ENVIRONMENT CREATION TESTS
  // ============================================================================

  describe('Environment Creation', () => {
    it('should create environment successfully', async () => {
      const environmentId = await manager.createEnvironment(mockEnvironmentConfig);
      expect(environmentId).toBe(mockEnvironmentConfig.id);

      const environment = await manager.getEnvironment(environmentId);
      expect(environment).toBeDefined();
      expect(environment!.id).toBe(mockEnvironmentConfig.id);
      expect(environment!.name).toBe(mockEnvironmentConfig.name);
    });

    it('should validate environment configuration before creation', async () => {
      const invalidConfig = { ...mockEnvironmentConfig, id: '' };
      await expect(manager.createEnvironment(invalidConfig)).rejects.toThrow('Environment validation failed');
    });

    it('should enforce environment limits', async () => {
      // Create environments up to the limit
      const promises: Promise<string>[] = [];
      for (let i = 0; i < 1000; i++) {
        const config = { ...mockEnvironmentConfig, id: `env-${i}` };
        promises.push(manager.createEnvironment(config));
      }
      await Promise.all(promises);

      // Try to create one more
      const extraConfig = { ...mockEnvironmentConfig, id: 'env-extra' };
      await expect(manager.createEnvironment(extraConfig)).rejects.toThrow('Maximum number of environments reached');
    });

    it('should handle concurrent environment creation', async () => {
      const promises: Promise<string>[] = [];
      for (let i = 0; i < 5; i++) {
        const config = { ...mockEnvironmentConfig, id: `concurrent-env-${i}` };
        promises.push(manager.createEnvironment(config));
      }

      const results = await Promise.all(promises);
      expect(results).toHaveLength(5);
      results.forEach((id, index) => {
        expect(id).toBe(`concurrent-env-${index}`);
      });
    });

    it('should update performance metrics on creation', async () => {
      const initialMetrics = await manager.getMetrics();
      const initialTotal = initialMetrics.usage.peakConcurrentUsers;

      await manager.createEnvironment(mockEnvironmentConfig);

      const updatedMetrics = await manager.getMetrics();
      expect(updatedMetrics.usage.peakConcurrentUsers).toBe(initialTotal + 1);
    });
  });

  // ============================================================================
  // ENVIRONMENT VALIDATION TESTS
  // ============================================================================

  describe('Environment Validation', () => {
    it('should validate valid environment configuration', async () => {
      const validation = await manager.validateEnvironment(mockEnvironmentConfig);
      expect(validation.status).toBe('valid');
      expect(validation.overallScore).toBe(100);
      expect(validation.errors).toHaveLength(0);
    });

    it('should detect missing required fields', async () => {
      const invalidConfig = { ...mockEnvironmentConfig };
      delete (invalidConfig as any).id;

      const validation = await manager.validateEnvironment(invalidConfig);
      expect(validation.status).toBe('invalid');
      expect(validation.errors).toContain('Environment ID is required');
    });

    it('should validate environment type', async () => {
      const invalidConfig = { ...mockEnvironmentConfig, type: 'invalid' as any };
      const validation = await manager.validateEnvironment(invalidConfig);
      expect(validation.status).toBe('invalid');
      expect(validation.errors).toContain('Invalid environment type: invalid');
    });

    it('should validate resource limits', async () => {
      const invalidConfig = {
        ...mockEnvironmentConfig,
        configuration: {
          ...mockEnvironmentConfig.configuration,
          resources: {
            ...mockEnvironmentConfig.configuration.resources,
            cpu: { ...mockEnvironmentConfig.configuration.resources.cpu, limit: 0 },
          },
        },
      };

      const validation = await manager.validateEnvironment(invalidConfig);
      expect(validation.status).toBe('invalid');
      expect(validation.errors).toContain('CPU limit must be greater than 0');
    });

    it('should provide comprehensive validation results', async () => {
      const validation = await manager.validateEnvironment(mockEnvironmentConfig);
      expect(validation.validationId).toBeDefined();
      expect(validation.componentId).toBe('governance-rule-environment-manager');
      expect(validation.timestamp).toBeInstanceOf(Date);
      expect(validation.executionTime).toBeDefined();
      expect(validation.references).toBeDefined();
      expect(validation.metadata).toBeDefined();
    });
  });

  // ============================================================================
  // ENVIRONMENT MANAGEMENT TESTS
  // ============================================================================

  describe('Environment Management', () => {
    let environmentId: string;

    beforeEach(async () => {
      environmentId = await manager.createEnvironment(mockEnvironmentConfig);
    });

    it('should update environment successfully', async () => {
      const updates = {
        name: 'Updated Test Environment',
        description: 'Updated description',
        variables: { NEW_VAR: 'new-value' },
      };

      await manager.updateEnvironment(environmentId, updates);

      const environment = await manager.getEnvironment(environmentId);
      expect(environment!.name).toBe(updates.name);
      expect(environment!.description).toBe(updates.description);
      expect(environment!.variables.NEW_VAR).toBe('new-value');
    });

    it('should handle partial updates', async () => {
      const originalName = mockEnvironmentConfig.name;
      const updates = { description: 'Only description updated' };

      await manager.updateEnvironment(environmentId, updates);

      const environment = await manager.getEnvironment(environmentId);
      expect(environment!.name).toBe(originalName); // Should remain unchanged
      expect(environment!.description).toBe(updates.description);
    });

    it('should delete environment successfully', async () => {
      await manager.deleteEnvironment(environmentId);

      const environment = await manager.getEnvironment(environmentId);
      expect(environment).toBeNull();
    });

    it('should prevent deletion of running environment', async () => {
      await manager.startEnvironment(environmentId);
      await expect(manager.deleteEnvironment(environmentId)).rejects.toThrow('Cannot delete running environment');
    });

    it('should handle non-existent environment operations', async () => {
      const nonExistentId = 'non-existent-env';

      await expect(manager.updateEnvironment(nonExistentId, {})).rejects.toThrow('Environment not found');
      await expect(manager.deleteEnvironment(nonExistentId)).rejects.toThrow('Environment not found');
      await expect(manager.startEnvironment(nonExistentId)).rejects.toThrow('Environment not found');
      await expect(manager.stopEnvironment(nonExistentId)).rejects.toThrow('Environment not found');
    });
  });

  // ============================================================================
  // ENVIRONMENT LISTING AND FILTERING TESTS
  // ============================================================================

  describe('Environment Listing and Filtering', () => {
    beforeEach(async () => {
      // Create multiple environments for testing
      const environments = [
        { ...mockEnvironmentConfig, id: 'env-dev-1', type: 'development' as const, tags: { env: 'dev', team: 'frontend' } },
        { ...mockEnvironmentConfig, id: 'env-test-1', type: 'testing' as const, tags: { env: 'test', team: 'backend' } },
        { ...mockEnvironmentConfig, id: 'env-prod-1', type: 'production' as const, tags: { env: 'prod', team: 'platform' } },
        { ...mockEnvironmentConfig, id: 'env-dev-2', type: 'development' as const, tags: { env: 'dev', team: 'platform' } },
      ];

      for (const env of environments) {
        await manager.createEnvironment(env);
      }
    });

    it('should list all environments without filter', async () => {
      const environments = await manager.listEnvironments();
      expect(environments.length).toBeGreaterThanOrEqual(4);
    });

    it('should filter environments by type', async () => {
      const filter: IEnvironmentFilter = { type: ['development'] };
      const environments = await manager.listEnvironments(filter);
      expect(environments.length).toBe(2);
      environments.forEach(env => expect(env.type).toBe('development'));
    });

    it('should filter environments by multiple types', async () => {
      const filter: IEnvironmentFilter = { type: ['development', 'testing'] };
      const environments = await manager.listEnvironments(filter);
      expect(environments.length).toBe(3);
    });

    it('should filter environments by tags', async () => {
      const filter: IEnvironmentFilter = { tags: { team: 'platform' } };
      const environments = await manager.listEnvironments(filter);
      expect(environments.length).toBe(2);
    });

    it('should filter environments by date range', async () => {
      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
      const filter: IEnvironmentFilter = {
        createdRange: { start: oneHourAgo, end: now },
      };
      const environments = await manager.listEnvironments(filter);
      expect(environments.length).toBeGreaterThanOrEqual(4);
    });

    it('should combine multiple filters', async () => {
      const filter: IEnvironmentFilter = {
        type: ['development'],
        tags: { team: 'platform' },
      };
      const environments = await manager.listEnvironments(filter);
      expect(environments.length).toBe(1);
      expect(environments[0].id).toBe('env-dev-2');
    });
  });

  // ============================================================================
  // ENVIRONMENT DEPLOYMENT TESTS
  // ============================================================================

  describe('Environment Deployment', () => {
    let environmentId: string;

    beforeEach(async () => {
      environmentId = await manager.createEnvironment(mockEnvironmentConfig);
    });

    it('should deploy environment successfully', async () => {
      const deploymentOptions: IDeploymentOptions = {
        strategy: 'blue-green',
        timeout: 300000,
        rollbackOnFailure: true,
        preDeploymentHooks: ['pre-deploy-hook'],
        postDeploymentHooks: ['post-deploy-hook'],
      };

      const result = await manager.deployEnvironment(environmentId, deploymentOptions);

      expect(result.status).toBe('success');
      expect(result.deploymentId).toBeDefined();
      expect(result.startTime).toBeInstanceOf(Date);
      expect(result.endTime).toBeInstanceOf(Date);
      expect(result.logs).toContain('Deployment completed successfully');
    });

    it('should handle deployment without options', async () => {
      const result = await manager.deployEnvironment(environmentId);
      expect(result.status).toBe('success');
      expect(result.deploymentId).toBeDefined();
    });

    it('should reject deployment of non-existent environment', async () => {
      await expect(manager.deployEnvironment('non-existent')).rejects.toThrow('Environment not found');
    });

    it('should update environment status during deployment', async () => {
      const result = await manager.deployEnvironment(environmentId);
      
      // Check that deployment was successful
      expect(result.status).toBe('success');
      expect(result.deploymentId).toBeDefined();

      // Check final status after deployment
      const finalEnvironment = await manager.getEnvironment(environmentId);
      expect(finalEnvironment!.status).toBe('active');
    });
  });

  // ============================================================================
  // ENVIRONMENT LIFECYCLE TESTS
  // ============================================================================

  describe('Environment Lifecycle', () => {
    let environmentId: string;

    beforeEach(async () => {
      environmentId = await manager.createEnvironment(mockEnvironmentConfig);
    });

    it('should start environment successfully', async () => {
      await manager.startEnvironment(environmentId);

      const status = await manager.getEnvironmentStatus(environmentId);
      expect(status.status).toBe('active');
      expect(status.health).toBe('healthy');
    });

    it('should stop environment successfully', async () => {
      await manager.startEnvironment(environmentId);
      await manager.stopEnvironment(environmentId);

      const status = await manager.getEnvironmentStatus(environmentId);
      expect(status.status).toBe('inactive');
      expect(status.health).toBe('unknown');
    });

    it('should get environment status', async () => {
      const status = await manager.getEnvironmentStatus(environmentId);

      expect(status.environmentId).toBe(environmentId);
      expect(status.status).toBeDefined();
      expect(status.health).toBeDefined();
      expect(status.uptime).toBeDefined();
      expect(status.lastCheck).toBeInstanceOf(Date);
      expect(status.instances).toBeDefined();
      expect(status.resources).toBeDefined();
    });

    it('should get environment metrics', async () => {
      const metrics = await manager.getEnvironmentMetrics(environmentId);

      expect(metrics.environmentId).toBe(environmentId);
      expect(metrics.timestamp).toBeInstanceOf(Date);
      expect(metrics.performance).toBeDefined();
      expect(metrics.performance.cpu).toBeDefined();
      expect(metrics.performance.memory).toBeDefined();
      expect(metrics.performance.storage).toBeDefined();
      expect(metrics.performance.network).toBeDefined();
      expect(metrics.application).toBeDefined();
    });
  });

  // ============================================================================
  // HEALTH AND METRICS TESTS
  // ============================================================================

  describe('Health and Metrics', () => {
    it('should provide health status', async () => {
      const health = await manager.getHealth();

      expect(health.status).toBe('healthy');
      expect(health.componentId).toBe('governance-rule-environment-manager');
      expect(health.version).toBeDefined();
      expect(health.uptime).toBeDefined();
      expect(health.performance).toBeDefined();
      expect(health.environments).toBeDefined();
      expect(health.authority).toBeDefined();
    });

    it('should provide comprehensive metrics', async () => {
      const metrics = await manager.getMetrics();

      expect(metrics.timestamp).toBeDefined();
      expect(metrics.service).toBe('governance-rule-environment-manager');
      expect(metrics.performance).toBeDefined();
      expect(metrics.usage).toBeDefined();
      expect(metrics.errors).toBeDefined();
      expect(metrics.custom).toBeDefined();
    });

    it('should track performance metrics accurately', async () => {
      const initialMetrics = await manager.getMetrics();
      
      // Create an environment to trigger metrics update
      await manager.createEnvironment(mockEnvironmentConfig);
      
      const updatedMetrics = await manager.getMetrics();
      expect(updatedMetrics.usage.totalOperations).toBeGreaterThanOrEqual(initialMetrics.usage.totalOperations);
    });
  });

  // ============================================================================
  // ERROR HANDLING TESTS
  // ============================================================================

  describe('Error Handling', () => {
    it('should handle invalid environment configuration gracefully', async () => {
      const invalidConfig = { ...mockEnvironmentConfig };
      delete (invalidConfig as any).id;
      delete (invalidConfig as any).name;
      delete (invalidConfig as any).type;

      await expect(manager.createEnvironment(invalidConfig)).rejects.toThrow();
    });

    it('should handle resource limit violations', async () => {
      const invalidConfig = {
        ...mockEnvironmentConfig,
        configuration: {
          ...mockEnvironmentConfig.configuration,
          resources: {
            ...mockEnvironmentConfig.configuration.resources,
            cpu: { ...mockEnvironmentConfig.configuration.resources.cpu, limit: -1 },
            memory: { ...mockEnvironmentConfig.configuration.resources.memory, limit: -1 },
            storage: { ...mockEnvironmentConfig.configuration.resources.storage, limit: -1 },
          },
        },
      };

      const validation = await manager.validateEnvironment(invalidConfig);
      expect(validation.status).toBe('invalid');
      expect(validation.errors.length).toBeGreaterThan(0);
    });

    it('should handle concurrent access safely', async () => {
      const environmentId = await manager.createEnvironment(mockEnvironmentConfig);

      // Simulate concurrent operations
      const operations = [
        manager.updateEnvironment(environmentId, { name: 'Updated Name 1' }),
        manager.updateEnvironment(environmentId, { name: 'Updated Name 2' }),
        manager.getEnvironment(environmentId),
        manager.getEnvironmentStatus(environmentId),
      ];

      await expect(Promise.all(operations)).resolves.toBeDefined();
    });
  });

  // ============================================================================
  // SECURITY TESTS
  // ============================================================================

  describe('Security', () => {
    it('should validate authority data', async () => {
      const health = await manager.getHealth();
      expect(health.authority.validator).toBe('President & CEO, E.Z. Consultancy');
      expect(health.authority.level).toBe('architectural-authority');
      expect(health.authority.validationStatus).toBe('validated');
    });

    it('should handle sensitive data properly', async () => {
      const environmentId = await manager.createEnvironment(mockEnvironmentConfig);
      const environment = await manager.getEnvironment(environmentId);

      // Secrets should be preserved but not exposed in logs
      expect(environment!.secrets.API_KEY).toBe('test-key-123');
      expect(environment!.secrets.DB_PASSWORD).toBe('test-password');
    });

    it('should validate security configuration', async () => {
      const validation = await manager.validateEnvironment(mockEnvironmentConfig);
      expect(validation.status).toBe('valid');
      // Security validation should pass for properly configured environments
    });
  });

  // ============================================================================
  // PERFORMANCE TESTS
  // ============================================================================

  describe('Performance', () => {
    it('should handle large number of environments efficiently', async () => {
      const startTime = Date.now();
      const promises: Promise<string>[] = [];

      for (let i = 0; i < 100; i++) {
        const config = { ...mockEnvironmentConfig, id: `perf-env-${i}` };
        promises.push(manager.createEnvironment(config));
      }

      await Promise.all(promises);
      const endTime = Date.now();

      expect(endTime - startTime).toBeLessThan(10000); // Should complete in under 10 seconds
    });

    it('should maintain performance under concurrent load', async () => {
      const startTime = Date.now();
      const operations: Promise<any>[] = [];

      // Mix of different operations
      for (let i = 0; i < 50; i++) {
        const config = { ...mockEnvironmentConfig, id: `concurrent-${i}` };
        operations.push(manager.createEnvironment(config));
      }

      for (let i = 0; i < 10; i++) {
        operations.push(manager.listEnvironments());
      }

      await Promise.all(operations);
      const endTime = Date.now();

      expect(endTime - startTime).toBeLessThan(15000); // Should complete in under 15 seconds
    });

    it('should efficiently filter large datasets', async () => {
      // Create environments with various configurations
      const promises: Promise<string>[] = [];
      for (let i = 0; i < 200; i++) {
        const type = ['development', 'testing', 'staging', 'production'][i % 4] as any;
        const config = { ...mockEnvironmentConfig, id: `filter-env-${i}`, type };
        promises.push(manager.createEnvironment(config));
      }
      await Promise.all(promises);

      const startTime = Date.now();
      const filtered = await manager.listEnvironments({ type: ['development'] });
      const endTime = Date.now();

      expect(filtered.length).toBe(50); // Should return 50 development environments
      expect(endTime - startTime).toBeLessThan(1000); // Should complete in under 1 second
    });
  });

  // ============================================================================
  // INTEGRATION TESTS
  // ============================================================================

  describe('Integration', () => {
    it('should integrate with tracking service', async () => {
      const validation = await manager.validate();
      expect(validation.componentId).toBe('governance-rule-environment-manager');
      expect(validation.status).toBe('valid');
    });

    it('should provide governance service interface', () => {
      expect(manager.id).toBeDefined();
      expect(manager.authority).toBeDefined();
      expect(manager.isReady()).toBe(true);
    });

    it('should implement management service interface', async () => {
      const health = await manager.getHealth();
      const metrics = await manager.getMetrics();

      expect(health).toBeDefined();
      expect(metrics).toBeDefined();
    });
  });

  // ============================================================================
  // SHUTDOWN TESTS
  // ============================================================================

  describe('Shutdown', () => {
    it('should shutdown gracefully', async () => {
      const newManager = new GovernanceRuleEnvironmentManager();
      await newManager.initialize();

      await expect(newManager.shutdown()).resolves.not.toThrow();
      expect(newManager.isReady()).toBe(false);
    });

    it('should handle operations after shutdown', async () => {
      const newManager = new GovernanceRuleEnvironmentManager();
      await newManager.initialize();
      await newManager.shutdown();

      const validation = await newManager.validate();
      expect(validation.status).toBe('invalid');
      expect(validation.errors).toContain('Service is shutdown');
    });
  });

  // ============================================================================
  // EDGE CASES AND BOUNDARY TESTS
  // ============================================================================

  describe('Edge Cases and Boundaries', () => {
    it('should handle empty environment list', async () => {
      const newManager = new GovernanceRuleEnvironmentManager();
      await newManager.initialize();

      const environments = await newManager.listEnvironments();
      expect(environments).toEqual([]);

      await newManager.shutdown();
    });

    it('should handle complex filter combinations', async () => {
      const environmentId = await manager.createEnvironment(mockEnvironmentConfig);
      
      const filter: IEnvironmentFilter = {
        type: ['testing'],
        tags: { environment: 'test' },
        createdRange: {
          start: new Date(Date.now() - 60000),
          end: new Date(Date.now() + 60000),
        },
      };

      const environments = await manager.listEnvironments(filter);
      expect(environments.length).toBeGreaterThanOrEqual(1);
    });

    it('should handle environment type conversions', async () => {
      // Test with a valid type that gets converted in the return
      const testConfig = { ...mockEnvironmentConfig, id: 'conversion-env', type: 'testing' as const };
      const environmentId = await manager.createEnvironment(testConfig);

      const environment = await manager.getEnvironment(environmentId);
      expect(environment!.type).toBe('testing'); // Should remain as testing
      expect(environment!.id).toBe('conversion-env');
    });

    it('should handle status conversions', async () => {
      const environmentId = await manager.createEnvironment(mockEnvironmentConfig);
      
      // Simulate archived status
      const environment = await manager.getEnvironment(environmentId);
      expect(environment!.status).not.toBe('archived'); // Should be converted
    });
  });
}); 