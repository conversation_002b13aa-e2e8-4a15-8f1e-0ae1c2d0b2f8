/**
 * @file GovernanceRuleTemplateSecurity Tests
 * @filepath server/src/platform/governance/management-configuration/__tests__/GovernanceRuleTemplateSecurity.test.ts
 * @reference G-TSK-07.SUB-07.2.TEST-01
 * @template templates/contexts/foundation-context/governance/security-test.template
 * @tier T2
 * @context foundation-context
 * @category Foundation
 * @created 2025-01-27
 * @modified 2025-07-04
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-009-template-security-architecture
 * @governance-dcr DCR-foundation-008-template-security-standards
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @tests server/src/platform/governance/management-configuration/GovernanceRuleTemplateSecurity
 * @depends-on shared/src/types/platform/governance/security-types
 * @related-contexts foundation-context, governance-context, security-context
 * @governance-impact framework-foundation, template-security-testing
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type governance-template-security-test
 * @lifecycle-stage testing
 * @testing-status comprehensive
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/contexts/foundation-context/governance/GovernanceRuleTemplateSecurity-tests.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 */

import { GovernanceRuleTemplateSecurity } from '../GovernanceRuleTemplateSecurity';

describe('GovernanceRuleTemplateSecurity', () => {
  let templateSecurity: GovernanceRuleTemplateSecurity;

  beforeEach(() => {
    templateSecurity = new GovernanceRuleTemplateSecurity();
  });

  afterEach(() => {
    templateSecurity.shutdown();
  });

  describe('Service Lifecycle', () => {
    test('should initialize successfully', async () => {
      await expect(templateSecurity.initialize()).resolves.not.toThrow();
    });

    test('should shutdown gracefully', async () => {
      await templateSecurity.initialize();
      await expect(templateSecurity.shutdown()).resolves.not.toThrow();
    });
  });

  describe('Security Configuration', () => {
    test('should get security configuration', () => {
      const config = templateSecurity.getSecurityConfig();
      expect(config).toBeDefined();
      expect(typeof config).toBe('object');
    });

    test('should update security configuration', async () => {
      const updates = {
        xssProtection: true,
        csrfProtection: true
      };
      
      await expect(templateSecurity.updateSecurityConfig(updates)).resolves.not.toThrow();
    });
  });

  describe('Security Validation', () => {
    test('should validate security compliance', async () => {
      const isCompliant = await templateSecurity.validateSecurityCompliance();
      expect(typeof isCompliant).toBe('boolean');
    });

    test('should generate security report', async () => {
      const report = await templateSecurity.generateSecurityReport();
      expect(report).toBeDefined();
      expect(typeof report).toBe('object');
    });
  });

  describe('Template Security Validation', () => {
    test('should validate safe template content', async () => {
      const safeTemplate = 'Hello {{name}}, welcome to our platform!';
      const result = await templateSecurity.validateTemplateSecurity(safeTemplate);
      
      expect(result).toBeDefined();
      expect(result.isSecure).toBe(true);
      expect(result.threats).toHaveLength(0);
      expect(result.warnings).toHaveLength(0);
    });

    test('should detect XSS threats in template', async () => {
      const maliciousTemplate = 'Hello {{name}}, <script>alert("XSS")</script>';
      const result = await templateSecurity.validateTemplateSecurity(maliciousTemplate);
      
      expect(result).toBeDefined();
      expect(result.isSecure).toBe(false);
      expect(result.threats.length).toBeGreaterThan(0);
    });

    test('should detect template injection attempts', async () => {
      const templateInjectionContent = '{{constructor.constructor("return process")().exit()}}';
      const result = await templateSecurity.validateTemplateSecurity(templateInjectionContent);
      
      expect(result).toBeDefined();
      expect(result.isSecure).toBe(false);
      expect(result.threats.length).toBeGreaterThan(0);
    });
  });

  describe('Threat Detection', () => {
    test('should detect threats in input', async () => {
      const maliciousContent = '<script>alert("XSS")</script>';
      const threats = await templateSecurity.detectThreats(maliciousContent);
      
      expect(threats).toBeDefined();
      expect(Array.isArray(threats)).toBe(true);
      expect(threats.length).toBeGreaterThan(0);
    });

    test('should return empty array for safe content', async () => {
      const safeContent = 'This is safe content';
      const threats = await templateSecurity.detectThreats(safeContent);
      
      expect(threats).toBeDefined();
      expect(Array.isArray(threats)).toBe(true);
      expect(threats.length).toBe(0);
    });
  });

  describe('Template Output Sanitization', () => {
    test('should sanitize template output', async () => {
      const output = '<script>alert("XSS")</script><div>Safe content</div>';
      const sanitized = await templateSecurity.sanitizeTemplateOutput(output);
      
      expect(sanitized).toBeDefined();
      expect(typeof sanitized).toBe('string');
      expect(sanitized).not.toContain('<script>');
    });

    test('should preserve safe content during sanitization', async () => {
      const safeOutput = '<div>Safe content</div>';
      const sanitized = await templateSecurity.sanitizeTemplateOutput(safeOutput);
      
      expect(sanitized).toBeDefined();
      expect(sanitized).toContain('Safe content');
    });
  });

  describe('Security Audit', () => {
    test('should audit security events', async () => {
      const event = {
        logId: 'test-log-id',
        timestamp: new Date(),
        eventType: 'configuration' as const,
        action: 'template_validation',
        resource: 'test-template',
        result: 'success' as const,
        details: { test: true },
        riskLevel: 'low' as const
      };
      
      await expect(templateSecurity.auditSecurityEvent(event)).resolves.not.toThrow();
    });
  });

  describe('Error Handling', () => {
    test('should handle null input gracefully', async () => {
      const result = await templateSecurity.validateTemplateSecurity(null as any);
      
      expect(result).toBeDefined();
      expect(result.isSecure).toBe(false);
    });

    test('should handle undefined input gracefully', async () => {
      const result = await templateSecurity.validateTemplateSecurity(undefined as any);
      
      expect(result).toBeDefined();
      expect(result.isSecure).toBe(false);
    });

    test('should handle empty string input', async () => {
      const result = await templateSecurity.validateTemplateSecurity('');
      
      expect(result).toBeDefined();
      expect(result.isSecure).toBe(true);
    });
  });
}); 