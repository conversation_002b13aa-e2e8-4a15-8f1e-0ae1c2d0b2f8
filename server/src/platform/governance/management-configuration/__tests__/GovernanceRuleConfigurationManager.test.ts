/**
 * @file GovernanceRuleConfigurationManager Test Suite
 * @filepath server/src/platform/governance/management-configuration/__tests__/GovernanceRuleConfigurationManager.test.ts
 * @reference G-TSK-07.SUB-07.1.TEST-01
 * @tier T2
 * @context foundation-context
 * @category Foundation
 * @created 2025-01-27
 * @modified 2025-01-27
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { 
  GovernanceRuleConfigurationManager,
  IConfigurationManager,
  TConfigurationValue,
  TConfigurationData,
  TConfigurationHierarchy
} from '../GovernanceRuleConfigurationManager';

describe('GovernanceRuleConfigurationManager', () => {
  let configurationManager: GovernanceRuleConfigurationManager;

  beforeEach(async () => {
    configurationManager = new GovernanceRuleConfigurationManager();
    await configurationManager.initialize();
  });

  afterEach(async () => {
    await configurationManager.shutdown();
  });

  // ============================================================================
  // INITIALIZATION TESTS
  // ============================================================================

  describe('Initialization', () => {
    it('should initialize successfully', async () => {
      const manager = new GovernanceRuleConfigurationManager();
      await expect(manager.initialize()).resolves.not.toThrow();
      expect(manager.isReady()).toBe(true);
      await manager.shutdown();
    });

    it('should have correct component identification', () => {
      expect(configurationManager.id).toBe('governance-rule-configuration-manager');
      expect(configurationManager.authority).toBe('President & CEO, E.Z. Consultancy');
    });

    it('should be ready after initialization', () => {
      expect(configurationManager.isReady()).toBe(true);
    });
  });

  // ============================================================================
  // CONFIGURATION MANAGEMENT TESTS
  // ============================================================================

  describe('Configuration Management', () => {
    it('should set and get configuration values', async () => {
      const key = 'test.setting';
      const value = 'test-value';

      await configurationManager.setConfiguration(key, value);
      const result = await configurationManager.getConfiguration(key);

      expect(result.value).toBe(value);
      expect(result.source).toBe('global');
      expect(result.encrypted).toBe(false);
    });

    it('should handle context-specific configurations', async () => {
      const key = 'context.setting';
      const value = 'context-value';
      const context = 'test-context';

      await configurationManager.setConfiguration(key, value, context);
      const result = await configurationManager.getConfiguration(key, context);

      expect(result.value).toBe(value);
      expect(result.source).toBe('context');
    });

    it('should resolve configuration hierarchy correctly', async () => {
      const key = 'hierarchy.test';
      const globalValue = 'global-value';
      const contextValue = 'context-value';
      const context = 'test-context';

      // Set global configuration
      await configurationManager.setConfiguration(key, globalValue);
      
      // Set context-specific configuration
      await configurationManager.setConfiguration(key, contextValue, context);

      // Context should override global
      const contextResult = await configurationManager.getConfiguration(key, context);
      expect(contextResult.value).toBe(contextValue);
      expect(contextResult.source).toBe('context');

      // Global should be used when no context specified
      const globalResult = await configurationManager.getConfiguration(key);
      expect(globalResult.value).toBe(globalValue);
      expect(globalResult.source).toBe('global');
    });

    it('should throw error for non-existent configuration', async () => {
      await expect(configurationManager.getConfiguration('non.existent.key'))
        .rejects.toThrow('Configuration key \'non.existent.key\' not found in hierarchy');
    });
  });

  // ============================================================================
  // VALIDATION TESTS
  // ============================================================================

  describe('Configuration Validation', () => {
    it('should validate configuration data successfully', async () => {
      const validConfig: TConfigurationData = {
        id: 'test-config',
        name: 'Test Configuration',
        type: 'global',
        data: { key: 'value' },
        schema: {},
        encrypted: false,
        version: '1.0.0',
        created: new Date(),
        modified: new Date(),
        metadata: {}
      };

      const result = await configurationManager.validateConfiguration(validConfig);
      expect(result.status).toBe('valid');
      expect(result.errors).toHaveLength(0);
    });

    it('should fail validation for invalid configuration', async () => {
      const invalidConfig: TConfigurationData = {
        id: '',
        name: '',
        type: 'invalid' as any,
        data: null as any,
        schema: {},
        encrypted: false,
        version: '1.0.0',
        created: new Date(),
        modified: new Date(),
        metadata: {}
      };

      const result = await configurationManager.validateConfiguration(invalidConfig);
      expect(result.status).toBe('invalid');
      expect(result.errors.length).toBeGreaterThan(0);
    });

    it('should validate service state', async () => {
      const result = await configurationManager.validate();
      expect(result.status).toBe('valid');
      expect(result.componentId).toBe('governance-rule-configuration-manager');
    });
  });

  // ============================================================================
  // ENCRYPTION TESTS
  // ============================================================================

  describe('Encryption Operations', () => {
    it('should encrypt and decrypt configuration data', async () => {
      const originalData = 'sensitive-configuration-data';
      
      const encrypted = await configurationManager.encryptConfiguration(originalData);
      expect(encrypted).not.toBe(originalData);
      expect(encrypted).toContain(':');

      const decrypted = await configurationManager.decryptConfiguration(encrypted);
      expect(decrypted).toBe(originalData);
    });

    it('should handle sensitive configuration keys', async () => {
      const sensitiveKey = 'database.password';
      const sensitiveValue = 'secret-password';

      await configurationManager.setConfiguration(sensitiveKey, sensitiveValue);
      const result = await configurationManager.getConfiguration(sensitiveKey);

      expect(result.value).toBeDefined();
      // Note: In real implementation, sensitive values would be automatically encrypted
    });

    it('should handle encryption errors gracefully', async () => {
      await expect(configurationManager.decryptConfiguration('invalid-encrypted-data'))
        .rejects.toThrow();
    });
  });

  // ============================================================================
  // HIERARCHY TESTS
  // ============================================================================

  describe('Configuration Hierarchy', () => {
    it('should return configuration hierarchy', async () => {
      const hierarchy = await configurationManager.getConfigurationHierarchy();
      
      expect(hierarchy.global).toBeDefined();
      expect(hierarchy.contexts).toBeInstanceOf(Map);
      expect(hierarchy.environments).toBeInstanceOf(Map);
      expect(hierarchy.components).toBeInstanceOf(Map);
    });

    it('should merge configurations correctly', async () => {
      const config1: TConfigurationData = {
        id: 'config1',
        name: 'Config 1',
        type: 'global',
        data: { key1: 'value1', shared: 'global' },
        schema: {},
        encrypted: false,
        version: '1.0.0',
        created: new Date(),
        modified: new Date(),
        metadata: {}
      };

      const config2: TConfigurationData = {
        id: 'config2',
        name: 'Config 2',
        type: 'component',
        data: { key2: 'value2', shared: 'component' },
        schema: {},
        encrypted: false,
        version: '1.0.0',
        created: new Date(),
        modified: new Date(),
        metadata: {}
      };

      const merged = await configurationManager.mergeConfigurations([config1, config2]);
      
      expect(merged.data.key1).toBe('value1');
      expect(merged.data.key2).toBe('value2');
      expect(merged.data.shared).toBe('component'); // Component should override global
    });
  });

  // ============================================================================
  // CACHE TESTS
  // ============================================================================

  describe('Cache Operations', () => {
    it('should cache configuration values', async () => {
      const key = 'cached.setting';
      const value = 'cached-value';

      await configurationManager.setConfiguration(key, value);
      
      // First call should cache the value
      const result1 = await configurationManager.getConfiguration(key);
      expect(result1.value).toBe(value);

      // Second call should use cache
      const result2 = await configurationManager.getConfiguration(key);
      expect(result2.value).toBe(value);
    });

    it('should invalidate cache on configuration update', async () => {
      const key = 'cache.test';
      const value1 = 'initial-value';
      const value2 = 'updated-value';

      await configurationManager.setConfiguration(key, value1);
      const result1 = await configurationManager.getConfiguration(key);
      expect(result1.value).toBe(value1);

      await configurationManager.setConfiguration(key, value2);
      const result2 = await configurationManager.getConfiguration(key);
      expect(result2.value).toBe(value2);
    });
  });

  // ============================================================================
  // PERFORMANCE TESTS
  // ============================================================================

  describe('Performance Metrics', () => {
    it('should track performance metrics', async () => {
      const key = 'performance.test';
      const value = 'test-value';

      await configurationManager.setConfiguration(key, value);
      await configurationManager.getConfiguration(key);

      const metrics = await configurationManager.getMetrics();
      expect(metrics.performance.totalOperations).toBeGreaterThan(0);
      expect(metrics.performance.successfulOperations).toBeGreaterThan(0);
    });

    it('should calculate cache hit rate', async () => {
      const key = 'cache.rate.test';
      const value = 'test-value';

      await configurationManager.setConfiguration(key, value);
      
      // Multiple gets should improve cache hit rate
      for (let i = 0; i < 5; i++) {
        await configurationManager.getConfiguration(key);
      }

      const metrics = await configurationManager.getMetrics();
      expect(metrics.performance.cacheHitRate).toBeGreaterThan(0);
    });
  });

  // ============================================================================
  // ERROR HANDLING TESTS
  // ============================================================================

  describe('Error Handling', () => {
    it('should handle invalid configuration values', async () => {
      await expect(configurationManager.setConfiguration('test.key', undefined))
        .rejects.toThrow('Configuration validation failed');
    });

    it('should handle service shutdown gracefully', async () => {
      await expect(configurationManager.shutdown()).resolves.toBeUndefined();
      expect(configurationManager.isReady()).toBe(false);
    });

    it('should track failed operations', async () => {
      try {
        await configurationManager.setConfiguration('test.key', undefined);
      } catch (error) {
        // Expected to fail
      }

      const metrics = await configurationManager.getMetrics();
      expect(metrics.performance.failedOperations).toBeGreaterThan(0);
    });
  });

  // ============================================================================
  // INTEGRATION TESTS
  // ============================================================================

  describe('Integration Tests', () => {
    it('should integrate with BaseTrackingService', async () => {
      const trackingData = { operation: 'test-tracking' };
      
      // Should not throw when tracking data
      await (configurationManager as any).doTrack(trackingData);
      expect(true).toBe(true); // Test passes if no exception is thrown
    });

    it('should provide health status', async () => {
      const health = await configurationManager.getHealth();
      
      expect(health.status).toBe('healthy');
      expect(health.componentId).toBe('governance-rule-configuration-manager');
      expect(health.version).toBe('1.0.0');
      expect(health.performance).toBeDefined();
      expect(health.authority).toBeDefined();
    });
  });

  // ============================================================================
  // BASETRACKINGSERVICE COMPLIANCE TESTS
  // ============================================================================

  describe('BaseTrackingService Compliance', () => {
    it('should implement required abstract methods', () => {
      expect(typeof (configurationManager as any).getServiceName).toBe('function');
      expect(typeof (configurationManager as any).getServiceVersion).toBe('function');
      expect(typeof (configurationManager as any).doInitialize).toBe('function');
      expect(typeof (configurationManager as any).doTrack).toBe('function');
      expect(typeof (configurationManager as any).doValidate).toBe('function');
    });

    it('should return correct service name and version', () => {
      expect((configurationManager as any).getServiceName()).toBe('governance-rule-configuration-manager');
      expect((configurationManager as any).getServiceVersion()).toBe('1.0.0');
    });

    it('should handle tracking data', async () => {
      const trackingData = { operation: 'test-tracking' };
      
      await expect((configurationManager as any).doTrack(trackingData))
        .resolves.not.toThrow();
    });

    it('should validate service state', async () => {
      const validation = await (configurationManager as any).doValidate();
      
      expect(validation).toBeDefined();
      expect(validation.validationId).toBeDefined();
      expect(validation.componentId).toBe('governance-rule-configuration-manager');
      expect(validation.status).toBe('valid');
    });
  });

  // ============================================================================
  // AUTHORITY VALIDATION TESTS
  // ============================================================================

  describe('Authority Validation', () => {
    it('should have correct authority data', () => {
      expect(configurationManager.authority).toBe('President & CEO, E.Z. Consultancy');
    });

    it('should include authority in validation results', async () => {
      const validation = await configurationManager.validate();
      expect(validation.metadata).toBeDefined();
      expect(validation.status).toBe('valid');
      expect(validation.componentId).toBe('governance-rule-configuration-manager');
    });

    it('should include authority in configuration values', async () => {
      const key = 'authority.test';
      const value = 'test-value';

      await configurationManager.setConfiguration(key, value);
      const result = await configurationManager.getConfiguration(key);

      expect(result.metadata.authority).toBe('President & CEO, E.Z. Consultancy');
      expect(result.metadata.compliance).toContain('authority-validation');
    });
  });

  // ============================================================================
  // CONCURRENT OPERATIONS TESTS
  // ============================================================================

  describe('Concurrent Operations', () => {
    it('should handle concurrent configuration operations', async () => {
      const operations: Promise<void>[] = [];
      
      for (let i = 0; i < 10; i++) {
        operations.push(
          configurationManager.setConfiguration(`concurrent.test.${i}`, `value-${i}`)
        );
      }

      await expect(Promise.all(operations)).resolves.not.toThrow();

      // Verify all configurations were set
      for (let i = 0; i < 10; i++) {
        const result = await configurationManager.getConfiguration(`concurrent.test.${i}`);
        expect(result.value).toBe(`value-${i}`);
      }
    });

    it('should handle concurrent cache operations', async () => {
      const key = 'concurrent.cache.test';
      const value = 'cache-value';

      await configurationManager.setConfiguration(key, value);

      const operations: Promise<TConfigurationValue>[] = [];
      for (let i = 0; i < 20; i++) {
        operations.push(configurationManager.getConfiguration(key));
      }

      const results = await Promise.all(operations);
      results.forEach(result => {
        expect(result.value).toBe(value);
      });
    });
  });

  // ============================================================================
  // EDGE CASES TESTS
  // ============================================================================

  describe('Edge Cases', () => {
    it('should handle empty configuration keys', async () => {
      await expect(configurationManager.setConfiguration('', 'value'))
        .rejects.toThrow();
    });

    it('should handle very long configuration keys', async () => {
      const longKey = 'a'.repeat(1000);
      const value = 'test-value';

      await expect(configurationManager.setConfiguration(longKey, value))
        .resolves.not.toThrow();

      const result = await configurationManager.getConfiguration(longKey);
      expect(result.value).toBe(value);
    });

    it('should handle complex nested objects', async () => {
      const complexValue = {
        nested: {
          deep: {
            value: 'test',
            array: [1, 2, 3],
            object: { key: 'value' }
          }
        }
      };

      await configurationManager.setConfiguration('complex.test', complexValue);
      const result = await configurationManager.getConfiguration('complex.test');
      
      expect(result.value).toEqual(complexValue);
    });

    it('should handle null and undefined contexts', async () => {
      const key = 'null.context.test';
      const value = 'test-value';

      await configurationManager.setConfiguration(key, value, undefined);
      const result = await configurationManager.getConfiguration(key, null as any);
      
      expect(result.value).toBe(value);
    });
  });
}); 