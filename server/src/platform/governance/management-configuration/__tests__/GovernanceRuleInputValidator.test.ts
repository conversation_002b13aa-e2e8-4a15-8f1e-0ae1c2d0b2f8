/**
 * @file GovernanceRuleInputValidator Tests
 * @filepath server/src/platform/governance/management-configuration/__tests__/GovernanceRuleInputValidator.test.ts
 * @reference G-TSK-07.SUB-07.2.TEST-04
 * @template templates/contexts/foundation-context/governance/security-test.template
 * @tier T2
 * @context foundation-context
 * @category Foundation
 * @created 2025-01-27
 * @modified 2025-07-04
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-009-template-security-architecture
 * @governance-dcr DCR-foundation-008-template-security-standards
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @tests server/src/platform/governance/management-configuration/GovernanceRuleInputValidator
 * @depends-on shared/src/types/platform/governance/security-types
 * @related-contexts foundation-context, governance-context, security-context
 * @governance-impact framework-foundation, input-validation-testing
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type governance-input-validator-test
 * @lifecycle-stage testing
 * @testing-status comprehensive
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/contexts/foundation-context/governance/GovernanceRuleInputValidator-tests.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 */

import { GovernanceRuleInputValidator } from '../GovernanceRuleInputValidator';

describe('GovernanceRuleInputValidator', () => {
  let inputValidator: GovernanceRuleInputValidator;

  beforeEach(() => {
    inputValidator = new GovernanceRuleInputValidator();
  });

  afterEach(() => {
    inputValidator.shutdown();
  });

  describe('Service Lifecycle', () => {
    test('should initialize successfully', async () => {
      await expect(inputValidator.initialize()).resolves.not.toThrow();
    });

    test('should shutdown gracefully', async () => {
      await inputValidator.initialize();
      await expect(inputValidator.shutdown()).resolves.not.toThrow();
    });
  });

  describe('Security Configuration', () => {
    test('should get security configuration', () => {
      const config = inputValidator.getSecurityConfig();
      expect(config).toBeDefined();
      expect(typeof config).toBe('object');
    });

    test('should update security configuration', async () => {
      const updates = {
        inputSanitization: true,
        threatDetection: true
      };
      
      await expect(inputValidator.updateSecurityConfig(updates)).resolves.not.toThrow();
    });
  });

  describe('Template Input Validation', () => {
    test('should validate safe template input', async () => {
      const safeInput = 'Hello {{user.name}}, welcome to our platform!';
      const result = await inputValidator.validateTemplateInput(safeInput);
      
      expect(result).toBeDefined();
      expect(result.isSecure).toBe(true);
      expect(result.threats).toHaveLength(0);
    });

    test('should detect XSS in template input', async () => {
      const maliciousInput = 'Hello {{user.name}}, <script>alert("XSS")</script>';
      const result = await inputValidator.validateTemplateInput(maliciousInput);
      
      expect(result).toBeDefined();
      expect(result.isSecure).toBe(false);
      expect(result.threats.length).toBeGreaterThan(0);
    });

    test('should validate template input with schema', async () => {
      const input = 'User: {{user.name}}';
      const schema = {
        schemaId: 'template-schema-001',
        version: '1.0.0',
        fields: [
          {
            name: 'user.name',
            type: 'string' as const,
            required: true,
            maxLength: 100
          }
        ],
        required: ['user.name'],
        additionalProperties: false,
        validationRules: []
      };
      
      const result = await inputValidator.validateTemplateInput(input, schema);
      
      expect(result).toBeDefined();
      expect(result.isSecure).toBe(true);
    });
  });

  describe('User Input Sanitization', () => {
    test('should sanitize user input', async () => {
      const maliciousInput = '<script>alert("XSS")</script><div>Safe content</div>';
      const sanitized = await inputValidator.sanitizeUserInput(maliciousInput);
      
      expect(sanitized).toBeDefined();
      expect(typeof sanitized).toBe('string');
      expect(sanitized).not.toContain('<script>');
      expect(sanitized).toContain('Safe content');
    });

    test('should sanitize user input with custom rules', async () => {
      const input = 'Test <b>bold</b> and <script>alert("XSS")</script>';
      const rules = [
        {
          ruleId: 'remove-scripts',
          name: 'Remove Script Tags',
          type: 'html' as const,
          pattern: '<script[^>]*>.*?</script>',
          replacement: '',
          flags: 'gi',
          enabled: true,
          priority: 1
        }
      ];
      
      const sanitized = await inputValidator.sanitizeUserInput(input, rules);
      
      expect(sanitized).toBeDefined();
      expect(typeof sanitized).toBe('string');
      expect(sanitized).not.toContain('<script>');
      expect(sanitized).toContain('<b>bold</b>');
    });

    test('should handle object input sanitization', async () => {
      const objectInput = {
        name: 'John<script>alert("XSS")</script>',
        email: '<EMAIL>',
        message: '<p>Hello world</p>'
      };
      
      const sanitized = await inputValidator.sanitizeUserInput(objectInput);
      
      expect(sanitized).toBeDefined();
      expect(typeof sanitized).toBe('object');
      expect(sanitized.name).not.toContain('<script>');
      expect(sanitized.email).toBe('<EMAIL>');
    });
  });

  describe('Data Schema Validation', () => {
    test('should validate data against schema', async () => {
      const data = {
        name: 'John Doe',
        email: '<EMAIL>',
        age: 30
      };
      
      const schema = {
        schemaId: 'user-schema-001',
        version: '1.0.0',
        fields: [
          {
            name: 'name',
            type: 'string' as const,
            required: true,
            maxLength: 100
          },
          {
            name: 'email',
            type: 'email' as const,
            required: true
          },
          {
            name: 'age',
            type: 'number' as const,
            required: false
          }
        ],
        required: ['name', 'email'],
        additionalProperties: false,
        validationRules: []
      };
      
      const result = await inputValidator.validateDataSchema(data, schema);
      
      expect(result).toBeDefined();
      expect(result.isSecure).toBe(true);
    });

    test('should reject invalid data against schema', async () => {
      const data = {
        name: '',
        email: 'invalid-email',
        age: 'not-a-number'
      };
      
      const schema = {
        schemaId: 'user-schema-001',
        version: '1.0.0',
        fields: [
          {
            name: 'name',
            type: 'string' as const,
            required: true,
            minLength: 1
          },
          {
            name: 'email',
            type: 'email' as const,
            required: true
          },
          {
            name: 'age',
            type: 'number' as const,
            required: false
          }
        ],
        required: ['name', 'email'],
        additionalProperties: false,
        validationRules: []
      };
      
      const result = await inputValidator.validateDataSchema(data, schema);
      
      expect(result).toBeDefined();
      expect(result.isSecure).toBe(false);
      expect(result.warnings.length).toBeGreaterThan(0);
    });
  });

  describe('Injection Detection', () => {
    test('should detect SQL injection attempts', async () => {
      const maliciousInput = "'; DROP TABLE users; --";
      const threats = await inputValidator.detectInjectionAttempts(maliciousInput);
      
      expect(threats).toBeDefined();
      expect(Array.isArray(threats)).toBe(true);
      expect(threats.length).toBeGreaterThan(0);
      expect(threats[0].type).toBe('sql');
    });

    test('should detect XSS injection attempts', async () => {
      const maliciousInput = '<script>alert("XSS")</script>';
      const threats = await inputValidator.detectInjectionAttempts(maliciousInput);
      
      expect(threats).toBeDefined();
      expect(Array.isArray(threats)).toBe(true);
      expect(threats.length).toBeGreaterThan(0);
      expect(threats[0].type).toBe('xss');
    });

    test('should detect command injection attempts', async () => {
      const maliciousInput = 'test; rm -rf /';
      const threats = await inputValidator.detectInjectionAttempts(maliciousInput);
      
      expect(threats).toBeDefined();
      expect(Array.isArray(threats)).toBe(true);
      expect(threats.length).toBeGreaterThan(0);
      expect(threats[0].type).toBe('command');
    });

    test('should return empty array for safe input', async () => {
      const safeInput = 'This is safe user input';
      const threats = await inputValidator.detectInjectionAttempts(safeInput);
      
      expect(threats).toBeDefined();
      expect(Array.isArray(threats)).toBe(true);
      expect(threats.length).toBe(0);
    });
  });

  describe('Input Sanitization with Rules', () => {
    test('should apply sanitization rules', async () => {
      const input = 'Test <script>alert("XSS")</script> content';
      const rules = [
        {
          ruleId: 'remove-scripts',
          name: 'Remove Script Tags',
          type: 'html' as const,
          pattern: '<script[^>]*>.*?</script>',
          replacement: '',
          flags: 'gi',
          enabled: true,
          priority: 1
        }
      ];
      
      const sanitized = await inputValidator.applyInputSanitization(input, rules);
      
      expect(sanitized).toBeDefined();
      expect(typeof sanitized).toBe('string');
      expect(sanitized).not.toContain('<script>');
      expect(sanitized).toContain('Test');
      expect(sanitized).toContain('content');
    });
  });

  describe('Input Compliance Validation', () => {
    test('should validate input compliance', async () => {
      const input = {
        name: 'John Doe',
        email: '<EMAIL>'
      };
      
      const rules = [
        {
          ruleId: 'name-length',
          name: 'Name Length Check',
          type: 'length' as const,
          parameters: { min: 1, max: 100 },
          errorMessage: 'Name must be between 1 and 100 characters',
          severity: 'error' as const,
          enabled: true
        }
      ];
      
      const result = await inputValidator.validateInputCompliance(input, rules);
      
      expect(result).toBeDefined();
      expect(result.isSecure).toBe(true);
    });
  });

  describe('Security Compliance', () => {
    test('should validate security compliance', async () => {
      const isCompliant = await inputValidator.validateSecurityCompliance();
      expect(typeof isCompliant).toBe('boolean');
    });

    test('should generate security report', async () => {
      const report = await inputValidator.generateSecurityReport();
      expect(report).toBeDefined();
      expect(typeof report).toBe('object');
    });
  });

  describe('Security Audit', () => {
    test('should audit security events', async () => {
      const event = {
        logId: 'test-log-id',
        timestamp: new Date(),
        eventType: 'data_access' as const,
        action: 'input_validation',
        resource: 'user-input',
        result: 'success' as const,
        details: { test: true },
        riskLevel: 'low' as const
      };
      
      await expect(inputValidator.auditSecurityEvent(event)).resolves.not.toThrow();
    });
  });

  describe('Error Handling', () => {
    test('should handle null input gracefully', async () => {
      const result = await inputValidator.validateTemplateInput(null as any);
      
      expect(result).toBeDefined();
      expect(result.isSecure).toBe(false);
    });

    test('should handle undefined input gracefully', async () => {
      const result = await inputValidator.validateTemplateInput(undefined as any);
      
      expect(result).toBeDefined();
      expect(result.isSecure).toBe(false);
    });

    test('should handle empty string input', async () => {
      const result = await inputValidator.validateTemplateInput('');
      
      expect(result).toBeDefined();
      expect(result.isSecure).toBe(true);
    });

    test('should handle sanitization of null input', async () => {
      const result = await inputValidator.sanitizeUserInput(null);
      
      expect(result).toBeDefined();
    });
  });

  describe('Performance Tests', () => {
    test('should validate large inputs efficiently', async () => {
      const largeInput = 'Safe content '.repeat(1000);
      const startTime = Date.now();
      
      const result = await inputValidator.validateTemplateInput(largeInput);
      const endTime = Date.now();
      
      expect(endTime - startTime).toBeLessThan(2000); // Should complete within 2 seconds
      expect(result).toBeDefined();
      expect(result.isSecure).toBe(true);
    });

    test('should handle concurrent validations', async () => {
      const inputs = Array(50).fill(0).map((_, i) => `Safe input ${i}`);
      const promises: Promise<any>[] = inputs.map(input => inputValidator.validateTemplateInput(input));
      
      const results = await Promise.all(promises);
      
      expect(results).toHaveLength(50);
      results.forEach(result => {
        expect(result.isSecure).toBe(true);
      });
    });
  });
}); 