/**
 * @file Governance Rule Event Manager
 * @filepath server/src/platform/governance/automation-processing/GovernanceRuleEventManager.ts
 * @task-id G-SUB-05.2.CORE.002
 * @component governance-rule-event-manager
 * @reference foundation-context.PROCESSING.002
 * @template enterprise-event-manager
 * @tier T0
 * @context foundation-context
 * @category Processing
 * @created 2025-06-30
 * @modified 2025-07-01 03:01:05 +03
 * 
 * @description
 * Enterprise-grade governance rule event manager providing:
 * - Real-time event streaming with publish-subscribe patterns
 * - Event filtering and routing with complex event processing
 * - Event sourcing and replay capabilities
 * - Distributed processing with performance monitoring
 * - Advanced error handling and recovery mechanisms
 * - Analytics and monitoring integration
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-tracking-architecture
 * @governance-dcr DCR-foundation-001-tracking-development
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on server/src/platform/tracking/core-data/base/BaseTrackingService
 * @depends-on shared/src/types/platform/tracking/core/tracking-data-types
 * @depends-on shared/src/constants/platform/tracking/environment-constants-calculator
 * @enables server/src/platform/governance/automation-processing
 * @related-contexts foundation-context
 * @governance-impact framework-foundation, governance-automation
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type event-manager-service
 * @lifecycle-stage implementation
 * @testing-status unit-tested, integration-tested
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/contexts/foundation-context/services/governance-rule-event-manager.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 * 
 * 📝 VERSION HISTORY
 * @version-history
 * v1.2.0 (2025-07-01) - Enhanced event management with improved streaming and filtering capabilities
 * v1.1.0 (2025-06-30) - Added event sourcing and replay functionality
 * v1.0.0 (2025-06-30) - Initial implementation with real-time event processing
 */

// ============================================================================
// IMPORTS
// ============================================================================

import { BaseTrackingService } from '../../tracking/core-data/base/BaseTrackingService';
import { getTimerCoordinator } from '../../../../../shared/src/base/TimerCoordinationService';
import { getEnvironmentCalculator, EnvironmentConstantsCalculator } from '../../../../../shared/src/constants/platform/tracking/environment-constants-calculator';
// 🚨 PHASE 3.3: EventHandlerRegistry Integration - Enhanced Subscriber Lifecycle Management
import { getEventHandlerRegistry, EventHandlerRegistry } from '../../../../../shared/src/base/EventHandlerRegistry';
import { IRuleAuditLogger, RuleAuditLoggerFactory } from './factories/RuleAuditLoggerFactory';
import {
  EventProcessingError,
  StreamManagementError,
  StreamCreationError,
  ConfigurationError,
  EVENT_PROCESSING_BATCH_SIZE
} from './errors/ProcessingErrors';

import {
  TValidationResult,
  TAuthorityData
} from '../../../../../shared/src/types/platform/tracking/core/tracking-data-types';

import { 
  TAuthorityLevel,
  TValidationStatus
} from '../../../../../shared/src/types/platform/tracking/core/base-types';

import { TTrackingData } from '../../../../../shared/src/types/platform/tracking/core/tracking-data-types';

  // ============================================================================
// EVENT MANAGER INTERFACES
  // ============================================================================

interface IEventStream {
  id: string;
  name: string;
  eventTypes: string[];
  subscribers: IEventSubscriber[];
  filters: IEventFilter[];
  isActive: boolean;
  created: Date;
}

interface IEventSubscriber {
  id: string;
  name: string;
  handler: (event: IGovernanceEvent) => Promise<void>;
  eventTypes: string[];
  priority: number;
  isActive: boolean;
  // 🚨 PHASE 3.3: Added metadata for EventHandlerRegistry integration
  metadata?: {
    handlerIds?: string[];
    [key: string]: unknown;
  };
}

interface IEventFilter {
  id: string;
  condition: (event: IGovernanceEvent) => boolean;
  action: 'allow' | 'deny' | 'transform';
  transformer?: (event: IGovernanceEvent) => IGovernanceEvent;
}

interface IGovernanceEvent {
  id: string;
  type: string;
  source: string;
  timestamp: Date;
  data: any;
  metadata: {
    correlationId?: string;
    causationId?: string;
    streamId?: string;
    retryCount?: number;
    priority: number;
  };
}

interface IEventProcessingResult {
  success: boolean;
  processedEvents: number;
  failedEvents: number;
  errors: string[];
  warnings: string[];
  metrics: IEventProcessingMetrics;
}

interface IEventProcessingMetrics {
  executionTime: number;
  throughput: number;
  eventsByType: Record<string, number>;
  subscriberMetrics: Record<string, { processed: number; failed: number }>;
  streamMetrics: Record<string, { events: number; active: boolean }>;
}

interface IEventReplayOptions {
  fromTimestamp?: Date;
  toTimestamp?: Date;
  eventTypes?: string[];
  batchSize?: number;
  maxEvents?: number;
}

// ============================================================================
// EVENT MANAGER IMPLEMENTATION
// ============================================================================

export class GovernanceRuleEventManager extends BaseTrackingService {
  private readonly _componentId = 'governance-rule-event-manager';
  private readonly _environmentCalculator: EnvironmentConstantsCalculator;

  /** Authority chain */
  private readonly _authorityChain: TAuthorityData = {
    level: 'architectural-authority' as TAuthorityLevel,
    validator: 'E.Z. Consultancy',
    validationStatus: 'validated' as TValidationStatus,
    validatedAt: new Date().toISOString(),
    complianceScore: 100
  };

  // Getter to access authority chain (prevents unused variable warning)
  public get authorityChain(): TAuthorityData {
    return this._authorityChain;
  }

  /** Event streams registry */
  private readonly _streams = new Map<string, IEventStream>();

  /** Event subscribers registry */
  private readonly _subscribers = new Map<string, IEventSubscriber>();

  // 🚨 PHASE 3.3: EventHandlerRegistry Integration - Enhanced Subscriber Management
  private _eventRegistry: EventHandlerRegistry;

  /** Event store for sourcing and replay */
  private readonly _eventStore = new Map<string, IGovernanceEvent>();

  /** Event processing queues */
  private readonly _processingQueues = new Map<string, IGovernanceEvent[]>();

  /** Event processing metrics - properly structured for BaseTrackingService */
  private readonly _eventMetrics = {
    totalEvents: 0,
    processedEvents: 0,
    failedEvents: 0,
    activeStreams: 0,
    activeSubscribers: 0,
    averageProcessingTime: 0,
    lastProcessedAt: null as Date | null
  };

  /** Performance analytics */
  private readonly _analytics = {
    throughput: 0,
    eventsByType: {} as Record<string, number>,
    trends: [] as number[],
      metadata: {
        component: this._componentId,
        initialized: new Date().toISOString()
      }
    };

  // ✅ TIMER COORDINATION: Timer management now handled by TimerCoordinationService

  /** Audit logger for G-SUB-05.2 components */
  protected auditLogger: IRuleAuditLogger;

  constructor() {
    super();
    this._environmentCalculator = getEnvironmentCalculator();
    this.auditLogger = this.createAuditLogger();

    // 🚨 PHASE 3.3: Initialize EventHandlerRegistry for enhanced subscriber management
    this._eventRegistry = getEventHandlerRegistry();

    this._initializeDefaultStreams();
  }

  // ============================================================================
  // ABSTRACT METHOD IMPLEMENTATIONS
  // ============================================================================

  protected getServiceName(): string {
    return this._componentId;
  }

  protected getServiceVersion(): string {
    return '1.0.0';
  }

  protected async doInitialize(): Promise<void> {
    try {
      this.auditLogger.info('Initializing Governance Rule Event Manager');

      // Initialize environment constraints
      try {
        await (this._environmentCalculator as any).enforceMemoryBoundaries?.();
      } catch (error) {
        console.warn('Memory boundary enforcement not available:', error);
        // Fallback: trigger garbage collection if available
        if (global.gc) {
          global.gc();
        }
      }

      // Validate configuration
      await this._validateEventConfiguration();
      
      // Initialize event streams
      await this._initializeEventStreams();
      
      // Start real-time processing
      this._startRealTimeProcessing();
      
      // Start performance monitoring
      this._startPerformanceMonitoring();
      
      this.auditLogger.info('Event Manager initialized successfully');

    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.auditLogger.error('Failed to initialize Event Manager', { error: err.message });
      throw new ConfigurationError('Event Manager initialization failed', undefined, { originalError: err.message });
    }
  }

  protected async doTrack(data: TTrackingData): Promise<void> {
    try {
      this.auditLogger.debug('Tracking event processing data', { componentId: data.componentId });
      
      // Track event processing performance
      this._updateEventMetrics(data);
      
      // Store analytics data
      this._storeEventAnalyticsData(data);
      
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.auditLogger.error('Failed to track event data', { error: err.message });
      throw err;
    }
  }

  protected async doValidate(): Promise<TValidationResult> {
    try {
      const startTime = Date.now();
      
    const validations = await Promise.all([
        this._validateEventStreams(),
        this._validateEventSubscribers(),
        this._validateEventStore(),
        this._validateProcessingQueues(),
        this._validateMemoryConstraints()
    ]);

    const errors = validations.filter(v => v.status === 'invalid');
      const warnings = validations.filter(v => v.errors && v.errors.length > 0 && v.status === 'valid');
      const executionTime = Date.now() - startTime;

    return {
        validationId: this.generateId(),
        componentId: this._componentId,
      timestamp: new Date(),
        executionTime,
        status: errors.length === 0 ? 'valid' : 'invalid',
        overallScore: validations.reduce((sum, v) => sum + (v.overallScore || 0), 0) / validations.length,
        checks: validations,
        references: {
          componentId: this._componentId,
          internalReferences: [],
          externalReferences: [],
          circularReferences: [],
          missingReferences: [],
          redundantReferences: [],
      metadata: {
            totalReferences: 0,
            buildTimestamp: new Date(),
            analysisDepth: 0
          }
        },
        recommendations: [
          'Monitor event processing performance regularly',
          'Optimize stream configurations',
          'Review subscriber configurations'
        ],
        warnings: warnings.flatMap(v => v.warnings || []),
        errors: errors.flatMap(v => v.errors || []),
        metadata: {
          validationMethod: 'event-manager-validation',
          rulesApplied: validations.length,
          dependencyDepth: 0,
          cyclicDependencies: [],
          orphanReferences: []
        }
      };

    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.auditLogger.error('Validation failed', { error: err.message });
      
      return {
        validationId: this.generateId(),
        componentId: this._componentId,
        timestamp: new Date(),
        executionTime: 0,
        status: 'invalid',
        overallScore: 0,
        checks: [],
        references: {
          componentId: this._componentId,
          internalReferences: [],
          externalReferences: [],
          circularReferences: [],
          missingReferences: [],
          redundantReferences: [],
        metadata: {
            totalReferences: 0,
            buildTimestamp: new Date(),
            analysisDepth: 0
          }
        },
        recommendations: ['Fix validation errors and retry'],
        warnings: [],
        errors: [err.message],
        metadata: {
          validationMethod: 'error-fallback',
          rulesApplied: 0,
          dependencyDepth: 0,
          cyclicDependencies: [],
          orphanReferences: []
        }
      };
    }
  }

  protected async doShutdown(): Promise<void> {
    try {
      this.auditLogger.info('Shutting down Event Manager');
      
      // ✅ TIMER COORDINATION: Timer cleanup now handled automatically by TimerCoordinationService
      
      // Clear all data structures
      this._streams.clear();
      this._subscribers.clear();
      this._eventStore.clear();
      this._processingQueues.clear();
      
      // Clear analytics
      this._analytics.trends.length = 0;
      Object.keys(this._analytics.eventsByType).forEach(key => delete this._analytics.eventsByType[key]);
      
      this.auditLogger.info('Event Manager shutdown complete');
      
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.auditLogger.error('Error during shutdown', { error: err.message });
      throw err;
    }
  }

  // ============================================================================
  // PROTECTED UTILITY METHODS
  // ============================================================================

  /**
   * Create audit logger instance
   */
  protected createAuditLogger(): IRuleAuditLogger {
    return RuleAuditLoggerFactory.create(this._componentId);
  }

  // ============================================================================
  // ENTERPRISE EVENT MANAGEMENT FEATURES
  // ============================================================================

  /**
   * Publish event to specified stream
   */
  async publishEvent(streamId: string, event: Omit<IGovernanceEvent, 'id' | 'timestamp'>): Promise<string> {
    try {
      const fullEvent: IGovernanceEvent = {
        ...event,
        id: this.generateId(),
        timestamp: new Date()
      };

      this.auditLogger.info('Publishing event', { streamId, eventType: event.type, eventId: fullEvent.id });

      // Validate stream exists
      const stream = this._streams.get(streamId);
      if (!stream) {
        throw new StreamManagementError(`Stream not found: ${streamId}`, [streamId]);
      }

      if (!stream.isActive) {
        throw new StreamManagementError(`Stream is not active: ${streamId}`, [streamId]);
      }

      // Store event for sourcing
      this._eventStore.set(fullEvent.id, fullEvent);

      // Apply filters
      const filteredEvent = this._applyEventFilters(fullEvent, stream.filters);
      if (!filteredEvent) {
        this.auditLogger.debug('Event filtered out', { eventId: fullEvent.id });
        return fullEvent.id;
      }

      // Add to processing queue
      await this._enqueueEventForProcessing(streamId, filteredEvent);

      // Update metrics
      this._eventMetrics.totalEvents++;
      this._analytics.eventsByType[event.type] = (this._analytics.eventsByType[event.type] || 0) + 1;

      this.auditLogger.info('Event published successfully', { eventId: fullEvent.id, streamId });
      return fullEvent.id;

    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.auditLogger.error('Failed to publish event', { streamId, error: err.message });
      throw new EventProcessingError(`Event publishing failed: ${err.message}`, undefined, { streamId });
    }
  }

  /**
   * Subscribe to events in a stream with enhanced handler management
   * 🚨 PHASE 3.3: Enhanced with EventHandlerRegistry for proper lifecycle management
   */
  async subscribeToEvents(
    streamId: string,
    subscriber: Omit<IEventSubscriber, 'id'>,
    options: { autoStart?: boolean; clientId?: string } = {}
  ): Promise<string> {
    try {
      const { autoStart = true, clientId = 'governance-system' } = options;

      const fullSubscriber: IEventSubscriber = {
        ...subscriber,
        id: this.generateId()
      };

      this.auditLogger.info('Creating event subscription', {
        streamId,
        subscriberId: fullSubscriber.id,
        eventTypes: subscriber.eventTypes
      });

      // Validate stream exists
      const stream = this._streams.get(streamId);
      if (!stream) {
        throw new StreamManagementError(`Stream not found: ${streamId}`, [streamId]);
      }

      // 🚨 PHASE 3.3: Register handler with EventHandlerRegistry for proper lifecycle management
      for (const eventType of subscriber.eventTypes) {
        const handlerId = this._eventRegistry.registerHandler(
          clientId,
          `${streamId}:${eventType}`,
          subscriber.handler as any, // Type compatibility bridge
          {
            subscriberId: fullSubscriber.id,
            streamId,
            eventType,
            priority: subscriber.priority,
            registrationTime: new Date(),
            source: 'governance-rule-event-manager'
          }
        );

        // Store handler ID in subscriber metadata for cleanup
        if (!fullSubscriber.metadata) {
          fullSubscriber.metadata = {};
        }
        if (!fullSubscriber.metadata.handlerIds) {
          fullSubscriber.metadata.handlerIds = [];
        }
        (fullSubscriber.metadata.handlerIds as string[]).push(handlerId);
      }

      // Add subscriber to registry
      this._subscribers.set(fullSubscriber.id, fullSubscriber);

      // Add subscriber to stream
      stream.subscribers.push(fullSubscriber);

      // Update metrics
      this._eventMetrics.activeSubscribers++;

      if (autoStart && !fullSubscriber.isActive) {
        fullSubscriber.isActive = true;
      }

      this.auditLogger.info('Event subscription created with handler registry', {
        subscriberId: fullSubscriber.id,
        streamId,
        isActive: fullSubscriber.isActive,
        handlerCount: subscriber.eventTypes.length
      });

      return fullSubscriber.id;

    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.auditLogger.error('Failed to create subscription', { streamId, error: err.message });
      throw new EventProcessingError(`Subscription failed: ${err.message}`, undefined, { streamId });
    }
  }

  /**
   * Unsubscribe from events in a stream
   * 🚨 PHASE 3.3: NEW FUNCTIONALITY - Proper subscriber cleanup with EventHandlerRegistry
   */
  async unsubscribeFromEvents(subscriberId: string): Promise<boolean> {
    try {
      this.auditLogger.info('Unsubscribing from events', { subscriberId });

      // Find subscriber
      const subscriber = this._subscribers.get(subscriberId);
      if (!subscriber) {
        this.auditLogger.warn('Subscriber not found for unsubscription', { subscriberId });
        return false;
      }

      // 🚨 PHASE 3.3: Cleanup handlers using EventHandlerRegistry
      if (subscriber.metadata?.handlerIds) {
        for (const handlerId of subscriber.metadata.handlerIds) {
          const removed = this._eventRegistry.unregisterHandler(handlerId);
          if (!removed) {
            this.auditLogger.warn('Handler not found in registry during cleanup', {
              handlerId,
              subscriberId
            });
          }
        }
      }

      // Remove from all streams
      for (const stream of this._streams.values()) {
        const index = stream.subscribers.findIndex(sub => sub.id === subscriberId);
        if (index !== -1) {
          stream.subscribers.splice(index, 1);
          this.auditLogger.debug('Removed subscriber from stream', {
            subscriberId,
            streamId: stream.id
          });
        }
      }

      // Remove from subscriber registry
      this._subscribers.delete(subscriberId);

      // Update metrics
      this._eventMetrics.activeSubscribers = Math.max(0, this._eventMetrics.activeSubscribers - 1);

      this.auditLogger.info('Event subscription removed successfully', {
        subscriberId,
        handlersRemoved: subscriber.metadata?.handlerIds?.length || 0
      });

      return true;

    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.auditLogger.error('Failed to unsubscribe from events', { subscriberId, error: err.message });
      throw new EventProcessingError(`Unsubscription failed: ${err.message}`, undefined, { subscriberId });
    }
  }

  /**
   * Unsubscribe all subscribers for a specific client
   * 🚨 PHASE 3.3: NEW FUNCTIONALITY - Client-based cleanup for memory safety
   */
  async unsubscribeClientSubscribers(clientId: string): Promise<number> {
    try {
      this.auditLogger.info('Unsubscribing all client subscribers', { clientId });

      // Use EventHandlerRegistry for efficient client cleanup
      const removedHandlers = this._eventRegistry.unregisterClientHandlers(clientId);

      // Find and remove subscribers that belong to this client
      const subscribersToRemove: string[] = [];
      for (const [subscriberId, subscriber] of this._subscribers.entries()) {
        // Check if subscriber belongs to this client (based on handler metadata)
        if (subscriber.metadata?.handlerIds) {
          // If any handler was removed, this subscriber belongs to the client
          const hasRemovedHandlers = subscriber.metadata.handlerIds.some(handlerId =>
            !this._eventRegistry.getHandler(handlerId)
          );
          if (hasRemovedHandlers) {
            subscribersToRemove.push(subscriberId);
          }
        }
      }

      // Remove subscribers from streams and registry
      for (const subscriberId of subscribersToRemove) {
        await this.unsubscribeFromEvents(subscriberId);
      }

      this.auditLogger.info('Client subscribers unsubscribed successfully', {
        clientId,
        subscribersRemoved: subscribersToRemove.length,
        handlersRemoved: removedHandlers
      });

      return subscribersToRemove.length;

    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.auditLogger.error('Failed to unsubscribe client subscribers', { clientId, error: err.message });
      throw new EventProcessingError(`Client unsubscription failed: ${err.message}`, undefined, { clientId });
    }
  }

  /**
   * Process events in batch
   */
  async processEventBatch(streamId: string, batchSize: number = EVENT_PROCESSING_BATCH_SIZE): Promise<IEventProcessingResult> {
    const startTime = Date.now();

    try {
      this.auditLogger.info('Processing event batch', { streamId, batchSize });

      // Get events from queue
      const queue = this._processingQueues.get(streamId) || [];
      const batch = queue.splice(0, batchSize);

      if (batch.length === 0) {
          return {
          success: true,
          processedEvents: 0,
          failedEvents: 0,
          errors: [],
          warnings: [],
          metrics: {
            executionTime: Date.now() - startTime,
            throughput: 0,
            eventsByType: {},
            subscriberMetrics: {},
            streamMetrics: {}
          }
        };
      }

      // Process batch with subscribers
      const results = await this._processBatchWithSubscribers(streamId, batch);

      const executionTime = Date.now() - startTime;
      const throughput = batch.length / (executionTime / 1000);

      // Update analytics
      this._analytics.throughput = throughput;
      this._analytics.trends.push(throughput);
      if (this._analytics.trends.length > 100) {
        this._analytics.trends.shift();
      }

      this.auditLogger.info('Event batch processed', { 
        streamId, 
        processed: results.processedEvents, 
        failed: results.failedEvents,
        throughput: throughput.toFixed(2)
      });

      return {
        ...results,
        metrics: {
          ...results.metrics,
          executionTime,
          throughput
        }
      };

    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.auditLogger.error('Batch processing failed', { streamId, error: err.message });
      
      return {
        success: false,
        processedEvents: 0,
        failedEvents: 0,
        errors: [err.message],
        warnings: [],
        metrics: {
          executionTime: Date.now() - startTime,
          throughput: 0,
          eventsByType: {},
          subscriberMetrics: {},
          streamMetrics: {}
        }
      };
    }
  }

  /**
   * Replay events from event store
   */
  async replayEvents(streamId: string, options: IEventReplayOptions = {}): Promise<IEventProcessingResult> {
    try {
      this.auditLogger.info('Starting event replay', { streamId, options });

      const {
        fromTimestamp,
        toTimestamp,
        eventTypes,
        batchSize = EVENT_PROCESSING_BATCH_SIZE,
        maxEvents = 1000
      } = options;

      // Filter events for replay
      const eventsToReplay = Array.from(this._eventStore.values())
        .filter(event => {
          if (fromTimestamp && event.timestamp < fromTimestamp) return false;
          if (toTimestamp && event.timestamp > toTimestamp) return false;
          if (eventTypes && !eventTypes.includes(event.type)) return false;
          return true;
        })
        .sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime())
        .slice(0, maxEvents);

      this.auditLogger.info('Replaying filtered events', { 
        streamId, 
        totalEvents: eventsToReplay.length,
        batchSize 
      });

      // Process in batches
      let totalProcessed = 0;
      let totalFailed = 0;
      const errors: string[] = [];
      const warnings: string[] = [];

      for (let i = 0; i < eventsToReplay.length; i += batchSize) {
        const batch = eventsToReplay.slice(i, i + batchSize);
        const result = await this._processBatchWithSubscribers(streamId, batch);
        
        totalProcessed += result.processedEvents;
        totalFailed += result.failedEvents;
        errors.push(...result.errors);
        warnings.push(...result.warnings);
      }

      return {
        success: totalFailed === 0,
        processedEvents: totalProcessed,
        failedEvents: totalFailed,
        errors,
        warnings,
        metrics: {
          executionTime: 0,
          throughput: 0,
          eventsByType: {},
          subscriberMetrics: {},
          streamMetrics: {}
        }
      };

    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.auditLogger.error('Event replay failed', { streamId, error: err.message });
      throw new EventProcessingError(`Event replay failed: ${err.message}`, undefined, { streamId });
    }
  }

  /**
   * Create new event stream
   */
  createEventStream(stream: Omit<IEventStream, 'id' | 'created' | 'subscribers'>): string {
    try {
      const fullStream: IEventStream = {
        ...stream,
        id: this.generateId(),
        created: new Date(),
        subscribers: []
      };

      this.auditLogger.info('Creating event stream', { 
        streamId: fullStream.id, 
        name: stream.name,
        eventTypes: stream.eventTypes 
      });

      // Validate stream configuration
      this._validateStreamConfiguration(fullStream);

      // Register stream
      this._streams.set(fullStream.id, fullStream);

      // Initialize processing queue
      this._processingQueues.set(fullStream.id, []);

      // Update metrics
      this._eventMetrics.activeStreams++;

      this.auditLogger.info('Event stream created', { streamId: fullStream.id });
      return fullStream.id;

    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.auditLogger.error('Failed to create stream', { error: err.message });
      throw new StreamCreationError(`Stream creation failed: ${err.message}`, stream);
    }
  }

  /**
   * Get event management metrics with EventHandlerRegistry integration
   * 🚨 PHASE 3.3: Enhanced metrics with handler registry data
   */
  getEventMetrics(): any {
    const registryMetrics = this._eventRegistry.getMetrics();

    return {
      ...this._eventMetrics,
      streams: {
        total: this._streams.size,
        active: Array.from(this._streams.values()).filter(s => s.isActive).length
      },
      subscribers: {
        total: this._subscribers.size,
        active: Array.from(this._subscribers.values()).filter(s => s.isActive).length
      },
      eventStore: {
        totalEvents: this._eventStore.size
      },
      // 🚨 PHASE 3.3: EventHandlerRegistry metrics for enhanced monitoring
      handlerRegistry: {
        totalHandlers: registryMetrics.totalHandlers,
        handlersByType: registryMetrics.handlersByType,
        handlersByClient: registryMetrics.handlersByClient,
        orphanedHandlers: registryMetrics.orphanedHandlers,
        lastCleanup: registryMetrics.lastCleanup
      },
      analytics: this._analytics
    };
  }

  // ============================================================================
  // PRIVATE IMPLEMENTATION METHODS
  // ============================================================================

  private _initializeDefaultStreams(): void {
    // Governance rule events stream
    this.createEventStream({
      name: 'Governance Rule Events',
      eventTypes: ['rule.created', 'rule.updated', 'rule.deleted', 'rule.validated'],
      filters: [],
      isActive: true
    });

    // System events stream
    this.createEventStream({
      name: 'System Events',
      eventTypes: ['system.startup', 'system.shutdown', 'system.error', 'system.warning'],
      filters: [],
      isActive: true
    });
  }

  private async _validateEventConfiguration(): Promise<void> {
    let healthMetrics;
    try {
      healthMetrics = await (this._environmentCalculator as any).getSystemHealthMetrics?.();
    } catch (error) {
      console.warn('System health metrics not available:', error);
      // Fallback: use basic memory check
      const memoryUsage = process.memoryUsage();
      const memoryUsageMB = memoryUsage.heapUsed / 1024 / 1024;
      healthMetrics = {
        memory: { usage: memoryUsageMB > 200 ? 85 : 50 }, // Simple threshold check
        cpu: { cores: require('os').cpus().length }
      };
    }

    if (healthMetrics && healthMetrics.memory.usage > 80) {
      throw new ConfigurationError('Insufficient memory for event processing operations');
    }

    if (healthMetrics && healthMetrics.cpu.cores < 2) {
      this.auditLogger.warn('Low CPU core count may impact event processing performance');
    }
  }

  private async _initializeEventStreams(): Promise<void> {
    this.auditLogger.info('Initializing event streams', { 
      streamCount: this._streams.size 
    });
    
    // Validate all registered streams
    Array.from(this._streams.entries()).forEach(([id, stream]) => {
      try {
        this._validateStreamConfiguration(stream);
    } catch (error) {
        const err = error instanceof Error ? error : new Error(String(error));
        this.auditLogger.error('Stream validation failed', { streamId: id, error: err.message });
        throw new ConfigurationError(`Stream ${id} validation failed: ${err.message}`, id);
      }
    });
  }

  private _startRealTimeProcessing(): void {
    // ✅ TIMER COORDINATION: Timer management now handled by TimerCoordinationService
    const timerCoordinator = getTimerCoordinator();

    // Process each stream's queue every 100ms
    Array.from(this._streams.keys()).forEach(streamId => {
      timerCoordinator.createCoordinatedInterval(
        async () => {
          try {
            await this.processEventBatch(streamId, 10); // Small batch for real-time
          } catch (error) {
            const err = error instanceof Error ? error : new Error(String(error));
            this.auditLogger.error('Real-time processing error', { streamId, error: err.message });
          }
        },
        100,
        'GovernanceRuleEventManager',
        `realtime-processing-${streamId}`
      );
    });
  }

  private _startPerformanceMonitoring(): void {
    // ✅ TIMER COORDINATION: Timer management now handled by TimerCoordinationService
    const timerCoordinator = getTimerCoordinator();
    timerCoordinator.createCoordinatedInterval(
      () => {
        const memoryUsage = process.memoryUsage().heapUsed / 1024 / 1024; // MB

        // Log performance metrics
        this.auditLogger.debug('Performance metrics', {
          memoryUsage: `${memoryUsage.toFixed(2)}MB`,
          totalEvents: this._eventMetrics.totalEvents,
          throughput: this._analytics.throughput.toFixed(2),
          activeStreams: this._eventMetrics.activeStreams,
          activeSubscribers: this._eventMetrics.activeSubscribers
        });
      },
      30000, // Every 30 seconds
      'GovernanceRuleEventManager',
      'performance-monitoring'
    );
  }

  private _applyEventFilters(event: IGovernanceEvent, filters: IEventFilter[]): IGovernanceEvent | null {
    let processedEvent = event;

    for (const filter of filters) {
      const matches = filter.condition(processedEvent);
      
      if (filter.action === 'deny' && matches) {
        return null; // Event is filtered out
      }
      
      if (filter.action === 'transform' && matches && filter.transformer) {
        processedEvent = filter.transformer(processedEvent);
      }
    }

    return processedEvent;
  }

  private async _enqueueEventForProcessing(streamId: string, event: IGovernanceEvent): Promise<void> {
    const queue = this._processingQueues.get(streamId);
    if (!queue) {
      throw new StreamManagementError(`Processing queue not found for stream: ${streamId}`, [streamId]);
    }

    queue.push(event);

    // Prevent queue overflow
    const maxQueueSize = 10000;
    if (queue.length > maxQueueSize) {
      queue.shift(); // Remove oldest event
      this.auditLogger.warn('Event queue overflow, oldest event removed', { streamId, queueSize: queue.length });
    }
  }

  private async _processBatchWithSubscribers(
    streamId: string, 
    events: IGovernanceEvent[]
  ): Promise<IEventProcessingResult> {
    const stream = this._streams.get(streamId);
    if (!stream) {
      throw new StreamManagementError(`Stream not found: ${streamId}`, [streamId]);
    }

    let processedEvents = 0;
    let failedEvents = 0;
    const errors: string[] = [];
    const warnings: string[] = [];
    const eventsByType: Record<string, number> = {};
    const subscriberMetrics: Record<string, { processed: number; failed: number }> = {};

    // Initialize subscriber metrics
    for (const subscriber of stream.subscribers) {
      subscriberMetrics[subscriber.id] = { processed: 0, failed: 0 };
    }

    // Process each event with all relevant subscribers
    for (const event of events) {
      eventsByType[event.type] = (eventsByType[event.type] || 0) + 1;

      // Find subscribers interested in this event type
      const relevantSubscribers = stream.subscribers.filter(sub => 
        sub.isActive && sub.eventTypes.includes(event.type)
      );

      // Sort by priority
      relevantSubscribers.sort((a, b) => b.priority - a.priority);

      // Process with each subscriber
      for (const subscriber of relevantSubscribers) {
        try {
          await subscriber.handler(event);
          subscriberMetrics[subscriber.id].processed++;
          processedEvents++;
    } catch (error) {
          const err = error instanceof Error ? error : new Error(String(error));
          subscriberMetrics[subscriber.id].failed++;
          failedEvents++;
          errors.push(`Subscriber ${subscriber.id}: ${err.message}`);
          
          this.auditLogger.error('Subscriber processing failed', {
            subscriberId: subscriber.id,
            eventId: event.id,
            error: err.message
      });
    }
  }
    }

    return {
      success: failedEvents === 0,
      processedEvents,
      failedEvents,
      errors,
      warnings,
      metrics: {
        executionTime: 0,
        throughput: 0,
        eventsByType,
        subscriberMetrics,
        streamMetrics: {
          [streamId]: {
            events: events.length,
            active: stream.isActive
          }
        }
      }
    };
  }

  private _validateStreamConfiguration(stream: IEventStream): void {
    if (!stream.id || !stream.name) {
      throw new ConfigurationError('Stream must have id and name');
    }

    if (!stream.eventTypes || stream.eventTypes.length === 0) {
      throw new ConfigurationError('Stream must have at least one event type');
    }

    // Validate filters
    for (const filter of stream.filters) {
      if (!filter.id || !filter.condition) {
        throw new ConfigurationError(`Invalid filter configuration in stream ${stream.id}`);
      }
    }
  }

  private _updateEventMetrics(data: TTrackingData): void {
    this._eventMetrics.lastProcessedAt = new Date();
    
    if (data.metadata?.custom?.success) {
      this._eventMetrics.processedEvents++;
    } else {
      this._eventMetrics.failedEvents++;
    }
    
    if (data.metadata?.custom?.executionTime) {
      // Update average processing time
      const currentAvg = this._eventMetrics.averageProcessingTime;
      const newValue = data.metadata.custom.executionTime as number;
      this._eventMetrics.averageProcessingTime = (currentAvg + newValue) / 2;
    }
  }

  private _storeEventAnalyticsData(data: TTrackingData): void {
    // Store throughput trends
    if (data.metadata?.custom?.throughput) {
      this._analytics.trends.push(data.metadata.custom.throughput as number);
      
      // Keep only last 100 measurements
      if (this._analytics.trends.length > 100) {
        this._analytics.trends.shift();
      }
    }
    
    // Update event type counters
    if (data.metadata?.custom?.eventType) {
      const eventType = data.metadata.custom.eventType as string;
      this._analytics.eventsByType[eventType] = (this._analytics.eventsByType[eventType] || 0) + 1;
    }
  }

  private async _validateEventStreams(): Promise<TValidationResult> {
    const streamIds = Array.from(this._streams.keys());
    const errors: string[] = [];
    
    for (const id of streamIds) {
      const stream = this._streams.get(id)!;
      try {
        this._validateStreamConfiguration(stream);
      } catch (error) {
        const err = error instanceof Error ? error : new Error(String(error));
        errors.push(`Stream ${id}: ${err.message}`);
      }
    }

    return {
      validationId: this.generateId(),
      componentId: `${this._componentId}-streams`,
      timestamp: new Date(),
      executionTime: 0,
      status: errors.length === 0 ? 'valid' : 'invalid',
      overallScore: errors.length === 0 ? 100 : Math.max(0, 100 - (errors.length * 10)),
      checks: [],
      references: {
        componentId: this._componentId,
        internalReferences: [],
        externalReferences: [],
        circularReferences: [],
        missingReferences: [],
        redundantReferences: [],
        metadata: {
          totalReferences: 0,
          buildTimestamp: new Date(),
          analysisDepth: 0
        }
      },
      recommendations: ['Ensure all streams are properly configured'],
      warnings: [],
      errors,
      metadata: {
        validationMethod: 'stream-validation',
        rulesApplied: 1,
        dependencyDepth: 0,
        cyclicDependencies: [],
        orphanReferences: []
      }
    };
  }

  private async _validateEventSubscribers(): Promise<TValidationResult> {
    const subscriberIds = Array.from(this._subscribers.keys());
    const warnings: string[] = [];

    // Use subscriberIds for validation
    this.auditLogger.debug('Validating event subscribers', {
      totalSubscribers: subscriberIds.length
    });
    
    // Check for inactive subscribers
    const inactiveCount = Array.from(this._subscribers.values()).filter(s => !s.isActive).length;
    if (inactiveCount > 0) {
      warnings.push(`${inactiveCount} inactive subscribers detected`);
    }

    return {
      validationId: this.generateId(),
      componentId: `${this._componentId}-subscribers`,
      timestamp: new Date(),
      executionTime: 0,
      status: 'valid',
      overallScore: warnings.length === 0 ? 100 : 90,
      checks: [],
      references: {
        componentId: this._componentId,
        internalReferences: [],
        externalReferences: [],
        circularReferences: [],
        missingReferences: [],
        redundantReferences: [],
        metadata: {
          totalReferences: 0,
          buildTimestamp: new Date(),
          analysisDepth: 0
        }
      },
      recommendations: ['Monitor subscriber activity'],
      warnings,
      errors: [],
      metadata: {
        validationMethod: 'subscriber-validation',
        rulesApplied: 1,
        dependencyDepth: 0,
        cyclicDependencies: [],
        orphanReferences: []
      }
    };
  }

  private async _validateEventStore(): Promise<TValidationResult> {
    const warnings: string[] = [];
    
    if (this._eventStore.size > 50000) {
      warnings.push('Event store size is large, consider cleanup');
    }

    return {
      validationId: this.generateId(),
      componentId: `${this._componentId}-store`,
      timestamp: new Date(),
      executionTime: 0,
      status: 'valid',
      overallScore: warnings.length === 0 ? 100 : 90,
      checks: [],
      references: {
        componentId: this._componentId,
        internalReferences: [],
        externalReferences: [],
        circularReferences: [],
        missingReferences: [],
        redundantReferences: [],
        metadata: {
          totalReferences: 0,
          buildTimestamp: new Date(),
          analysisDepth: 0
        }
      },
      recommendations: ['Monitor event store size'],
      warnings,
      errors: [],
      metadata: {
        validationMethod: 'store-validation',
        rulesApplied: 1,
        dependencyDepth: 0,
        cyclicDependencies: [],
        orphanReferences: []
      }
    };
  }

  private async _validateProcessingQueues(): Promise<TValidationResult> {
    const warnings: string[] = [];
    
    Array.from(this._processingQueues.entries()).forEach(([streamId, queue]) => {
      if (queue.length > 1000) {
        warnings.push(`Processing queue for stream ${streamId} is large: ${queue.length} events`);
      }
    });

    return {
      validationId: this.generateId(),
      componentId: `${this._componentId}-queues`,
      timestamp: new Date(),
      executionTime: 0,
      status: 'valid',
      overallScore: warnings.length === 0 ? 100 : 90,
      checks: [],
      references: {
        componentId: this._componentId,
        internalReferences: [],
        externalReferences: [],
        circularReferences: [],
        missingReferences: [],
        redundantReferences: [],
        metadata: {
          totalReferences: 0,
          buildTimestamp: new Date(),
          analysisDepth: 0
        }
      },
      recommendations: ['Monitor queue sizes'],
      warnings,
      errors: [],
      metadata: {
        validationMethod: 'queue-validation',
        rulesApplied: 1,
        dependencyDepth: 0,
        cyclicDependencies: [],
        orphanReferences: []
      }
    };
  }

  private async _validateMemoryConstraints(): Promise<TValidationResult> {
    let memoryValidation;
    try {
      memoryValidation = await (this._environmentCalculator as any).validateMemoryConstraints?.();
    } catch (error) {
      console.warn('Memory constraint validation not available:', error);
      // Fallback: basic memory validation
      const memoryUsage = process.memoryUsage();
      const memoryUsageMB = memoryUsage.heapUsed / 1024 / 1024;
      memoryValidation = {
        valid: memoryUsageMB < 200, // 200MB threshold
        details: `Memory usage: ${memoryUsageMB.toFixed(2)}MB`
      };
    }

    return {
      validationId: this.generateId(),
      componentId: `${this._componentId}-memory`,
      timestamp: new Date(),
      executionTime: 0,
      status: memoryValidation?.valid ? 'valid' : 'invalid',
      overallScore: memoryValidation?.valid ? 100 : 50,
      checks: [],
      references: {
        componentId: this._componentId,
        internalReferences: [],
        externalReferences: [],
        circularReferences: [],
        missingReferences: [],
        redundantReferences: [],
        metadata: {
          totalReferences: 0,
          buildTimestamp: new Date(),
          analysisDepth: 0
        }
      },
      recommendations: memoryValidation.valid ? [] : ['Free up memory resources'],
      warnings: [],
      errors: memoryValidation.valid ? [] : [memoryValidation.message],
      metadata: {
        validationMethod: 'memory-validation',
        rulesApplied: 1,
        dependencyDepth: 0,
        cyclicDependencies: [],
        orphanReferences: []
      }
    };
  }
}