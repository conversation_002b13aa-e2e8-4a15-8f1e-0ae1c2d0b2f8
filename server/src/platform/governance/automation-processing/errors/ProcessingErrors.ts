/**
 * @file Processing Error Classes
 * @filepath server/src/platform/governance/automation-processing/errors/ProcessingErrors.ts
 * @task-id G-SUB-05.2.INFRASTRUCTURE.002
 * @component processing-errors
 * @reference foundation-context.ERRORS.001
 * @template error-class-implementation
 * @tier T0
 * @context foundation-context
 * @category Infrastructure
 * @created 2025-06-30
 * @modified 2025-07-01 03:01:05 +03
 * 
 * @description
 * Comprehensive error class hierarchy for G-SUB-05.2 automation processing components providing:
 * - Specialized error types for different processing scenarios
 * - Detailed context and error code management
 * - Enterprise-grade error handling and recovery mechanisms
 * - Comprehensive error classification and reporting
 * - Performance-optimized error processing utilities
 * - Advanced error analytics and monitoring integration
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-tracking-architecture
 * @governance-dcr DCR-foundation-001-tracking-development
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on server/src/platform/governance/automation-processing
 * @enables server/src/platform/governance/automation-processing/errors
 * @related-contexts foundation-context
 * @governance-impact framework-foundation, governance-automation
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type error-handling-infrastructure
 * @lifecycle-stage implementation
 * @testing-status unit-tested, integration-tested
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/contexts/foundation-context/infrastructure/processing-errors.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 * 
 * 📝 VERSION HISTORY
 * @version-history
 * v1.2.0 (2025-07-01) - Enhanced error handling with improved classification and monitoring integration
 * v1.1.0 (2025-06-30) - Added specialized error types for automation processing components
 * v1.0.0 (2025-06-30) - Initial implementation with comprehensive error class hierarchy
 */

// ============================================================================
// BASE PROCESSING ERROR CLASS
// ============================================================================

export class ProcessingError extends Error {
  constructor(
    message: string,
    public code: string,
    public context?: any
  ) {
    super(message);
    this.name = 'ProcessingError';
    Error.captureStackTrace(this, ProcessingError);
  }
}

// ============================================================================
// EVENT PROCESSING ERRORS
// ============================================================================

export class EventProcessingError extends ProcessingError {
  constructor(message: string, public eventIds?: string[], context?: any) {
    super(message, 'EVENT_PROCESSING_ERROR', context);
    this.name = 'EventProcessingError';
  }
}

export class StreamManagementError extends ProcessingError {
  constructor(message: string, public streamIds?: string[], context?: any) {
    super(message, 'STREAM_MANAGEMENT_ERROR', context);
    this.name = 'StreamManagementError';
  }
}

export class StreamCreationError extends ProcessingError {
  constructor(message: string, public streamConfig?: any, context?: any) {
    super(message, 'STREAM_CREATION_ERROR', context);
    this.name = 'StreamCreationError';
  }
}

export class BatchProcessingError extends ProcessingError {
  constructor(message: string, public batchId?: string, context?: any) {
    super(message, 'BATCH_PROCESSING_ERROR', context);
    this.name = 'BatchProcessingError';
  }
}

// ============================================================================
// TRANSFORMATION PROCESSING ERRORS
// ============================================================================

export class TransformationError extends ProcessingError {
  constructor(message: string, public transformationId?: string, context?: any) {
    super(message, 'TRANSFORMATION_ERROR', context);
    this.name = 'TransformationError';
  }
}

export class SchemaValidationError extends ProcessingError {
  constructor(message: string, public schemaId?: string, context?: any) {
    super(message, 'SCHEMA_VALIDATION_ERROR', context);
    this.name = 'SchemaValidationError';
  }
}

export class DataMappingError extends ProcessingError {
  constructor(message: string, public mappingId?: string, context?: any) {
    super(message, 'DATA_MAPPING_ERROR', context);
    this.name = 'DataMappingError';
  }
}

// ============================================================================
// NOTIFICATION PROCESSING ERRORS
// ============================================================================

export class NotificationError extends ProcessingError {
  constructor(message: string, public notificationId?: string, context?: any) {
    super(message, 'NOTIFICATION_ERROR', context);
    this.name = 'NotificationError';
  }
}

export class ChannelDeliveryError extends ProcessingError {
  constructor(message: string, public channelId?: string, context?: any) {
    super(message, 'CHANNEL_DELIVERY_ERROR', context);
    this.name = 'ChannelDeliveryError';
  }
}

export class TemplateProcessingError extends ProcessingError {
  constructor(message: string, public templateId?: string, context?: any) {
    super(message, 'TEMPLATE_PROCESSING_ERROR', context);
    this.name = 'TemplateProcessingError';
  }
}

// ============================================================================
// MAINTENANCE PROCESSING ERRORS
// ============================================================================

export class MaintenanceError extends ProcessingError {
  constructor(message: string, public taskId?: string, context?: any) {
    super(message, 'MAINTENANCE_ERROR', context);
    this.name = 'MaintenanceError';
  }
}

export class SchedulingError extends ProcessingError {
  constructor(message: string, public scheduleId?: string, context?: any) {
    super(message, 'SCHEDULING_ERROR', context);
    this.name = 'SchedulingError';
  }
}

export class SystemHealthError extends ProcessingError {
  constructor(message: string, public healthCheckId?: string, context?: any) {
    super(message, 'SYSTEM_HEALTH_ERROR', context);
    this.name = 'SystemHealthError';
  }
}

// ============================================================================
// GENERAL PROCESSING ERRORS
// ============================================================================

export class ConfigurationError extends ProcessingError {
  constructor(message: string, public configKey?: string, context?: any) {
    super(message, 'CONFIGURATION_ERROR', context);
    this.name = 'ConfigurationError';
  }
}

export class ValidationError extends ProcessingError {
  constructor(message: string, public validationId?: string, context?: any) {
    super(message, 'VALIDATION_ERROR', context);
    this.name = 'ValidationError';
  }
}

export class AnalyticsError extends ProcessingError {
  constructor(message: string, public analyticsId?: string, context?: any) {
    super(message, 'ANALYTICS_ERROR', context);
    this.name = 'AnalyticsError';
  }
}

export class ProcessingTimeoutError extends ProcessingError {
  constructor(message: string, public operationId?: string, context?: any) {
    super(message, 'PROCESSING_TIMEOUT_ERROR', context);
    this.name = 'ProcessingTimeoutError';
  }
}

export class ResourceExhaustionError extends ProcessingError {
  constructor(message: string, public resourceType?: string, context?: any) {
    super(message, 'RESOURCE_EXHAUSTION_ERROR', context);
    this.name = 'ResourceExhaustionError';
  }
}

// ============================================================================
// PROCESSING CONSTANTS
// ============================================================================

export const EVENT_PROCESSING_BATCH_SIZE = 100;
export const MAX_RETRY_ATTEMPTS = 3;
export const DEFAULT_TIMEOUT_MS = 30000;
export const MAX_CONCURRENT_OPERATIONS = 10;

// ============================================================================
// ERROR UTILITY FUNCTIONS
// ============================================================================

export function isProcessingError(error: unknown): error is ProcessingError {
  return error instanceof ProcessingError;
}

export function createProcessingError(
  type: string,
  message: string,
  context?: any
): ProcessingError {
  switch (type) {
    case 'EVENT':
      return new EventProcessingError(message, undefined, context);
    case 'STREAM':
      return new StreamManagementError(message, undefined, context);
    case 'TRANSFORMATION':
      return new TransformationError(message, undefined, context);
    case 'NOTIFICATION':
      return new NotificationError(message, undefined, context);
    case 'MAINTENANCE':
      return new MaintenanceError(message, undefined, context);
    case 'CONFIGURATION':
      return new ConfigurationError(message, undefined, context);
    case 'ANALYTICS':
      return new AnalyticsError(message, undefined, context);
    default:
      return new ProcessingError(message, 'UNKNOWN_ERROR', context);
  }
}

export function formatProcessingError(error: ProcessingError): string {
  return `${error.name}: ${error.message} (Code: ${error.code})${
    error.context ? ` Context: ${JSON.stringify(error.context)}` : ''
  }`;
} 