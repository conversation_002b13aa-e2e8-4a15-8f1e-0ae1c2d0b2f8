/**
 * ============================================================================
 * Governance Rule Report Scheduler Tests - Phase 6 Implementation
 * Purpose: Comprehensive test suite for Rule Report Scheduler
 * Author: OA Framework Team
 * Created: 2025-01-27 11:23:37 EST
 * ============================================================================
 */

import { 
  GovernanceRuleReportScheduler,
  IReportScheduler
} from '../GovernanceRuleReportScheduler';

import { 
  TReportSchedule,
  TRecurringReportSchedule,
  TBatchReportSchedule,
  TReportType,
  TSchedulePriority,
  TScheduleStatus,
  TScheduleDefinition,
  TImmediateReport,
  TReportFormat
} from '../../../../../../shared/src/types/platform/governance/automation-processing-types';

import { TValidationResult } from '../../../../../../shared/src/types/platform/tracking/core/tracking-data-types';

// ============================================================================
// TEST SETUP
// ============================================================================

describe('GovernanceRuleReportScheduler', () => {
  let scheduler: GovernanceRuleReportScheduler;
  let mockSchedule: TReportSchedule;
  let mockRecurringSchedule: TRecurringReportSchedule;
  let mockBatchSchedule: TBatchReportSchedule;

  beforeEach(async () => {
    scheduler = new GovernanceRuleReportScheduler();
    await scheduler.initialize();

    // Mock data setup
    mockSchedule = createMockReportSchedule();
    mockRecurringSchedule = createMockRecurringReportSchedule();
    mockBatchSchedule = createMockBatchReportSchedule();
  });

  afterEach(async () => {
    if (scheduler) {
      await scheduler.shutdown();
    }
  });

  // ============================================================================
  // BASIC FUNCTIONALITY TESTS
  // ============================================================================

  describe('Basic Functionality', () => {
    
    test('should initialize successfully', async () => {
      const newScheduler = new GovernanceRuleReportScheduler();
      await expect(newScheduler.initialize()).resolves.not.toThrow();
      await newScheduler.shutdown();
    });

    test('should have correct service properties', () => {
      expect(scheduler.id).toBe('governance-rule-report-scheduler');
      expect(scheduler.name).toBe('Governance Rule Report Scheduler');
      expect(scheduler.version).toBe('1.0.0');
      expect(scheduler.category).toBe('reporting-infrastructure');
    });

    test('should validate successfully after initialization', async () => {
      const validation = await scheduler.validate();
      expect(validation.status).toBe('valid');
      expect(validation.errors).toHaveLength(0);
    });

    test('should shutdown gracefully', async () => {
      await expect(scheduler.shutdown()).resolves.not.toThrow();
    });
  });

  // ============================================================================
  // SCHEDULE REPORT TESTS
  // ============================================================================

  describe('Schedule Report', () => {
    
    test('should schedule a report successfully', async () => {
      const result = await scheduler.scheduleReport(mockSchedule);
      
      expect(result).toBeDefined();
      expect(result.scheduleId).toBe(mockSchedule.scheduleId);
      expect(result.reportId).toBe(mockSchedule.reportId);
      expect(result.status).toBe('scheduled');
      expect(result.scheduledTime).toBeInstanceOf(Date);
      expect(result.metadata).toBeDefined();
    });

    test('should handle schedule conflicts', async () => {
      // Schedule first report
      await scheduler.scheduleReport(mockSchedule);
      
      // Try to schedule conflicting report
      const conflictingSchedule = { 
        ...mockSchedule, 
        scheduleId: 'different-id-same-time' 
      };
      
      const result = await scheduler.scheduleReport(conflictingSchedule);
      expect(result).toBeDefined();
      expect(result.conflicts).toBeDefined();
    });

    test('should validate schedule configuration', async () => {
      const invalidSchedule = { 
        ...mockSchedule, 
        scheduleId: '' // Invalid empty ID
      };
      
      await expect(scheduler.scheduleReport(invalidSchedule))
        .rejects.toThrow('Invalid schedule configuration');
    });

    test('should update metrics on schedule creation', async () => {
      const initialAnalytics = await scheduler.getSchedulingAnalytics();
      const initialCount = initialAnalytics.overview.totalSchedules;
      
      await scheduler.scheduleReport(mockSchedule);
      
      const updatedAnalytics = await scheduler.getSchedulingAnalytics();
      expect(updatedAnalytics.overview.totalSchedules).toBe(initialCount + 1);
    });
  });

  // ============================================================================
  // RECURRING SCHEDULE TESTS
  // ============================================================================

  describe('Recurring Schedule Report', () => {
    
    test('should schedule recurring report successfully', async () => {
      const result = await scheduler.scheduleRecurringReport(mockRecurringSchedule);
      
      expect(result).toBeDefined();
      expect(result.scheduleId).toBe(mockRecurringSchedule.scheduleId);
      expect(result.reportId).toBe(mockRecurringSchedule.reportId);
      expect(result.status).toBe('scheduled');
      expect(result.nextExecution).toBeInstanceOf(Date);
    });

    test('should validate recurring pattern', async () => {
      const invalidRecurring = { 
        ...mockRecurringSchedule,
        recurrencePattern: {
          type: 'cron' as const,
          expression: '',
          timezone: 'UTC'
        }
      };
      
      await expect(scheduler.scheduleRecurringReport(invalidRecurring))
        .rejects.toThrow('Invalid recurring schedule');
    });

    test('should calculate execution schedule', async () => {
      const result = await scheduler.scheduleRecurringReport(mockRecurringSchedule);
      
      expect(result.totalExecutions).toBeGreaterThan(0);
      expect(result.estimatedResource).toBeDefined();
    });
  });

  // ============================================================================
  // BATCH SCHEDULE TESTS
  // ============================================================================

  describe('Batch Schedule Reports', () => {
    
    test('should schedule batch reports successfully', async () => {
      const result = await scheduler.scheduleBatchReports(mockBatchSchedule);
      
      expect(result).toBeDefined();
      expect(result.batchId).toBe(mockBatchSchedule.batchId);
      expect(result.status).toBe('scheduled');
      expect(result.reportCount).toBe(mockBatchSchedule.reports.length);
    });

    test('should optimize batch execution order', async () => {
      const result = await scheduler.scheduleBatchReports(mockBatchSchedule);
      
      expect(result.executionPlan).toBeDefined();
      expect(result.estimatedDuration).toBeGreaterThan(0);
    });

    test('should handle parallel execution settings', async () => {
      const parallelBatch = { 
        ...mockBatchSchedule, 
        parallelExecution: true,
        maxConcurrency: 5 
      };
      
      const result = await scheduler.scheduleBatchReports(parallelBatch);
      expect(result.metadata.parallelExecution).toBe(true);
    });
  });

  // ============================================================================
  // SCHEDULE MANAGEMENT TESTS
  // ============================================================================

  describe('Schedule Management', () => {
    
    test('should cancel scheduled report', async () => {
      await scheduler.scheduleReport(mockSchedule);
      
      const result = await scheduler.cancelScheduledReport(mockSchedule.scheduleId);
      
      expect(result.cancelled).toBe(true);
      expect(result.scheduleId).toBe(mockSchedule.scheduleId);
    });

    test('should handle cancel non-existent schedule', async () => {
      await expect(scheduler.cancelScheduledReport('non-existent-id'))
        .rejects.toThrow('Schedule not found');
    });

    test('should modify existing schedule', async () => {
      await scheduler.scheduleReport(mockSchedule);
      
      const modifications = { 
        priority: 'high' as TSchedulePriority,
        timeout: 3600000 
      };
      
      const result = await scheduler.modifySchedule(mockSchedule.scheduleId, modifications);
      
      expect(result.modified).toBe(true);
      expect(result.scheduleId).toBe(mockSchedule.scheduleId);
    });

    test('should get schedule status', async () => {
      await scheduler.scheduleReport(mockSchedule);
      
      const status = await scheduler.getScheduleStatus(mockSchedule.scheduleId);
      
      expect(status.scheduleId).toBe(mockSchedule.scheduleId);
      expect(status.schedule).toBeDefined();
      expect(status.status).toBe('scheduled');
    });
  });

  // ============================================================================
  // IMMEDIATE EXECUTION TESTS
  // ============================================================================

  describe('Immediate Execution', () => {
    
    test('should execute immediate report', async () => {
      const immediateReport: TImmediateReport = {
        reportId: 'immediate-001',
        reportType: 'governance' as TReportType,
        format: 'pdf' as TReportFormat,
        priority: 'urgent' as TSchedulePriority
      };
      
      const result = await scheduler.executeImmediateReport(immediateReport);
      
      expect(result.executionId).toBeDefined();
      expect(result.reportId).toBe(immediateReport.reportId);
      expect(result.status).toBe('completed');
    });

    test('should validate immediate report request', async () => {
      const invalidReport: TImmediateReport = {
        reportId: '',
        reportType: 'governance' as TReportType,
        priority: 'normal' as TSchedulePriority
      };
      
      await expect(scheduler.executeImmediateReport(invalidReport))
        .rejects.toThrow('Invalid immediate report');
    });
  });

  // ============================================================================
  // ANALYTICS TESTS
  // ============================================================================

  describe('Analytics', () => {
    
    test('should provide scheduling analytics', async () => {
      // Create some schedules
      await scheduler.scheduleReport(mockSchedule);
      await scheduler.scheduleRecurringReport(mockRecurringSchedule);
      
      const analytics = await scheduler.getSchedulingAnalytics();
      
      expect(analytics.overview).toBeDefined();
      expect(analytics.performance).toBeDefined();
      expect(analytics.scheduleDistribution).toBeDefined();
      expect(analytics.optimization).toBeDefined();
      expect(analytics.forecasting).toBeDefined();
    });

    test('should track performance metrics', async () => {
      const analytics = await scheduler.getSchedulingAnalytics();
      
      expect(analytics.performance.throughput).toBeGreaterThanOrEqual(0);
      expect(analytics.performance.resourceUtilization).toBeDefined();
      expect(analytics.performance.performanceScore).toBeGreaterThanOrEqual(0);
      expect(analytics.performance.reliabilityScore).toBeGreaterThanOrEqual(0);
    });
  });

  // ============================================================================
  // OPTIMIZATION TESTS
  // ============================================================================

  describe('Optimization', () => {
    
    test('should optimize schedules', async () => {
      // Create multiple schedules
      await scheduler.scheduleReport(mockSchedule);
      await scheduler.scheduleRecurringReport(mockRecurringSchedule);
      await scheduler.scheduleBatchReports(mockBatchSchedule);
      
      const result = await scheduler.optimizeSchedules();
      
      expect(result.optimizationId).toBeDefined();
      expect(result.timestamp).toBeInstanceOf(Date);
      expect(result.optimizationType).toBe('performance');
      expect(result.schedulesOptimized).toBeGreaterThanOrEqual(0);
    });

    test('should provide optimization recommendations', async () => {
      const result = await scheduler.optimizeSchedules();
      
      expect(result.recommendations).toBeDefined();
      expect(Array.isArray(result.recommendations)).toBe(true);
    });

    test('should calculate optimization benefits', async () => {
      const result = await scheduler.optimizeSchedules();
      
      expect(result.resourceSavings).toBeDefined();
      expect(result.performanceImprovements).toBeDefined();
      expect(result.estimatedBenefits).toBeDefined();
    });
  });

  // ============================================================================
  // ERROR HANDLING TESTS
  // ============================================================================

  describe('Error Handling', () => {
    
    test('should handle initialization errors gracefully', async () => {
      const errorScheduler = new GovernanceRuleReportScheduler();
      
      // Force error during initialization
      jest.spyOn(errorScheduler as any, '_initializeScheduler')
        .mockRejectedValue(new Error('Init failed'));
      
      await expect(errorScheduler.initialize()).rejects.toThrow('Init failed');
    });

    test('should handle validation errors', async () => {
      const invalidSchedule = {
        ...mockSchedule,
        reportType: 'invalid-type' as any
      };
      
      await expect(scheduler.scheduleReport(invalidSchedule))
        .rejects.toThrow();
    });

    test('should handle resource exhaustion', async () => {
      // Mock resource exhaustion
      jest.spyOn(scheduler as any, '_checkImmediateResourceAvailability')
        .mockResolvedValue({ available: false, reason: 'Resource exhausted' });
      
      const immediateReport: TImmediateReport = {
        reportId: 'resource-test',
        reportType: 'governance' as TReportType,
        priority: 'normal' as TSchedulePriority
      };
      
      await expect(scheduler.executeImmediateReport(immediateReport))
        .rejects.toThrow('Insufficient resources');
    });
  });

  // ============================================================================
  // PERFORMANCE TESTS
  // ============================================================================

  describe('Performance', () => {
    
    test('should handle multiple concurrent schedules', async () => {
      const schedulePromises: Promise<any>[] = [];
      
      for (let i = 0; i < 10; i++) {
        const schedule = {
          ...mockSchedule,
          scheduleId: `concurrent-${i}`,
          reportId: `report-${i}`
        };
        schedulePromises.push(scheduler.scheduleReport(schedule));
      }
      
      const results = await Promise.all(schedulePromises);
      expect(results).toHaveLength(10);
      results.forEach(result => {
        expect(result.status).toBe('scheduled');
      });
    });

    test('should maintain performance under load', async () => {
      const startTime = Date.now();
      
      // Create multiple schedules rapidly
      for (let i = 0; i < 50; i++) {
        const schedule = {
          ...mockSchedule,
          scheduleId: `load-test-${i}`,
          reportId: `load-report-${i}`
        };
        await scheduler.scheduleReport(schedule);
      }
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Should complete within reasonable time (adjust threshold as needed)
      expect(duration).toBeLessThan(5000); // 5 seconds
    });
  });

  // ============================================================================
  // INTEGRATION TESTS
  // ============================================================================

  describe('Integration', () => {
    
    test('should integrate with base tracking service', async () => {
      expect(scheduler).toBeInstanceOf(GovernanceRuleReportScheduler);
      expect(typeof scheduler.track).toBe('function');
      expect(typeof scheduler.validate).toBe('function');
      expect(typeof scheduler.getMetrics).toBe('function');
    });

    test('should track operations for monitoring', async () => {
      const initialMetrics = await scheduler.getMetrics();
      
      await scheduler.scheduleReport(mockSchedule);
      
      const updatedMetrics = await scheduler.getMetrics();
      expect(updatedMetrics).toBeDefined();
      // Metrics should have been updated
    });
  });
});

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

function createMockReportSchedule(): TReportSchedule {
  return {
    scheduleId: 'test-schedule-001',
    reportId: 'test-report-001',
    name: 'Test Governance Report',
    description: 'Test report for unit testing',
    reportType: 'governance',
    schedule: createMockScheduleDefinition(),
    outputConfig: {
      format: 'pdf',
      compression: 'none',
      encryption: { enabled: false },
      watermark: false,
      fileNamingPattern: 'report-{date}-{id}',
      outputDirectory: '/tmp/reports',
      retention: { days: 30, action: 'delete' },
      templateId: undefined,
      customFields: {}
    },
    deliveryConfig: {
      method: 'email',
      recipients: [{ email: '<EMAIL>', name: 'Test User' }],
      notificationConfig: { enabled: true },
      deliveryRetries: 3,
      deliveryTimeout: 30000
    },
    priority: 'normal',
    constraints: [],
    dependencies: [],
    retryPolicy: { 
      maxRetries: 3, 
      retryDelay: 5000,
      backoffMultiplier: 2,
      maxRetryDelay: 30000
    },
    timeout: 1800000,
    timezone: 'UTC',
    tags: ['test', 'governance'],
    metadata: { testRun: true },
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: 'test-user',
    status: 'scheduled'
  };
}

function createMockRecurringReportSchedule(): TRecurringReportSchedule {
  const baseSchedule = createMockReportSchedule();
  return {
    ...baseSchedule,
    scheduleId: 'test-recurring-001',
    reportId: 'test-recurring-report-001',
    recurrencePattern: {
      type: 'cron',
      expression: '0 9 * * 1', // Every Monday at 9 AM
      timezone: 'UTC'
    },
    endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
    maxExecutions: 52, // Weekly for a year
    executionCount: 0,
    nextExecution: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
    skipHolidays: true,
    businessDaysOnly: false
  };
}

function createMockBatchReportSchedule(): TBatchReportSchedule {
  return {
    batchId: 'test-batch-001',
    name: 'Test Batch Reports',
    description: 'Batch of test reports',
    reports: [
      createMockReportSchedule(),
      { ...createMockReportSchedule(), scheduleId: 'test-schedule-002', reportId: 'test-report-002' },
      { ...createMockReportSchedule(), scheduleId: 'test-schedule-003', reportId: 'test-report-003' }
    ],
    batchSchedule: createMockScheduleDefinition(),
    executionMode: 'parallel',
    parallelExecution: true,
    maxConcurrency: 3,
    failureHandling: { 
      continueOnFailure: true, 
      maxFailures: 1,
      notifyOnFailure: true
    },
    consolidatedOutput: true,
    consolidationConfig: { 
      format: 'zip', 
      includeIndex: true,
      summaryReport: true
    },
    priority: 'normal',
    createdAt: new Date(),
    updatedAt: new Date(),
    status: 'scheduled'
  };
}

function createMockScheduleDefinition(): TScheduleDefinition {
  return {
    type: 'cron',
    cronExpression: '0 8 * * *', // Daily at 8 AM
    timezone: 'UTC',
    businessHoursOnly: false,
    excludeHolidays: false
  };
} 