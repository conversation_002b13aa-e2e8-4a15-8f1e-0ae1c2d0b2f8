/**
 * ============================================================================
 * AI CONTEXT: Rule Dashboard Generator Tests - Comprehensive test suite
 * Purpose: Unit and integration tests for the Rule Dashboard Generator component
 * Complexity: Complex - Comprehensive testing of dashboard generation functionality
 * AI Navigation: 5 logical sections, testing domain
 * ============================================================================
 */

import { GovernanceRuleDashboardGenerator, TDashboardGeneratorConfig, TTimeRange, TComplianceScope, TExecutiveLevel, TDashboardTemplate, TExportFormat } from '../GovernanceRuleDashboardGenerator';
import { TDashboardData } from '../../../../../../shared/src/types/tracking/tracking-management-types';

// ============================================================================
// TEST SETUP AND MOCKS
// ============================================================================

// Mock dependencies
jest.mock('../../automation-processing/factories/RuleAuditLoggerFactory');

describe('GovernanceRuleDashboardGenerator', () => {
  let dashboardGenerator: GovernanceRuleDashboardGenerator;
  let mockConfig: TDashboardGeneratorConfig;
  let mockTimeRange: TTimeRange;
  let mockComplianceScope: TComplianceScope;
  let mockDashboardTemplate: TDashboardTemplate;

  beforeEach(async () => {
    // Initialize dashboard generator
    dashboardGenerator = new GovernanceRuleDashboardGenerator();
    await dashboardGenerator.initialize();

    // Setup test data
    mockConfig = {
      dashboardType: 'governance',
      title: 'Test Governance Dashboard',
      description: 'Test dashboard for governance monitoring',
      refreshInterval: 300000,
      autoRefresh: true,
      theme: 'light',
      layout: 'grid',
      filters: [],
      widgets: [],
      permissions: {
        view: ['admin', 'user'],
        edit: ['admin'],
        delete: ['admin'],
        share: ['admin'],
        export: ['admin', 'user']
      },
      metadata: { testMode: true }
    };

    mockTimeRange = {
      startTime: new Date(Date.now() - 24 * 60 * 60 * 1000),
      endTime: new Date(),
      granularity: 'hour'
    };

    mockComplianceScope = {
      frameworks: ['SOX', 'GDPR'],
      ruleCategories: ['security', 'compliance'],
      severityLevels: ['high', 'critical'],
      timeRange: mockTimeRange
    };

    mockDashboardTemplate = {
      templateId: 'test-template',
      name: 'Test Template',
      description: 'Test dashboard template',
      category: 'testing',
      widgets: [],
      defaultFilters: [],
      permissions: mockConfig.permissions,
      variables: []
    };
  });

  afterEach(async () => {
    if (dashboardGenerator) {
      await dashboardGenerator.shutdown();
    }
  });

  // ============================================================================
  // INITIALIZATION TESTS
  // ============================================================================

  describe('Initialization', () => {
    test('should initialize successfully', async () => {
      const newGenerator = new GovernanceRuleDashboardGenerator();
      await expect(newGenerator.initialize()).resolves.not.toThrow();
      expect(newGenerator.isReady()).toBe(true);
      await newGenerator.shutdown();
    });

    test('should have correct component identification', () => {
      expect(dashboardGenerator.id).toBe('governance-rule-dashboard-generator');
      expect(dashboardGenerator.authority).toBe('President & CEO, E.Z. Consultancy');
    });

    test('should validate successfully after initialization', async () => {
      const validation = await dashboardGenerator.validate();
      expect(validation.status).toBe('valid');
      expect(validation.overallScore).toBeGreaterThan(0);
    });
  });

  // ============================================================================
  // GOVERNANCE DASHBOARD GENERATION TESTS
  // ============================================================================

  describe('Governance Dashboard Generation', () => {
    test('should generate governance dashboard with default configuration', async () => {
      const dashboard = await dashboardGenerator.generateGovernanceDashboard(mockConfig);

      expect(dashboard).toBeDefined();
      expect(dashboard.id).toBeDefined();
      expect(dashboard.title).toBe(mockConfig.title);
      expect(dashboard.description).toBe(mockConfig.description);
      expect(dashboard.config.layout).toBe(mockConfig.layout);
      expect(dashboard.config.theme).toBe(mockConfig.theme);
      expect(dashboard.config.refreshInterval).toBe(mockConfig.refreshInterval);
      expect(dashboard.config.autoRefresh).toBe(mockConfig.autoRefresh);
      expect(dashboard.widgets).toBeDefined();
      expect(Array.isArray(dashboard.widgets)).toBe(true);
      expect(dashboard.filters).toBeDefined();
      expect(dashboard.metadata).toBeDefined();
    });

    test('should generate governance dashboard with custom widgets', async () => {
      const configWithWidgets = {
        ...mockConfig,
        widgets: [
          {
            type: 'chart' as const,
            title: 'Custom Chart',
            dataSource: 'custom-metrics',
            visualization: { chartType: 'line' as const },
            position: { x: 0, y: 0, width: 6, height: 4 },
            updateInterval: 60000,
            filters: []
          }
        ]
      };

      const dashboard = await dashboardGenerator.generateGovernanceDashboard(configWithWidgets);
      expect(dashboard.widgets.length).toBeGreaterThan(0);
    });

    test('should generate governance dashboard with filters', async () => {
      const configWithFilters = {
        ...mockConfig,
        filters: [
          {
            id: 'test-filter',
            name: 'Test Filter',
            type: 'select' as const,
            value: 'all',
            config: {}
          }
        ]
      };

      const dashboard = await dashboardGenerator.generateGovernanceDashboard(configWithFilters);
      expect(dashboard.filters.length).toBeGreaterThan(0);
      expect(dashboard.filters[0].name).toBe('Test Filter');
    });

    test('should handle errors gracefully during governance dashboard generation', async () => {
      const invalidConfig = {
        ...mockConfig,
        title: '' // Invalid empty title
      };

      // The implementation should handle this gracefully or validate the config
      await expect(dashboardGenerator.generateGovernanceDashboard(invalidConfig))
        .resolves.toBeDefined();
    });
  });

  // ============================================================================
  // PERFORMANCE DASHBOARD GENERATION TESTS
  // ============================================================================

  describe('Performance Dashboard Generation', () => {
    test('should generate rule performance dashboard', async () => {
      const ruleIds = ['rule-001', 'rule-002', 'rule-003'];
      const dashboard = await dashboardGenerator.generateRulePerformanceDashboard(ruleIds, mockTimeRange);

      expect(dashboard).toBeDefined();
      expect(dashboard.id).toBeDefined();
      expect(dashboard.title).toBe('Rule Performance Dashboard');
      expect(dashboard.description).toContain(ruleIds.length.toString());
      expect(dashboard.config.layout).toBe('grid');
      expect(dashboard.config.refreshInterval).toBe(60000); // 1 minute for performance data
      expect(dashboard.widgets).toBeDefined();
      expect(dashboard.filters).toBeDefined();
    });

    test('should generate performance dashboard with correct time range filters', async () => {
      const ruleIds = ['rule-001'];
      const dashboard = await dashboardGenerator.generateRulePerformanceDashboard(ruleIds, mockTimeRange);

      const timeRangeFilter = dashboard.filters.find(f => f.id === 'time-range');
      expect(timeRangeFilter).toBeDefined();
      expect(timeRangeFilter?.value.start).toEqual(mockTimeRange.startTime);
      expect(timeRangeFilter?.value.end).toEqual(mockTimeRange.endTime);
    });

    test('should handle empty rule IDs array', async () => {
      const dashboard = await dashboardGenerator.generateRulePerformanceDashboard([], mockTimeRange);
      expect(dashboard).toBeDefined();
      expect(dashboard.description).toContain('0');
    });

    test('should handle large number of rule IDs', async () => {
      const ruleIds = Array.from({ length: 100 }, (_, i) => `rule-${i.toString().padStart(3, '0')}`);
      const dashboard = await dashboardGenerator.generateRulePerformanceDashboard(ruleIds, mockTimeRange);
      
      expect(dashboard).toBeDefined();
      expect(dashboard.description).toContain('100');
    });
  });

  // ============================================================================
  // COMPLIANCE DASHBOARD GENERATION TESTS
  // ============================================================================

  describe('Compliance Dashboard Generation', () => {
    test('should generate compliance dashboard', async () => {
      const dashboard = await dashboardGenerator.generateComplianceDashboard(mockComplianceScope);

      expect(dashboard).toBeDefined();
      expect(dashboard.id).toBeDefined();
      expect(dashboard.title).toBe('Compliance Monitoring Dashboard');
      expect(dashboard.description).toBe('Comprehensive compliance status and violation tracking');
      expect(dashboard.config.layout).toBe('tabs');
      expect(dashboard.config.refreshInterval).toBe(180000); // 3 minutes
      expect(dashboard.widgets).toBeDefined();
      expect(dashboard.filters).toBeDefined();
    });

    test('should include framework filters in compliance dashboard', async () => {
      const dashboard = await dashboardGenerator.generateComplianceDashboard(mockComplianceScope);

      const frameworkFilter = dashboard.filters.find(f => f.id === 'frameworks');
      expect(frameworkFilter).toBeDefined();
      expect(frameworkFilter?.value).toEqual(mockComplianceScope.frameworks);
      expect(frameworkFilter?.options).toContain('SOX');
      expect(frameworkFilter?.options).toContain('GDPR');
    });

    test('should include severity filters in compliance dashboard', async () => {
      const dashboard = await dashboardGenerator.generateComplianceDashboard(mockComplianceScope);

      const severityFilter = dashboard.filters.find(f => f.id === 'severity');
      expect(severityFilter).toBeDefined();
      expect(severityFilter?.value).toEqual(mockComplianceScope.severityLevels);
    });

    test('should handle different compliance scopes', async () => {
      const differentScope: TComplianceScope = {
        frameworks: ['HIPAA', 'PCI-DSS'],
        ruleCategories: ['data-protection', 'access-control'],
        severityLevels: ['medium', 'low'],
        timeRange: mockTimeRange
      };

      const dashboard = await dashboardGenerator.generateComplianceDashboard(differentScope);
      expect(dashboard).toBeDefined();
      
      const frameworkFilter = dashboard.filters.find(f => f.id === 'frameworks');
      expect(frameworkFilter?.value).toEqual(differentScope.frameworks);
    });
  });

  // ============================================================================
  // EXECUTIVE DASHBOARD GENERATION TESTS
  // ============================================================================

  describe('Executive Dashboard Generation', () => {
    const executiveLevels: TExecutiveLevel[] = ['board', 'ceo', 'cto', 'cfo', 'coo', 'vp', 'director'];

    test.each(executiveLevels)('should generate executive dashboard for %s level', async (level) => {
      const dashboard = await dashboardGenerator.generateExecutiveDashboard(level);

      expect(dashboard).toBeDefined();
      expect(dashboard.id).toBeDefined();
      expect(dashboard.title).toBe(`${level.toUpperCase()} Executive Dashboard`);
      expect(dashboard.description).toContain(level);
      expect(dashboard.config.layout).toBe('flex');
      expect(dashboard.config.theme).toBe('dark');
      expect(dashboard.config.refreshInterval).toBe(600000); // 10 minutes
      expect(dashboard.widgets).toBeDefined();
      expect(dashboard.filters).toBeDefined();
    });

    test('should include executive-specific filters', async () => {
      const dashboard = await dashboardGenerator.generateExecutiveDashboard('ceo');

      const perspectiveFilter = dashboard.filters.find(f => f.id === 'perspective');
      expect(perspectiveFilter).toBeDefined();
      expect(perspectiveFilter?.options).toContain('strategic');
      expect(perspectiveFilter?.options).toContain('operational');

      const timeHorizonFilter = dashboard.filters.find(f => f.id === 'time-horizon');
      expect(timeHorizonFilter).toBeDefined();
      expect(timeHorizonFilter?.options).toContain('quarterly');
    });

    test('should include appropriate tags for executive dashboards', async () => {
      const dashboard = await dashboardGenerator.generateExecutiveDashboard('cto');

      expect(dashboard.metadata.tags).toContain('executive');
      expect(dashboard.metadata.tags).toContain('cto');
      expect(dashboard.metadata.tags).toContain('summary');
    });
  });

  // ============================================================================
  // CUSTOM DASHBOARD GENERATION TESTS
  // ============================================================================

  describe('Custom Dashboard Generation', () => {
    test('should generate custom dashboard from template', async () => {
      const dashboard = await dashboardGenerator.generateCustomDashboard(mockDashboardTemplate);

      expect(dashboard).toBeDefined();
      expect(dashboard.id).toBeDefined();
      expect(dashboard.title).toBe(mockDashboardTemplate.name);
      expect(dashboard.description).toBe(mockDashboardTemplate.description);
      expect(dashboard.config.layout).toBe('grid');
      expect(dashboard.config.theme).toBe('auto');
      expect(dashboard.filters).toEqual(mockDashboardTemplate.defaultFilters);
    });

    test('should include template metadata in custom dashboard', async () => {
      const dashboard = await dashboardGenerator.generateCustomDashboard(mockDashboardTemplate);

      expect(dashboard.metadata.tags).toContain('custom');
      expect(dashboard.metadata.tags).toContain(mockDashboardTemplate.category);
      expect(dashboard.metadata.tags).toContain('template');
    });

    test('should handle template with variables', async () => {
      const templateWithVariables: TDashboardTemplate = {
        ...mockDashboardTemplate,
        variables: [
          {
            name: 'timeRange',
            type: 'date',
            defaultValue: '24h',
            required: true,
            description: 'Time range for data'
          },
          {
            name: 'threshold',
            type: 'number',
            defaultValue: 95,
            required: false,
            description: 'Performance threshold'
          }
        ]
      };

      const dashboard = await dashboardGenerator.generateCustomDashboard(templateWithVariables);
      expect(dashboard).toBeDefined();
    });
  });

  // ============================================================================
  // DASHBOARD EXPORT TESTS
  // ============================================================================

  describe('Dashboard Export', () => {
    let sampleDashboard: TDashboardData;

    beforeEach(async () => {
      sampleDashboard = await dashboardGenerator.generateGovernanceDashboard(mockConfig);
    });

    const exportFormats: TExportFormat[] = ['json', 'csv', 'pdf', 'png', 'svg', 'xlsx'];

    test.each(exportFormats)('should export dashboard in %s format', async (format) => {
      const exportResult = await dashboardGenerator.exportDashboard(sampleDashboard.id, format);

      expect(exportResult).toBeDefined();
      expect(exportResult.exportId).toBeDefined();
      expect(exportResult.dashboardId).toBe(sampleDashboard.id);
      expect(exportResult.format).toBe(format);
      expect(exportResult.data).toBeDefined();
      expect(exportResult.metadata).toBeDefined();
      expect(exportResult.metadata.generatedAt).toBeInstanceOf(Date);
      expect(exportResult.metadata.size).toBeGreaterThan(0);
      expect(exportResult.metadata.checksum).toBeDefined();
    });

    test('should handle JSON export correctly', async () => {
      const exportResult = await dashboardGenerator.exportDashboard(sampleDashboard.id, 'json');
      
      expect(typeof exportResult.data).toBe('string');
      const parsedData = JSON.parse(exportResult.data);
      expect(parsedData.id).toBe(sampleDashboard.id);
    });

    test('should handle CSV export correctly', async () => {
      const exportResult = await dashboardGenerator.exportDashboard(sampleDashboard.id, 'csv');
      
      expect(typeof exportResult.data).toBe('string');
      expect(exportResult.data).toContain('Widget,Type,Title,Status');
    });

    test('should throw error for non-existent dashboard', async () => {
      await expect(dashboardGenerator.exportDashboard('non-existent-id', 'json'))
        .rejects.toThrow('Dashboard not found: non-existent-id');
    });

    test('should throw error for unsupported format', async () => {
      await expect(dashboardGenerator.exportDashboard(sampleDashboard.id, 'unsupported' as TExportFormat))
        .rejects.toThrow('Unsupported export format: unsupported');
    });
  });

  // ============================================================================
  // METRICS AND SERVICE INTERFACE TESTS
  // ============================================================================

  describe('Metrics and Service Interface', () => {
    test('should provide reporting metrics', async () => {
      const metrics = await dashboardGenerator.getReportingMetrics();

      expect(metrics).toBeDefined();
      expect(typeof metrics.totalDashboards).toBe('number');
      expect(typeof metrics.activeDashboards).toBe('number');
      expect(typeof metrics.dashboardViews).toBe('number');
      expect(typeof metrics.dashboardExports).toBe('number');
      expect(typeof metrics.averageGenerationTime).toBe('number');
      expect(typeof metrics.errorRate).toBe('number');
      expect(typeof metrics.cacheHitRate).toBe('number');
      expect(typeof metrics.userEngagement).toBe('number');
      expect(typeof metrics.performanceScore).toBe('number');
    });

    test('should provide service metrics with custom data', async () => {
      const metrics = await dashboardGenerator.getMetrics();

      expect(metrics.custom).toBeDefined();
      expect(metrics.custom.dashboards).toBeDefined();
      expect(metrics.custom.views).toBeDefined();
      expect(metrics.custom.exports).toBeDefined();
      expect(metrics.custom.generationTime).toBeDefined();
      expect(metrics.custom.cacheHitRate).toBeDefined();
      expect(metrics.custom.userEngagement).toBeDefined();
      expect(metrics.custom.performanceScore).toBeDefined();
    });

    test('should process reporting data', async () => {
      const testData = { test: 'data', timestamp: new Date() };
      const result = await dashboardGenerator.processReportingData(testData);

      expect(result).toBeDefined();
      expect(result.processed).toBe(true);
      expect(result.timestamp).toBeInstanceOf(Date);
    });

    test('should generate dashboard report', async () => {
      const testConfig = { reportType: 'summary', timeRange: '24h' };
      const report = await dashboardGenerator.generateDashboardReport(testConfig);

      expect(report).toBeDefined();
      expect(report.reportId).toBeDefined();
      expect(report.config).toEqual(testConfig);
      expect(report.generatedAt).toBeInstanceOf(Date);
    });
  });

  // ============================================================================
  // UI SERVICE INTERFACE TESTS
  // ============================================================================

  describe('UI Service Interface', () => {
    test('should render UI component', async () => {
      const componentId = 'test-component';
      const testData = { value: 42, label: 'Test' };
      
      const result = await dashboardGenerator.renderComponent(componentId, testData);

      expect(result).toBeDefined();
      expect(result.componentId).toBe(componentId);
      expect(result.rendered).toBe(true);
      expect(result.data).toEqual(testData);
    });

    test('should update UI component', async () => {
      const componentId = 'test-component';
      const updates = { value: 100 };

      await expect(dashboardGenerator.updateComponent(componentId, updates))
        .resolves.not.toThrow();
    });

    test('should get UI state', async () => {
      const componentId = 'test-component';
      const state = await dashboardGenerator.getUIState(componentId);

      expect(state).toBeDefined();
      expect(state.componentId).toBe(componentId);
      expect(state.state).toBeDefined();
    });

    test('should set UI state', async () => {
      const componentId = 'test-component';
      const newState = { active: true, value: 50 };

      await expect(dashboardGenerator.setUIState(componentId, newState))
        .resolves.not.toThrow();
    });
  });

  // ============================================================================
  // ERROR HANDLING AND EDGE CASES
  // ============================================================================

  describe('Error Handling and Edge Cases', () => {
    test('should handle shutdown gracefully', async () => {
      await expect(dashboardGenerator.shutdown()).resolves.not.toThrow();
      expect(dashboardGenerator.isReady()).toBe(false);
    });

    test('should validate successfully', async () => {
      const validation = await dashboardGenerator.validate();
      
      expect(validation.status).toBe('valid');
      expect(validation.overallScore).toBeGreaterThan(0);
      expect(validation.checks.length).toBeGreaterThan(0);
      expect(validation.references).toBeDefined();
      expect(validation.recommendations).toBeDefined();
      expect(validation.warnings).toBeDefined();
      expect(validation.errors).toBeDefined();
      expect(validation.metadata).toBeDefined();
    });

    test('should track data properly', async () => {
      const testData = { 
        component: 'dashboard-generator',
        operation: 'test',
        timestamp: new Date().toISOString()
      };

      // doTrack is protected, so we test indirectly through a public method
      await expect(dashboardGenerator.validate()).resolves.not.toThrow();
    });

    test('should handle concurrent dashboard generation', async () => {
      const promises = Array.from({ length: 5 }, (_, i) => 
        dashboardGenerator.generateGovernanceDashboard({
          ...mockConfig,
          title: `Concurrent Dashboard ${i + 1}`
        })
      );

      const dashboards = await Promise.all(promises);
      
      expect(dashboards).toHaveLength(5);
      dashboards.forEach((dashboard, index) => {
        expect(dashboard.title).toBe(`Concurrent Dashboard ${index + 1}`);
        expect(dashboard.id).toBeDefined();
      });
    });

    test('should maintain metrics during multiple operations', async () => {
      const initialMetrics = await dashboardGenerator.getReportingMetrics();
      
      // Generate some dashboards
      await dashboardGenerator.generateGovernanceDashboard(mockConfig);
      await dashboardGenerator.generateRulePerformanceDashboard(['rule-001'], mockTimeRange);
      
      const finalMetrics = await dashboardGenerator.getReportingMetrics();
      
      expect(finalMetrics.totalDashboards).toBeGreaterThan(initialMetrics.totalDashboards);
    });
  });
}); 