/**
 * ============================================================================
 * AI CONTEXT: Rule Dashboard Generator Factory Tests - Comprehensive factory test suite
 * Purpose: Unit and integration tests for the Rule Dashboard Generator Factory component
 * Complexity: Moderate - Factory pattern testing with instance management
 * AI Navigation: 4 logical sections, factory testing domain
 * ============================================================================
 */

import { 
  GovernanceRuleDashboardGeneratorFactory, 
  TDashboardGeneratorFactoryConfig, 
  TDashboardGeneratorInstanceConfig,
  createDashboardGenerator,
  createConfiguredDashboardGenerator,
  releaseDashboardGenerator,
  getFactoryStats
} from '../GovernanceRuleDashboardGeneratorFactory';
import { GovernanceRuleDashboardGenerator, TDashboardGeneratorConfig } from '../GovernanceRuleDashboardGenerator';

// ============================================================================
// TEST SETUP AND MOCKS
// ============================================================================

// Mock dependencies
jest.mock('../../automation-processing/factories/RuleAuditLoggerFactory');

describe('GovernanceRuleDashboardGeneratorFactory', () => {
  let factory: GovernanceRuleDashboardGeneratorFactory;
  let factoryConfig: TDashboardGeneratorFactoryConfig;

  beforeEach(() => {
    // Reset singleton
    (GovernanceRuleDashboardGeneratorFactory as any)._instance = null;

    factoryConfig = {
      factoryId: 'test-dashboard-generator-factory',
      debug: false,
      logLevel: 'info',
      maxInstances: 10,
      poolSize: 3,
      generationTimeout: 30000,
      enableCaching: true,
      cacheTTL: 300000,
      custom: { testMode: true }
    };

    factory = GovernanceRuleDashboardGeneratorFactory.getInstance(factoryConfig);
  });

  afterEach(async () => {
    if (factory) {
      await factory.shutdown();
    }
  });

  // ============================================================================
  // SINGLETON AND INITIALIZATION TESTS
  // ============================================================================

  describe('Singleton and Initialization', () => {
    test('should create singleton instance', () => {
      const instance1 = GovernanceRuleDashboardGeneratorFactory.getInstance();
      const instance2 = GovernanceRuleDashboardGeneratorFactory.getInstance();
      
      expect(instance1).toBe(instance2);
    });

    test('should initialize with default configuration', () => {
      // Reset singleton to test default configuration
      (GovernanceRuleDashboardGeneratorFactory as any)._instance = null;
      
      const defaultFactory = GovernanceRuleDashboardGeneratorFactory.getInstance();
      const stats = defaultFactory.getFactoryStats();
      
      expect(stats.factoryId).toBe('governance-rule-dashboard-generator-factory');
      expect(stats.maxInstances).toBe(50);
      expect(stats.poolSize).toBe(5);
    });

    test('should initialize with custom configuration', () => {
      const stats = factory.getFactoryStats();
      
      expect(stats.factoryId).toBe(factoryConfig.factoryId);
      expect(stats.maxInstances).toBe(factoryConfig.maxInstances);
      expect(stats.poolSize).toBe(factoryConfig.poolSize);
      expect(stats.config.debug).toBe(factoryConfig.debug);
      expect(stats.config.logLevel).toBe(factoryConfig.logLevel);
    });

    test('should initialize factory properly', async () => {
      await factory.initialize();
      
      const stats = factory.getFactoryStats();
      expect(stats.isInitialized).toBe(true);
      expect(stats.pooledInstances).toBe(factoryConfig.poolSize);
      expect(stats.activeInstances).toBe(0);
    });

    test('should not reinitialize if already initialized', async () => {
      await factory.initialize();
      const initialStats = factory.getFactoryStats();
      
      await factory.initialize(); // Second initialization
      const finalStats = factory.getFactoryStats();
      
      expect(finalStats).toEqual(initialStats);
    });
  });

  // ============================================================================
  // INSTANCE CREATION AND MANAGEMENT TESTS
  // ============================================================================

  describe('Instance Creation and Management', () => {
    beforeEach(async () => {
      await factory.initialize();
    });

    test('should create dashboard generator instance', async () => {
      const instance = await factory.createDashboardGenerator();
      
      expect(instance).toBeInstanceOf(GovernanceRuleDashboardGenerator);
      expect(instance.isReady()).toBe(true);
      
      const stats = factory.getFactoryStats();
      expect(stats.activeInstances).toBe(1);
    });

    test('should create instance with configuration', async () => {
      const config: TDashboardGeneratorInstanceConfig = {
        instanceId: 'test-instance',
        dashboardType: 'performance',
        enableOptimization: true,
        enableRealTime: true,
        enableExport: true,
        customSettings: { testMode: true }
      };

      const instance = await factory.createDashboardGenerator(config);
      
      expect(instance).toBeInstanceOf(GovernanceRuleDashboardGenerator);
      expect(instance.isReady()).toBe(true);
    });

    test('should reuse instance from pool when possible', async () => {
      const initialStats = factory.getFactoryStats();
      const initialPoolSize = initialStats.pooledInstances;
      
      const instance = await factory.createDashboardGenerator();
      
      const finalStats = factory.getFactoryStats();
      expect(finalStats.pooledInstances).toBe(initialPoolSize - 1);
      expect(finalStats.activeInstances).toBe(1);
    });

    test('should create new instance when pool is empty', async () => {
      // Exhaust the pool
      const instances: GovernanceRuleDashboardGenerator[] = [];
      for (let i = 0; i < factoryConfig.poolSize + 1; i++) {
        instances.push(await factory.createDashboardGenerator());
      }
      
      const stats = factory.getFactoryStats();
      expect(stats.pooledInstances).toBe(0);
      expect(stats.activeInstances).toBe(factoryConfig.poolSize + 1);
    });

    test('should enforce maximum instances limit', async () => {
      const instances: GovernanceRuleDashboardGenerator[] = [];
      
      // Create maximum allowed instances
      for (let i = 0; i < factoryConfig.maxInstances; i++) {
        instances.push(await factory.createDashboardGenerator());
      }
      
      // Attempt to create one more instance
      await expect(factory.createDashboardGenerator())
        .rejects.toThrow(`Maximum instances limit reached: ${factoryConfig.maxInstances}`);
    });

    test('should get existing instance by ID', async () => {
      const instance = await factory.createDashboardGenerator({ instanceId: 'test-get-instance' });
      const retrievedInstance = factory.getDashboardGenerator('test-get-instance');
      
      expect(retrievedInstance).toBe(instance);
    });

    test('should return null for non-existent instance', () => {
      const retrievedInstance = factory.getDashboardGenerator('non-existent');
      expect(retrievedInstance).toBeNull();
    });
  });

  // ============================================================================
  // CONFIGURED GENERATOR CREATION TESTS
  // ============================================================================

  describe('Configured Generator Creation', () => {
    beforeEach(async () => {
      await factory.initialize();
    });

    test('should create configured generator from dashboard config', async () => {
      const dashboardConfig: TDashboardGeneratorConfig = {
        dashboardType: 'governance',
        title: 'Test Dashboard',
        description: 'Test description',
        refreshInterval: 60000,
        autoRefresh: true,
        theme: 'dark',
        layout: 'grid',
        filters: [],
        widgets: [],
        permissions: {
          view: ['admin'],
          edit: ['admin'],
          delete: ['admin'],
          share: ['admin'],
          export: ['admin']
        },
        metadata: {}
      };

      const instance = await factory.createConfiguredGenerator(dashboardConfig);
      
      expect(instance).toBeInstanceOf(GovernanceRuleDashboardGenerator);
      expect(instance.isReady()).toBe(true);
    });

    test('should handle different dashboard types in configured generator', async () => {
      const dashboardTypes: Array<'governance' | 'performance' | 'compliance' | 'executive' | 'custom'> = 
        ['governance', 'performance', 'compliance', 'executive', 'custom'];

      for (const type of dashboardTypes) {
        const dashboardConfig: TDashboardGeneratorConfig = {
          dashboardType: type,
          title: `${type} Dashboard`,
          description: `${type} description`,
          refreshInterval: 60000,
          autoRefresh: true,
          theme: 'light',
          layout: 'grid',
          filters: [],
          widgets: [],
          permissions: {
            view: ['admin'],
            edit: ['admin'],
            delete: ['admin'],
            share: ['admin'],
            export: ['admin']
          },
          metadata: {}
        };

        const instance = await factory.createConfiguredGenerator(dashboardConfig);
        expect(instance).toBeInstanceOf(GovernanceRuleDashboardGenerator);
      }
    });
  });

  // ============================================================================
  // INSTANCE RELEASE AND CLEANUP TESTS
  // ============================================================================

  describe('Instance Release and Cleanup', () => {
    beforeEach(async () => {
      await factory.initialize();
    });

    test('should release instance and return to pool', async () => {
      const instance = await factory.createDashboardGenerator({ instanceId: 'test-release' });
      const initialStats = factory.getFactoryStats();
      
      await factory.releaseDashboardGenerator('test-release');
      
      const finalStats = factory.getFactoryStats();
      expect(finalStats.activeInstances).toBe(initialStats.activeInstances - 1);
      expect(finalStats.pooledInstances).toBe(initialStats.pooledInstances + 1);
    });

    test('should shutdown instance when pool is full', async () => {
      // Fill the pool first by creating instances with specific IDs
      const instanceIds: string[] = [];
      for (let i = 0; i < factoryConfig.poolSize; i++) {
        const instanceId = `pool-instance-${i}`;
        instanceIds.push(instanceId);
        await factory.createDashboardGenerator({ instanceId });
      }

      // Release all instances to fill the pool
      for (const instanceId of instanceIds) {
        await factory.releaseDashboardGenerator(instanceId);
      }

      // Create one more instance
      const extraInstance = await factory.createDashboardGenerator({ instanceId: 'extra-instance' });
      
      const beforeReleaseStats = factory.getFactoryStats();
      await factory.releaseDashboardGenerator('extra-instance');
      
      const afterReleaseStats = factory.getFactoryStats();
      // Pool should remain at max size
      expect(afterReleaseStats.pooledInstances).toBe(factoryConfig.poolSize);
    });

    test('should handle release of non-existent instance gracefully', async () => {
      await expect(factory.releaseDashboardGenerator('non-existent'))
        .resolves.not.toThrow();
    });

    test('should shutdown all instances when factory shuts down', async () => {
      // Create some instances
      await factory.createDashboardGenerator({ instanceId: 'instance-1' });
      await factory.createDashboardGenerator({ instanceId: 'instance-2' });
      
      const beforeShutdownStats = factory.getFactoryStats();
      expect(beforeShutdownStats.activeInstances).toBeGreaterThan(0);
      expect(beforeShutdownStats.pooledInstances).toBeGreaterThan(0);
      
      await factory.shutdown();
      
      const afterShutdownStats = factory.getFactoryStats();
      expect(afterShutdownStats.isInitialized).toBe(false);
      expect(afterShutdownStats.activeInstances).toBe(0);
      expect(afterShutdownStats.pooledInstances).toBe(0);
    });
  });

  // ============================================================================
  // CONFIGURATION VALIDATION TESTS
  // ============================================================================

  describe('Configuration Validation', () => {
    test('should validate valid configuration', () => {
      const validConfig: Partial<TDashboardGeneratorFactoryConfig> = {
        factoryId: 'valid-factory',
        maxInstances: 20,
        poolSize: 5
      };

      const isValid = factory.validateConfiguration(validConfig);
      expect(isValid).toBe(true);
    });

    test('should reject configuration with missing required fields', () => {
      const invalidConfig = {
        factoryId: 'invalid-factory'
        // Missing maxInstances and poolSize
      };

      const isValid = factory.validateConfiguration(invalidConfig);
      expect(isValid).toBe(false);
    });

    test('should reject configuration with invalid values', () => {
      const invalidConfig: Partial<TDashboardGeneratorFactoryConfig> = {
        factoryId: 'invalid-factory',
        maxInstances: 0, // Invalid: must be positive
        poolSize: 5
      };

      const isValid = factory.validateConfiguration(invalidConfig);
      expect(isValid).toBe(false);
    });

    test('should reject configuration where poolSize exceeds maxInstances', () => {
      const invalidConfig: Partial<TDashboardGeneratorFactoryConfig> = {
        factoryId: 'invalid-factory',
        maxInstances: 5,
        poolSize: 10 // Invalid: pool size exceeds max instances
      };

      const isValid = factory.validateConfiguration(invalidConfig);
      expect(isValid).toBe(false);
    });

    test('should update configuration with valid values', () => {
      const newConfig: Partial<TDashboardGeneratorFactoryConfig> = {
        factoryId: 'updated-factory',
        maxInstances: 30,
        poolSize: 8,
        debug: true
      };

      factory.updateConfiguration(newConfig);
      
      const stats = factory.getFactoryStats();
      expect(stats.config.factoryId).toBe('updated-factory');
      expect(stats.config.maxInstances).toBe(30);
      expect(stats.config.poolSize).toBe(8);
      expect(stats.config.debug).toBe(true);
    });

    test('should throw error when updating with invalid configuration', () => {
      const invalidConfig: Partial<TDashboardGeneratorFactoryConfig> = {
        maxInstances: -1, // Invalid
        poolSize: 5
      };

      expect(() => factory.updateConfiguration(invalidConfig))
        .toThrow('Invalid factory configuration');
    });
  });

  // ============================================================================
  // HELPER FUNCTIONS TESTS
  // ============================================================================

  describe('Helper Functions', () => {
    test('should create dashboard generator using helper function', async () => {
      const instance = await createDashboardGenerator();
      
      expect(instance).toBeInstanceOf(GovernanceRuleDashboardGenerator);
      expect(instance.isReady()).toBe(true);
    });

    test('should create dashboard generator with config using helper function', async () => {
      const config: TDashboardGeneratorInstanceConfig = {
        dashboardType: 'compliance',
        enableOptimization: true
      };

      const instance = await createDashboardGenerator(config);
      
      expect(instance).toBeInstanceOf(GovernanceRuleDashboardGenerator);
      expect(instance.isReady()).toBe(true);
    });

    test('should create configured dashboard generator using helper function', async () => {
      const dashboardConfig: TDashboardGeneratorConfig = {
        dashboardType: 'executive',
        title: 'Executive Dashboard',
        description: 'Executive level dashboard',
        refreshInterval: 300000,
        autoRefresh: true,
        theme: 'dark',
        layout: 'flex',
        filters: [],
        widgets: [],
        permissions: {
          view: ['executive'],
          edit: ['admin'],
          delete: ['admin'],
          share: ['executive'],
          export: ['executive']
        },
        metadata: {}
      };

      const instance = await createConfiguredDashboardGenerator(dashboardConfig);
      
      expect(instance).toBeInstanceOf(GovernanceRuleDashboardGenerator);
      expect(instance.isReady()).toBe(true);
    });

    test('should release dashboard generator using helper function', async () => {
      const instance = await createDashboardGenerator({ instanceId: 'helper-test' });
      
      await expect(releaseDashboardGenerator('helper-test'))
        .resolves.not.toThrow();
    });

    test('should get factory stats using helper function', () => {
      const stats = getFactoryStats();
      
      expect(stats).toBeDefined();
      expect(stats.factoryId).toBeDefined();
      expect(typeof stats.isInitialized).toBe('boolean');
      expect(typeof stats.activeInstances).toBe('number');
      expect(typeof stats.pooledInstances).toBe('number');
      expect(typeof stats.maxInstances).toBe('number');
      expect(typeof stats.poolSize).toBe('number');
      expect(stats.config).toBeDefined();
    });
  });

  // ============================================================================
  // STRESS AND PERFORMANCE TESTS
  // ============================================================================

  describe('Stress and Performance Tests', () => {
    beforeEach(async () => {
      await factory.initialize();
    });

    test('should handle rapid instance creation and release', async () => {
      const operations: Promise<GovernanceRuleDashboardGenerator>[] = [];
      
      // Create multiple instances rapidly
      for (let i = 0; i < 5; i++) {
        operations.push(factory.createDashboardGenerator({ instanceId: `rapid-${i}` }));
      }
      
      const instances = await Promise.all(operations);
      expect(instances).toHaveLength(5);
      
      // Release them rapidly
      const releaseOperations: Promise<void>[] = [];
      for (let i = 0; i < 5; i++) {
        releaseOperations.push(factory.releaseDashboardGenerator(`rapid-${i}`));
      }
      
      await Promise.all(releaseOperations);
      
      // Factory should be in a clean state
      const stats = factory.getFactoryStats();
      expect(stats.activeInstances).toBe(0);
    });

    test('should maintain consistency under concurrent access', async () => {
      const concurrentOperations: Promise<GovernanceRuleDashboardGenerator>[] = [];
      
      // Create instances concurrently
      for (let i = 0; i < 3; i++) {
        concurrentOperations.push(factory.createDashboardGenerator());
      }
      
      const instances = await Promise.all(concurrentOperations);
      
      // All instances should be valid
      instances.forEach(instance => {
        expect(instance).toBeInstanceOf(GovernanceRuleDashboardGenerator);
        expect(instance.isReady()).toBe(true);
      });
      
      const stats = factory.getFactoryStats();
      expect(stats.activeInstances).toBe(3);
    });

    test('should handle factory reinitialization properly', async () => {
      // Create some instances
      await factory.createDashboardGenerator({ instanceId: 'before-shutdown' });
      
      // Shutdown and reinitialize
      await factory.shutdown();
      
      // Reset singleton for fresh start
      (GovernanceRuleDashboardGeneratorFactory as any)._instance = null;
      const newFactory = GovernanceRuleDashboardGeneratorFactory.getInstance(factoryConfig);
      await newFactory.initialize();
      
      // Should be able to create new instances
      const newInstance = await newFactory.createDashboardGenerator({ instanceId: 'after-reinit' });
      expect(newInstance).toBeInstanceOf(GovernanceRuleDashboardGenerator);
      
      await newFactory.shutdown();
    });
  });
}); 