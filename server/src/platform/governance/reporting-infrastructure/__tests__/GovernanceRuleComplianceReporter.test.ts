/**
 * @file Governance Rule Compliance Reporter Tests
 * @filepath server/src/platform/governance/reporting-infrastructure/__tests__/GovernanceRuleComplianceReporter.test.ts
 * @task-id G-TSK-06.SUB-06.8.TEST-01
 * @component governance-rule-compliance-reporter-tests
 * @reference foundation-context.COMPLIANCE.003
 * @template on-demand-creation-with-latest-standards
 * @tier T2
 * @context foundation-context
 * @category Compliance
 * @created 2025-07-03 02:05:51 +03
 * @modified 2025-07-03 02:05:51 +03
 * 
 * @description
 * Comprehensive unit tests for Governance Rule Compliance Reporter providing:
 * - Interface compliance validation
 * - Compliance report generation testing
 * - Regulatory framework testing
 * - Violation detection testing
 * - Audit trail generation testing
 * - Performance and error handling testing
 * - Integration testing with other components
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-tracking-architecture
 * @governance-dcr DCR-foundation-001-tracking-development
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on server/src/platform/governance/reporting-infrastructure/GovernanceRuleComplianceReporter
 * @enables server/src/platform/governance/reporting-infrastructure
 * @related-contexts foundation-context, governance-context
 * @governance-impact framework-foundation, governance-compliance
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type governance-test-suite
 * @lifecycle-stage implementation
 * @testing-status comprehensive
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/contexts/foundation-context/compliance/governance-rule-compliance-reporter-tests.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 * 
 * 📝 VERSION HISTORY
 * @version-history
 * v1.0.0 (2025-07-03) - Initial comprehensive test implementation
 */

import {
  GovernanceRuleComplianceReporter,
  IComplianceReporter,
  IReportingService,
  TComplianceScope,
  TRegulatoryFramework,
  TRegulatoryOptions,
  TComplianceReport,
  TRegulatoryReport,
  TComplianceStatus,
  TComplianceViolation,
  TAuditTrail,
  TComplianceTrends,
  TTimeRange,
  TAnalyticsPeriod,
  TReportFormat
} from '../GovernanceRuleComplianceReporter';

import {
  TGovernanceRule,
  TGovernanceService
} from '../../../../../../shared/src/types/platform/governance/automation-processing-types';

import {
  TValidationResult,
  TMetrics
} from '../../../../../../shared/src/types/platform/tracking/tracking-types';

// ============================================================================
// TEST SETUP AND UTILITIES
// ============================================================================

describe('GovernanceRuleComplianceReporter', () => {
  let complianceReporter: GovernanceRuleComplianceReporter;
  let mockRules: TGovernanceRule[];
  let mockComplianceScope: TComplianceScope;
  let mockTimeRange: TTimeRange;

  beforeEach(async () => {
    complianceReporter = new GovernanceRuleComplianceReporter();
    await complianceReporter.initialize();

    // Setup mock data
    mockRules = [
      {
        ruleId: 'rule_001',
        name: 'Data Protection Rule',
        description: 'Ensures data protection compliance',
        type: 'security',
        category: 'security',
        priority: 'high',
        status: 'active',
        conditions: { dataType: 'PII' },
        actions: { encrypt: true },
        version: '1.0.0',
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: 'system',
        metadata: {}
      },
      {
        ruleId: 'rule_002',
        name: 'Access Control Rule',
        description: 'Manages access control compliance',
        type: 'access',
        category: 'access',
        priority: 'medium',
        status: 'active',
        conditions: { role: 'admin' },
        actions: { requireMFA: true },
        version: '1.0.0',
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: 'system',
        metadata: {}
      }
    ];

    mockComplianceScope = {
      ruleCategories: ['security', 'access', 'audit'],
      severityLevels: ['low', 'medium', 'high', 'critical'],
      timeRange: {
        startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
        endDate: new Date(),
        granularity: 'day'
      },
      includeViolations: true,
      includeRemediation: true,
      regulatoryFrameworks: ['SOX', 'GDPR', 'HIPAA'],
      organizationalUnits: ['IT', 'Finance', 'HR'],
      riskLevels: ['low', 'medium', 'high']
    };

    mockTimeRange = {
      startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
      endDate: new Date(),
      granularity: 'hour'
    };
  });

  afterEach(async () => {
    await complianceReporter.shutdown();
  });

  // ============================================================================
  // BASIC FUNCTIONALITY TESTS
  // ============================================================================

  describe('Basic Functionality', () => {
    test('should initialize successfully', async () => {
      const newReporter = new GovernanceRuleComplianceReporter();
      await expect(newReporter.initialize()).resolves.not.toThrow();
      expect(newReporter.isReady()).toBe(true);
      await newReporter.shutdown();
    });

    test('should have correct component identification', () => {
      expect(complianceReporter.id).toBe('governance-rule-compliance-reporter');
      expect(complianceReporter.authority).toBe('President & CEO, E.Z. Consultancy');
    });

    test('should validate successfully', async () => {
      const validation: TValidationResult = await complianceReporter.validate();
      expect(validation).toBeDefined();
      expect(validation.status).toBe('valid');
      expect(validation.overallScore).toBeGreaterThan(80);
      expect(validation.componentId).toBe('governance-rule-compliance-reporter');
    });

    test('should provide metrics', async () => {
      const metrics: TMetrics = await complianceReporter.getMetrics();
      expect(metrics).toBeDefined();
      expect(metrics.service).toBe('governance-rule-compliance-reporter');
      expect(metrics.custom).toBeDefined();
      expect(typeof metrics.custom.totalReports).toBe('number');
      expect(typeof metrics.custom.complianceScore).toBe('number');
    });

    test('should shutdown gracefully', async () => {
      await expect(complianceReporter.shutdown()).resolves.not.toThrow();
    });
  });

  // ============================================================================
  // ICOMPLIANCEREPORTER INTERFACE TESTS
  // ============================================================================

  describe('IComplianceReporter Interface', () => {
    test('should implement IComplianceReporter interface', () => {
      const reporter: IComplianceReporter = complianceReporter;
      expect(reporter).toBeDefined();
      expect(typeof reporter.generateComplianceReport).toBe('function');
      expect(typeof reporter.generateRegulatoryReport).toBe('function');
      expect(typeof reporter.monitorComplianceStatus).toBe('function');
      expect(typeof reporter.detectComplianceViolations).toBe('function');
      expect(typeof reporter.generateAuditTrail).toBe('function');
      expect(typeof reporter.analyzeComplianceTrends).toBe('function');
    });

    test('should generate comprehensive compliance report', async () => {
      const report: TComplianceReport = await complianceReporter.generateComplianceReport(
        mockComplianceScope,
        'pdf'
      );

      expect(report).toBeDefined();
      expect(report.reportId).toMatch(/^compliance_report_/);
      expect(report.scope).toEqual(mockComplianceScope);
      expect(report.format).toBe('pdf');
      expect(report.overallScore).toBeGreaterThan(0);
      expect(report.overallScore).toBeLessThanOrEqual(100);
      expect(report.sections).toBeDefined();
      expect(report.sections.executiveSummary).toBeDefined();
      expect(report.sections.complianceOverview).toBeDefined();
      expect(report.sections.violationAnalysis).toBeDefined();
      expect(report.sections.riskAssessment).toBeDefined();
      expect(report.sections.recommendations).toBeDefined();
      expect(report.sections.auditFindings).toBeDefined();
      expect(report.sections.remediation).toBeDefined();
      expect(report.sections.appendices).toBeDefined();
      expect(report.metadata).toBeDefined();
      expect(report.metadata.authority).toBe('President & CEO, E.Z. Consultancy');
      expect(report.metadata.confidentiality).toBe('confidential');
    });

    test('should generate regulatory compliance report', async () => {
      const framework: TRegulatoryFramework = 'SOX';
      const options: TRegulatoryOptions = {
        framework,
        sections: ['Section 404', 'Section 302'],
        evidenceLevel: 'comprehensive',
        includeGaps: true,
        includeRecommendations: true,
        auditReadiness: true,
        timeRange: mockTimeRange,
        metadata: { testMode: true }
      };

      const report: TRegulatoryReport = await complianceReporter.generateRegulatoryReport(
        framework,
        options
      );

      expect(report).toBeDefined();
      expect(report.reportId).toMatch(/^compliance_report_/);
      expect(report.framework).toBe(framework);
      expect(report.options).toEqual(options);
      expect(report.complianceScore).toBeGreaterThan(0);
      expect(report.complianceScore).toBeLessThanOrEqual(100);
      expect(report.sections).toBeDefined();
      expect(report.sections.frameworkOverview).toBeDefined();
      expect(report.sections.controlsAssessment).toBeDefined();
      expect(report.sections.gapAnalysis).toBeDefined();
      expect(report.sections.evidenceCollection).toBeDefined();
      expect(report.sections.riskMatrix).toBeDefined();
      expect(report.sections.correctionActions).toBeDefined();
      expect(report.sections.certificationReadiness).toBeDefined();
      expect(report.attestation).toBeDefined();
    });

    test('should monitor real-time compliance status', async () => {
      const status: TComplianceStatus = await complianceReporter.monitorComplianceStatus(mockRules);

      expect(status).toBeDefined();
      expect(status.statusId).toMatch(/^compliance_status_/);
      expect(status.timestamp).toBeInstanceOf(Date);
      expect(status.overallCompliance).toBeGreaterThan(0);
      expect(status.overallCompliance).toBeLessThanOrEqual(100);
      expect(Array.isArray(status.ruleCompliance)).toBe(true);
      expect(status.ruleCompliance.length).toBe(mockRules.length);
      expect(Array.isArray(status.frameworkCompliance)).toBe(true);
      expect(status.trends).toBeDefined();
      expect(['improving', 'degrading', 'stable']).toContain(status.trends.direction);
      expect(Array.isArray(status.alerts)).toBe(true);
      expect(status.metadata).toBeDefined();
    });

    test('should detect compliance violations', async () => {
      const ruleId = 'rule_001';
      const threshold = 0.5;

      const violations: TComplianceViolation[] = await complianceReporter.detectComplianceViolations(
        ruleId,
        threshold
      );

      expect(Array.isArray(violations)).toBe(true);
      violations.forEach(violation => {
        expect(violation.violationId).toMatch(/^VIO-/);
        expect(violation.ruleId).toBe(ruleId);
        expect(violation.violationType).toBeDefined();
        expect(['low', 'medium', 'high', 'critical']).toContain(violation.severity);
        expect(violation.detectedAt).toBeInstanceOf(Date);
        expect(violation.description).toBeDefined();
        expect(violation.impact).toBeDefined();
        expect(Array.isArray(violation.evidence)).toBe(true);
        expect(Array.isArray(violation.remediation)).toBe(true);
        expect(['open', 'in-progress', 'resolved', 'accepted', 'deferred']).toContain(violation.status);
        expect(violation.assignedTo).toBeDefined();
        expect(violation.dueDate).toBeInstanceOf(Date);
        expect(Array.isArray(violation.framework)).toBe(true);
      });
    });

    test('should generate audit trail', async () => {
      const trail: TAuditTrail = await complianceReporter.generateAuditTrail(
        mockComplianceScope,
        mockTimeRange
      );

      expect(trail).toBeDefined();
      expect(trail.trailId).toMatch(/^audit_trail_/);
      expect(trail.scope).toEqual(mockComplianceScope);
      expect(trail.timeRange).toEqual(mockTimeRange);
      expect(Array.isArray(trail.events)).toBe(true);
      expect(trail.summary).toBeDefined();
      expect(trail.summary.totalEvents).toBe(trail.events.length);
      expect(typeof trail.summary.eventsByType).toBe('object');
      expect(typeof trail.summary.eventsBySeverity).toBe('object');
      expect(typeof trail.summary.complianceChanges).toBe('number');
      expect(typeof trail.summary.violationsDetected).toBe('number');
      expect(trail.integrity).toBeDefined();
      expect(trail.integrity.checksum).toBeDefined();
      expect(trail.integrity.digitalSignature).toBeDefined();
      expect(typeof trail.integrity.tamperEvidence).toBe('boolean');
    });

    test('should analyze compliance trends', async () => {
      const period: TAnalyticsPeriod = 'last_30d';

      const trends: TComplianceTrends = await complianceReporter.analyzeComplianceTrends(period);

      expect(trends).toBeDefined();
      expect(trends.trendsId).toMatch(/^compliance_trends_/);
      expect(trends.period).toBe(period);
      expect(trends.overallTrend).toBeDefined();
      expect(['improving', 'degrading', 'stable']).toContain(trends.overallTrend.direction);
      expect(trends.overallTrend.rate).toBeGreaterThanOrEqual(0);
      expect(trends.overallTrend.confidence).toBeGreaterThan(0);
      expect(trends.overallTrend.confidence).toBeLessThanOrEqual(1);
      expect(Array.isArray(trends.frameworkTrends)).toBe(true);
      expect(trends.violationTrends).toBeDefined();
      expect(Array.isArray(trends.violationTrends.frequency)).toBe(true);
      expect(typeof trends.violationTrends.severity).toBe('object');
      expect(typeof trends.violationTrends.categories).toBe('object');
      expect(trends.predictions).toBeDefined();
      expect(trends.predictions.futureCompliance).toBeGreaterThan(0);
      expect(trends.predictions.futureCompliance).toBeLessThanOrEqual(100);
      expect(Array.isArray(trends.predictions.riskAreas)).toBe(true);
      expect(Array.isArray(trends.predictions.recommendedActions)).toBe(true);
    });
  });

  // ============================================================================
  // IREPORTINGSERVICE INTERFACE TESTS
  // ============================================================================

  describe('IReportingService Interface', () => {
    test('should implement IReportingService interface', () => {
      const service: IReportingService = complianceReporter;
      expect(service).toBeDefined();
      expect(typeof service.processReportingData).toBe('function');
      expect(typeof service.generateReport).toBe('function');
      expect(typeof service.getReportingMetrics).toBe('function');
      expect(typeof service.scheduleComplianceReport).toBe('function');
      expect(typeof service.distributeReport).toBe('function');
    });

    test('should process reporting data', async () => {
      const testData = { type: 'compliance', rules: mockRules };

      const result = await complianceReporter.processReportingData(testData);

      expect(result).toBeDefined();
      expect(result.processId).toMatch(/^process_/);
      expect(result.status).toBe('processed');
      expect(result.processedAt).toBeInstanceOf(Date);
      expect(result.originalData).toEqual(testData);
      expect(result.results).toBeDefined();
      expect(typeof result.results.recordsProcessed).toBe('number');
      expect(typeof result.results.complianceIssues).toBe('number');
      expect(typeof result.results.violations).toBe('number');
      expect(typeof result.results.recommendations).toBe('number');
    });

    test('should generate report from configuration', async () => {
      const config = { format: 'pdf', type: 'compliance', scope: mockComplianceScope };

      const result = await complianceReporter.generateReport(config);

      expect(result).toBeDefined();
      expect(result.reportId).toMatch(/^compliance_report_/);
      expect(result.generatedAt).toBeInstanceOf(Date);
      expect(result.config).toEqual(config);
      expect(result.status).toBe('completed');
      expect(result.output).toBeDefined();
      expect(result.output.format).toBe('pdf');
      expect(typeof result.output.size).toBe('number');
      expect(result.output.checksum).toBeDefined();
      expect(result.output.location).toBeDefined();
    });

    test('should get reporting metrics', async () => {
      const metrics = await complianceReporter.getReportingMetrics();

      expect(metrics).toBeDefined();
      expect(typeof metrics.totalReports).toBe('number');
      expect(typeof metrics.successfulReports).toBe('number');
      expect(typeof metrics.failedReports).toBe('number');
      expect(typeof metrics.averageProcessingTime).toBe('number');
      expect(typeof metrics.cacheHitRate).toBe('number');
      expect(typeof metrics.performanceScore).toBe('number');
      expect(metrics.lastUpdated).toBeInstanceOf(Date);
      expect(typeof metrics.complianceScore).toBe('number');
      expect(typeof metrics.violationsDetected).toBe('number');
      expect(typeof metrics.auditTrailsGenerated).toBe('number');
    });

    test('should schedule compliance report', async () => {
      const schedule = {
        name: 'Monthly Compliance Report',
        frequency: 'monthly',
        format: 'pdf' as TReportFormat,
        scope: mockComplianceScope
      };

      const result = await complianceReporter.scheduleComplianceReport(schedule);

      expect(result).toBeDefined();
      expect(result.scheduleId).toMatch(/^schedule_/);
      expect(result.reportSchedule).toEqual(schedule);
      expect(result.status).toBe('scheduled');
      expect(result.nextExecution).toBeInstanceOf(Date);
      expect(result.createdAt).toBeInstanceOf(Date);
      expect(result.metadata).toBeDefined();
      expect(result.metadata.compliance).toBe(true);
    });

    test('should distribute report', async () => {
      const reportId = 'test_report_001';
      const distribution = {
        recipients: ['<EMAIL>', '<EMAIL>'],
        methods: ['email', 'portal']
      };

      const result = await complianceReporter.distributeReport(reportId, distribution);

      expect(result).toBeDefined();
      expect(result.distributionId).toMatch(/^distribution_/);
      expect(result.reportId).toBe(reportId);
      expect(result.distribution).toEqual(distribution);
      expect(result.status).toBe('completed');
      expect(result.distributedAt).toBeInstanceOf(Date);
      expect(Array.isArray(result.recipients)).toBe(true);
      expect(Array.isArray(result.deliveryMethods)).toBe(true);
      expect(result.metadata).toBeDefined();
    });
  });

  // ============================================================================
  // REGULATORY FRAMEWORK TESTS
  // ============================================================================

  describe('Regulatory Framework Support', () => {
    const frameworks: TRegulatoryFramework[] = ['SOX', 'GDPR', 'HIPAA', 'ISO27001', 'PCI-DSS', 'NIST', 'COBIT', 'ITIL'];

    test.each(frameworks)('should support %s framework', async (framework) => {
      const options: TRegulatoryOptions = {
        framework,
        sections: ['Section 1', 'Section 2'],
        evidenceLevel: 'standard',
        includeGaps: true,
        includeRecommendations: true,
        auditReadiness: false,
        timeRange: mockTimeRange,
        metadata: { framework }
      };

      const report = await complianceReporter.generateRegulatoryReport(framework, options);

      expect(report).toBeDefined();
      expect(report.framework).toBe(framework);
      expect(report.complianceScore).toBeGreaterThan(0);
      expect(report.sections.frameworkOverview.framework).toBe(framework);
    });

    test('should handle multiple frameworks in compliance scope', async () => {
      const multiFrameworkScope: TComplianceScope = {
        ...mockComplianceScope,
        regulatoryFrameworks: ['SOX', 'GDPR', 'HIPAA', 'ISO27001']
      };

      const report = await complianceReporter.generateComplianceReport(multiFrameworkScope, 'json');

      expect(report).toBeDefined();
      expect(report.scope.regulatoryFrameworks).toHaveLength(4);
      expect(report.scope.regulatoryFrameworks).toContain('SOX');
      expect(report.scope.regulatoryFrameworks).toContain('GDPR');
      expect(report.scope.regulatoryFrameworks).toContain('HIPAA');
      expect(report.scope.regulatoryFrameworks).toContain('ISO27001');
    });
  });

  // ============================================================================
  // REPORT FORMAT TESTS
  // ============================================================================

  describe('Report Format Support', () => {
    const formats: TReportFormat[] = ['pdf', 'csv', 'json', 'xml', 'excel', 'markdown', 'html', 'docx'];

    test.each(formats)('should generate report in %s format', async (format) => {
      const report = await complianceReporter.generateComplianceReport(mockComplianceScope, format);

      expect(report).toBeDefined();
      expect(report.format).toBe(format);
      expect(report.reportId).toMatch(/^compliance_report_/);
    });

    test('should handle format-specific requirements', async () => {
      // Test JSON format for structured data
      const jsonReport = await complianceReporter.generateComplianceReport(mockComplianceScope, 'json');
      expect(jsonReport.format).toBe('json');

      // Test PDF format for formal reports
      const pdfReport = await complianceReporter.generateComplianceReport(mockComplianceScope, 'pdf');
      expect(pdfReport.format).toBe('pdf');
      expect(pdfReport.metadata.confidentiality).toBe('confidential');

      // Test CSV format for data analysis
      const csvReport = await complianceReporter.generateComplianceReport(mockComplianceScope, 'csv');
      expect(csvReport.format).toBe('csv');
    });
  });

  // ============================================================================
  // PERFORMANCE TESTS
  // ============================================================================

  describe('Performance Tests', () => {
    test('should generate compliance report within acceptable time', async () => {
      const startTime = Date.now();

      await complianceReporter.generateComplianceReport(mockComplianceScope, 'pdf');

      const executionTime = Date.now() - startTime;
      expect(executionTime).toBeLessThan(10000); // Should complete within 10 seconds
    });

    test('should handle concurrent report generation', async () => {
      const promises = Array.from({ length: 5 }, (_, i) =>
        complianceReporter.generateComplianceReport(mockComplianceScope, 'json')
      );

      const reports = await Promise.all(promises);

      expect(reports).toHaveLength(5);
      reports.forEach(report => {
        expect(report).toBeDefined();
        expect(report.reportId).toMatch(/^compliance_report_/);
      });

      // Verify all reports have unique IDs
      const reportIds = reports.map(r => r.reportId);
      const uniqueIds = new Set(reportIds);
      expect(uniqueIds.size).toBe(5);
    });

    test('should maintain performance under load', async () => {
      const iterations = 10;
      const executionTimes: number[] = [];

      for (let i = 0; i < iterations; i++) {
        const startTime = Date.now();
        await complianceReporter.detectComplianceViolations('rule_001', 0.5);
        executionTimes.push(Date.now() - startTime);
      }

      const averageTime = executionTimes.reduce((sum, time) => sum + time, 0) / iterations;
      expect(averageTime).toBeLessThan(5000); // Average should be under 5 seconds

      // Performance should not degrade significantly
      const firstHalf = executionTimes.slice(0, 5);
      const secondHalf = executionTimes.slice(5);
      const firstAvg = firstHalf.reduce((sum, time) => sum + time, 0) / firstHalf.length;
      const secondAvg = secondHalf.reduce((sum, time) => sum + time, 0) / secondHalf.length;
      
      // Ensure we have valid averages before comparison
      if (firstAvg > 0 && secondAvg > 0) {
        expect(secondAvg / firstAvg).toBeLessThan(2); // Should not be more than 2x slower
      } else {
        // If timing data is invalid, just verify the operations completed
        expect(executionTimes.length).toBe(10);
      }
    });
  });

  // ============================================================================
  // ERROR HANDLING TESTS
  // ============================================================================

  describe('Error Handling', () => {
    test('should handle invalid compliance scope gracefully', async () => {
      const invalidScope = {
        ...mockComplianceScope,
        ruleCategories: [], // Empty categories
        regulatoryFrameworks: [] // Empty frameworks
      };

      await expect(
        complianceReporter.generateComplianceReport(invalidScope, 'pdf')
      ).resolves.toBeDefined();
    });

    test('should handle invalid rule ID in violation detection', async () => {
      const invalidRuleId = '';

      const violations = await complianceReporter.detectComplianceViolations(invalidRuleId, 0.5);

      expect(Array.isArray(violations)).toBe(true);
      // Should handle gracefully, possibly returning empty array
    });

    test('should handle invalid time range in audit trail', async () => {
      const invalidTimeRange: TTimeRange = {
        startDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // Future date
        endDate: new Date(Date.now() - 24 * 60 * 60 * 1000), // Past date (invalid range)
        granularity: 'day'
      };

      await expect(
        complianceReporter.generateAuditTrail(mockComplianceScope, invalidTimeRange)
      ).resolves.toBeDefined();
    });

    test('should handle service shutdown during operation', async () => {
      // Start a long-running operation
      const reportPromise = complianceReporter.generateComplianceReport(mockComplianceScope, 'pdf');

      // Shutdown the service
      await complianceReporter.shutdown();

      // The operation should still complete or handle shutdown gracefully
      await expect(reportPromise).resolves.toBeDefined();
    });
  });

  // ============================================================================
  // INTEGRATION TESTS
  // ============================================================================

  describe('Integration Tests', () => {
    test('should integrate compliance reporting with violation detection', async () => {
      // First detect violations
      const violations = await complianceReporter.detectComplianceViolations('rule_001', 0.3);

      // Then generate a compliance report that should include these violations
      const report = await complianceReporter.generateComplianceReport(mockComplianceScope, 'json');

      expect(report).toBeDefined();
      expect(report.sections.violationAnalysis).toBeDefined();
      
      // The report should reflect the current violation state
      expect(typeof report.sections.violationAnalysis.totalViolations).toBe('number');
    });

    test('should integrate audit trail with compliance trends', async () => {
      // Generate audit trail
      const trail = await complianceReporter.generateAuditTrail(mockComplianceScope, mockTimeRange);

      // Analyze trends
      const trends = await complianceReporter.analyzeComplianceTrends('last_7d');

      expect(trail).toBeDefined();
      expect(trends).toBeDefined();
      
      // Both should be consistent in their time-based analysis
      expect(trail.events.length).toBeGreaterThan(0);
      expect(trends.violationTrends.frequency.length).toBeGreaterThan(0);
    });

    test('should integrate regulatory reporting with compliance monitoring', async () => {
      // Monitor compliance status
      const status = await complianceReporter.monitorComplianceStatus(mockRules);

      // Generate regulatory report
      const regulatoryReport = await complianceReporter.generateRegulatoryReport('SOX', {
        framework: 'SOX',
        sections: ['Section 404'],
        evidenceLevel: 'standard',
        includeGaps: true,
        includeRecommendations: true,
        auditReadiness: true,
        timeRange: mockTimeRange,
        metadata: {}
      });

      expect(status).toBeDefined();
      expect(regulatoryReport).toBeDefined();
      
      // Both should provide consistent compliance scoring
      expect(status.overallCompliance).toBeGreaterThan(0);
      expect(regulatoryReport.complianceScore).toBeGreaterThan(0);
    });
  });

  // ============================================================================
  // EDGE CASE TESTS
  // ============================================================================

  describe('Edge Cases', () => {
    test('should handle empty rule set in compliance monitoring', async () => {
      const emptyRules: TGovernanceRule[] = [];

      const status = await complianceReporter.monitorComplianceStatus(emptyRules);

      expect(status).toBeDefined();
      expect(status.ruleCompliance).toHaveLength(0);
      expect(status.overallCompliance).toBeGreaterThanOrEqual(0);
    });

    test('should handle very large compliance scope', async () => {
      const largeScope: TComplianceScope = {
        ruleCategories: Array.from({ length: 100 }, (_, i) => `category_${i}`),
        severityLevels: ['low', 'medium', 'high', 'critical'],
        timeRange: {
          startDate: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000), // 1 year ago
          endDate: new Date(),
          granularity: 'day'
        },
        includeViolations: true,
        includeRemediation: true,
        regulatoryFrameworks: ['SOX', 'GDPR', 'HIPAA', 'ISO27001', 'PCI-DSS', 'NIST', 'COBIT', 'ITIL'],
        organizationalUnits: Array.from({ length: 50 }, (_, i) => `unit_${i}`),
        riskLevels: ['low', 'medium', 'high', 'critical']
      };

      const report = await complianceReporter.generateComplianceReport(largeScope, 'json');

      expect(report).toBeDefined();
      expect(report.scope.ruleCategories).toHaveLength(100);
      expect(report.scope.organizationalUnits).toHaveLength(50);
    });

    test('should handle high violation threshold', async () => {
      const violations = await complianceReporter.detectComplianceViolations('rule_001', 0.99);

      expect(Array.isArray(violations)).toBe(true);
      // With high threshold, should likely return few or no violations
    });

    test('should handle zero violation threshold', async () => {
      const violations = await complianceReporter.detectComplianceViolations('rule_001', 0.0);

      expect(Array.isArray(violations)).toBe(true);
      // With zero threshold, might return violations based on implementation
    });
  });
}); 