/**
 * ============================================================================
 * Governance Rule Report Scheduler Factory Tests - Phase 6 Implementation
 * Purpose: Comprehensive test suite for Rule Report Scheduler Factory
 * Author: OA Framework Team
 * Created: 2025-01-27 11:23:37 EST
 * ============================================================================
 */

import { 
  GovernanceRuleReportSchedulerFactory,
  IReportSchedulerFactory,
  TSchedulerConfig,
  TFactoryMetrics
} from '../GovernanceRuleReportSchedulerFactory';

import { 
  GovernanceRuleReportScheduler,
  IReportScheduler 
} from '../GovernanceRuleReportScheduler';

import { TValidationResult } from '../../../../../../shared/src/types/platform/tracking/core/tracking-data-types';

// ============================================================================
// TEST SETUP
// ============================================================================

describe('GovernanceRuleReportSchedulerFactory', () => {
  let factory: GovernanceRuleReportSchedulerFactory;

  beforeEach(async () => {
    factory = GovernanceRuleReportSchedulerFactory.getInstance();
    await factory.initialize();
  });

  afterEach(async () => {
    if (factory) {
      await factory.shutdown();
    }
    // Clean up all mocks
    jest.restoreAllMocks();
  });

  // ============================================================================
  // BASIC FUNCTIONALITY TESTS
  // ============================================================================

  describe('Basic Functionality', () => {
    
    test('should be a singleton', () => {
      const factory1 = GovernanceRuleReportSchedulerFactory.getInstance();
      const factory2 = GovernanceRuleReportSchedulerFactory.getInstance();
      
      expect(factory1).toBe(factory2);
    });

    test('should initialize successfully', async () => {
      const newFactory = GovernanceRuleReportSchedulerFactory.getInstance();
      await expect(newFactory.initialize()).resolves.not.toThrow();
    });

    test('should validate configuration successfully', async () => {
      const validation = await factory.validateConfiguration();
      
      expect(validation).toBeDefined();
      expect(validation.status).toBe('valid');
      expect(validation.errors).toHaveLength(0);
      expect(validation.timestamp).toBeDefined();
    });

    test('should provide factory metrics', async () => {
      const metrics = await factory.getFactoryMetrics();
      
      expect(metrics).toBeDefined();
      expect(metrics.totalInstancesCreated).toBeGreaterThanOrEqual(0);
      expect(metrics.activeInstances).toBeGreaterThanOrEqual(0);
      expect(metrics.destroyedInstances).toBeGreaterThanOrEqual(0);
      expect(metrics.factoryUptime).toBeGreaterThanOrEqual(0);
      expect(metrics.resourceUtilization).toBeDefined();
      expect(metrics.instancePerformance).toBeDefined();
      expect(metrics.lastUpdated).toBeInstanceOf(Date);
    });
  });

  // ============================================================================
  // INSTANCE CREATION TESTS
  // ============================================================================

  describe('Instance Creation', () => {
    
    test('should create scheduler instance successfully', async () => {
      const scheduler = await factory.createScheduler();
      
      expect(scheduler).toBeDefined();
      expect(scheduler).toBeInstanceOf(GovernanceRuleReportScheduler);
      expect(scheduler.id).toBe('governance-rule-report-scheduler');
      expect((scheduler as any).name).toBe('Governance Rule Report Scheduler');
    });

    test('should create scheduler with custom configuration', async () => {
      const config: TSchedulerConfig = {
        instanceId: 'custom-scheduler-001',
        maxConcurrentExecutions: 5,
        enableOptimization: true,
        enableConflictResolution: true,
        enableMetrics: true,
        enableLogging: true,
        customSettings: {
          customSetting1: 'value1',
          customSetting2: 42
        }
      };
      
      const scheduler = await factory.createScheduler(config);
      
      expect(scheduler).toBeDefined();
      expect(scheduler).toBeInstanceOf(GovernanceRuleReportScheduler);
    });

    test('should return existing instance for same ID', async () => {
      const config: TSchedulerConfig = {
        instanceId: 'same-id-test'
      };
      
      const scheduler1 = await factory.createScheduler(config);
      const scheduler2 = await factory.createScheduler(config);
      
      expect(scheduler1).toBe(scheduler2);
    });

    test('should track instance creation metrics', async () => {
      const initialMetrics = await factory.getFactoryMetrics();
      const initialCount = initialMetrics.totalInstancesCreated;
      
      await factory.createScheduler();
      
      const updatedMetrics = await factory.getFactoryMetrics();
      expect(updatedMetrics.totalInstancesCreated).toBe(initialCount + 1);
      expect(updatedMetrics.activeInstances).toBeGreaterThan(0);
    });

    test('should handle multiple concurrent creations', async () => {
      const createPromises: Promise<IReportScheduler>[] = [];
      
      for (let i = 0; i < 5; i++) {
        createPromises.push(factory.createScheduler({
          instanceId: `concurrent-${i}`
        }));
      }
      
      const schedulers = await Promise.all(createPromises);
      
      expect(schedulers).toHaveLength(5);
      schedulers.forEach(scheduler => {
        expect(scheduler).toBeInstanceOf(GovernanceRuleReportScheduler);
      });
    });
  });

  // ============================================================================
  // INSTANCE RETRIEVAL TESTS
  // ============================================================================

  describe('Instance Retrieval', () => {
    
    test('should get existing scheduler instance', async () => {
      const config: TSchedulerConfig = {
        instanceId: 'retrieval-test-001'
      };
      
      const createdScheduler = await factory.createScheduler(config);
      const retrievedScheduler = await factory.getScheduler(config.instanceId);
      
      expect(retrievedScheduler).toBe(createdScheduler);
    });

    test('should get default scheduler when no ID specified', async () => {
      await factory.createScheduler();
      
      const scheduler = await factory.getScheduler();
      
      expect(scheduler).toBeDefined();
      expect(scheduler).toBeInstanceOf(GovernanceRuleReportScheduler);
    });

    test('should create new instance if none exist and no ID specified', async () => {
      const scheduler = await factory.getScheduler();
      
      expect(scheduler).toBeDefined();
      expect(scheduler).toBeInstanceOf(GovernanceRuleReportScheduler);
    });

    test('should throw error for non-existent instance ID', async () => {
      await expect(factory.getScheduler('non-existent-id'))
        .rejects.toThrow('Scheduler instance not found: non-existent-id');
    });
  });

  // ============================================================================
  // INSTANCE DESTRUCTION TESTS
  // ============================================================================

  describe('Instance Destruction', () => {
    
    test('should destroy scheduler instance successfully', async () => {
      const config: TSchedulerConfig = {
        instanceId: 'destruction-test-001'
      };
      
      await factory.createScheduler(config);
      const result = await factory.destroyScheduler(config.instanceId!);
      
      expect(result).toBe(true);
    });

    test('should return false for non-existent instance', async () => {
      const result = await factory.destroyScheduler('non-existent-id');
      
      expect(result).toBe(false);
    });

    test('should update metrics on instance destruction', async () => {
      const config: TSchedulerConfig = {
        instanceId: 'metrics-destruction-test'
      };
      
      await factory.createScheduler(config);
      const initialMetrics = await factory.getFactoryMetrics();
      
      await factory.destroyScheduler(config.instanceId!);
      
      const updatedMetrics = await factory.getFactoryMetrics();
      expect(updatedMetrics.destroyedInstances).toBeGreaterThan(initialMetrics.destroyedInstances);
      expect(updatedMetrics.activeInstances).toBeLessThan(initialMetrics.activeInstances);
    });

    test('should handle graceful shutdown of instance', async () => {
      const config: TSchedulerConfig = {
        instanceId: 'graceful-shutdown-test'
      };
      
      const scheduler = await factory.createScheduler(config);
      
      // Mock shutdown method
      const shutdownSpy = jest.spyOn(scheduler as any, 'shutdown')
        .mockResolvedValue(undefined);
      
      const result = await factory.destroyScheduler(config.instanceId!);
      
      expect(result).toBe(true);
      expect(shutdownSpy).toHaveBeenCalled();
    });
  });

  // ============================================================================
  // ACTIVE INSTANCES TESTS
  // ============================================================================

  describe('Active Instances Management', () => {
    
    test('should list all active schedulers', async () => {
      // Create multiple instances
      await factory.createScheduler({ instanceId: 'active-1' });
      await factory.createScheduler({ instanceId: 'active-2' });
      await factory.createScheduler({ instanceId: 'active-3' });
      
      const activeSchedulers = factory.getActiveSchedulers();
      
      expect(activeSchedulers).toHaveLength(3);
      activeSchedulers.forEach(scheduler => {
        expect(scheduler).toBeInstanceOf(GovernanceRuleReportScheduler);
      });
    });

    test('should update active list after destruction', async () => {
      await factory.createScheduler({ instanceId: 'active-destroy-1' });
      await factory.createScheduler({ instanceId: 'active-destroy-2' });
      
      let activeSchedulers = factory.getActiveSchedulers();
      expect(activeSchedulers.length).toBeGreaterThanOrEqual(2);
      
      await factory.destroyScheduler('active-destroy-1');
      
      activeSchedulers = factory.getActiveSchedulers();
      expect(activeSchedulers.length).toBeGreaterThanOrEqual(1);
    });
  });

  // ============================================================================
  // CONFIGURATION VALIDATION TESTS
  // ============================================================================

  describe('Configuration Validation', () => {
    
    test('should validate valid configuration', async () => {
      const validation = await factory.validateConfiguration();
      
      expect(validation.status).toBe('valid');
      expect(validation.errors).toHaveLength(0);
    });

    test('should detect configuration warnings', async () => {
      // Create many instances to potentially trigger warnings
      const promises: Promise<IReportScheduler>[] = [];
      for (let i = 0; i < 15; i++) {
        promises.push(factory.createScheduler({ instanceId: `warning-test-${i}` }));
      }
      
      await Promise.all(promises);
      
      const validation = await factory.validateConfiguration();
      
      // Might have warnings about exceeding recommended limits
      expect(validation.timestamp).toBeDefined();
    });
  });

  // ============================================================================
  // PERFORMANCE TESTS
  // ============================================================================

  describe('Performance', () => {
    
    test('should handle rapid instance creation', async () => {
      const startTime = Date.now();
      const promises: Promise<IReportScheduler>[] = [];
      
      for (let i = 0; i < 10; i++) {
        promises.push(factory.createScheduler({ instanceId: `perf-${i}` }));
      }
      
      const schedulers = await Promise.all(promises);
      const endTime = Date.now();
      
      expect(schedulers).toHaveLength(10);
      expect(endTime - startTime).toBeLessThan(5000); // Should complete in under 5 seconds
    });

    test('should maintain performance metrics', async () => {
      const metrics = await factory.getFactoryMetrics();
      
      expect(metrics.instancePerformance.averageInitTime).toBeGreaterThanOrEqual(0);
      expect(metrics.instancePerformance.averageResponseTime).toBeGreaterThanOrEqual(0);
      expect(metrics.instancePerformance.successRate).toBeGreaterThanOrEqual(0);
      expect(metrics.instancePerformance.successRate).toBeLessThanOrEqual(100);
    });
  });

  // ============================================================================
  // ERROR HANDLING TESTS
  // ============================================================================

  describe('Error Handling', () => {
    
    test('should handle initialization errors gracefully', async () => {
      // Shutdown existing factory to reset state
      await factory.shutdown();
      
      const errorFactory = GovernanceRuleReportSchedulerFactory.getInstance();
      
      // Force error during pool initialization
      const poolInitSpy = jest.spyOn(errorFactory as any, '_initializeInstancePool')
        .mockRejectedValue(new Error('Pool init failed'));
      
      await expect(errorFactory.initialize()).rejects.toThrow('Pool init failed');
      
      // Clean up the mock
      poolInitSpy.mockRestore();
    });

    test('should handle instance creation errors', async () => {
      // Empty the instance pool to force new instance creation
      (factory as any)._instancePool.length = 0;
      
      // Mock instance creation to throw error
      const createInstanceSpy = jest.spyOn(factory as any, '_createNewSchedulerInstance')
        .mockRejectedValue(new Error('Instance creation failed'));
      
      // Use a unique instance ID to force new instance creation
      await expect(factory.createScheduler({ instanceId: 'error-test-unique-id' }))
        .rejects.toThrow('Instance creation failed');
      
      // Clean up the mock
      createInstanceSpy.mockRestore();
    });

    test('should handle shutdown during operations', async () => {
      const config: TSchedulerConfig = {
        instanceId: 'shutdown-during-ops'
      };
      
      // Start shutdown
      const shutdownPromise = factory.shutdown();
      
      // Try to create instance during shutdown
      await expect(factory.createScheduler(config))
        .rejects.toThrow('Factory is shutting down');
      
      await shutdownPromise;
    });
  });

  // ============================================================================
  // FACTORY LIFECYCLE TESTS
  // ============================================================================

  describe('Factory Lifecycle', () => {
    
    test('should shutdown gracefully', async () => {
      // Create some instances
      await factory.createScheduler({ instanceId: 'lifecycle-1' });
      await factory.createScheduler({ instanceId: 'lifecycle-2' });
      
      await expect(factory.shutdown()).resolves.not.toThrow();
      
      // Verify cleanup
      const activeSchedulers = factory.getActiveSchedulers();
      expect(activeSchedulers).toHaveLength(0);
    });

    test('should handle multiple shutdown calls', async () => {
      await factory.createScheduler();
      
      // Multiple shutdown calls should not cause issues
      await factory.shutdown();
      await expect(factory.shutdown()).resolves.not.toThrow();
    });

    test('should prevent operations after shutdown', async () => {
      await factory.shutdown();
      
      await expect(factory.createScheduler())
        .rejects.toThrow('Factory is shutting down');
    });
  });

  // ============================================================================
  // INTEGRATION TESTS
  // ============================================================================

  describe('Integration', () => {
    
    test('should create functional scheduler instances', async () => {
      const scheduler = await factory.createScheduler();
      
      // Test basic scheduler functionality
      expect(typeof scheduler.scheduleReport).toBe('function');
      expect(typeof scheduler.getSchedulingAnalytics).toBe('function');
      expect(typeof scheduler.optimizeSchedules).toBe('function');
    });

    test('should handle scheduler operations through factory', async () => {
      const scheduler = await factory.createScheduler();
      
      // Test that we can perform operations on the created scheduler
      const analytics = await scheduler.getSchedulingAnalytics();
      expect(analytics).toBeDefined();
      expect(analytics.overview).toBeDefined();
    });

    test('should maintain instance isolation', async () => {
      const scheduler1 = await factory.createScheduler({ instanceId: 'isolation-1' });
      const scheduler2 = await factory.createScheduler({ instanceId: 'isolation-2' });
      
      expect(scheduler1).not.toBe(scheduler2);
      
      // Verify they're independent instances
      const analytics1 = await scheduler1.getSchedulingAnalytics();
      const analytics2 = await scheduler2.getSchedulingAnalytics();
      
      expect(analytics1).toBeDefined();
      expect(analytics2).toBeDefined();
    });
  });

  // ============================================================================
  // RESOURCE MANAGEMENT TESTS
  // ============================================================================

  describe('Resource Management', () => {
    
    test('should track resource utilization', async () => {
      const metrics = await factory.getFactoryMetrics();
      
      expect(metrics.resourceUtilization).toBeDefined();
      expect(metrics.resourceUtilization.memory).toBeGreaterThanOrEqual(0);
      expect(metrics.resourceUtilization.cpu).toBeGreaterThanOrEqual(0);
    });

    test('should manage instance pool efficiently', async () => {
      // Create and destroy instances to test pooling
      const config1: TSchedulerConfig = { instanceId: 'pool-test-1' };
      const config2: TSchedulerConfig = { instanceId: 'pool-test-2' };
      
      const scheduler1 = await factory.createScheduler(config1);
      await factory.destroyScheduler(config1.instanceId!);
      
      const scheduler2 = await factory.createScheduler(config2);
      
      expect(scheduler2).toBeDefined();
      expect(scheduler2).toBeInstanceOf(GovernanceRuleReportScheduler);
    });
  });
});

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

function createMockSchedulerConfig(overrides?: Partial<TSchedulerConfig>): TSchedulerConfig {
  return {
    instanceId: 'mock-scheduler-001',
    maxConcurrentExecutions: 10,
    enableOptimization: true,
    enableConflictResolution: true,
    enableMetrics: true,
    enableLogging: true,
    customSettings: {
      mockSetting: 'mockValue'
    },
    ...overrides
  };
} 