/**
 * ============================================================================
 * AI CONTEXT: Reporting Infrastructure Module Exports
 * Purpose: Centralized exports for governance rule reporting infrastructure components
 * Complexity: Simple - Module index for component exports
 * AI Navigation: 1 logical section, module exports domain
 * ============================================================================
 */

// ============================================================================
// MAIN COMPONENTS
// ============================================================================

export {
  GovernanceRuleDashboardGenerator,
  IDashboardGenerator,
  IReportingService,
  TDashboardGeneratorData,
  TDashboardGeneratorConfig,
  TWidgetConfig,
  TVisualizationConfig,
  TWidgetFilter,
  TWidgetThreshold,
  TDashboardPermissions,
  TTimeRange,
  TExecutiveLevel,
  TDashboardTemplate,
  TTemplateVariable,
  TExportFormat,
  TExportResult,
  TReportingData,
  TReportingResult,
  TReportConfig,
  TDashboardReport,
  TReportingMetrics
} from './GovernanceRuleDashboardGenerator';

// ============================================================================
// FACTORY COMPONENTS
// ============================================================================

export {
  GovernanceRuleDashboardGeneratorFactory,
  TDashboardGeneratorFactoryConfig,
  TDashboardGeneratorInstanceConfig,
  createDashboardGenerator,
  createConfiguredDashboardGenerator,
  releaseDashboardGenerator,
  getFactoryStats
} from './GovernanceRuleDashboardGeneratorFactory';

// ============================================================================
// PHASE 6: RULE REPORT SCHEDULER COMPONENTS
// ============================================================================

export {
  GovernanceRuleReportScheduler,
  IReportScheduler,
  TReportSchedulerData
} from './GovernanceRuleReportScheduler';

// Re-export shared types from automation-processing-types
export type {
  TReportSchedule,
  TRecurringReportSchedule,
  TBatchReportSchedule,
  TScheduleDefinition,
  TOutputConfiguration,
  TDeliveryConfiguration,
  TScheduleReportResult,
  TSchedulingMetrics,
  TOptimizationResult,
  TReportType,
  TScheduleType,
  TSchedulePriority,
  TScheduleStatus,
  TReportFormat,
  TDeliveryMethod,
  TBatchExecutionMode,
  TOptimizationType
} from '../../../../../shared/src/types/platform/governance/automation-processing-types';

export {
  GovernanceRuleReportSchedulerFactory,
  IReportSchedulerFactory,
  TSchedulerConfig,
  TFactoryMetrics
} from './GovernanceRuleReportSchedulerFactory';

// ============================================================================
// PHASE 7: RULE ALERT MANAGER COMPONENTS
// ============================================================================

export {
  GovernanceRuleAlertManager,
  IAlertManagerData,
  IAlertManagerConfig
} from './GovernanceRuleAlertManager';

export {
  GovernanceRuleAlertManagerFactory,
  IAlertManagerFactory,
  TAlertManagerConfig
} from './GovernanceRuleAlertManagerFactory';

// ============================================================================
// PHASE 8: RULE COMPLIANCE REPORTER COMPONENTS (FINAL PHASE)
// ============================================================================

export {
  GovernanceRuleComplianceReporter,
  IComplianceReporter,
  TComplianceReporterData,
  TComplianceScope,
  TRegulatoryFramework,
  TRegulatoryOptions,
  TComplianceReport,
  TRegulatoryReport,
  TComplianceStatus,
  TComplianceViolation,
  TAuditTrail,
  TComplianceTrends,
  TComplianceMetrics
} from './GovernanceRuleComplianceReporter';

export {
  GovernanceRuleComplianceReporterFactory,
  TComplianceReporterConfig,
  TFactoryMetrics as TComplianceFactoryMetrics,
  TInstanceInfo
} from './GovernanceRuleComplianceReporterFactory';

// ============================================================================
// MODULE METADATA
// ============================================================================

/**
 * Reporting Infrastructure Module Information
 */
export const REPORTING_INFRASTRUCTURE_MODULE = {
  name: 'governance-reporting-infrastructure',
  version: '1.0.0',
  description: 'Advanced reporting and dashboard capabilities for governance rule infrastructure',
  components: [
    'GovernanceRuleDashboardGenerator',
    'GovernanceRuleDashboardGeneratorFactory',
    'GovernanceRuleReportScheduler',
    'GovernanceRuleReportSchedulerFactory',
    'GovernanceRuleAlertManager',
    'GovernanceRuleAlertManagerFactory',
    'GovernanceRuleComplianceReporter',
    'GovernanceRuleComplianceReporterFactory'
  ],
  capabilities: [
    'Multi-type dashboard generation',
    'Advanced widget configuration',
    'Real-time data integration',
    'Export capabilities',
    'Template-based customization',
    'Enterprise permissions',
    'Performance monitoring',
    'Advanced report scheduling',
    'Recurring report automation',
    'Batch report processing',
    'Schedule optimization',
    'Conflict resolution',
    'Resource management',
    'Immediate report execution',
    'Real-time alert management',
    'Multi-channel notifications',
    'Alert escalation workflows',
    'Compliance reporting',
    'Regulatory framework support',
    'Audit trail generation',
    'Violation detection',
    'Compliance trend analysis',
    'Executive compliance dashboards'
  ],
  interfaces: [
    'IDashboardGenerator',
    'IReportingService',
    'IUIService',
    'IReportScheduler',
    'ISchedulingService',
    'IAlertManagerData',
    'IAlertManagerConfig',
    'IAlertManagerFactory',
    'IComplianceReporter'
  ],
  phases: {
    'Phase 5': ['GovernanceRuleDashboardGenerator', 'GovernanceRuleDashboardGeneratorFactory'],
    'Phase 6': ['GovernanceRuleReportScheduler', 'GovernanceRuleReportSchedulerFactory'],
    'Phase 7': ['GovernanceRuleAlertManager', 'GovernanceRuleAlertManagerFactory'],
    'Phase 8': ['GovernanceRuleComplianceReporter', 'GovernanceRuleComplianceReporterFactory']
  },
  authority: 'President & CEO, E.Z. Consultancy',
  compliance: 'OA Framework Governance Standards v2.1'
} as const;