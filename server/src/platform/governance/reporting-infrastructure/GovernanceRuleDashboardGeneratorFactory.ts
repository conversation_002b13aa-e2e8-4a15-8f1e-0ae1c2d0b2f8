/**
 * ============================================================================
 * AI CONTEXT: Rule Dashboard Generator Factory - Factory pattern for dashboard generation
 * Purpose: Factory for creating and managing Rule Dashboard Generator instances
 * Complexity: Moderate - Factory pattern with configuration management
 * AI Navigation: 3 logical sections, factory pattern domain
 * ============================================================================
 */

import { GovernanceRuleDashboardGenerator, TDashboardGeneratorConfig } from './GovernanceRuleDashboardGenerator';
import { DEFAULT_TRACKING_CONFIG } from '../../../../../shared/src/constants/platform/tracking/tracking-constants';

// ============================================================================
// FACTORY CONFIGURATION TYPES
// ============================================================================

export type TDashboardGeneratorFactoryConfig = {
  /** Factory identifier */
  factoryId: string;
  /** Enable debug mode */
  debug: boolean;
  /** Log level */
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  /** Maximum dashboard instances */
  maxInstances: number;
  /** Instance pool size */
  poolSize: number;
  /** Dashboard generation timeout */
  generationTimeout: number;
  /** Enable caching */
  enableCaching: boolean;
  /** Cache TTL */
  cacheTTL: number;
  /** Custom configuration */
  custom: Record<string, any>;
};

export type TDashboardGeneratorInstanceConfig = {
  /** Instance identifier */
  instanceId?: string;
  /** Dashboard type */
  dashboardType?: 'governance' | 'performance' | 'compliance' | 'executive' | 'custom';
  /** Performance optimization */
  enableOptimization?: boolean;
  /** Real-time updates */
  enableRealTime?: boolean;
  /** Export capabilities */
  enableExport?: boolean;
  /** Custom settings */
  customSettings?: Record<string, any>;
};

// ============================================================================
// FACTORY IMPLEMENTATION
// ============================================================================

/**
 * Governance Rule Dashboard Generator Factory
 * 
 * Factory class for creating and managing Rule Dashboard Generator instances.
 * Provides centralized configuration and instance management for dashboard
 * generation components across the governance infrastructure.
 * 
 * Features:
 * - Instance pool management
 * - Configuration validation
 * - Performance optimization
 * - Resource cleanup
 * - Logging and monitoring
 * 
 * @class GovernanceRuleDashboardGeneratorFactory
 */
export class GovernanceRuleDashboardGeneratorFactory {
  private static _instance: GovernanceRuleDashboardGeneratorFactory | null = null;
  private _factoryConfig: TDashboardGeneratorFactoryConfig;
  private _instances: Map<string, GovernanceRuleDashboardGenerator> = new Map();
  private _instancePool: GovernanceRuleDashboardGenerator[] = [];
  private _isInitialized: boolean = false;

  // ============================================================================
  // CONSTRUCTOR AND SINGLETON
  // ============================================================================

  private constructor(config?: Partial<TDashboardGeneratorFactoryConfig>) {
    this._factoryConfig = {
      factoryId: 'governance-rule-dashboard-generator-factory',
      debug: false,
      logLevel: 'info',
      maxInstances: 50,
      poolSize: 5,
      generationTimeout: 30000, // 30 seconds
      enableCaching: true,
      cacheTTL: 300000, // 5 minutes
      custom: {},
      ...config
    };
  }

  /**
   * Get factory singleton instance
   */
  public static getInstance(config?: Partial<TDashboardGeneratorFactoryConfig>): GovernanceRuleDashboardGeneratorFactory {
    if (!GovernanceRuleDashboardGeneratorFactory._instance) {
      GovernanceRuleDashboardGeneratorFactory._instance = new GovernanceRuleDashboardGeneratorFactory(config);
    }
    return GovernanceRuleDashboardGeneratorFactory._instance;
  }

  // ============================================================================
  // FACTORY OPERATIONS
  // ============================================================================

  /**
   * Initialize the factory
   */
  public async initialize(): Promise<void> {
    if (this._isInitialized) {
      return;
    }

    this._log('info', 'Initializing Dashboard Generator Factory', { factoryId: this._factoryConfig.factoryId });

    // Pre-populate instance pool
    for (let i = 0; i < this._factoryConfig.poolSize; i++) {
      const instance = new GovernanceRuleDashboardGenerator();
      await instance.initialize();
      this._instancePool.push(instance);
    }

    this._isInitialized = true;
    this._log('info', 'Dashboard Generator Factory initialized successfully', { 
      poolSize: this._instancePool.length 
    });
  }

  /**
   * Create a new dashboard generator instance
   */
  public async createDashboardGenerator(config?: TDashboardGeneratorInstanceConfig): Promise<GovernanceRuleDashboardGenerator> {
    if (!this._isInitialized) {
      await this.initialize();
    }

    const instanceId = config?.instanceId || this._generateInstanceId();

    this._log('info', 'Creating dashboard generator instance', { instanceId });

    // Check if we can reuse from pool
    if (this._instancePool.length > 0 && !config?.customSettings) {
      const instance = this._instancePool.pop()!;
      this._instances.set(instanceId, instance);
      this._log('info', 'Reused instance from pool', { instanceId });
      return instance;
    }

    // Create new instance
    if (this._instances.size >= this._factoryConfig.maxInstances) {
      throw new Error(`Maximum instances limit reached: ${this._factoryConfig.maxInstances}`);
    }

    const instance = new GovernanceRuleDashboardGenerator();
    await instance.initialize();

    this._instances.set(instanceId, instance);

    this._log('info', 'New dashboard generator instance created', { instanceId });
    return instance;
  }

  /**
   * Create dashboard generator with specific configuration
   */
  public async createConfiguredGenerator(dashboardConfig: TDashboardGeneratorConfig): Promise<GovernanceRuleDashboardGenerator> {
    const instance = await this.createDashboardGenerator({
      dashboardType: dashboardConfig.dashboardType,
      enableOptimization: true,
      enableRealTime: dashboardConfig.autoRefresh,
      enableExport: true
    });

    return instance;
  }

  /**
   * Get existing dashboard generator instance
   */
  public getDashboardGenerator(instanceId: string): GovernanceRuleDashboardGenerator | null {
    return this._instances.get(instanceId) || null;
  }

  /**
   * Release dashboard generator instance
   */
  public async releaseDashboardGenerator(instanceId: string): Promise<void> {
    const instance = this._instances.get(instanceId);
    if (!instance) {
      this._log('warn', 'Attempted to release non-existent instance', { instanceId });
      return;
    }

    this._instances.delete(instanceId);

    // Return to pool if pool has space
    if (this._instancePool.length < this._factoryConfig.poolSize) {
      this._instancePool.push(instance);
      this._log('info', 'Instance returned to pool', { instanceId });
    } else {
      await instance.shutdown();
      this._log('info', 'Instance shutdown', { instanceId });
    }
  }

  /**
   * Get factory statistics
   */
  public getFactoryStats(): Record<string, any> {
    return {
      factoryId: this._factoryConfig.factoryId,
      isInitialized: this._isInitialized,
      activeInstances: this._instances.size,
      pooledInstances: this._instancePool.length,
      maxInstances: this._factoryConfig.maxInstances,
      poolSize: this._factoryConfig.poolSize,
      config: this._factoryConfig
    };
  }

  /**
   * Shutdown factory and all instances
   */
  public async shutdown(): Promise<void> {
    this._log('info', 'Shutting down Dashboard Generator Factory');

    // Shutdown active instances
    const shutdownPromises = Array.from(this._instances.values()).map(instance => instance.shutdown());
    await Promise.all(shutdownPromises);
    this._instances.clear();

    // Shutdown pooled instances
    const poolShutdownPromises = this._instancePool.map(instance => instance.shutdown());
    await Promise.all(poolShutdownPromises);
    this._instancePool.length = 0;

    this._isInitialized = false;
    GovernanceRuleDashboardGeneratorFactory._instance = null;

    this._log('info', 'Dashboard Generator Factory shutdown complete');
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  /**
   * Validate factory configuration
   */
  public validateConfiguration(config: Partial<TDashboardGeneratorFactoryConfig>): boolean {
    const requiredFields = ['factoryId', 'maxInstances', 'poolSize'];
    
    for (const field of requiredFields) {
      if (!(field in config)) {
        this._log('error', `Missing required configuration field: ${field}`);
        return false;
      }
    }

    if (config.maxInstances! <= 0 || config.poolSize! <= 0) {
      this._log('error', 'Invalid configuration: maxInstances and poolSize must be positive');
      return false;
    }

    if (config.poolSize! > config.maxInstances!) {
      this._log('error', 'Invalid configuration: poolSize cannot exceed maxInstances');
      return false;
    }

    return true;
  }

  /**
   * Update factory configuration
   */
  public updateConfiguration(config: Partial<TDashboardGeneratorFactoryConfig>): void {
    if (!this.validateConfiguration(config)) {
      throw new Error('Invalid factory configuration');
    }

    this._factoryConfig = { ...this._factoryConfig, ...config };
    this._log('info', 'Factory configuration updated', { config });
  }

  // ============================================================================
  // PRIVATE METHODS
  // ============================================================================

  private _generateInstanceId(): string {
    return `dashboard-gen-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
  }

  private _log(level: 'debug' | 'info' | 'warn' | 'error', message: string, data?: any): void {
    if (this._shouldLog(level)) {
      const timestamp = new Date().toISOString();
      const logEntry = {
        timestamp,
        level,
        factory: this._factoryConfig.factoryId,
        message,
        ...(data && { data })
      };

      console.log(JSON.stringify(logEntry));
    }
  }

  private _shouldLog(level: 'debug' | 'info' | 'warn' | 'error'): boolean {
    const levels = ['debug', 'info', 'warn', 'error'];
    const currentLevelIndex = levels.indexOf(this._factoryConfig.logLevel);
    const messageLevelIndex = levels.indexOf(level);
    return messageLevelIndex >= currentLevelIndex;
  }
}

// ============================================================================
// FACTORY HELPER FUNCTIONS
// ============================================================================

/**
 * Create dashboard generator with default configuration
 */
export async function createDashboardGenerator(config?: TDashboardGeneratorInstanceConfig): Promise<GovernanceRuleDashboardGenerator> {
  const factory = GovernanceRuleDashboardGeneratorFactory.getInstance();
  return await factory.createDashboardGenerator(config);
}

/**
 * Create configured dashboard generator
 */
export async function createConfiguredDashboardGenerator(dashboardConfig: TDashboardGeneratorConfig): Promise<GovernanceRuleDashboardGenerator> {
  const factory = GovernanceRuleDashboardGeneratorFactory.getInstance();
  return await factory.createConfiguredGenerator(dashboardConfig);
}

/**
 * Release dashboard generator instance
 */
export async function releaseDashboardGenerator(instanceId: string): Promise<void> {
  const factory = GovernanceRuleDashboardGeneratorFactory.getInstance();
  await factory.releaseDashboardGenerator(instanceId);
}

/**
 * Get factory statistics
 */
export function getFactoryStats(): Record<string, any> {
  const factory = GovernanceRuleDashboardGeneratorFactory.getInstance();
  return factory.getFactoryStats();
} 