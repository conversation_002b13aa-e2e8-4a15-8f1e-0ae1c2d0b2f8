/**
 * @file BufferOperationsManager
 * @filepath shared/src/base/atomic-circular-buffer-enhanced/modules/BufferOperationsManager.ts
 * @task-id M-TSK-01.SUB-01.2.ENH-01.MOD-02
 * @component buffer-operations-manager
 * @reference foundation-context.MEMORY-SAFETY.007.MOD-02
 * @template enhanced-memory-safe-module
 * @tier T0
 * @context foundation-context
 * @category Memory-Safety-Enhanced-Module
 * @created 2025-07-29 07:30:00 +03
 * @modified 2025-07-29 07:30:00 +03
 *
 * @description
 * Core buffer operations management module providing:
 * - Enhanced getItem and setItem operations with access pattern tracking
 * - Performance-optimized operations with resilient timing integration
 * - Access count and timestamp tracking for strategy decisions
 * - Monotonic timestamp counter for reliable LRU ordering
 * - Integration with BufferStrategyManager for intelligent eviction
 * - Enterprise-grade error handling and monitoring
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E<PERSON><PERSON>. Consultancy"
 * @governance-adr ADR-foundation-001-tracking-architecture
 * @governance-dcr DCR-foundation-001-tracking-development
 * @governance-status approved
 * @governance-compliance authority-validated
 * @task-compliance M-TSK-01.SUB-01.2.ENH-01.MOD-02
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/base/MemorySafeResourceManager
 * @depends-on shared/src/base/utils/ResilientTiming
 * @depends-on shared/src/base/utils/ResilientMetrics
 * @integrates-with shared/src/base/AtomicCircularBufferEnhanced
 * @integrates-with BufferStrategyManager
 * @enables enhanced-buffer-operations
 * @enables access-pattern-tracking
 * @related-contexts foundation-context, memory-safety-context, enhancement-context
 * @governance-impact framework-foundation, enhanced-atomic-operations
 *
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type memory-safety-module-enhanced
 * @lifecycle-stage implementation
 * @testing-status comprehensive-test-coverage
 * @deployment-ready true
 * @monitoring-enabled true
 * @enhancement-phase phase-1
 * @backward-compatibility 100%
 * @performance-requirements <2ms-buffer-operations
 *
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 *   enhancement-validated: true
 *   anti-simplification-compliant: true
 *
 * 📝 VERSION HISTORY
 * @version-history
 * v1.0.0 (2025-07-29) - Initial modular extraction from AtomicCircularBufferEnhanced.ts
 * v1.0.0 (2025-07-29) - Added resilient timing integration for performance monitoring
 * v1.0.0 (2025-07-29) - Implemented access pattern tracking with monotonic timestamps
 */

import { MemorySafeResourceManager } from '../../MemorySafeResourceManager';
import { SimpleLogger, ILoggingService } from '../../LoggingMixin';
import { ResilientTimer } from '../../utils/ResilientTiming';
import { ResilientMetricsCollector } from '../../utils/ResilientMetrics';

// ============================================================================
// SECTION 1: OPERATIONS INTERFACES & TYPES (Lines 1-80)
// AI Context: "Buffer operations interfaces and analytics tracking types"
// ============================================================================

/**
 * Buffer analytics tracking interface
 */
export interface IBufferAnalyticsTracking {
  totalAccesses: number;
  totalHits: number;
  totalMisses: number;
  accessTimes: number[];
  accessHistory: Array<{
    timestamp: Date;
    key: string;
    hit: boolean;
  }>;
}

/**
 * Access tracking result
 */
export interface IAccessTrackingResult {
  wasHit: boolean;
  accessTime: number;
  timingReliable: boolean;
}

// ============================================================================
// SECTION 2: BUFFER OPERATIONS MANAGER CLASS (Lines 81-150)
// AI Context: "Main operations manager class with resilient timing integration"
// ============================================================================

/**
 * Core buffer operations manager with enhanced access tracking
 * 
 * Provides enterprise-grade buffer operations with:
 * - Enhanced getItem/setItem operations with performance monitoring
 * - Access pattern tracking for analytics and strategy decisions
 * - Monotonic timestamp counter for reliable LRU ordering
 * - Resilient timing integration for performance measurement
 */
export class BufferOperationsManager extends MemorySafeResourceManager implements ILoggingService {
  private _logger: SimpleLogger;
  private _resilientTimer!: ResilientTimer;
  private _metricsCollector!: ResilientMetricsCollector;
  
  // Access tracking for analytics and strategy decisions
  private _accessCounts: Map<string, number>;
  private _lastAccessed: Map<string, Date>;
  private _timestampCounter: number;
  private _analytics: IBufferAnalyticsTracking;

  constructor(
    accessCounts: Map<string, number>,
    lastAccessed: Map<string, Date>,
    timestampCounter: number,
    analytics: IBufferAnalyticsTracking
  ) {
    super();
    this._logger = new SimpleLogger('BufferOperationsManager');
    this._accessCounts = accessCounts;
    this._lastAccessed = lastAccessed;
    this._timestampCounter = timestampCounter;
    this._analytics = analytics;
  }

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Initialize operations manager with resilient timing
   */
  protected async doInitialize(): Promise<void> {
    this._initializeSync();
    this.logInfo('BufferOperationsManager initialized with resilient timing (async)');
  }

  /**
   * ✅ JEST COMPATIBILITY: Synchronous initialization for constructor usage
   */
  public initializeSync(): void {
    this._initializeSync();
    this.logInfo('BufferOperationsManager initialized with resilient timing (sync)');
  }

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Core initialization logic
   */
  private _initializeSync(): void {
    // ✅ RESILIENT TIMING: Initialize timing infrastructure
    this._resilientTimer = new ResilientTimer({
      enableFallbacks: true,
      maxExpectedDuration: 2000, // 2 seconds max for buffer operations
      unreliableThreshold: 3,
      estimateBaseline: 1 // 1ms baseline for buffer operations
    });

    this._metricsCollector = new ResilientMetricsCollector({
      enableFallbacks: true,
      cacheUnreliableValues: false,
      maxMetricsAge: 300000, // 5 minutes
      defaultEstimates: new Map([
        ['bufferGetOperation', 1],
        ['bufferSetOperation', 2],
        ['accessTracking', 0.5]
      ])
    });
  }

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Shutdown operations manager
   */
  protected async doShutdown(): Promise<void> {
    this.logInfo('BufferOperationsManager shutdown completed');
  }

  // ============================================================================
  // SECTION 3: ENHANCED BUFFER OPERATIONS (Lines 151-250)
  // AI Context: "Core buffer operations with resilient timing and access tracking"
  // ============================================================================

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Enhanced getItem with access pattern tracking
   */
  public enhancedGetItem<T>(key: string, baseGetItem: (key: string) => T | undefined): T | undefined {
    // ✅ CRITICAL FIX: Check if resilient timer is initialized before using it
    const operationContext = this._resilientTimer ? this._resilientTimer.start() : null;

    try {
      const item = baseGetItem(key);
      // ✅ CRITICAL FIX: Handle timing and tracking safely
      let trackingResult;
      let timingResult;
      
      if (operationContext) {
        timingResult = operationContext.end();
        trackingResult = this._trackAccess(key, item !== undefined, timingResult.duration);
        this._metricsCollector?.recordTiming('bufferGetOperation', timingResult);
      } else {
        trackingResult = this._trackAccess(key, item !== undefined, 0);
        timingResult = { reliable: false, duration: 0 };
      }

      this.logDebug('Enhanced getItem completed', {
        key,
        hit: trackingResult.wasHit,
        accessTime: trackingResult.accessTime,
        timingReliable: timingResult.reliable
      });

      return item;

    } catch (error) {
      if (operationContext) {
        const errorTiming = operationContext.end();
        this._metricsCollector?.recordTiming('bufferGetError', errorTiming);
      }
      this.logError('Enhanced getItem failed', error);
      throw error;
    }
  }

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Enhanced setItem with intelligent eviction coordination
   */
  public enhancedSetItem<T>(
    key: string,
    item: T,
    baseSetItem: (key: string, item: T) => void,
    performEvictionIfNeeded: () => void
  ): void {
    // ✅ CRITICAL FIX: Check if resilient timer is initialized before using it
    const operationContext = this._resilientTimer ? this._resilientTimer.start() : null;

    try {
      const isExistingKey = this._accessCounts.has(key);

      // Perform eviction if needed for new keys
      if (!isExistingKey) {
        performEvictionIfNeeded();
      }

      // Execute base setItem operation
      baseSetItem(key, item);

      // Track access for new items with monotonic timestamps
      if (!this._lastAccessed.has(key)) {
        this._timestampCounter++;
        this._lastAccessed.set(key, new Date(this._timestampCounter));
        this._accessCounts.set(key, 0);
      }

      // ✅ CRITICAL FIX: Only record timing if timing context exists
      if (operationContext) {
        const timingResult = operationContext.end();
        this._metricsCollector?.recordTiming('bufferSetOperation', timingResult);
        
        this.logDebug('Enhanced setItem completed', {
          key,
          isExistingKey,
          operationTime: timingResult.duration,
          timingReliable: timingResult.reliable
        });
      } else {
        this.logDebug('Enhanced setItem completed', {
          key,
          isExistingKey,
          operationTime: 0,
          timingReliable: false
        });
      }

    } catch (error) {
      if (operationContext) {
        const errorTiming = operationContext.end();
        this._metricsCollector?.recordTiming('bufferSetError', errorTiming);
      }
      this.logError('Enhanced setItem failed', error);
      throw error;
    }
  }

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Track access patterns for analytics
   */
  private _trackAccess(key: string, wasHit: boolean, accessTime: number): IAccessTrackingResult {
    const trackingContext = this._resilientTimer.start();

    try {
      // Update analytics
      this._analytics.totalAccesses++;
      this._analytics.accessTimes.push(accessTime);

      if (wasHit) {
        this._analytics.totalHits++;
        
        // Update access tracking for existing items
        const currentCount = this._accessCounts.get(key) || 0;
        this._accessCounts.set(key, currentCount + 1);
        
        // Ensure accessed items always get newer timestamps using monotonic counter
        this._timestampCounter++;
        this._lastAccessed.set(key, new Date(this._timestampCounter));
      } else {
        this._analytics.totalMisses++;
      }

      // Add to access history
      this._analytics.accessHistory.push({
        timestamp: new Date(),
        key,
        hit: wasHit
      });

      const timingResult = trackingContext.end();
      
      // ✅ RESILIENT TIMING: Record tracking performance
      this._metricsCollector.recordTiming('accessTracking', timingResult);

      return {
        wasHit,
        accessTime,
        timingReliable: timingResult.reliable
      };

    } catch (error) {
      trackingContext.end();
      this.logError('Access tracking failed', error);
      throw error;
    }
  }

  // ============================================================================
  // SECTION 4: ACCESS TRACKING UTILITIES (Lines 251-320)
  // AI Context: "Access tracking utilities and timestamp management"
  // ============================================================================

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Get current timestamp counter
   */
  public getTimestampCounter(): number {
    return this._timestampCounter;
  }

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Update timestamp counter
   */
  public updateTimestampCounter(newValue: number): void {
    this._timestampCounter = newValue;
    this.logDebug('Timestamp counter updated', { newValue });
  }

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Get access counts map
   */
  public getAccessCounts(): Map<string, number> {
    return this._accessCounts;
  }

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Get last accessed map
   */
  public getLastAccessed(): Map<string, Date> {
    return this._lastAccessed;
  }

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Get analytics tracking data
   */
  public getAnalyticsTracking(): IBufferAnalyticsTracking {
    return this._analytics;
  }

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Clear access tracking for removed item
   */
  public clearAccessTracking(key: string): void {
    const trackingContext = this._resilientTimer.start();

    try {
      this._accessCounts.delete(key);
      this._lastAccessed.delete(key);

      const timingResult = trackingContext.end();
      this._metricsCollector.recordTiming('clearAccessTracking', timingResult);

      this.logDebug('Access tracking cleared for key', { key });

    } catch (error) {
      trackingContext.end();
      this.logError('Failed to clear access tracking', error);
      throw error;
    }
  }

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Reset all analytics data
   */
  public resetAnalytics(): void {
    const resetContext = this._resilientTimer.start();

    try {
      this._analytics.totalAccesses = 0;
      this._analytics.totalHits = 0;
      this._analytics.totalMisses = 0;
      this._analytics.accessTimes.length = 0;
      this._analytics.accessHistory.length = 0;

      const timingResult = resetContext.end();
      this._metricsCollector.recordTiming('resetAnalytics', timingResult);

      this.logInfo('Analytics data reset completed');

    } catch (error) {
      resetContext.end();
      this.logError('Failed to reset analytics', error);
      throw error;
    }
  }

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Get operation metrics summary
   */
  public getOperationMetrics(): Record<string, number> {
    try {
      const snapshot = this._metricsCollector.createSnapshot();
      if (snapshot.reliable) {
        // Convert Map to Record
        const metrics: Record<string, number> = {};
        snapshot.metrics.forEach((value, key) => {
          metrics[key] = typeof value === 'object' ? value.value : value;
        });
        return metrics;
      }
      return {};
    } catch (error) {
      this.logError('Failed to get operation metrics', error);
      return {};
    }
  }

  // Logging interface implementation
  logInfo(message: string, details?: Record<string, unknown>): void {
    this._logger.logInfo(message, details);
  }

  logError(message: string, error: unknown, details?: Record<string, unknown>): void {
    this._logger.logError(message, error, details);
  }

  logDebug(message: string, details?: Record<string, unknown>): void {
    this._logger.logDebug(message, details);
  }

  logWarning(message: string, details?: Record<string, unknown>): void {
    this._logger.logWarning(message, details);
  }
}
