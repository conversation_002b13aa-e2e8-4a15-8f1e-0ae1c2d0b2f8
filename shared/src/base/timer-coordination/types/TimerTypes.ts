/**
 * @file Timer Coordination Types
 * @filepath shared/src/base/timer-coordination/types/TimerTypes.ts
 * @task-id M-TSK-01.SUB-02.1.TYP-01
 * @component timer-coordination-types
 * @reference foundation-context.TIMER-COORDINATION.002
 * @template timer-coordination-type-definitions
 * @tier T2
 * @context timer-coordination-context
 * @category Timer-Coordination-Types
 * @created 2025-07-28 16:00:00 +03
 * @modified 2025-07-28 16:00:00 +03
 *
 * @description
 * Comprehensive type definitions for enhanced timer coordination providing:
 * - Timer configuration interfaces and type structures
 * - Advanced scheduling type definitions with phase integration
 * - Timer pool management type specifications
 * - Coordination pattern type definitions for complex workflows
 * - Performance optimization type structures
 * - Enterprise-grade type safety for timer operations
 * - Memory-safe timer type definitions with resource management
 * - Integration type definitions for modular timer architecture
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-011-timer-coordination-types
 * @governance-dcr DCR-foundation-011-timer-coordination-types-development
 * @governance-status approved
 * @governance-compliance authority-validated
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/base/utils/ResilientTiming
 * @enables shared/src/base/TimerCoordinationServiceEnhanced
 * @enables shared/src/base/timer-coordination/modules/*
 * @related-contexts timer-coordination-context, foundation-context, type-definitions-context
 * @governance-impact framework-foundation, timer-coordination, type-safety
 *
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type timer-coordination-type-definitions
 * @lifecycle-stage implementation
 * @testing-status type-checked
 * @deployment-ready true
 * @monitoring-enabled false
 * @documentation docs/contexts/timer-coordination-context/types/TimerTypes.md
 *
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 *   anti-simplification-compliant: true
 *
 * 📝 VERSION HISTORY
 * @version-history
 * v1.0.0 (2025-07-28) - Initial timer coordination type definitions
 * v1.1.0 (2025-07-28) - Added advanced scheduling and coordination pattern types
 */

/**
 * @file Timer Coordination Types
 * @filepath shared/src/base/timer-coordination/types/TimerTypes.ts
 * @extracted-from TimerCoordinationServiceEnhanced.ts (lines 100-400)
 * @component timer-coordination-types
 * @tier T0
 * @context foundation-context
 * @category Enhanced-Timer-Types
 * @created 2025-07-26 18:25:00 +03
 * @authority President & CEO, E.Z. Consultancy
 * @governance ADR-foundation-011, DCR-foundation-010
 */

// ============================================================================
// SECTION 1: TIMER POOL MANAGEMENT INTERFACES
// AI Context: "Enterprise-grade resource pooling interfaces"
// ============================================================================

/**
 * Timer pool management interfaces for enterprise-grade resource pooling
 */
export interface ITimerPool {
  poolId: string;
  timers: Set<string>;
  maxPoolSize: number;
  currentSize: number;
  sharedResources: Map<string, any>;
  poolStrategy: 'round_robin' | 'least_used' | 'random' | 'custom';
  customStrategy?: (pool: ITimerPool, candidates: string[]) => string;
  onPoolExhaustion: 'queue' | 'reject' | 'expand' | 'evict_oldest';
  createdAt: Date;
  lastAccessed: Date;
  utilizationMetrics: IPoolUtilizationMetrics;
}

export interface ITimerPoolConfig {
  maxPoolSize: number;
  initialSize: number;
  poolStrategy: 'round_robin' | 'least_used' | 'random' | 'custom';
  customStrategy?: (pool: ITimerPool, candidates: string[]) => string;
  autoExpansion: boolean;
  maxExpansionSize: number;
  idleTimeout: number; // Remove unused timers after X ms
  sharedResourcesEnabled: boolean;
  monitoringEnabled: boolean;
  onPoolExhaustion: 'queue' | 'reject' | 'expand' | 'evict_oldest';
}

export interface IPoolUtilizationMetrics {
  utilizationRate: number; // 0.0-1.0
  averageAccessTime: number;
  peakUtilization: number;
  idleTime: number;
  thrashingEvents: number;
  lastOptimization: Date;
}

export interface ITimerPoolStatistics {
  poolId: string;
  currentSize: number;
  maxSize: number;
  utilizationRate: number;
  activeTimers: string[];
  sharedResourceCount: number;
  strategy: string;
  healthScore: number;
  performanceMetrics: IPoolPerformanceMetrics;
}

export interface IPoolPerformanceMetrics {
  averageCreationTime: number;
  averageAccessTime: number;
  cacheHitRate: number;
  resourceContentionEvents: number;
  optimizationCount: number;
  lastPerformanceCheck: Date;
}

// ============================================================================
// SECTION 2: ADVANCED SCHEDULING INTERFACES
// AI Context: "Enterprise timer management and scheduling interfaces"
// ============================================================================

/**
 * Advanced scheduling interfaces for enterprise timer management
 */
export interface IAdvancedTimerScheduling {
  scheduleRecurringTimer(config: IRecurringTimerConfig): string;
  scheduleCronTimer(cronExpression: string, callback: () => void, serviceId: string, timerId?: string): string;
  scheduleConditionalTimer(condition: () => boolean, callback: () => void, checkInterval: number, serviceId: string, timerId?: string): string;
  scheduleDelayedTimer(callback: () => void, delayMs: number, serviceId: string, timerId?: string): string;
  schedulePriorityTimer(callback: () => void, priority: number, intervalMs: number, serviceId: string, timerId?: string): string;
}

export interface IRecurringTimerConfig {
  callback: () => void;
  schedule: ITimerSchedule;
  serviceId: string;
  timerId?: string;
  maxExecutions?: number;
  endDate?: Date;
  priority?: number;
  retryPolicy?: ITimerRetryPolicy;
  onComplete?: () => void;
  onError?: (error: Error) => void;
  onMaxExecutionsReached?: () => void;
  metadata?: Record<string, unknown>;
}

export interface ITimerSchedule {
  type: 'interval' | 'cron' | 'once' | 'daily' | 'weekly' | 'monthly' | 'conditional';
  value: number | string; // interval in ms or cron expression
  timezone?: string;
  startDate?: Date;
  endDate?: Date;
  priority?: number;
  jitterMs?: number; // Random variance to prevent thundering herd
  backoffStrategy?: 'none' | 'linear' | 'exponential' | 'custom';
  customBackoffFn?: (attempt: number) => number;
}

export interface ITimerRetryPolicy {
  maxRetries: number;
  retryDelay: number;
  backoffMultiplier: number;
  maxRetryDelay: number;
  retryableErrors: string[];
  onRetryExhausted?: (error: Error) => void;
}

export interface IScheduledTimer {
  id: string;
  config: IRecurringTimerConfig;
  executionCount: number;
  nextExecution: Date;
  lastExecution?: Date;
  status: 'scheduled' | 'running' | 'paused' | 'completed' | 'failed';
  monitorTimerId?: string;
  retryCount: number;
  errors: Error[];
  performanceMetrics: ITimerPerformanceMetrics;
}

export interface ITimerPerformanceMetrics {
  averageExecutionTime: number;
  totalExecutionTime: number;
  minExecutionTime: number;
  maxExecutionTime: number;
  successRate: number;
  lastPerformanceUpdate: Date;
}

// ============================================================================
// SECTION 3: TIMER COORDINATION PATTERN INTERFACES
// AI Context: "Complex workflow management and coordination interfaces"
// ============================================================================

/**
 * Timer coordination pattern interfaces for complex workflow management
 */
export interface ITimerCoordination {
  createTimerGroup(groupId: string, timerIds: string[], coordinationType?: 'parallel' | 'sequential' | 'conditional'): ITimerGroup;
  synchronizeTimerGroup(groupId: string): Promise<ISynchronizationResult>;
  createTimerChain(steps: ITimerChainStep[]): string;
  createTimerBarrier(timers: string[], callback: () => void, barrierType?: 'all' | 'any' | 'majority'): string;
  pauseTimerGroup(groupId: string): Promise<void>;
  resumeTimerGroup(groupId: string): Promise<void>;
  destroyTimerGroup(groupId: string): Promise<IGroupDestructionResult>;
}

export interface ITimerGroup {
  groupId: string;
  timers: Set<string>;
  coordinationType: 'parallel' | 'sequential' | 'conditional';
  healthThreshold: number; // Minimum healthy components to keep group active
  status: 'active' | 'degraded' | 'failed' | 'paused' | 'synchronized';
  createdAt: Date;
  lastSynchronization?: Date;
  synchronizationCount: number;
  groupMetrics: IGroupMetrics;
  synchronizationHistory: ISynchronizationEvent[];
}

export interface IGroupMetrics {
  totalSynchronizations: number;
  averageSynchronizationTime: number;
  successfulSynchronizations: number;
  failedSynchronizations: number;
  groupHealthScore: number;
  lastHealthCheck: Date;
}

export interface ISynchronizationEvent {
  timestamp: Date;
  type: 'manual' | 'automatic' | 'recovery';
  duration: number;
  success: boolean;
  participatingTimers: number;
  errors?: Error[];
}

export interface ITimerChainStep {
  componentId: string;
  operation: string;
  parameters?: any;
  waitForPrevious: boolean;
  timeout: number;
  priority?: number;
  condition?: (context: IChainContext) => boolean;
  onStepComplete?: (result: any) => void;
  onStepError?: (error: Error) => boolean; // true = continue chain
  retryPolicy?: ITimerRetryPolicy;
  metadata?: Record<string, unknown>;
}

export interface IChainContext {
  chainId: string;
  stepIndex: number;
  previousResults: any[];
  executionAttempt: number;
  chainMetrics: IChainMetrics;
  globalContext?: Record<string, unknown>;
}

export interface IChainMetrics {
  totalSteps: number;
  completedSteps: number;
  failedSteps: number;
  averageStepTime: number;
  chainStartTime: Date;
  estimatedCompletionTime?: Date;
}

export interface ITimerChain {
  id: string;
  steps: ITimerChainStep[];
  currentStep: number;
  status: 'ready' | 'running' | 'completed' | 'failed' | 'paused';
  createdAt: Date;
  completedAt?: Date;
  executedSteps: number;
  stepResults?: any[];
  chainMetrics: IChainMetrics;
  errorHistory: Error[];
}

export interface ISynchronizationResult {
  groupId: string;
  synchronizedTimers: number;
  failedTimers: number;
  synchronizationTime: number;
  nextSynchronization?: Date;
  healthScoreAfter: number;
  warnings: string[];
  errors: Error[];
}

export interface IGroupDestructionResult {
  groupId: string;
  destroyedTimers: number;
  failedDestructions: number;
  destructionTime: number;
  errors: Error[];
  resourcesReleased: number;
}

// ============================================================================
// SECTION 4: ENHANCED CONFIGURATION INTERFACE
// AI Context: "Comprehensive configuration combining all enhancement features"
// ============================================================================

/**
 * Enhanced configuration interface combining all enhancement features
 */
export interface ITimerCoordinationServiceEnhancedConfig {
  // Base configuration from TimerCoordinationService
  maxTimersPerService: number;
  maxGlobalTimers: number;
  minIntervalMs: number;
  timerAuditIntervalMs: number;
  
  // Pool management configuration
  pooling: {
    enabled: boolean;
    defaultPoolSize: number;
    maxPools: number;
    poolMonitoringInterval: number;
    autoOptimization: boolean;
  };
  
  // Advanced scheduling configuration
  scheduling: {
    cronParsingEnabled: boolean;
    conditionalTimersEnabled: boolean;
    prioritySchedulingEnabled: boolean;
    jitterEnabled: boolean;
    maxJitterMs: number;
  };
  
  // Coordination configuration
  coordination: {
    groupingEnabled: boolean;
    chainExecutionEnabled: boolean;
    synchronizationEnabled: boolean;
    maxGroupSize: number;
    maxChainLength: number;
  };
  
  // Integration configuration
  integration: {
    phase1BufferEnabled: boolean;
    phase2EventEnabled: boolean;
    bufferSize: number;
    eventEmissionEnabled: boolean;
  };
  
  // Performance configuration
  performance: {
    poolOperationTimeoutMs: number;
    schedulingTimeoutMs: number;
    synchronizationTimeoutMs: number;
    monitoringEnabled: boolean;
    metricsCollectionInterval: number;
  };
}
