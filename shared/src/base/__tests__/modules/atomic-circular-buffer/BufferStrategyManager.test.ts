/**
 * @file BufferStrategyManager.test.ts
 * @task-id T-TSK-02.SUB-05.1.BSM-01
 * @component buffer-strategy-manager-testing
 * @description Comprehensive test suite for BufferStrategyManager module
 * 
 * Testing Coverage:
 * - Core functionality and initialization
 * - All eviction policies (LRU, LFU, FIFO, custom, random)
 * - Strategy validation and configuration
 * - Performance benchmarks (<2ms eviction operations)
 * - Error handling and edge cases
 * - Integration with MemorySafeResourceManager
 * - Resilient timing integration
 * - Memory safety patterns
 * 
 * Target: 95%+ coverage across all metrics
 * Anti-Simplification Policy: Complete functionality implementation
 */

import { BufferStrategyManager, IBufferStrategy, IEvictionResult, IStrategyValidationResult } from '../../../atomic-circular-buffer-enhanced/modules/BufferStrategyManager';

// ============================================================================
// RESILIENT TIMING INTEGRATION MOCKING - Gold Standard Pattern
// AI Context: "Comprehensive ResilientTimer and ResilientMetricsCollector mocking for enterprise testing"
// Reference: TimerPoolManager test suite (117 timing-related test lines)
// ============================================================================

// Mock ResilientTimer with comprehensive timing context lifecycle
jest.mock('../../../utils/ResilientTiming', () => ({
  ResilientTimer: jest.fn(() => ({
    start: jest.fn(() => ({
      end: jest.fn(() => ({
        duration: 1.5,
        reliable: true,
        startTime: Date.now(),
        endTime: Date.now() + 1.5,
        method: 'performance.now'
      })),
    })),
  })),
}));

// Mock ResilientMetricsCollector with comprehensive metrics recording
jest.mock('../../../utils/ResilientMetrics', () => ({
  ResilientMetricsCollector: jest.fn(() => ({
    recordTiming: jest.fn(),
    getMetrics: jest.fn(() => new Map()),
    clearMetrics: jest.fn(),
    createSnapshot: jest.fn(() => ({}))
  })),
}));

// Mock ResilientTimer and ResilientMetricsCollector for comprehensive testing
jest.mock('../../../utils/ResilientTiming', () => ({
  ResilientTimer: jest.fn(() => ({
    start: jest.fn(() => ({
      end: jest.fn(() => ({
        duration: 1.5,
        reliable: true,
        startTime: Date.now(),
        endTime: Date.now() + 1.5
      })),
    })),
  })),
}));

jest.mock('../../../utils/ResilientMetrics', () => ({
  ResilientMetricsCollector: jest.fn(() => ({
    recordTiming: jest.fn(),
    getMetrics: jest.fn(() => new Map()),
    clearMetrics: jest.fn()
  })),
}));

// ============================================================================
// SECTION 1: TEST SETUP & UTILITIES (Lines 1-80)
// AI Context: "Test infrastructure and helper functions"
// ============================================================================

describe('BufferStrategyManager', () => {
  let bufferStrategyManager: BufferStrategyManager;
  let mockAccessCounts: Map<string, number>;
  let mockLastAccessed: Map<string, Date>;
  let mockItems: Map<string, any>;
  let mockInsertionOrder: string[];

  // Helper function to create valid strategy configurations
  const createValidStrategy = (overrides: Partial<IBufferStrategy> = {}): IBufferStrategy => ({
    evictionPolicy: 'lru',
    compactionThreshold: 0.3,
    autoCompaction: true,
    ...overrides
  });

  // Helper function to setup test data
  const setupTestData = () => {
    mockAccessCounts = new Map([
      ['key1', 5],
      ['key2', 2],
      ['key3', 8],
      ['key4', 1]
    ]);

    const now = new Date();
    mockLastAccessed = new Map([
      ['key1', new Date(now.getTime() - 1000)], // 1 second ago
      ['key2', new Date(now.getTime() - 5000)], // 5 seconds ago (oldest)
      ['key3', new Date(now.getTime() - 500)],  // 0.5 seconds ago (newest)
      ['key4', new Date(now.getTime() - 3000)]  // 3 seconds ago
    ]);

    mockItems = new Map([
      ['key1', { value: 'value1' }],
      ['key2', { value: 'value2' }],
      ['key3', { value: 'value3' }],
      ['key4', { value: 'value4' }]
    ]);

    mockInsertionOrder = ['key2', 'key4', 'key1', 'key3']; // key2 is oldest (FIFO)
  };

  beforeEach(() => {
    setupTestData();
    const strategy = createValidStrategy();
    bufferStrategyManager = new BufferStrategyManager(strategy, mockAccessCounts, mockLastAccessed);
  });

  afterEach(async () => {
    if (bufferStrategyManager) {
      await bufferStrategyManager.shutdown();
    }
  });

  // ============================================================================
  // SECTION 2: CORE FUNCTIONALITY TESTING (Lines 81-160)
  // AI Context: "Basic initialization, configuration, and lifecycle testing"
  // ============================================================================

  describe('Core Functionality', () => {
    test('should initialize with valid strategy configuration', async () => {
      expect(bufferStrategyManager).toBeDefined();
      
      // Initialize the manager
      await (bufferStrategyManager as any).doInitialize();
      expect(bufferStrategyManager.isHealthy()).toBe(true);
      
      // Verify strategy configuration
      const strategy = bufferStrategyManager.getStrategy();
      expect(strategy.evictionPolicy).toBe('lru');
      expect(strategy.compactionThreshold).toBe(0.3);
      expect(strategy.autoCompaction).toBe(true);
    });

    test('should extend MemorySafeResourceManager properly', async () => {
      await (bufferStrategyManager as any).doInitialize();
      
      // Verify inheritance from MemorySafeResourceManager
      expect(bufferStrategyManager.isHealthy()).toBe(true);
      expect(typeof bufferStrategyManager.getResourceMetrics).toBe('function');
      expect(typeof (bufferStrategyManager as any).doInitialize).toBe('function');
      expect(typeof bufferStrategyManager.shutdown).toBe('function');
    });

    test('should handle strategy updates correctly', async () => {
      await (bufferStrategyManager as any).doInitialize();
      
      const newStrategy = createValidStrategy({
        evictionPolicy: 'lfu',
        compactionThreshold: 0.5,
        autoCompaction: false
      });
      
      bufferStrategyManager.updateStrategy(newStrategy);
      
      const updatedStrategy = bufferStrategyManager.getStrategy();
      expect(updatedStrategy.evictionPolicy).toBe('lfu');
      expect(updatedStrategy.compactionThreshold).toBe(0.5);
      expect(updatedStrategy.autoCompaction).toBe(false);
    });

    test('should provide immutable strategy configuration', async () => {
      await (bufferStrategyManager as any).doInitialize();
      
      const strategy = bufferStrategyManager.getStrategy();
      const originalPolicy = strategy.evictionPolicy;
      
      // Attempt to modify returned strategy
      strategy.evictionPolicy = 'random';
      
      // Verify original strategy is unchanged
      const freshStrategy = bufferStrategyManager.getStrategy();
      expect(freshStrategy.evictionPolicy).toBe(originalPolicy);
    });
  });

  // ============================================================================
  // SECTION 3: STRATEGY VALIDATION TESTING (Lines 161-240)
  // AI Context: "Strategy validation logic and error handling"
  // ============================================================================

  describe('Strategy Validation', () => {
    test('should validate correct strategy configurations', () => {
      const validStrategies = [
        { evictionPolicy: 'lru' as const },
        { evictionPolicy: 'lfu' as const },
        { evictionPolicy: 'fifo' as const },
        { evictionPolicy: 'random' as const },
        { 
          evictionPolicy: 'custom' as const,
          customEvictionFn: (items: Map<string, any>) => Array.from(items.keys())[0]
        }
      ];

      validStrategies.forEach((strategy, index) => {
        const result = bufferStrategyManager.validateStrategy(strategy);
        expect(result.valid).toBe(true);
        expect(result.errors).toHaveLength(0);
        expect(result.normalizedStrategy).toBeDefined();
      });
    });

    test('should reject invalid eviction policies', () => {
      const invalidStrategy = { evictionPolicy: 'invalid' as any };
      const result = bufferStrategyManager.validateStrategy(invalidStrategy);
      
      expect(result.valid).toBe(false);
      expect(result.errors[0]).toContain('Invalid eviction policy: invalid');
    });

    test('should require custom function for custom policy', () => {
      const customStrategyWithoutFn = { evictionPolicy: 'custom' as const };
      const result = bufferStrategyManager.validateStrategy(customStrategyWithoutFn);
      
      expect(result.valid).toBe(false);
      expect(result.errors).toContain(
        'Custom eviction policy requires customEvictionFn to be provided'
      );
    });

    test('should validate compaction threshold range', () => {
      const invalidThresholds = [-0.1, 1.1, 2.0];
      
      invalidThresholds.forEach(threshold => {
        const strategy = { evictionPolicy: 'lru' as const, compactionThreshold: threshold };
        const result = bufferStrategyManager.validateStrategy(strategy);
        
        expect(result.valid).toBe(false);
        expect(result.errors).toContain(
          'Compaction threshold must be between 0.0 and 1.0'
        );
      });
    });

    test('should normalize strategy with defaults', () => {
      const minimalStrategy = { evictionPolicy: 'lru' as const };
      const result = bufferStrategyManager.validateStrategy(minimalStrategy);
      
      expect(result.valid).toBe(true);
      expect(result.normalizedStrategy.compactionThreshold).toBe(0.3);
      expect(result.normalizedStrategy.autoCompaction).toBe(true);
      expect(result.normalizedStrategy.evictionPolicy).toBe('lru');
    });

    test('should throw error when updating with invalid strategy', async () => {
      await (bufferStrategyManager as any).doInitialize();

      const invalidStrategy = {
        evictionPolicy: 'invalid' as any,
        compactionThreshold: 0.3,
        autoCompaction: true
      };

      expect(() => {
        bufferStrategyManager.updateStrategy(invalidStrategy);
      }).toThrow('Invalid strategy configuration');
    });
  });

  // ============================================================================
  // SECTION 4: EVICTION POLICY TESTING (Lines 241-400)
  // AI Context: "Comprehensive testing of all eviction strategies"
  // ============================================================================

  describe('Eviction Policy Testing', () => {
    beforeEach(async () => {
      await (bufferStrategyManager as any).doInitialize();
    });

    describe('LRU (Least Recently Used) Eviction', () => {
      test('should evict least recently used item', async () => {
        const lruStrategy = createValidStrategy({ evictionPolicy: 'lru' });
        bufferStrategyManager.updateStrategy(lruStrategy);

        const result = await bufferStrategyManager.performIntelligentEviction(
          mockItems,
          mockInsertionOrder,
          mockItems.size
        );

        // key2 should be evicted (oldest access time: 5 seconds ago)
        expect(result.evictedKeys).toContain('key2');
        expect(result.evictedKeys).toHaveLength(1);
        expect(result.remainingSize).toBe(3);
        expect(result.operationTime).toBeGreaterThan(0);
      });

      test('should handle empty items for LRU eviction', async () => {
        const lruStrategy = createValidStrategy({ evictionPolicy: 'lru' });
        bufferStrategyManager.updateStrategy(lruStrategy);

        const emptyItems = new Map<string, any>();
        const result = await bufferStrategyManager.performIntelligentEviction(
          emptyItems,
          [],
          0
        );

        expect(result.evictedKeys).toHaveLength(0);
        expect(result.remainingSize).toBe(0);
      });
    });

    describe('LFU (Least Frequently Used) Eviction', () => {
      test('should evict least frequently used item', async () => {
        const lfuStrategy = createValidStrategy({ evictionPolicy: 'lfu' });
        bufferStrategyManager.updateStrategy(lfuStrategy);

        const result = await bufferStrategyManager.performIntelligentEviction(
          mockItems,
          mockInsertionOrder,
          mockItems.size
        );

        // key4 should be evicted (lowest access count: 1)
        expect(result.evictedKeys).toContain('key4');
        expect(result.evictedKeys).toHaveLength(1);
        expect(result.remainingSize).toBe(3);
      });

      test('should handle items with no access counts for LFU', async () => {
        const lfuStrategy = createValidStrategy({ evictionPolicy: 'lfu' });
        bufferStrategyManager.updateStrategy(lfuStrategy);

        // Create items without access count data
        const itemsWithoutCounts = new Map([['newKey', { value: 'newValue' }]]);

        const result = await bufferStrategyManager.performIntelligentEviction(
          itemsWithoutCounts,
          ['newKey'],
          1
        );

        expect(result.evictedKeys).toContain('newKey');
        expect(result.remainingSize).toBe(0);
      });
    });

    describe('FIFO (First In First Out) Eviction', () => {
      test('should evict first inserted item', async () => {
        const fifoStrategy = createValidStrategy({ evictionPolicy: 'fifo' });
        bufferStrategyManager.updateStrategy(fifoStrategy);

        const result = await bufferStrategyManager.performIntelligentEviction(
          mockItems,
          mockInsertionOrder,
          mockItems.size
        );

        // key2 should be evicted (first in insertion order)
        expect(result.evictedKeys).toContain('key2');
        expect(result.evictedKeys).toHaveLength(1);
        expect(result.remainingSize).toBe(3);
      });

      test('should handle empty insertion order for FIFO', async () => {
        const fifoStrategy = createValidStrategy({ evictionPolicy: 'fifo' });
        bufferStrategyManager.updateStrategy(fifoStrategy);

        const result = await bufferStrategyManager.performIntelligentEviction(
          mockItems,
          [], // Empty insertion order
          mockItems.size
        );

        expect(result.evictedKeys).toHaveLength(0);
        expect(result.remainingSize).toBe(mockItems.size);
      });
    });

    describe('Random Eviction', () => {
      test('should evict random item', async () => {
        const randomStrategy = createValidStrategy({ evictionPolicy: 'random' });
        bufferStrategyManager.updateStrategy(randomStrategy);

        const result = await bufferStrategyManager.performIntelligentEviction(
          mockItems,
          mockInsertionOrder,
          mockItems.size
        );

        // Should evict exactly one item
        expect(result.evictedKeys).toHaveLength(1);
        expect(result.remainingSize).toBe(3);

        // Evicted key should be one of the existing keys
        const evictedKey = result.evictedKeys[0];
        expect(Array.from(mockItems.keys())).toContain(evictedKey);
      });

      test('should handle empty items for random eviction', async () => {
        const randomStrategy = createValidStrategy({ evictionPolicy: 'random' });
        bufferStrategyManager.updateStrategy(randomStrategy);

        const emptyItems = new Map<string, any>();
        const result = await bufferStrategyManager.performIntelligentEviction(
          emptyItems,
          [],
          0
        );

        expect(result.evictedKeys).toHaveLength(0);
        expect(result.remainingSize).toBe(0);
      });
    });

    describe('Custom Eviction', () => {
      test('should use custom eviction function', async () => {
        const customEvictionFn = jest.fn((_items: Map<string, any>) => {
          // Always evict 'key3'
          return 'key3';
        });

        const customStrategy = createValidStrategy({
          evictionPolicy: 'custom',
          customEvictionFn
        });
        bufferStrategyManager.updateStrategy(customStrategy);

        const result = await bufferStrategyManager.performIntelligentEviction(
          mockItems,
          mockInsertionOrder,
          mockItems.size
        );

        expect(customEvictionFn).toHaveBeenCalledWith(
          mockItems,
          mockInsertionOrder,
          mockAccessCounts
        );
        expect(result.evictedKeys).toContain('key3');
        expect(result.evictedKeys).toHaveLength(1);
      });

      test('should fallback to FIFO when custom function fails', async () => {
        const failingCustomFn = jest.fn(() => {
          throw new Error('Custom function error');
        });

        const customStrategy = createValidStrategy({
          evictionPolicy: 'custom',
          customEvictionFn: failingCustomFn
        });
        bufferStrategyManager.updateStrategy(customStrategy);

        const result = await bufferStrategyManager.performIntelligentEviction(
          mockItems,
          mockInsertionOrder,
          mockItems.size
        );

        // Should fallback to FIFO (evict key2 - first in insertion order)
        expect(result.evictedKeys).toContain('key2');
        expect(result.evictedKeys).toHaveLength(1);
      });

      test('should handle custom function returning non-existent key', async () => {
        const customEvictionFn = jest.fn((_items: Map<string, any>) => 'nonExistentKey');

        const customStrategy = createValidStrategy({
          evictionPolicy: 'custom',
          customEvictionFn
        });
        bufferStrategyManager.updateStrategy(customStrategy);

        const result = await bufferStrategyManager.performIntelligentEviction(
          mockItems,
          mockInsertionOrder,
          mockItems.size
        );

        // Should not evict anything when key doesn't exist
        expect(result.evictedKeys).toHaveLength(0);
        expect(result.remainingSize).toBe(mockItems.size);
      });
    });
  });

  // ============================================================================
  // SECTION 5: PERFORMANCE & TIMING TESTING (Lines 444-520)
  // AI Context: "Performance benchmarks and resilient timing validation"
  // ============================================================================

  describe('Performance & Timing Testing', () => {
    beforeEach(async () => {
      await (bufferStrategyManager as any).doInitialize();
    });

    test('should complete eviction operations within 2ms target', async () => {
      const strategies: Array<'lru' | 'lfu' | 'fifo' | 'random'> = ['lru', 'lfu', 'fifo', 'random'];

      for (const policy of strategies) {
        const strategy = createValidStrategy({ evictionPolicy: policy });
        bufferStrategyManager.updateStrategy(strategy);

        const startTime = performance.now();
        const result = await bufferStrategyManager.performIntelligentEviction(
          mockItems,
          mockInsertionOrder,
          mockItems.size
        );
        const endTime = performance.now();

        const actualTime = endTime - startTime;

        // Performance requirement: <2ms eviction operations
        expect(actualTime).toBeLessThan(2);
        expect(result.operationTime).toBeGreaterThan(0);

        // Reset for next iteration
        setupTestData();
      }
    });

    test('should record timing metrics with resilient timing', async () => {
      const lruStrategy = createValidStrategy({ evictionPolicy: 'lru' });
      bufferStrategyManager.updateStrategy(lruStrategy);

      const result = await bufferStrategyManager.performIntelligentEviction(
        mockItems,
        mockInsertionOrder,
        mockItems.size
      );

      // Verify timing metrics are recorded
      expect(result.operationTime).toBeGreaterThan(0);
      expect(typeof result.operationTime).toBe('number');
      expect(result.fragmentationReduced).toBeGreaterThanOrEqual(0);
    });

    test('should handle timing failures gracefully', async () => {
      // Test with strategy that might cause timing issues
      const customStrategy = createValidStrategy({
        evictionPolicy: 'custom',
        customEvictionFn: () => {
          // Simulate some processing time
          const start = Date.now();
          while (Date.now() - start < 1) {
            // Small delay
          }
          return 'key1';
        }
      });
      bufferStrategyManager.updateStrategy(customStrategy);

      const result = await bufferStrategyManager.performIntelligentEviction(
        mockItems,
        mockInsertionOrder,
        mockItems.size
      );

      // Should still complete successfully
      expect(result.evictedKeys).toContain('key1');
      expect(result.operationTime).toBeGreaterThan(0);
    });
  });

  // ============================================================================
  // SECTION 6: SYNCHRONOUS EVICTION TESTING (Lines 522-600)
  // AI Context: "Synchronous eviction methods for atomic operations"
  // ============================================================================

  describe('Synchronous Eviction Testing', () => {
    beforeEach(async () => {
      await (bufferStrategyManager as any).doInitialize();
    });

    test('should perform synchronous LRU eviction', () => {
      const lruStrategy = createValidStrategy({ evictionPolicy: 'lru' });
      bufferStrategyManager.updateStrategy(lruStrategy);

      const itemsCopy = new Map(mockItems);
      const orderCopy = [...mockInsertionOrder];

      bufferStrategyManager.performSyncIntelligentEviction(itemsCopy, orderCopy);

      // key2 should be removed (oldest access time)
      expect(itemsCopy.has('key2')).toBe(false);
      expect(itemsCopy.size).toBe(3);
    });

    test('should perform synchronous LFU eviction', () => {
      const lfuStrategy = createValidStrategy({ evictionPolicy: 'lfu' });
      bufferStrategyManager.updateStrategy(lfuStrategy);

      const itemsCopy = new Map(mockItems);
      const orderCopy = [...mockInsertionOrder];

      bufferStrategyManager.performSyncIntelligentEviction(itemsCopy, orderCopy);

      // key4 should be removed (lowest access count)
      expect(itemsCopy.has('key4')).toBe(false);
      expect(itemsCopy.size).toBe(3);
    });

    test('should perform synchronous FIFO eviction', () => {
      const fifoStrategy = createValidStrategy({ evictionPolicy: 'fifo' });
      bufferStrategyManager.updateStrategy(fifoStrategy);

      const itemsCopy = new Map(mockItems);
      const orderCopy = [...mockInsertionOrder];

      bufferStrategyManager.performSyncIntelligentEviction(itemsCopy, orderCopy);

      // key2 should be removed (first in insertion order)
      expect(itemsCopy.has('key2')).toBe(false);
      expect(orderCopy).not.toContain('key2');
      expect(itemsCopy.size).toBe(3);
    });

    test('should handle synchronous eviction without timing infrastructure', () => {
      // Create manager without initialization to test timing fallback
      const uninitializedManager = new BufferStrategyManager(
        createValidStrategy({ evictionPolicy: 'lru' }),
        mockAccessCounts,
        mockLastAccessed
      );

      const itemsCopy = new Map(mockItems);
      const orderCopy = [...mockInsertionOrder];

      // Should not throw even without timing infrastructure
      expect(() => {
        uninitializedManager.performSyncIntelligentEviction(itemsCopy, orderCopy);
      }).not.toThrow();

      expect(itemsCopy.size).toBe(3);
    });

    test('should perform synchronous custom eviction with fallback', () => {
      const failingCustomFn = jest.fn(() => {
        throw new Error('Sync custom function error');
      });

      const customStrategy = createValidStrategy({
        evictionPolicy: 'custom',
        customEvictionFn: failingCustomFn
      });
      bufferStrategyManager.updateStrategy(customStrategy);

      const itemsCopy = new Map(mockItems);
      const orderCopy = [...mockInsertionOrder];

      bufferStrategyManager.performSyncIntelligentEviction(itemsCopy, orderCopy);

      // Should fallback to FIFO and remove key2
      expect(itemsCopy.has('key2')).toBe(false);
      expect(orderCopy).not.toContain('key2');
    });
  });

  // ============================================================================
  // SECTION 7: ERROR HANDLING & EDGE CASES (Lines 616-700)
  // AI Context: "Error scenarios and edge case validation"
  // ============================================================================

  describe('Error Handling & Edge Cases', () => {
    beforeEach(async () => {
      // Use public method to initialize
      await (bufferStrategyManager as any).doInitialize();
    });

    test('should handle strategy configuration edge cases', async () => {
      const lruStrategy = createValidStrategy({ evictionPolicy: 'lru' });
      bufferStrategyManager.updateStrategy(lruStrategy);

      // Test with minimal configuration
      const result = await bufferStrategyManager.performIntelligentEviction(
        mockItems,
        mockInsertionOrder,
        mockItems.size
      );

      expect(result.evictedKeys).toHaveLength(1);
      expect(result.operationTime).toBeGreaterThan(0);
    });

    test('should handle eviction with empty access tracking', async () => {
      // Create manager with empty tracking data
      const emptyAccessCounts = new Map<string, number>();
      const emptyLastAccessed = new Map<string, Date>();

      const emptyTrackingManager = new BufferStrategyManager(
        createValidStrategy({ evictionPolicy: 'lru' }),
        emptyAccessCounts,
        emptyLastAccessed
      );

      await (emptyTrackingManager as any).doInitialize();

      const result = await emptyTrackingManager.performIntelligentEviction(
        mockItems,
        mockInsertionOrder,
        mockItems.size
      );

      // Should still evict something (items with no access data get 0 timestamp)
      expect(result.evictedKeys).toHaveLength(1);

      await emptyTrackingManager.shutdown();
    });

    test('should handle eviction operation errors', async () => {
      const lruStrategy = createValidStrategy({ evictionPolicy: 'lru' });
      bufferStrategyManager.updateStrategy(lruStrategy);

      // Create items that might cause issues during eviction
      const problematicItems = new Map([
        ['key1', null],
        ['key2', undefined],
        ['key3', { toString: () => { throw new Error('toString error'); } }]
      ]);

      // Should handle problematic items gracefully
      const result = await bufferStrategyManager.performIntelligentEviction(
        problematicItems,
        ['key1', 'key2', 'key3'],
        3
      );

      expect(result.evictedKeys).toHaveLength(1);
      expect(result.remainingSize).toBe(2);
    });

    test('should validate fragmentation calculation', async () => {
      const lruStrategy = createValidStrategy({ evictionPolicy: 'lru' });
      bufferStrategyManager.updateStrategy(lruStrategy);

      const result = await bufferStrategyManager.performIntelligentEviction(
        mockItems,
        mockInsertionOrder,
        mockItems.size
      );

      // Fragmentation reduction should be a valid number
      expect(typeof result.fragmentationReduced).toBe('number');
      expect(result.fragmentationReduced).toBeGreaterThanOrEqual(0);
      expect(result.fragmentationReduced).toBeLessThanOrEqual(1);
    });

    test('should handle synchronous initialization', () => {
      const syncManager = new BufferStrategyManager(
        createValidStrategy({ evictionPolicy: 'lru' }),
        mockAccessCounts,
        mockLastAccessed
      );

      // Test synchronous initialization
      expect(() => {
        syncManager.initializeSync();
      }).not.toThrow();

      // Verify it's functional after sync init
      const itemsCopy = new Map(mockItems);
      const orderCopy = [...mockInsertionOrder];

      syncManager.performSyncIntelligentEviction(itemsCopy, orderCopy);
      expect(itemsCopy.size).toBe(3);
    });

    test('should handle eviction errors and fallback gracefully', async () => {
      await (bufferStrategyManager as any).doInitialize();

      // Create a strategy that will cause an error during eviction
      const errorStrategy = createValidStrategy({
        evictionPolicy: 'custom',
        customEvictionFn: (_items: Map<string, any>) => {
          throw new Error('Eviction processing error');
        }
      });
      bufferStrategyManager.updateStrategy(errorStrategy);

      // Should handle the error gracefully and fallback to FIFO
      const result = await bufferStrategyManager.performIntelligentEviction(
        mockItems,
        mockInsertionOrder,
        mockItems.size
      );

      // Should fallback to FIFO and evict key2 (first in insertion order)
      expect(result.evictedKeys).toContain('key2');
      expect(result.evictedKeys).toHaveLength(1);
    });

    test('should handle synchronous eviction errors with fallback', async () => {
      await (bufferStrategyManager as any).doInitialize();

      // Create a strategy that will cause an error during sync eviction
      const errorStrategy = createValidStrategy({
        evictionPolicy: 'custom',
        customEvictionFn: (_items: Map<string, any>) => {
          throw new Error('Sync eviction processing error');
        }
      });
      bufferStrategyManager.updateStrategy(errorStrategy);

      const itemsCopy = new Map(mockItems);
      const orderCopy = [...mockInsertionOrder];

      // Should handle the error gracefully in sync mode and fallback to FIFO
      bufferStrategyManager.performSyncIntelligentEviction(itemsCopy, orderCopy);

      // Should fallback to FIFO and remove key2 (first in insertion order)
      expect(itemsCopy.has('key2')).toBe(false);
      expect(itemsCopy.size).toBe(3);
    });

    test('should test random eviction sync path', async () => {
      await (bufferStrategyManager as any).doInitialize();

      const randomStrategy = createValidStrategy({ evictionPolicy: 'random' });
      bufferStrategyManager.updateStrategy(randomStrategy);

      const itemsCopy = new Map(mockItems);
      const orderCopy = [...mockInsertionOrder];

      bufferStrategyManager.performSyncIntelligentEviction(itemsCopy, orderCopy);

      // Should evict exactly one item
      expect(itemsCopy.size).toBe(3);
    });

    test('should execute pre-eviction callback when configured', async () => {
      await (bufferStrategyManager as any).doInitialize();

      const evictedItems: Array<{key: string, item: any}> = [];
      const preEvictionCallback = jest.fn((key: string, item: any) => {
        evictedItems.push({ key, item });
      });

      const strategyWithCallback = createValidStrategy({
        evictionPolicy: 'lru',
        preEvictionCallback
      });
      bufferStrategyManager.updateStrategy(strategyWithCallback);

      const result = await bufferStrategyManager.performIntelligentEviction(
        mockItems,
        mockInsertionOrder,
        mockItems.size
      );

      // Verify callback was called
      expect(preEvictionCallback).toHaveBeenCalledTimes(1);
      expect(evictedItems).toHaveLength(1);

      // Verify the evicted item details
      const evictedItem = evictedItems[0];
      expect(result.evictedKeys).toContain(evictedItem.key);
      expect(mockItems.get(evictedItem.key)).toEqual(evictedItem.item);
    });

    test('should execute pre-eviction callback in sync mode', async () => {
      await (bufferStrategyManager as any).doInitialize();

      const evictedItems: Array<{key: string, item: any}> = [];
      const preEvictionCallback = jest.fn((key: string, item: any) => {
        evictedItems.push({ key, item });
      });

      const strategyWithCallback = createValidStrategy({
        evictionPolicy: 'lru',
        preEvictionCallback
      });
      bufferStrategyManager.updateStrategy(strategyWithCallback);

      const itemsCopy = new Map(mockItems);
      const orderCopy = [...mockInsertionOrder];

      bufferStrategyManager.performSyncIntelligentEviction(itemsCopy, orderCopy);

      // Verify callback was called
      expect(preEvictionCallback).toHaveBeenCalledTimes(1);
      expect(evictedItems).toHaveLength(1);

      // Verify the evicted item details
      const evictedItem = evictedItems[0];
      expect(itemsCopy.has(evictedItem.key)).toBe(false);
    });

    test('should handle custom eviction returning valid key in sync mode', async () => {
      await (bufferStrategyManager as any).doInitialize();

      const customEvictionFn = jest.fn((_items: Map<string, any>) => 'key3');
      const strategyWithCustom = createValidStrategy({
        evictionPolicy: 'custom',
        customEvictionFn
      });
      bufferStrategyManager.updateStrategy(strategyWithCustom);

      const itemsCopy = new Map(mockItems);
      const orderCopy = [...mockInsertionOrder];

      bufferStrategyManager.performSyncIntelligentEviction(itemsCopy, orderCopy);

      // Verify custom function was called and key3 was evicted
      expect(customEvictionFn).toHaveBeenCalled();
      expect(itemsCopy.has('key3')).toBe(false);
      expect(itemsCopy.size).toBe(3);
    });
  });

  // ============================================================================
  // SECTION 8: INTEGRATION & MEMORY SAFETY (Lines 771-850)
  // AI Context: "Integration testing and memory safety validation"
  // ============================================================================

  describe('Integration & Memory Safety', () => {
    test('should integrate with MemorySafeResourceManager lifecycle', async () => {
      // Test initialization
      await (bufferStrategyManager as any).doInitialize();
      expect(bufferStrategyManager.isHealthy()).toBe(true);

      // Test resource metrics
      const metrics = bufferStrategyManager.getResourceMetrics();
      expect(metrics).toBeDefined();
      expect(typeof metrics.totalResources).toBe('number');

      // Test shutdown
      await bufferStrategyManager.shutdown();
    });

    test('should handle multiple eviction operations safely', async () => {
      await (bufferStrategyManager as any).doInitialize();

      const strategies: Array<'lru' | 'lfu' | 'fifo' | 'random'> = ['lru', 'lfu', 'fifo', 'random'];

      for (const policy of strategies) {
        const strategy = createValidStrategy({ evictionPolicy: policy });
        bufferStrategyManager.updateStrategy(strategy);

        // Perform multiple evictions
        for (let i = 0; i < 3; i++) {
          setupTestData(); // Reset data for each iteration

          const result = await bufferStrategyManager.performIntelligentEviction(
            mockItems,
            mockInsertionOrder,
            mockItems.size
          );

          expect(result.evictedKeys.length).toBeGreaterThanOrEqual(0);
          expect(result.operationTime).toBeGreaterThan(0);
        }
      }
    });

    test('should maintain memory safety during stress testing', async () => {
      await (bufferStrategyManager as any).doInitialize();

      const lruStrategy = createValidStrategy({ evictionPolicy: 'lru' });
      bufferStrategyManager.updateStrategy(lruStrategy);

      // Perform many eviction operations
      const iterations = 100;
      for (let i = 0; i < iterations; i++) {
        const testItems = new Map([
          [`key_${i}_1`, { value: `value_${i}_1` }],
          [`key_${i}_2`, { value: `value_${i}_2` }]
        ]);

        const testOrder = [`key_${i}_1`, `key_${i}_2`];

        const result = await bufferStrategyManager.performIntelligentEviction(
          testItems,
          testOrder,
          2
        );

        expect(result.evictedKeys).toHaveLength(1);
      }

      // Verify manager is still healthy
      expect(bufferStrategyManager.isHealthy()).toBe(true);
    });

    test('should implement logging interface correctly', async () => {
      await (bufferStrategyManager as any).doInitialize();

      // Test logging methods exist and are callable
      expect(typeof bufferStrategyManager.logInfo).toBe('function');
      expect(typeof bufferStrategyManager.logError).toBe('function');
      expect(typeof bufferStrategyManager.logDebug).toBe('function');
      expect(typeof bufferStrategyManager.logWarning).toBe('function');

      // Test logging methods don't throw
      expect(() => {
        bufferStrategyManager.logInfo('Test info message');
        bufferStrategyManager.logError('Test error message', new Error('Test error'));
        bufferStrategyManager.logDebug('Test debug message');
        bufferStrategyManager.logWarning('Test warning message');
      }).not.toThrow();
    });
  });

  // ============================================================================
  // SECTION 9: ANTI-SIMPLIFICATION COMPLIANCE (Lines 801-850)
  // AI Context: "Anti-Simplification Policy compliance validation"
  // ============================================================================

  describe('Anti-Simplification Policy Compliance', () => {
    test('should implement all planned eviction policies without reduction', async () => {
      await (bufferStrategyManager as any).doInitialize();

      const allPolicies: Array<'lru' | 'lfu' | 'fifo' | 'random' | 'custom'> =
        ['lru', 'lfu', 'fifo', 'random', 'custom'];

      for (const policy of allPolicies) {
        const strategy = policy === 'custom'
          ? createValidStrategy({
              evictionPolicy: 'custom',
              customEvictionFn: (items: Map<string, any>) => Array.from(items.keys())[0]
            })
          : createValidStrategy({ evictionPolicy: policy });

        bufferStrategyManager.updateStrategy(strategy);

        const result = await bufferStrategyManager.performIntelligentEviction(
          mockItems,
          mockInsertionOrder,
          mockItems.size
        );

        // All policies should be functional
        expect(result).toBeDefined();
        expect(result.evictedKeys).toBeDefined();
        expect(result.operationTime).toBeGreaterThan(0);

        setupTestData(); // Reset for next policy
      }
    });

    test('should maintain enterprise-grade features throughout', async () => {
      await (bufferStrategyManager as any).doInitialize();

      // Verify all enterprise features are present
      expect(bufferStrategyManager.validateStrategy).toBeDefined();
      expect(bufferStrategyManager.updateStrategy).toBeDefined();
      expect(bufferStrategyManager.getStrategy).toBeDefined();
      expect(bufferStrategyManager.performIntelligentEviction).toBeDefined();
      expect(bufferStrategyManager.performSyncIntelligentEviction).toBeDefined();

      // Verify memory safety features
      expect(bufferStrategyManager.isHealthy).toBeDefined();
      expect(bufferStrategyManager.getResourceMetrics).toBeDefined();
      expect((bufferStrategyManager as any).doInitialize).toBeDefined();

      // Verify logging capabilities
      expect(bufferStrategyManager.logInfo).toBeDefined();
      expect(bufferStrategyManager.logError).toBeDefined();
      expect(bufferStrategyManager.logDebug).toBeDefined();
      expect(bufferStrategyManager.logWarning).toBeDefined();
    });
  });

  // ============================================================================
  // SECTION 10: RESILIENT TIMING INTEGRATION TESTS (Lines 1077-1350)
  // AI Context: "Comprehensive resilient timing integration testing - Priority P0 Critical"
  // Reference: TimerPoolManager gold standard with 117 timing-related test lines
  // ============================================================================

  describe('Resilient Timing Integration - Priority P0 Critical', () => {

    // ========================================================================
    // SUBSECTION 10.1: DIRECT RESILIENT TIMER INTEGRATION
    // AI Context: "Direct ResilientTimer usage validation and configuration testing"
    // ========================================================================

    test('should properly initialize ResilientTimer with correct configuration', async () => {
      const { ResilientTimer } = require('../../../utils/ResilientTiming');
      const mockResilientTimer = jest.mocked(ResilientTimer);

      await (bufferStrategyManager as any).doInitialize();

      // Verify ResilientTimer was initialized with proper configuration
      expect(mockResilientTimer).toHaveBeenCalledWith({
        enableFallbacks: true,
        maxExpectedDuration: 5000,
        unreliableThreshold: 3,
        estimateBaseline: 2
      });
    });

    test('should use timing context lifecycle correctly during eviction', async () => {
      await (bufferStrategyManager as any).doInitialize();

      const mockEnd = jest.fn(() => ({ duration: 1.5, reliable: true, startTime: Date.now(), endTime: Date.now() + 1.5 }));
      const mockContext = { end: mockEnd };
      const mockStart = jest.fn(() => mockContext);

      (bufferStrategyManager as any)._resilientTimer = { start: mockStart };

      const lruStrategy = createValidStrategy({ evictionPolicy: 'lru' });
      bufferStrategyManager.updateStrategy(lruStrategy);

      await bufferStrategyManager.performIntelligentEviction(mockItems, mockInsertionOrder, mockItems.size);

      // Verify timing context lifecycle: start() -> end()
      expect(mockStart).toHaveBeenCalled();
      expect(mockEnd).toHaveBeenCalled();
    });

    test('should handle timing context lifecycle for sync eviction', async () => {
      await (bufferStrategyManager as any).doInitialize();

      const mockEnd = jest.fn(() => ({ duration: 1.2, reliable: true, method: 'performance.now' }));
      const mockContext = { end: mockEnd };
      const mockStart = jest.fn(() => mockContext);

      (bufferStrategyManager as any)._resilientTimer = { start: mockStart };

      const fifoStrategy = createValidStrategy({ evictionPolicy: 'fifo' });
      bufferStrategyManager.updateStrategy(fifoStrategy);

      bufferStrategyManager.performSyncIntelligentEviction(mockItems, mockInsertionOrder);

      // Verify timing context lifecycle for synchronous operations
      expect(mockStart).toHaveBeenCalled();
      expect(mockEnd).toHaveBeenCalled();
    });

    test('should validate timing reliability and baseline configuration', async () => {
      await (bufferStrategyManager as any).doInitialize();

      // Verify timing infrastructure is properly configured
      const timer = (bufferStrategyManager as any)._resilientTimer;
      expect(timer).toBeDefined();

      // Test with reliable timing
      const reliableTimingResult = { duration: 1.8, reliable: true, method: 'performance.now' };
      const mockReliableContext = { end: jest.fn(() => reliableTimingResult) };
      const mockReliableStart = jest.fn(() => mockReliableContext);

      (bufferStrategyManager as any)._resilientTimer = { start: mockReliableStart };

      const strategy = createValidStrategy({ evictionPolicy: 'lru' });
      bufferStrategyManager.updateStrategy(strategy);

      const result = await bufferStrategyManager.performIntelligentEviction(mockItems, mockInsertionOrder, 4);

      // Verify reliable timing is used when available
      expect(result.operationTime).toBe(1.8);
      expect(mockReliableStart).toHaveBeenCalled();
      expect(mockReliableContext.end).toHaveBeenCalled();
    });

    test('should handle unreliable timing gracefully', async () => {
      await (bufferStrategyManager as any).doInitialize();

      // Test with unreliable timing
      const unreliableTimingResult = { duration: 1.5, reliable: false, method: 'Date.now' };
      const mockUnreliableContext = { end: jest.fn(() => unreliableTimingResult) };
      const mockUnreliableStart = jest.fn(() => mockUnreliableContext);

      (bufferStrategyManager as any)._resilientTimer = { start: mockUnreliableStart };

      const strategy = createValidStrategy({ evictionPolicy: 'random' });
      bufferStrategyManager.updateStrategy(strategy);

      const result = await bufferStrategyManager.performIntelligentEviction(mockItems, mockInsertionOrder, 4);

      // Should still provide timing data even when unreliable
      expect(result.operationTime).toBe(1.5);
      expect(mockUnreliableStart).toHaveBeenCalled();
      expect(mockUnreliableContext.end).toHaveBeenCalled();
    });

    // ========================================================================
    // SUBSECTION 10.2: RESILIENT METRICS COLLECTOR INTEGRATION
    // AI Context: "ResilientMetricsCollector integration verification and metrics recording validation"
    // ========================================================================

    test('should record timing metrics for successful eviction operations', async () => {
      await (bufferStrategyManager as any).doInitialize();

      const mockRecordTiming = jest.fn();
      const mockTimingResult = { duration: 1.5, reliable: true, startTime: Date.now(), endTime: Date.now() + 1.5 };

      (bufferStrategyManager as any)._metricsCollector = { recordTiming: mockRecordTiming };
      (bufferStrategyManager as any)._resilientTimer = {
        start: jest.fn(() => ({
          end: jest.fn(() => mockTimingResult)
        }))
      };

      const lruStrategy = createValidStrategy({ evictionPolicy: 'lru' });
      bufferStrategyManager.updateStrategy(lruStrategy);

      await bufferStrategyManager.performIntelligentEviction(mockItems, mockInsertionOrder, 4);

      // Verify metrics recording for successful operations
      expect(mockRecordTiming).toHaveBeenCalledWith('evictionOperation', mockTimingResult);
    });

    test('should record timing metrics for sync eviction operations', async () => {
      await (bufferStrategyManager as any).doInitialize();

      const mockRecordTiming = jest.fn();
      const mockTimingResult = { duration: 1.2, reliable: true, method: 'performance.now' };

      (bufferStrategyManager as any)._metricsCollector = { recordTiming: mockRecordTiming };
      (bufferStrategyManager as any)._resilientTimer = {
        start: jest.fn(() => ({
          end: jest.fn(() => mockTimingResult)
        }))
      };

      const randomStrategy = createValidStrategy({ evictionPolicy: 'random' });
      bufferStrategyManager.updateStrategy(randomStrategy);

      bufferStrategyManager.performSyncIntelligentEviction(mockItems, mockInsertionOrder);

      // Verify metrics recording for synchronous operations
      expect(mockRecordTiming).toHaveBeenCalledWith('syncEvictionOperation', mockTimingResult);
    });

    test('should record error metrics during failed operations', async () => {
      await (bufferStrategyManager as any).doInitialize();

      const mockRecordTiming = jest.fn();
      const mockTimingResult = { duration: 2.1, reliable: false, method: 'Date.now' };

      // Mock the timing infrastructure to simulate error scenario
      const mockEnd = jest.fn(() => mockTimingResult);
      const mockContext = { end: mockEnd };
      const mockStart = jest.fn(() => mockContext);

      (bufferStrategyManager as any)._resilientTimer = { start: mockStart };
      (bufferStrategyManager as any)._metricsCollector = { recordTiming: mockRecordTiming };

      // Force an error by mocking the internal eviction method to throw
      const originalEvictLRU = (bufferStrategyManager as any)._evictLeastRecentlyUsed;
      (bufferStrategyManager as any)._evictLeastRecentlyUsed = jest.fn(() => {
        throw new Error('Eviction operation failed');
      });

      const lruStrategy = createValidStrategy({ evictionPolicy: 'lru' });
      bufferStrategyManager.updateStrategy(lruStrategy);

      await expect(
        bufferStrategyManager.performIntelligentEviction(mockItems, mockInsertionOrder, 4)
      ).rejects.toThrow('Eviction operation failed');

      // Verify timing context was properly ended (current implementation doesn't record error metrics for async)
      // Note: This tests the current behavior - async errors call end() but don't record metrics
      expect(mockStart).toHaveBeenCalled();
      expect(mockEnd).toHaveBeenCalled();
      // Note: mockRecordTiming is NOT called in async error path (lines 319-322 in source)
      expect(mockRecordTiming).not.toHaveBeenCalled();

      // Restore original method
      (bufferStrategyManager as any)._evictLeastRecentlyUsed = originalEvictLRU;
    });

    test('should validate metrics collection integration with different eviction policies', async () => {
      const mockRecordTiming = jest.fn();
      const policies: Array<'lru' | 'lfu' | 'fifo' | 'random'> = ['lru', 'lfu', 'fifo', 'random'];

      (bufferStrategyManager as any)._metricsCollector = { recordTiming: mockRecordTiming };

      for (const policy of policies) {
        const mockTimingResult = { duration: 1.3, reliable: true, method: 'performance.now' };
        (bufferStrategyManager as any)._resilientTimer = {
          start: jest.fn(() => ({
            end: jest.fn(() => mockTimingResult)
          }))
        };

        const strategy = createValidStrategy({ evictionPolicy: policy });
        bufferStrategyManager.updateStrategy(strategy);

        await bufferStrategyManager.performIntelligentEviction(mockItems, mockInsertionOrder, 4);

        // Reset test data for next policy
        setupTestData();
      }

      // Verify metrics were recorded for all policies
      expect(mockRecordTiming).toHaveBeenCalledTimes(policies.length);
      policies.forEach(() => {
        expect(mockRecordTiming).toHaveBeenCalledWith('evictionOperation', expect.objectContaining({
          duration: 1.3,
          reliable: true
        }));
      });
    });
  });

  // ============================================================================
  // SECTION 11: ERROR PATH TIMING CLEANUP TESTS (Lines 1287-1450)
  // AI Context: "Critical error path timing cleanup for lines 320-322, 373-378"
  // ============================================================================

  describe('Error Path Timing Cleanup - Critical Lines Coverage', () => {

    test('should cleanup timing context on async eviction errors (lines 320-322)', async () => {
      await (bufferStrategyManager as any).doInitialize();

      const mockEnd = jest.fn(() => ({ duration: 1.8, reliable: true, startTime: Date.now(), endTime: Date.now() + 1.8 }));
      const mockContext = { end: mockEnd };
      const mockStart = jest.fn(() => mockContext);
      const mockRecordTiming = jest.fn();

      (bufferStrategyManager as any)._resilientTimer = { start: mockStart };
      (bufferStrategyManager as any)._metricsCollector = { recordTiming: mockRecordTiming };

      // Force an error by mocking the internal eviction method to throw
      const originalEvictLRU = (bufferStrategyManager as any)._evictLeastRecentlyUsed;
      (bufferStrategyManager as any)._evictLeastRecentlyUsed = jest.fn(() => {
        throw new Error('Async eviction error targeting lines 320-322');
      });

      const lruStrategy = createValidStrategy({ evictionPolicy: 'lru' });
      bufferStrategyManager.updateStrategy(lruStrategy);

      await expect(
        bufferStrategyManager.performIntelligentEviction(mockItems, mockInsertionOrder, 4)
      ).rejects.toThrow('Async eviction error targeting lines 320-322');

      // Verify timing context was properly ended even during error (lines 320-322)
      expect(mockStart).toHaveBeenCalled();
      expect(mockEnd).toHaveBeenCalled();
      // Note: Current implementation doesn't record metrics for async errors (only calls end())
      // This tests the actual behavior in lines 320-322 where only evictionContext.end() is called
      expect(mockRecordTiming).not.toHaveBeenCalled();

      // Restore original method
      (bufferStrategyManager as any)._evictLeastRecentlyUsed = originalEvictLRU;
    });

    test('should cleanup timing context on sync eviction errors (lines 373-378)', async () => {
      await (bufferStrategyManager as any).doInitialize();

      const mockEnd = jest.fn(() => ({ duration: 1.6, reliable: true, method: 'performance.now' }));
      const mockContext = { end: mockEnd };
      const mockStart = jest.fn(() => mockContext);
      const mockRecordTiming = jest.fn();

      (bufferStrategyManager as any)._resilientTimer = { start: mockStart };
      (bufferStrategyManager as any)._metricsCollector = { recordTiming: mockRecordTiming };

      // Force an error by mocking the internal sync eviction method to throw
      const originalEvictLRUSync = (bufferStrategyManager as any)._evictLeastRecentlyUsedSync;
      (bufferStrategyManager as any)._evictLeastRecentlyUsedSync = jest.fn(() => {
        throw new Error('Sync eviction error targeting lines 373-378');
      });

      const lruStrategy = createValidStrategy({ evictionPolicy: 'lru' });
      bufferStrategyManager.updateStrategy(lruStrategy);

      expect(() => {
        bufferStrategyManager.performSyncIntelligentEviction(mockItems, mockInsertionOrder);
      }).toThrow('Sync eviction error targeting lines 373-378');

      // Verify timing context was properly ended and error metrics recorded
      expect(mockStart).toHaveBeenCalled();
      expect(mockEnd).toHaveBeenCalled();
      expect(mockRecordTiming).toHaveBeenCalledWith('syncEvictionError', expect.objectContaining({
        duration: 1.6,
        reliable: true
      }));

      // Restore original method
      (bufferStrategyManager as any)._evictLeastRecentlyUsedSync = originalEvictLRUSync;
    });

    test('should handle timing infrastructure failures during error scenarios', async () => {
      const mockEnd = jest.fn(() => { throw new Error('Timing infrastructure failure during error'); });
      const mockContext = { end: mockEnd };
      const mockStart = jest.fn(() => mockContext);
      const mockRecordTiming = jest.fn();

      (bufferStrategyManager as any)._resilientTimer = { start: mockStart };
      (bufferStrategyManager as any)._metricsCollector = { recordTiming: mockRecordTiming };

      // Force both eviction error AND timing infrastructure failure
      const errorStrategy = createValidStrategy({
        evictionPolicy: 'custom',
        customEvictionFn: () => { throw new Error('Primary eviction error'); }
      });
      bufferStrategyManager.updateStrategy(errorStrategy);

      await expect(
        bufferStrategyManager.performIntelligentEviction(mockItems, mockInsertionOrder, 4)
      ).rejects.toThrow(); // Should throw either primary error or timing error

      // Verify timing infrastructure was attempted
      expect(mockStart).toHaveBeenCalled();
      expect(mockEnd).toHaveBeenCalled();
    });

    test('should handle metrics recording failures during error cleanup', async () => {
      const mockEnd = jest.fn(() => ({ duration: 1.9, reliable: false, method: 'Date.now' }));
      const mockContext = { end: mockEnd };
      const mockStart = jest.fn(() => mockContext);
      const mockRecordTiming = jest.fn(() => { throw new Error('Metrics recording failure'); });

      (bufferStrategyManager as any)._resilientTimer = { start: mockStart };
      (bufferStrategyManager as any)._metricsCollector = { recordTiming: mockRecordTiming };

      // Force eviction error with metrics recording failure
      const errorStrategy = createValidStrategy({
        evictionPolicy: 'custom',
        customEvictionFn: () => { throw new Error('Eviction error with metrics failure'); }
      });
      bufferStrategyManager.updateStrategy(errorStrategy);

      await expect(
        bufferStrategyManager.performIntelligentEviction(mockItems, mockInsertionOrder, 4)
      ).rejects.toThrow(); // Should handle gracefully

      // Verify timing context lifecycle completed despite metrics failure
      expect(mockStart).toHaveBeenCalled();
      expect(mockEnd).toHaveBeenCalled();
      expect(mockRecordTiming).toHaveBeenCalled();
    });
  });

  // ============================================================================
  // SECTION 12: JEST ENVIRONMENT COMPATIBILITY TESTS (Lines 1451-1550)
  // AI Context: "Jest fake timer environment testing and compatibility validation"
  // ============================================================================

  describe('Jest Environment Compatibility', () => {

    test('should work correctly with Jest fake timers', async () => {
      await (bufferStrategyManager as any).doInitialize();

      jest.useFakeTimers();

      try {
        const strategy = createValidStrategy({ evictionPolicy: 'lru' });
        bufferStrategyManager.updateStrategy(strategy);

        const result = await bufferStrategyManager.performIntelligentEviction(
          mockItems,
          mockInsertionOrder,
          4
        );

        // Should still provide timing data even in Jest fake timer environment
        expect(result.operationTime).toBeGreaterThan(0);
        expect(result.evictedKeys).toHaveLength(1);
        expect(result.fragmentationReduced).toBeGreaterThanOrEqual(0);
      } finally {
        jest.useRealTimers();
      }
    });

    test('should handle timing fallbacks in test environment', async () => {
      await (bufferStrategyManager as any).doInitialize();

      // Mock environment detection
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'test';

      try {
        const strategy = createValidStrategy({ evictionPolicy: 'fifo' });
        bufferStrategyManager.updateStrategy(strategy);

        const result = await bufferStrategyManager.performIntelligentEviction(
          mockItems,
          mockInsertionOrder,
          4
        );

        // Should still provide timing data even in test environment
        expect(result.operationTime).toBeGreaterThan(0);
        expect(result.evictedKeys).toBeDefined();
      } finally {
        process.env.NODE_ENV = originalEnv;
      }
    });

    test('should validate Jest compatibility with sync operations', () => {
      jest.useFakeTimers();

      try {
        const strategy = createValidStrategy({ evictionPolicy: 'random' });
        bufferStrategyManager.updateStrategy(strategy);

        // Sync operations return void but should not throw with Jest fake timers
        expect(() => {
          bufferStrategyManager.performSyncIntelligentEviction(mockItems, mockInsertionOrder);
        }).not.toThrow();

        // Verify the operation completed successfully (items should be modified)
        expect(mockItems.size).toBeGreaterThanOrEqual(0);
      } finally {
        jest.useRealTimers();
      }
    });

    test('should handle Jest timer advancement during eviction operations', async () => {
      await (bufferStrategyManager as any).doInitialize();

      jest.useFakeTimers();

      try {
        const strategy = createValidStrategy({ evictionPolicy: 'lfu' });
        bufferStrategyManager.updateStrategy(strategy);

        // Start eviction operation
        const evictionPromise = bufferStrategyManager.performIntelligentEviction(
          mockItems,
          mockInsertionOrder,
          4
        );

        // Advance Jest timers
        jest.advanceTimersByTime(100);

        const result = await evictionPromise;

        // Should complete successfully despite timer advancement
        expect(result.operationTime).toBeGreaterThan(0);
        expect(result.evictedKeys).toHaveLength(1);
      } finally {
        jest.useRealTimers();
      }
    });
  });

  // ============================================================================
  // SECTION 13: PERFORMANCE REQUIREMENTS VALIDATION (Lines 1551-1650)
  // AI Context: "Performance requirements validation with timing reliability testing"
  // ============================================================================

  describe('Performance Requirements Validation with Timing', () => {

    test('should validate <2ms eviction operation performance requirement', async () => {
      await (bufferStrategyManager as any).doInitialize();

      const fastTimingResult = { duration: 1.8, reliable: true, method: 'performance.now' };
      const mockFastContext = { end: jest.fn(() => fastTimingResult) };
      const mockFastStart = jest.fn(() => mockFastContext);

      (bufferStrategyManager as any)._resilientTimer = { start: mockFastStart };

      const strategy = createValidStrategy({ evictionPolicy: 'lru' });
      bufferStrategyManager.updateStrategy(strategy);

      const result = await bufferStrategyManager.performIntelligentEviction(mockItems, mockInsertionOrder, 4);

      // Verify <2ms performance requirement is met
      expect(result.operationTime).toBeLessThan(2);
      expect(result.operationTime).toBe(1.8);
      expect(mockFastStart).toHaveBeenCalled();
      expect(mockFastContext.end).toHaveBeenCalled();
    });

    test('should validate 2ms baseline timing configuration', async () => {
      await (bufferStrategyManager as any).doInitialize();

      // Verify 2ms baseline is configured (from report requirement)
      const timer = (bufferStrategyManager as any)._resilientTimer;
      expect(timer).toBeDefined();

      // Test baseline timing validation
      const baselineTimingResult = { duration: 2.0, reliable: true, method: 'performance.now' };
      const mockBaselineContext = { end: jest.fn(() => baselineTimingResult) };
      const mockBaselineStart = jest.fn(() => mockBaselineContext);

      (bufferStrategyManager as any)._resilientTimer = { start: mockBaselineStart };

      const strategy = createValidStrategy({ evictionPolicy: 'fifo' });
      bufferStrategyManager.updateStrategy(strategy);

      const result = await bufferStrategyManager.performIntelligentEviction(mockItems, mockInsertionOrder, 4);

      // Should handle baseline timing correctly
      expect(result.operationTime).toBe(2.0);
      expect(mockBaselineStart).toHaveBeenCalled();
    });

    test('should validate timing reliability thresholds', async () => {
      await (bufferStrategyManager as any).doInitialize();

      const reliabilityScenarios = [
        { duration: 1.5, reliable: true, method: 'performance.now' },
        { duration: 1.8, reliable: false, method: 'Date.now' },
        { duration: 1.2, reliable: true, method: 'performance.now' }
      ];

      for (const scenario of reliabilityScenarios) {
        const mockContext = { end: jest.fn(() => scenario) };
        const mockStart = jest.fn(() => mockContext);

        (bufferStrategyManager as any)._resilientTimer = { start: mockStart };

        const strategy = createValidStrategy({ evictionPolicy: 'random' });
        bufferStrategyManager.updateStrategy(strategy);

        const result = await bufferStrategyManager.performIntelligentEviction(mockItems, mockInsertionOrder, 4);

        // Should handle both reliable and unreliable timing
        expect(result.operationTime).toBe(scenario.duration);
        expect(mockStart).toHaveBeenCalled();
        expect(mockContext.end).toHaveBeenCalled();

        // Reset test data for next scenario
        setupTestData();
      }
    });

    test('should validate performance under different eviction policies', async () => {
      await (bufferStrategyManager as any).doInitialize();

      const policies: Array<'lru' | 'lfu' | 'fifo' | 'random'> = ['lru', 'lfu', 'fifo', 'random'];
      const performanceResults: number[] = [];

      for (const policy of policies) {
        const timingResult = { duration: 1.5, reliable: true, method: 'performance.now' };
        const mockContext = { end: jest.fn(() => timingResult) };
        const mockStart = jest.fn(() => mockContext);

        (bufferStrategyManager as any)._resilientTimer = { start: mockStart };

        const strategy = createValidStrategy({ evictionPolicy: policy });
        bufferStrategyManager.updateStrategy(strategy);

        const result = await bufferStrategyManager.performIntelligentEviction(mockItems, mockInsertionOrder, 4);

        // All policies should meet <2ms requirement
        expect(result.operationTime).toBeLessThan(2);
        performanceResults.push(result.operationTime);

        // Reset test data for next policy
        setupTestData();
      }

      // Verify consistent performance across all policies
      expect(performanceResults).toHaveLength(policies.length);
      performanceResults.forEach(time => {
        expect(time).toBeLessThan(2);
        expect(time).toBeGreaterThan(0);
      });
    });
  });
});
