/**
 * ============================================================================
 * AI CONTEXT: EventConfiguration Test Suite - Configuration Management & Validation
 * Purpose: Comprehensive testing of event configuration factory functions and validation
 * Complexity: Medium - Configuration validation with enterprise-grade settings testing
 * AI Navigation: 7 logical sections - Setup, Defaults, Factories, Validation, Utilities, Integration, Edge Cases
 * Dependencies: EventConfiguration (pure functions), EventTypes interfaces
 * Performance: <1ms per configuration operation
 * ============================================================================
 */

/**
 * @file EventConfiguration Test Suite
 * @filepath shared/src/base/__tests__/modules/event-handler-registry/EventConfiguration.test.ts
 * @task-id T-TSK-02.SUB-03.9.ECO-01
 * @component event-handler-registry-enhanced
 * @reference foundation-context.MEMORY-SAFETY.003
 * @template enhanced-configuration-testing
 * @tier T0
 * @context foundation-context
 * @category Memory-Safety-Enhanced-Configuration-Testing
 * @created 2025-08-05
 * @modified 2025-08-05
 *
 * @description
 * Comprehensive test suite for EventConfiguration module:
 * - Default configuration constants validation
 * - Factory function testing with partial configurations
 * - Configuration validation function testing
 * - Utility function testing for configuration analysis
 * - Integration testing with EventTypes interfaces
 * - Edge case and error handling validation
 * - Anti-Simplification Policy compliance with comprehensive configuration coverage
 */

// ============================================================================
// TABLE OF CONTENTS
// ============================================================================
// CLASSES:
//   None (Pure configuration functions testing)
// INTERFACES:
//   All EventTypes configuration interfaces tested
// GLOBAL FUNCTIONS:
//   createEnhancedConfig() (Line 151)
//   createDeduplicationConfig() (Line 175)
//   createBufferingConfig() (Line 194)
//   createRetryPolicy() (Line 221)
//   createEmissionOptions() (Line 246)
//   validateEnhancedConfig() (Line 270)
//   validateResilientTimingConfig() (Line 299)
//   isPerformanceMonitoringEnabled() (Line 322)
//   isBufferingEnabled() (Line 329)
//   isDeduplicationEnabled() (Line 336)
//   getEffectiveEmissionTimeout() (Line 343)
//   getConfigurationSummary() (Line 354)
// IMPORTED:
//   All EventConfiguration functions and constants (Imported from '../../../event-handler-registry/types/EventConfiguration')
//   All EventTypes interfaces (Imported from '../../../event-handler-registry/types/EventTypes')
// ============================================================================

import {
  // Default Configuration Constants
  DEFAULT_RESILIENT_TIMING_CONFIG,
  DEFAULT_DEDUPLICATION_CONFIG,
  DEFAULT_BUFFERING_CONFIG,
  DEFAULT_RETRY_POLICY,
  DEFAULT_EMISSION_OPTIONS,
  DEFAULT_ENHANCED_CONFIG,
  PERFORMANCE_THRESHOLDS,
  DEFAULT_MIDDLEWARE_CONFIG,
  MEMORY_SAFETY_LIMITS,
  
  // Factory Functions
  createEnhancedConfig,
  createDeduplicationConfig,
  createBufferingConfig,
  createRetryPolicy,
  createEmissionOptions,
  
  // Validation Functions
  validateEnhancedConfig,
  validateResilientTimingConfig,
  
  // Utility Functions
  isPerformanceMonitoringEnabled,
  isBufferingEnabled,
  isDeduplicationEnabled,
  getEffectiveEmissionTimeout,
  getConfigurationSummary
} from '../../../event-handler-registry/types/EventConfiguration';

import {
  IEventHandlerRegistryEnhancedConfig,
  IHandlerDeduplication,
  IEventBuffering,
  IRetryPolicy,
  IEmissionOptions
} from '../../../event-handler-registry/types/EventTypes';

// Jest configuration for comprehensive testing
jest.setTimeout(30000); // 30 seconds for comprehensive tests

describe('EventConfiguration - Comprehensive Configuration Management Suite', () => {

  // ============================================================================
  // SECTION 1: DEFAULT CONFIGURATION CONSTANTS TESTING (Lines 1-150)
  // AI Context: "Validation of default configuration constants and values"
  // ============================================================================

  describe('Default Configuration Constants', () => {
    test('should provide valid DEFAULT_RESILIENT_TIMING_CONFIG', () => {
      expect(DEFAULT_RESILIENT_TIMING_CONFIG).toBeDefined();
      expect(DEFAULT_RESILIENT_TIMING_CONFIG.enableFallbacks).toBe(true);
      expect(DEFAULT_RESILIENT_TIMING_CONFIG.maxExpectedDuration).toBe(10000);
      expect(DEFAULT_RESILIENT_TIMING_CONFIG.unreliableThreshold).toBe(3);
      expect(DEFAULT_RESILIENT_TIMING_CONFIG.estimateBaseline).toBe(5);
      expect(typeof DEFAULT_RESILIENT_TIMING_CONFIG.enableDetailedLogging).toBe('boolean');
    });

    test('should provide valid DEFAULT_DEDUPLICATION_CONFIG', () => {
      expect(DEFAULT_DEDUPLICATION_CONFIG).toBeDefined();
      expect(DEFAULT_DEDUPLICATION_CONFIG.enabled).toBe(false);
      expect(DEFAULT_DEDUPLICATION_CONFIG.strategy).toBe('signature');
      expect(DEFAULT_DEDUPLICATION_CONFIG.autoMergeMetadata).toBe(true);
    });

    test('should provide valid DEFAULT_BUFFERING_CONFIG', () => {
      expect(DEFAULT_BUFFERING_CONFIG).toBeDefined();
      expect(DEFAULT_BUFFERING_CONFIG.enabled).toBe(false);
      expect(DEFAULT_BUFFERING_CONFIG.bufferSize).toBe(1000);
      expect(DEFAULT_BUFFERING_CONFIG.flushInterval).toBe(100);
      expect(DEFAULT_BUFFERING_CONFIG.bufferStrategy).toBe('fifo');
      expect(DEFAULT_BUFFERING_CONFIG.autoFlushThreshold).toBe(0.8);
      expect(DEFAULT_BUFFERING_CONFIG.onBufferOverflow).toBe('drop_oldest');
    });

    test('should provide valid DEFAULT_RETRY_POLICY', () => {
      expect(DEFAULT_RETRY_POLICY).toBeDefined();
      expect(DEFAULT_RETRY_POLICY.maxRetries).toBe(3);
      expect(DEFAULT_RETRY_POLICY.retryDelayMs).toBe(1000);
      expect(DEFAULT_RETRY_POLICY.backoffMultiplier).toBe(2);
      expect(DEFAULT_RETRY_POLICY.maxBackoffDelayMs).toBe(10000);
      expect(Array.isArray(DEFAULT_RETRY_POLICY.retryableErrorTypes)).toBe(true);
      expect(Array.isArray(DEFAULT_RETRY_POLICY.nonRetryableErrorTypes)).toBe(true);
    });

    test('should provide valid DEFAULT_EMISSION_OPTIONS', () => {
      expect(DEFAULT_EMISSION_OPTIONS).toBeDefined();
      expect(DEFAULT_EMISSION_OPTIONS.priority).toBe('normal');
      expect(DEFAULT_EMISSION_OPTIONS.timeout).toBe(5000);
      expect(DEFAULT_EMISSION_OPTIONS.requireAcknowledgment).toBe(false);
    });

    test('should provide valid DEFAULT_ENHANCED_CONFIG', () => {
      expect(DEFAULT_ENHANCED_CONFIG).toBeDefined();
      expect(DEFAULT_ENHANCED_CONFIG.deduplication).toEqual(DEFAULT_DEDUPLICATION_CONFIG);
      expect(DEFAULT_ENHANCED_CONFIG.maxMiddleware).toBe(10);
      expect(DEFAULT_ENHANCED_CONFIG.emissionTimeoutMs).toBe(10000);
      expect(DEFAULT_ENHANCED_CONFIG.resilientTiming).toEqual(DEFAULT_RESILIENT_TIMING_CONFIG);
    });

    test('should provide valid PERFORMANCE_THRESHOLDS', () => {
      expect(PERFORMANCE_THRESHOLDS).toBeDefined();
      expect(PERFORMANCE_THRESHOLDS.EMISSION_MAX_DURATION_MS).toBe(10);
      expect(PERFORMANCE_THRESHOLDS.MIDDLEWARE_MAX_DURATION_MS).toBe(2);
      expect(PERFORMANCE_THRESHOLDS.DEDUPLICATION_MAX_DURATION_MS).toBe(1);
      expect(PERFORMANCE_THRESHOLDS.BUFFERING_FLUSH_MAX_DURATION_MS).toBe(5);
      expect(PERFORMANCE_THRESHOLDS.BATCH_EMISSION_MAX_DURATION_MS).toBe(50);
      expect(PERFORMANCE_THRESHOLDS.TIMING_RELIABILITY_THRESHOLD).toBe(0.8);
    });

    test('should provide valid DEFAULT_MIDDLEWARE_CONFIG', () => {
      expect(DEFAULT_MIDDLEWARE_CONFIG).toBeDefined();
      expect(DEFAULT_MIDDLEWARE_CONFIG.MAX_MIDDLEWARE_COUNT).toBe(10);
      expect(DEFAULT_MIDDLEWARE_CONFIG.PRIORITY_MIN).toBe(0);
      expect(DEFAULT_MIDDLEWARE_CONFIG.PRIORITY_MAX).toBe(1000);
      expect(DEFAULT_MIDDLEWARE_CONFIG.EXECUTION_TIMEOUT_MS).toBe(5000);
    });

    test('should provide valid MEMORY_SAFETY_LIMITS', () => {
      expect(MEMORY_SAFETY_LIMITS).toBeDefined();
      expect(MEMORY_SAFETY_LIMITS.MAX_INTERVALS).toBe(10);
      expect(MEMORY_SAFETY_LIMITS.MAX_TIMEOUTS).toBe(20);
      expect(MEMORY_SAFETY_LIMITS.MAX_CACHE_SIZE).toBe(2 * 1024 * 1024);
      expect(MEMORY_SAFETY_LIMITS.MEMORY_THRESHOLD_MB).toBe(100);
      expect(MEMORY_SAFETY_LIMITS.CLEANUP_INTERVAL_MS).toBe(300000);
    });
  });

  // ============================================================================
  // SECTION 2: FACTORY FUNCTIONS TESTING (Lines 151-300)
  // AI Context: "Configuration factory functions with partial configuration merging"
  // ============================================================================

  describe('Configuration Factory Functions', () => {
    test('should create enhanced config with defaults when no partial provided', () => {
      const config = createEnhancedConfig();

      expect(config).toEqual(DEFAULT_ENHANCED_CONFIG);
      expect(config.deduplication).toEqual(DEFAULT_DEDUPLICATION_CONFIG);
      expect(config.resilientTiming).toEqual(DEFAULT_RESILIENT_TIMING_CONFIG);
      expect(config.maxMiddleware).toBe(10);
      expect(config.emissionTimeoutMs).toBe(10000);
    });

    test('should create enhanced config with partial overrides', () => {
      const partial: Partial<IEventHandlerRegistryEnhancedConfig> = {
        maxMiddleware: 20,
        emissionTimeoutMs: 15000,
        resilientTiming: {
          enableFallbacks: false,
          maxExpectedDuration: 5000,
          unreliableThreshold: 5,
          estimateBaseline: 10
        }
      };

      const config = createEnhancedConfig(partial);

      expect(config.maxMiddleware).toBe(20);
      expect(config.emissionTimeoutMs).toBe(15000);
      expect(config.resilientTiming?.enableFallbacks).toBe(false);
      expect(config.resilientTiming?.maxExpectedDuration).toBe(5000);
      expect(config.resilientTiming?.unreliableThreshold).toBe(5);
      expect(config.resilientTiming?.estimateBaseline).toBe(10);

      // Should preserve defaults for non-overridden values
      expect(config.deduplication).toEqual(DEFAULT_DEDUPLICATION_CONFIG);
    });

    test('should create enhanced config with buffering when provided', () => {
      const partial: Partial<IEventHandlerRegistryEnhancedConfig> = {
        buffering: {
          enabled: true,
          bufferSize: 2000,
          flushInterval: 200,
          bufferStrategy: 'priority',
          autoFlushThreshold: 0.9,
          onBufferOverflow: 'force_flush'
        }
      };

      const config = createEnhancedConfig(partial);

      expect(config.buffering).toBeDefined();
      expect(config.buffering?.enabled).toBe(true);
      expect(config.buffering?.bufferSize).toBe(2000);
      expect(config.buffering?.bufferStrategy).toBe('priority');
    });

    test('should create deduplication config with defaults', () => {
      const config = createDeduplicationConfig();

      expect(config).toEqual(DEFAULT_DEDUPLICATION_CONFIG);
    });

    test('should create deduplication config with partial overrides', () => {
      const partial: Partial<IHandlerDeduplication> = {
        enabled: true,
        strategy: 'reference',
        autoMergeMetadata: false
      };

      const config = createDeduplicationConfig(partial);

      expect(config.enabled).toBe(true);
      expect(config.strategy).toBe('reference');
      expect(config.autoMergeMetadata).toBe(false);
    });

    test('should throw error for custom deduplication without function', () => {
      const partial: Partial<IHandlerDeduplication> = {
        enabled: true,
        strategy: 'custom'
        // Missing customDeduplicationFn
      };

      expect(() => createDeduplicationConfig(partial)).toThrow(
        'Custom deduplication function required when strategy is "custom"'
      );
    });

    test('should create valid custom deduplication config with function', () => {
      const customFn = (h1: any, h2: any) => h1.toString() === h2.toString();
      const partial: Partial<IHandlerDeduplication> = {
        enabled: true,
        strategy: 'custom',
        customDeduplicationFn: customFn
      };

      const config = createDeduplicationConfig(partial);

      expect(config.enabled).toBe(true);
      expect(config.strategy).toBe('custom');
      expect(config.customDeduplicationFn).toBe(customFn);
    });

    test('should create buffering config with defaults', () => {
      const config = createBufferingConfig();

      expect(config).toEqual(DEFAULT_BUFFERING_CONFIG);
    });

    test('should create buffering config with partial overrides', () => {
      const partial: Partial<IEventBuffering> = {
        enabled: true,
        bufferSize: 5000,
        flushInterval: 500,
        bufferStrategy: 'lifo',
        autoFlushThreshold: 0.7,
        onBufferOverflow: 'drop_newest'
      };

      const config = createBufferingConfig(partial);

      expect(config.enabled).toBe(true);
      expect(config.bufferSize).toBe(5000);
      expect(config.flushInterval).toBe(500);
      expect(config.bufferStrategy).toBe('lifo');
      expect(config.autoFlushThreshold).toBe(0.7);
      expect(config.onBufferOverflow).toBe('drop_newest');
    });

    test('should validate buffering config when enabled', () => {
      // Test invalid buffer size
      expect(() => createBufferingConfig({
        enabled: true,
        bufferSize: 0
      })).toThrow('Buffer size must be greater than 0');

      // Test invalid flush interval
      expect(() => createBufferingConfig({
        enabled: true,
        flushInterval: 0
      })).toThrow('Flush interval must be greater than 0');

      // Test invalid auto flush threshold
      expect(() => createBufferingConfig({
        enabled: true,
        autoFlushThreshold: 1.5
      })).toThrow('Auto flush threshold must be between 0 and 1');

      expect(() => createBufferingConfig({
        enabled: true,
        autoFlushThreshold: -0.1
      })).toThrow('Auto flush threshold must be between 0 and 1');
    });

    test('should create retry policy with defaults', () => {
      const config = createRetryPolicy();

      expect(config).toEqual(DEFAULT_RETRY_POLICY);
      expect(config.maxRetries).toBe(3);
      expect(config.retryDelayMs).toBe(1000);
      expect(config.backoffMultiplier).toBe(2);
    });

    test('should create retry policy with partial overrides', () => {
      const partial: Partial<IRetryPolicy> = {
        maxRetries: 5,
        retryDelayMs: 2000,
        backoffMultiplier: 1.5,
        maxBackoffDelayMs: 20000
      };

      const config = createRetryPolicy(partial);

      expect(config.maxRetries).toBe(5);
      expect(config.retryDelayMs).toBe(2000);
      expect(config.backoffMultiplier).toBe(1.5);
      expect(config.maxBackoffDelayMs).toBe(20000);

      // Should preserve default error types
      expect(config.retryableErrorTypes).toEqual(DEFAULT_RETRY_POLICY.retryableErrorTypes);
      expect(config.nonRetryableErrorTypes).toEqual(DEFAULT_RETRY_POLICY.nonRetryableErrorTypes);
    });

    test('should validate retry policy configuration', () => {
      // Test invalid max retries
      expect(() => createRetryPolicy({
        maxRetries: -1
      })).toThrow('Max retries must be non-negative');

      // Test invalid retry delay
      expect(() => createRetryPolicy({
        retryDelayMs: 0
      })).toThrow('Retry delay must be greater than 0');

      // Test invalid backoff multiplier
      expect(() => createRetryPolicy({
        backoffMultiplier: 0
      })).toThrow('Backoff multiplier must be greater than 0');
    });

    test('should create retry policy with custom error types', () => {
      const partial: Partial<IRetryPolicy> = {
        retryableErrorTypes: ['CustomNetworkError', 'CustomTimeoutError'],
        nonRetryableErrorTypes: ['CustomValidationError']
      };

      const config = createRetryPolicy(partial);

      expect(config.retryableErrorTypes).toEqual(['CustomNetworkError', 'CustomTimeoutError']);
      expect(config.nonRetryableErrorTypes).toEqual(['CustomValidationError']);

      // Should preserve default numeric values
      expect(config.maxRetries).toBe(DEFAULT_RETRY_POLICY.maxRetries);
      expect(config.retryDelayMs).toBe(DEFAULT_RETRY_POLICY.retryDelayMs);
      expect(config.backoffMultiplier).toBe(DEFAULT_RETRY_POLICY.backoffMultiplier);
    });

    test('should handle edge cases in retry policy validation', () => {
      // Test zero max retries (valid)
      expect(() => createRetryPolicy({ maxRetries: 0 })).not.toThrow();

      // Test very small retry delay
      expect(() => createRetryPolicy({ retryDelayMs: 1 })).not.toThrow();

      // Test very small backoff multiplier
      expect(() => createRetryPolicy({ backoffMultiplier: 0.1 })).not.toThrow();
    });
  });

  // ============================================================================
  // SECTION 3: VALIDATION FUNCTIONS TESTING (Lines 301-450)
  // AI Context: "Configuration validation functions with error handling"
  // ============================================================================

  describe('Configuration Validation Functions', () => {
    test('should validate enhanced config successfully with valid configuration', () => {
      const validConfig: IEventHandlerRegistryEnhancedConfig = {
        maxMiddleware: 15,
        emissionTimeoutMs: 8000,
        deduplication: {
          enabled: true,
          strategy: 'signature',
          autoMergeMetadata: true
        },
        buffering: {
          enabled: true,
          bufferSize: 2000,
          flushInterval: 150,
          bufferStrategy: 'priority',
          autoFlushThreshold: 0.75,
          onBufferOverflow: 'force_flush'
        },
        resilientTiming: {
          enableFallbacks: true,
          maxExpectedDuration: 8000,
          unreliableThreshold: 4,
          estimateBaseline: 8,
          enableDetailedLogging: true
        }
      };

      expect(() => validateEnhancedConfig(validConfig)).not.toThrow();
    });

    test('should throw error for invalid maxMiddleware', () => {
      const invalidConfig: IEventHandlerRegistryEnhancedConfig = {
        maxMiddleware: 0
      };

      expect(() => validateEnhancedConfig(invalidConfig)).toThrow(
        'Max middleware must be greater than 0'
      );

      const negativeConfig: IEventHandlerRegistryEnhancedConfig = {
        maxMiddleware: -5
      };

      expect(() => validateEnhancedConfig(negativeConfig)).toThrow(
        'Max middleware must be greater than 0'
      );
    });

    test('should throw error for invalid emissionTimeoutMs', () => {
      const invalidConfig: IEventHandlerRegistryEnhancedConfig = {
        emissionTimeoutMs: 0
      };

      expect(() => validateEnhancedConfig(invalidConfig)).toThrow(
        'Emission timeout must be greater than 0'
      );

      const negativeConfig: IEventHandlerRegistryEnhancedConfig = {
        emissionTimeoutMs: -1000
      };

      expect(() => validateEnhancedConfig(negativeConfig)).toThrow(
        'Emission timeout must be greater than 0'
      );
    });

    test('should validate deduplication config when provided', () => {
      const configWithInvalidDeduplication: IEventHandlerRegistryEnhancedConfig = {
        deduplication: {
          enabled: true,
          strategy: 'custom',
          autoMergeMetadata: true
          // Missing customDeduplicationFn
        }
      };

      expect(() => validateEnhancedConfig(configWithInvalidDeduplication)).toThrow(
        'Custom deduplication function required when strategy is "custom"'
      );
    });

    test('should validate buffering config when provided', () => {
      const configWithInvalidBuffering: IEventHandlerRegistryEnhancedConfig = {
        buffering: {
          enabled: true,
          bufferSize: 0, // Invalid
          flushInterval: 100,
          bufferStrategy: 'fifo',
          autoFlushThreshold: 0.8,
          onBufferOverflow: 'drop_oldest'
        }
      };

      expect(() => validateEnhancedConfig(configWithInvalidBuffering)).toThrow(
        'Buffer size must be greater than 0'
      );
    });

    test('should validate resilient timing config when provided', () => {
      const configWithInvalidTiming: IEventHandlerRegistryEnhancedConfig = {
        resilientTiming: {
          enableFallbacks: true,
          maxExpectedDuration: 0, // Invalid
          unreliableThreshold: 3,
          estimateBaseline: 5
        }
      };

      expect(() => validateEnhancedConfig(configWithInvalidTiming)).toThrow(
        'Max expected duration must be greater than 0'
      );
    });

    test('should validate resilient timing config directly', () => {
      const validConfig = {
        enableFallbacks: true,
        maxExpectedDuration: 5000,
        unreliableThreshold: 2,
        estimateBaseline: 10,
        enableDetailedLogging: false
      };

      expect(() => validateResilientTimingConfig(validConfig)).not.toThrow();
    });

    test('should throw error for invalid resilient timing maxExpectedDuration', () => {
      const invalidConfig = {
        enableFallbacks: true,
        maxExpectedDuration: 0,
        unreliableThreshold: 3,
        estimateBaseline: 5
      };

      expect(() => validateResilientTimingConfig(invalidConfig)).toThrow(
        'Max expected duration must be greater than 0'
      );
    });

    test('should throw error for invalid resilient timing unreliableThreshold', () => {
      const invalidConfig = {
        enableFallbacks: true,
        maxExpectedDuration: 5000,
        unreliableThreshold: 0,
        estimateBaseline: 5
      };

      expect(() => validateResilientTimingConfig(invalidConfig)).toThrow(
        'Unreliable threshold must be greater than 0'
      );
    });

    test('should throw error for invalid resilient timing estimateBaseline', () => {
      const invalidConfig = {
        enableFallbacks: true,
        maxExpectedDuration: 5000,
        unreliableThreshold: 3,
        estimateBaseline: 0
      };

      expect(() => validateResilientTimingConfig(invalidConfig)).toThrow(
        'Estimate baseline must be greater than 0'
      );
    });
  });

  // ============================================================================
  // SECTION 4: UTILITY FUNCTIONS TESTING (Lines 451-600)
  // AI Context: "Configuration utility functions and analysis helpers"
  // ============================================================================

  describe('Configuration Utility Functions', () => {
    test('should detect performance monitoring enabled correctly', () => {
      const configWithMonitoring: IEventHandlerRegistryEnhancedConfig = {
        resilientTiming: {
          enableFallbacks: true,
          maxExpectedDuration: 5000,
          unreliableThreshold: 3,
          estimateBaseline: 5,
          enableDetailedLogging: true
        }
      };

      expect(isPerformanceMonitoringEnabled(configWithMonitoring)).toBe(true);

      const configWithoutMonitoring: IEventHandlerRegistryEnhancedConfig = {
        resilientTiming: {
          enableFallbacks: true,
          maxExpectedDuration: 5000,
          unreliableThreshold: 3,
          estimateBaseline: 5,
          enableDetailedLogging: false
        }
      };

      expect(isPerformanceMonitoringEnabled(configWithoutMonitoring)).toBe(false);

      const configWithoutTiming: IEventHandlerRegistryEnhancedConfig = {};
      expect(isPerformanceMonitoringEnabled(configWithoutTiming)).toBe(false);
    });

    test('should detect buffering enabled correctly', () => {
      const configWithBuffering: IEventHandlerRegistryEnhancedConfig = {
        buffering: {
          enabled: true,
          bufferSize: 1000,
          flushInterval: 100,
          bufferStrategy: 'fifo',
          autoFlushThreshold: 0.8,
          onBufferOverflow: 'drop_oldest'
        }
      };

      expect(isBufferingEnabled(configWithBuffering)).toBe(true);

      const configWithoutBuffering: IEventHandlerRegistryEnhancedConfig = {
        buffering: {
          enabled: false,
          bufferSize: 1000,
          flushInterval: 100,
          bufferStrategy: 'fifo',
          autoFlushThreshold: 0.8,
          onBufferOverflow: 'drop_oldest'
        }
      };

      expect(isBufferingEnabled(configWithoutBuffering)).toBe(false);

      const configWithoutBufferingConfig: IEventHandlerRegistryEnhancedConfig = {};
      expect(isBufferingEnabled(configWithoutBufferingConfig)).toBe(false);
    });

    test('should detect deduplication enabled correctly', () => {
      const configWithDeduplication: IEventHandlerRegistryEnhancedConfig = {
        deduplication: {
          enabled: true,
          strategy: 'signature',
          autoMergeMetadata: true
        }
      };

      expect(isDeduplicationEnabled(configWithDeduplication)).toBe(true);

      const configWithoutDeduplication: IEventHandlerRegistryEnhancedConfig = {
        deduplication: {
          enabled: false,
          strategy: 'signature',
          autoMergeMetadata: true
        }
      };

      expect(isDeduplicationEnabled(configWithoutDeduplication)).toBe(false);

      const configWithoutDeduplicationConfig: IEventHandlerRegistryEnhancedConfig = {};
      expect(isDeduplicationEnabled(configWithoutDeduplicationConfig)).toBe(false);
    });

    test('should calculate effective emission timeout correctly', () => {
      // Test with config timeout smaller than resilient timing max
      const config1: IEventHandlerRegistryEnhancedConfig = {
        emissionTimeoutMs: 5000,
        resilientTiming: {
          enableFallbacks: true,
          maxExpectedDuration: 8000,
          unreliableThreshold: 3,
          estimateBaseline: 5
        }
      };

      expect(getEffectiveEmissionTimeout(config1)).toBe(5000);

      // Test with resilient timing max smaller than config timeout
      const config2: IEventHandlerRegistryEnhancedConfig = {
        emissionTimeoutMs: 15000,
        resilientTiming: {
          enableFallbacks: true,
          maxExpectedDuration: 8000,
          unreliableThreshold: 3,
          estimateBaseline: 5
        }
      };

      expect(getEffectiveEmissionTimeout(config2)).toBe(8000);

      // Test with defaults when no config provided
      const config3: IEventHandlerRegistryEnhancedConfig = {};
      const expectedTimeout = Math.min(
        DEFAULT_ENHANCED_CONFIG.emissionTimeoutMs!,
        DEFAULT_RESILIENT_TIMING_CONFIG.maxExpectedDuration
      );
      expect(getEffectiveEmissionTimeout(config3)).toBe(expectedTimeout);
    });

    test('should generate comprehensive configuration summary', () => {
      const complexConfig: IEventHandlerRegistryEnhancedConfig = {
        deduplication: {
          enabled: true,
          strategy: 'reference',
          autoMergeMetadata: false
        },
        buffering: {
          enabled: true,
          bufferSize: 2000,
          flushInterval: 200,
          bufferStrategy: 'priority',
          autoFlushThreshold: 0.9,
          onBufferOverflow: 'force_flush'
        },
        maxMiddleware: 15,
        emissionTimeoutMs: 12000,
        resilientTiming: {
          enableFallbacks: true,
          maxExpectedDuration: 8000,
          unreliableThreshold: 4,
          estimateBaseline: 10,
          enableDetailedLogging: true
        }
      };

      const summary = getConfigurationSummary(complexConfig);

      expect(summary.deduplicationEnabled).toBe(true);
      expect(summary.bufferingEnabled).toBe(true);
      expect(summary.performanceMonitoringEnabled).toBe(true);
      expect(summary.maxMiddleware).toBe(15);
      expect(summary.effectiveTimeout).toBe(8000); // Min of 12000 and 8000
      expect(summary.resilientTimingEnabled).toBe(true);
    });

    test('should generate configuration summary with defaults', () => {
      const minimalConfig: IEventHandlerRegistryEnhancedConfig = {};

      const summary = getConfigurationSummary(minimalConfig);

      expect(summary.deduplicationEnabled).toBe(false);
      expect(summary.bufferingEnabled).toBe(false);
      expect(summary.performanceMonitoringEnabled).toBe(false);
      expect(summary.maxMiddleware).toBe(DEFAULT_ENHANCED_CONFIG.maxMiddleware!);
      expect(summary.effectiveTimeout).toBe(
        Math.min(
          DEFAULT_ENHANCED_CONFIG.emissionTimeoutMs!,
          DEFAULT_RESILIENT_TIMING_CONFIG.maxExpectedDuration
        )
      );
      expect(summary.resilientTimingEnabled).toBe(false); // No resilient timing config provided
    });

    test('should create emission options with resilient timing warnings', () => {
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

      const partial: Partial<IEmissionOptions> = {
        timeout: 15000 // Exceeds DEFAULT_RESILIENT_TIMING_CONFIG.maxExpectedDuration (10000)
      };

      createEmissionOptions(partial);

      expect(consoleSpy).toHaveBeenCalledWith(
        'Emission timeout (15000ms) exceeds resilient timing max duration (10000ms)'
      );

      consoleSpy.mockRestore();
    });

    test('should handle edge cases in configuration analysis', () => {
      // Test configuration with undefined resilient timing
      const configWithUndefinedTiming: IEventHandlerRegistryEnhancedConfig = {
        resilientTiming: undefined
      };

      expect(isPerformanceMonitoringEnabled(configWithUndefinedTiming)).toBe(false);
      expect(getEffectiveEmissionTimeout(configWithUndefinedTiming)).toBe(
        DEFAULT_ENHANCED_CONFIG.emissionTimeoutMs!
      );

      // Test configuration with partial resilient timing
      const configWithPartialTiming: IEventHandlerRegistryEnhancedConfig = {
        resilientTiming: {
          enableFallbacks: false,
          maxExpectedDuration: 3000,
          unreliableThreshold: 2,
          estimateBaseline: 3
          // Missing enableDetailedLogging
        }
      };

      expect(isPerformanceMonitoringEnabled(configWithPartialTiming)).toBe(false);
    });
  });
});
