/**
 * ============================================================================
 * AI CONTEXT: MiddlewareManager Test Suite - Priority Middleware & Execution Hooks
 * Purpose: Comprehensive testing of middleware chain execution with resilient timing
 * Complexity: High - Complex middleware chain with enterprise-grade timing validation
 * AI Navigation: 7 logical sections - Setup, Core, Chain, Performance, Error, Timing, Memory
 * Dependencies: ResilientTiming, ResilientMetrics, EventTypes, MemorySafeResourceManager
 * Performance: <5ms per middleware execution validation
 * ============================================================================
 */

/**
 * @file MiddlewareManager Test Suite
 * @filepath shared/src/base/__tests__/modules/event-handler-registry/MiddlewareManager.test.ts
 * @task-id T-TSK-02.SUB-03.2.MWM-01
 * @component event-handler-registry-enhanced
 * @reference foundation-context.MEMORY-SAFETY.003
 * @template enhanced-middleware-testing
 * @tier T0
 * @context foundation-context
 * @category Memory-Safety-Enhanced-Middleware-Testing
 * @created 2025-08-04
 * @modified 2025-08-04
 *
 * @description
 * Comprehensive test suite for MiddlewareManager module:
 * - Priority-based middleware execution testing with resilient timing integration
 * - Middleware chain management with enterprise-grade performance validation
 * - Handler execution with middleware hooks and timing measurement testing
 * - Anti-Simplification Policy compliance with comprehensive middleware coverage
 * - Memory-safe resource management validation
 */

// ============================================================================
// TABLE OF CONTENTS
// ============================================================================
// CLASSES:
//   MiddlewareManager (Imported: 69)
//     - Properties:
//       - _resilientTimer (Mocked: 79)
//       - _metricsCollector (Mocked: 90)
//     - Methods:
//       - constructor (195)
//       - initialize (196)
//       - shutdown (202)
//       - addMiddleware (232)
//       - getMiddleware (234)
//       - getMiddlewareMetrics (210, 236)
//       - getMiddlewareByName (275)
//       - removeMiddleware (262)
//       - clearAllMiddleware (289)
//       - resetMiddlewareMetrics (974)
//       - executeHandlerWithMiddleware (336)
//   
// INTERFACES:
//   IMiddlewareManagerConfig (Imported: 69)
//   IHandlerMiddleware (Imported: 71)
//     - name (153)
//     - priority (154)
//     - beforeHandlerExecution (159, 161)
//     - afterHandlerExecution (167, 169)
//     - onHandlerError (175, 177)
//   IRegisteredHandler (Imported: 72)
//     - id (133)
//     - clientId (134)
//     - eventType (135)
//     - callback (136)
//   IHandlerResult (Imported: 73)
//     - handlerId (343)
//     - executionTime (346)
//     - success (347)
//   IHandlerExecutionContext (Imported: 74)
// 
// GLOBAL FUNCTIONS:
//   createMockHandler (131)
//   createMockMiddleware (143)
//
// IMPORTED:
//   MiddlewareManager (Imported from '../../../event-handler-registry/modules/MiddlewareManager') (69)
//   IMiddlewareManagerConfig (Imported from '../../../event-handler-registry/modules/MiddlewareManager') (69)
//   IHandlerMiddleware (Imported from '../../../event-handler-registry/types/EventTypes') (71)
//   IRegisteredHandler (Imported from '../../../event-handler-registry/types/EventTypes') (72)
//   IHandlerResult (Imported from '../../../event-handler-registry/types/EventTypes') (73)
//   IHandlerExecutionContext (Imported from '../../../event-handler-registry/types/EventTypes') (74)
//   ResilientTimer (Mocked: 78)
//   ResilientMetricsCollector (Mocked: 90)
// ============================================================================

import { MiddlewareManager, IMiddlewareManagerConfig } from '../../../event-handler-registry/modules/MiddlewareManager';
import { 
  IHandlerMiddleware, 
  IRegisteredHandler, 
  IHandlerResult,
  IHandlerExecutionContext
} from '../../../event-handler-registry/types/EventTypes';

// Mock ResilientTimer with comprehensive timing context lifecycle
jest.mock('../../../utils/ResilientTiming', () => ({
  ResilientTimer: jest.fn(() => ({
    start: jest.fn(() => ({
      end: jest.fn(() => ({
        duration: 2.5, // <5ms requirement for middleware execution
        reliable: true,
        startTime: Date.now(),
        endTime: Date.now() + 2.5,
        method: 'performance.now'
      })),
    })),
  })),
  ResilientMetricsCollector: jest.fn(() => ({
    recordTiming: jest.fn(),
    createSnapshot: jest.fn(() => ({
      timestamp: Date.now(),
      reliable: true,
      metrics: new Map(),
      warnings: []
    }))
  })),
}));

// ============================================================================
// SECTION 2: TEST CONFIGURATION & CONSTANTS (Lines 80-120)
// AI Context: "Test configuration constants and performance requirements"
// ============================================================================

const TEST_CONFIG = {
  PERFORMANCE: {
    MAX_MIDDLEWARE_EXECUTION_TIME_MS: 5, // <5ms requirement for middleware
    MAX_CHAIN_EXECUTION_TIME_MS: 10, // <10ms for complete chain
    MAX_HANDLER_EXECUTION_TIME_MS: 5, // <5ms per handler
    MEMORY_THRESHOLD_MB: 10, // Memory usage threshold
    MAX_MIDDLEWARE_COUNT: 10
  },
  TIMEOUTS: {
    TEST_TIMEOUT_MS: 30000, // 30 seconds for complex tests
    MIDDLEWARE_TIMEOUT_MS: 5000, // 5 seconds for middleware operations
    CLEANUP_TIMEOUT_MS: 10000 // 10 seconds for cleanup operations
  },
  LIMITS: {
    MAX_TEST_MIDDLEWARE: 20,
    MAX_TEST_HANDLERS: 100,
    MAX_EXECUTION_ATTEMPTS: 3
  }
};

// ============================================================================
// SECTION 3: TEST UTILITIES & HELPERS (Lines 121-180)
// AI Context: "Test utility functions and mock object creation"
// ============================================================================

function createMockHandler(id: string = 'test-handler', clientId: string = 'test-client'): IRegisteredHandler {
  return {
    id,
    clientId,
    eventType: 'test-event',
    callback: jest.fn().mockResolvedValue({ processed: true, data: 'test-result', handlerId: id }),
    registeredAt: new Date(),
    lastUsed: new Date(),
    metadata: { test: true }
  };
}

function createMockMiddleware(
  name: string, 
  priority: number = 1,
  options: {
    beforeHandler?: boolean | ((context: IHandlerExecutionContext) => Promise<boolean>);
    afterHandler?: boolean | ((context: IHandlerExecutionContext, result: unknown) => Promise<void>);
    errorHandler?: boolean | ((context: IHandlerExecutionContext, error: Error) => Promise<boolean>);
  } = {}
): IHandlerMiddleware {
  const middleware: IHandlerMiddleware = {
    name,
    priority
  };

  if (options.beforeHandler !== undefined) {
    if (typeof options.beforeHandler === 'function') {
      middleware.beforeHandlerExecution = options.beforeHandler;
    } else {
      middleware.beforeHandlerExecution = jest.fn().mockResolvedValue(options.beforeHandler);
    }
  }

  if (options.afterHandler !== undefined) {
    if (typeof options.afterHandler === 'function') {
      middleware.afterHandlerExecution = options.afterHandler;
    } else {
      middleware.afterHandlerExecution = jest.fn().mockResolvedValue(undefined);
    }
  }

  if (options.errorHandler !== undefined) {
    if (typeof options.errorHandler === 'function') {
      middleware.onHandlerError = options.errorHandler;
    } else {
      middleware.onHandlerError = jest.fn().mockResolvedValue(options.errorHandler);
    }
  }

  return middleware;
}

// ============================================================================
// SECTION 4: CORE FUNCTIONALITY TESTS (Lines 181-250)
// AI Context: "Core middleware management functionality testing"
// ============================================================================

describe('MiddlewareManager', () => {
  let middlewareManager: MiddlewareManager;
  let mockHandler: IRegisteredHandler;

  beforeEach(async () => {
    jest.clearAllMocks();
    middlewareManager = new MiddlewareManager();
    await middlewareManager.initialize();
    mockHandler = createMockHandler();
  });

  afterEach(async () => {
    if (middlewareManager) {
      await middlewareManager.shutdown();
    }
  });

  describe('Core Functionality', () => {
    test('should initialize with default configuration', () => {
      expect(middlewareManager).toBeDefined();
      expect(middlewareManager.getMiddleware()).toEqual([]);
      expect(middlewareManager.getMiddlewareMetrics().totalMiddleware).toBe(0);
    });

    test('should initialize with custom configuration', async () => {
      const config: IMiddlewareManagerConfig = {
        maxMiddleware: 5,
        enableTiming: false,
        timeoutMs: 3000
      };
      
      const customManager = new MiddlewareManager(config);
      await customManager.initialize();
      
      expect(customManager).toBeDefined();
      expect(customManager.getMiddleware()).toEqual([]);
      
      await customManager.shutdown();
    });

    test('should add middleware successfully', () => {
      const middleware = createMockMiddleware('test-middleware', 1);
      
      middlewareManager.addMiddleware(middleware);
      
      expect(middlewareManager.getMiddleware()).toHaveLength(1);
      expect(middlewareManager.getMiddleware()[0]).toEqual(middleware);
      expect(middlewareManager.getMiddlewareMetrics().totalMiddleware).toBe(1);
    });

    test('should sort middleware by priority (highest first)', () => {
      const middleware1 = createMockMiddleware('low-priority', 1);
      const middleware2 = createMockMiddleware('high-priority', 10);
      const middleware3 = createMockMiddleware('medium-priority', 5);
      
      middlewareManager.addMiddleware(middleware1);
      middlewareManager.addMiddleware(middleware2);
      middlewareManager.addMiddleware(middleware3);
      
      const sortedMiddleware = middlewareManager.getMiddleware();
      expect(sortedMiddleware).toHaveLength(3);
      expect(sortedMiddleware[0].name).toBe('high-priority');
      expect(sortedMiddleware[1].name).toBe('medium-priority');
      expect(sortedMiddleware[2].name).toBe('low-priority');
    });

    test('should remove middleware by name', () => {
      const middleware1 = createMockMiddleware('middleware-1', 1);
      const middleware2 = createMockMiddleware('middleware-2', 2);
      
      middlewareManager.addMiddleware(middleware1);
      middlewareManager.addMiddleware(middleware2);
      
      const removed = middlewareManager.removeMiddleware('middleware-1');
      
      expect(removed).toBe(true);
      expect(middlewareManager.getMiddleware()).toHaveLength(1);
      expect(middlewareManager.getMiddleware()[0].name).toBe('middleware-2');
    });
  });

  describe('Middleware Management', () => {
    test('should get middleware by name', () => {
      const middleware = createMockMiddleware('test-middleware', 1);
      middlewareManager.addMiddleware(middleware);

      const found = middlewareManager.getMiddlewareByName('test-middleware');
      expect(found).toEqual(middleware);

      const notFound = middlewareManager.getMiddlewareByName('non-existent');
      expect(notFound).toBeUndefined();
    });

    test('should clear all middleware', () => {
      const middleware1 = createMockMiddleware('middleware-1', 1);
      const middleware2 = createMockMiddleware('middleware-2', 2);

      middlewareManager.addMiddleware(middleware1);
      middlewareManager.addMiddleware(middleware2);

      middlewareManager.clearAllMiddleware();

      expect(middlewareManager.getMiddleware()).toHaveLength(0);
      expect(middlewareManager.getMiddlewareMetrics().totalMiddleware).toBe(0);
    });

    test('should enforce maximum middleware limit', () => {
      const config: IMiddlewareManagerConfig = { maxMiddleware: 2 };
      const limitedManager = new MiddlewareManager(config);

      limitedManager.addMiddleware(createMockMiddleware('middleware-1', 1));
      limitedManager.addMiddleware(createMockMiddleware('middleware-2', 2));

      expect(() => {
        limitedManager.addMiddleware(createMockMiddleware('middleware-3', 3));
      }).toThrow('Maximum middleware limit reached: 2');
    });

    test('should validate middleware properties', () => {
      expect(() => {
        middlewareManager.addMiddleware({ name: '', priority: 1 } as IHandlerMiddleware);
      }).toThrow('Invalid middleware: must have a valid name');

      expect(() => {
        middlewareManager.addMiddleware({ name: 'test', priority: 'invalid' } as any);
      }).toThrow('Invalid middleware: must have a numeric priority');
    });

    test('should prevent duplicate middleware names', () => {
      const middleware1 = createMockMiddleware('duplicate-name', 1);
      const middleware2 = createMockMiddleware('duplicate-name', 2);

      middlewareManager.addMiddleware(middleware1);

      expect(() => {
        middlewareManager.addMiddleware(middleware2);
      }).toThrow("Middleware with name 'duplicate-name' already exists");
    });
  });

  // ============================================================================
  // SECTION 5: MIDDLEWARE CHAIN EXECUTION TESTS (Lines 320-420)
  // AI Context: "Middleware chain execution and handler processing testing"
  // ============================================================================

  describe('Middleware Chain Execution', () => {
    test('should execute handler directly when no middleware', async () => {
      const result = await middlewareManager.executeHandlerWithMiddleware(
        mockHandler,
        { test: 'data' },
        'test-event'
      );

      expect(result).toEqual({
        handlerId: mockHandler.id,
        clientId: mockHandler.clientId,
        result: { processed: true, data: 'test-result', handlerId: mockHandler.id },
        executionTime: 2.5,
        success: true,
        skippedByMiddleware: undefined
      });

      expect(mockHandler.callback).toHaveBeenCalledWith(
        { test: 'data' },
        expect.objectContaining({
          eventType: 'test-event',
          clientId: mockHandler.clientId,
          timestamp: expect.any(Date),
          metadata: mockHandler.metadata
        })
      );
    });

    test('should execute middleware chain in priority order', async () => {
      const executionOrder: string[] = [];

      const middleware1 = createMockMiddleware('low-priority', 1, {
        beforeHandler: async (context) => {
          executionOrder.push('low-priority-before');
          return true;
        },
        afterHandler: async (context, result) => {
          executionOrder.push('low-priority-after');
        }
      });

      const middleware2 = createMockMiddleware('high-priority', 10, {
        beforeHandler: async (context) => {
          executionOrder.push('high-priority-before');
          return true;
        },
        afterHandler: async (context, result) => {
          executionOrder.push('high-priority-after');
        }
      });

      middlewareManager.addMiddleware(middleware1);
      middlewareManager.addMiddleware(middleware2);

      await middlewareManager.executeHandlerWithMiddleware(
        mockHandler,
        { test: 'data' },
        'test-event'
      );

      expect(executionOrder).toEqual([
        'high-priority-before',
        'low-priority-before',
        'high-priority-after',
        'low-priority-after'
      ]);
    });

    test('should skip handler execution when middleware returns false', async () => {
      const middleware = createMockMiddleware('skip-middleware', 1, {
        beforeHandler: async (context) => false
      });

      middlewareManager.addMiddleware(middleware);

      const result = await middlewareManager.executeHandlerWithMiddleware(
        mockHandler,
        { test: 'data' },
        'test-event'
      );

      expect(result).toEqual({
        handlerId: mockHandler.id,
        clientId: mockHandler.clientId,
        result: undefined,
        executionTime: 0,
        success: true,
        skippedByMiddleware: 'skip-middleware'
      });

      expect(mockHandler.callback).not.toHaveBeenCalled();
      expect(middlewareManager.getMiddlewareMetrics().skippedExecutions).toBe(1);
    });

    test('should handle middleware chain with mixed execution results', async () => {
      const middleware1 = createMockMiddleware('allow-middleware', 10, {
        beforeHandler: async (context) => true
      });

      const middleware2 = createMockMiddleware('skip-middleware', 5, {
        beforeHandler: async (context) => false
      });

      middlewareManager.addMiddleware(middleware1);
      middlewareManager.addMiddleware(middleware2);

      const result = await middlewareManager.executeHandlerWithMiddleware(
        mockHandler,
        { test: 'data' },
        'test-event'
      );

      expect(result.skippedByMiddleware).toBe('skip-middleware');
      expect(mockHandler.callback).not.toHaveBeenCalled();
    });
  });

  // ============================================================================
  // SECTION 6: PERFORMANCE VALIDATION TESTS (Lines 450-520)
  // AI Context: "Performance requirements validation and timing measurement"
  // ============================================================================

  describe('Performance Validation', () => {
    test('should meet middleware execution performance requirements', async () => {
      const startTime = Date.now();

      const middleware = createMockMiddleware('performance-middleware', 1, {
        beforeHandler: async (context) => true,
        afterHandler: async (context, result) => {}
      });

      middlewareManager.addMiddleware(middleware);

      const result = await middlewareManager.executeHandlerWithMiddleware(
        mockHandler,
        { test: 'data' },
        'test-event'
      );

      const endTime = Date.now();
      const totalTime = endTime - startTime;

      expect(totalTime).toBeLessThan(TEST_CONFIG.PERFORMANCE.MAX_CHAIN_EXECUTION_TIME_MS);
      expect(result.executionTime).toBeLessThan(TEST_CONFIG.PERFORMANCE.MAX_HANDLER_EXECUTION_TIME_MS);
      expect(result.success).toBe(true);
    });

    test('should handle high-volume middleware execution efficiently', async () => {
      // Add multiple middleware with different priorities
      for (let i = 0; i < 5; i++) {
        const middleware = createMockMiddleware(`middleware-${i}`, i + 1, {
          beforeHandler: async (context) => true,
          afterHandler: async (context, result) => {}
        });
        middlewareManager.addMiddleware(middleware);
      }

      const startTime = Date.now();
      const promises: Promise<IHandlerResult>[] = [];

      // Execute multiple handlers concurrently
      for (let i = 0; i < 10; i++) {
        const handler = createMockHandler(`handler-${i}`, `client-${i}`);
        promises.push(
          middlewareManager.executeHandlerWithMiddleware(
            handler,
            { test: `data-${i}` },
            'test-event'
          )
        );
      }

      const results = await Promise.all(promises);
      const endTime = Date.now();
      const totalTime = endTime - startTime;

      expect(totalTime).toBeLessThan(TEST_CONFIG.PERFORMANCE.MAX_CHAIN_EXECUTION_TIME_MS * 10);
      expect(results).toHaveLength(10);
      results.forEach(result => {
        expect(result.success).toBe(true);
        expect(result.executionTime).toBeLessThan(TEST_CONFIG.PERFORMANCE.MAX_HANDLER_EXECUTION_TIME_MS);
      });
    });

    test('should maintain performance under memory pressure', async () => {
      const middleware = createMockMiddleware('memory-test-middleware', 1, {
        beforeHandler: async (context) => true,
        afterHandler: async (context, result) => {}
      });

      middlewareManager.addMiddleware(middleware);

      // Execute multiple times to test memory efficiency
      for (let i = 0; i < 100; i++) {
        const handler = createMockHandler(`handler-${i}`, `client-${i}`);
        const result = await middlewareManager.executeHandlerWithMiddleware(
          handler,
          { test: `data-${i}` },
          'test-event'
        );

        expect(result.success).toBe(true);
        expect(result.executionTime).toBeLessThan(TEST_CONFIG.PERFORMANCE.MAX_HANDLER_EXECUTION_TIME_MS);
      }

      const metrics = middlewareManager.getMiddlewareMetrics();
      expect(metrics.totalExecutions).toBe(100);
      expect(metrics.successfulExecutions).toBe(100);
      expect(metrics.failedExecutions).toBe(0);
    });
  });

  // ============================================================================
  // SECTION 7: ERROR HANDLING TESTS (Lines 544-650)
  // AI Context: "Error handling, recovery mechanisms, and edge cases"
  // ============================================================================

  describe('Error Handling', () => {
    test('should handle middleware execution errors', async () => {
      const errorMiddleware = createMockMiddleware('error-middleware', 1, {
        beforeHandler: async (context) => {
          throw new Error('Middleware execution failed');
        }
      });

      middlewareManager.addMiddleware(errorMiddleware);

      await expect(
        middlewareManager.executeHandlerWithMiddleware(
          mockHandler,
          { test: 'data' },
          'test-event'
        )
      ).rejects.toThrow('Middleware execution failed');

      const metrics = middlewareManager.getMiddlewareMetrics();
      expect(metrics.failedExecutions).toBe(1);
    });

    test('should handle handler execution errors with middleware error handling', async () => {
      const failingHandler = createMockHandler('failing-handler');
      (failingHandler.callback as jest.Mock).mockRejectedValue(new Error('Handler failed'));

      const errorHandlingMiddleware = createMockMiddleware('error-handler-middleware', 1, {
        errorHandler: async (context, error) => {
          expect(error.message).toBe('Handler failed');
          return true; // Error handled
        }
      });

      middlewareManager.addMiddleware(errorHandlingMiddleware);

      const result = await middlewareManager.executeHandlerWithMiddleware(
        failingHandler,
        { test: 'data' },
        'test-event'
      );

      expect(result.success).toBe(true); // Error was handled by middleware
      expect(result.result).toBeUndefined();
    });

    test('should propagate unhandled errors', async () => {
      const failingHandler = createMockHandler('failing-handler');
      (failingHandler.callback as jest.Mock).mockRejectedValue(new Error('Unhandled error'));

      const nonHandlingMiddleware = createMockMiddleware('non-handling-middleware', 1, {
        errorHandler: async (context, error) => {
          return false; // Error not handled
        }
      });

      middlewareManager.addMiddleware(nonHandlingMiddleware);

      const result = await middlewareManager.executeHandlerWithMiddleware(
        failingHandler,
        { test: 'data' },
        'test-event'
      );

      expect(result.success).toBe(false);
      expect(result.result).toBeInstanceOf(Error);
    });

    test('should handle middleware error handler failures', async () => {
      const failingHandler = createMockHandler('failing-handler');
      (failingHandler.callback as jest.Mock).mockRejectedValue(new Error('Handler failed'));

      const failingErrorMiddleware = createMockMiddleware('failing-error-middleware', 1, {
        errorHandler: async (context, error) => {
          throw new Error('Error handler failed');
        }
      });

      middlewareManager.addMiddleware(failingErrorMiddleware);

      const result = await middlewareManager.executeHandlerWithMiddleware(
        failingHandler,
        { test: 'data' },
        'test-event'
      );

      // Should continue with original error since error handler failed
      expect(result.success).toBe(false);
      expect(result.result).toBeInstanceOf(Error);
    });

    test('should handle after-handler middleware errors gracefully', async () => {
      const errorAfterMiddleware = createMockMiddleware('error-after-middleware', 1, {
        beforeHandler: async (context) => true,
        afterHandler: async (context, result) => {
          throw new Error('After-handler error');
        }
      });

      // Mock console.warn to capture warning
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

      middlewareManager.addMiddleware(errorAfterMiddleware);

      const result = await middlewareManager.executeHandlerWithMiddleware(
        mockHandler,
        { test: 'data' },
        'test-event'
      );

      expect(result.success).toBe(true); // Should still succeed
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('After-handler middleware error-after-middleware failed:'),
        expect.any(Error)
      );

      consoleSpy.mockRestore();
    });
  });

  // ============================================================================
  // SECTION 8: RESILIENT TIMING INTEGRATION TESTS (Lines 668-800)
  // AI Context: "Resilient timing infrastructure integration and validation"
  // ============================================================================

  describe('Resilient Timing Integration', () => {
    test('should initialize resilient timing infrastructure', async () => {
      // Verify that the timing infrastructure is properly initialized
      expect(middlewareManager['_resilientTimer']).toBeDefined();
      expect(middlewareManager['_metricsCollector']).toBeDefined();

      // Verify timing infrastructure is functional
      const timer = middlewareManager['_resilientTimer'];
      const metricsCollector = middlewareManager['_metricsCollector'];

      expect(timer.start).toBeDefined();
      expect(typeof timer.start).toBe('function');

      expect(metricsCollector.recordTiming).toBeDefined();
      expect(typeof metricsCollector.recordTiming).toBe('function');
      expect(metricsCollector.createSnapshot).toBeDefined();
      expect(typeof metricsCollector.createSnapshot).toBe('function');
    });

    test('should use resilient timer for middleware chain execution', async () => {
      const middleware = createMockMiddleware('timing-middleware', 1, {
        beforeHandler: async (_context) => true,
        afterHandler: async (_context, _result) => {}
      });

      middlewareManager.addMiddleware(middleware);

      const mockTimer = middlewareManager['_resilientTimer'];
      const mockContext = {
        end: jest.fn().mockReturnValue({ duration: 2.5, reliable: true }),
        startTime: Date.now(),
        startMethod: 'date' as const,
        config: {},
        getCurrentTime: jest.fn(),
        validateAndAdjustTiming: jest.fn()
      };
      const startSpy = jest.spyOn(mockTimer, 'start').mockReturnValue(mockContext as any);

      await middlewareManager.executeHandlerWithMiddleware(
        mockHandler,
        { test: 'data' },
        'test-event'
      );

      expect(startSpy).toHaveBeenCalled();
      expect(mockContext.end).toHaveBeenCalled();
    });

    test('should record timing metrics for successful executions', async () => {
      const middleware = createMockMiddleware('metrics-middleware', 1, {
        beforeHandler: async (_context) => true
      });

      middlewareManager.addMiddleware(middleware);

      const mockMetricsCollector = middlewareManager['_metricsCollector'];
      const recordTimingSpy = jest.spyOn(mockMetricsCollector, 'recordTiming');

      await middlewareManager.executeHandlerWithMiddleware(
        mockHandler,
        { test: 'data' },
        'test-event'
      );

      expect(recordTimingSpy).toHaveBeenCalledWith(
        'middlewareChain',
        expect.objectContaining({
          duration: 2.5,
          reliable: true
        })
      );
    });

    test('should record timing metrics for error scenarios', async () => {
      const errorMiddleware = createMockMiddleware('error-timing-middleware', 1, {
        beforeHandler: async (_context) => {
          throw new Error('Timing test error');
        }
      });

      middlewareManager.addMiddleware(errorMiddleware);

      const mockMetricsCollector = middlewareManager['_metricsCollector'];
      const recordTimingSpy = jest.spyOn(mockMetricsCollector, 'recordTiming');

      try {
        await middlewareManager.executeHandlerWithMiddleware(
          mockHandler,
          { test: 'data' },
          'test-event'
        );
      } catch (error) {
        // Expected error
      }

      expect(recordTimingSpy).toHaveBeenCalledWith(
        'middlewareChainError',
        expect.objectContaining({
          duration: 2.5,
          reliable: true
        })
      );
    });

    test('should handle timing context cleanup in error paths', async () => {
      const errorMiddleware = createMockMiddleware('cleanup-test-middleware', 1, {
        beforeHandler: async (_context) => {
          throw new Error('Cleanup test error');
        }
      });

      middlewareManager.addMiddleware(errorMiddleware);

      const mockTimer = middlewareManager['_resilientTimer'];
      const mockContext = {
        end: jest.fn().mockReturnValue({ duration: 2.5, reliable: true }),
        startTime: Date.now(),
        startMethod: 'date' as const,
        config: {},
        getCurrentTime: jest.fn(),
        validateAndAdjustTiming: jest.fn()
      };
      jest.spyOn(mockTimer, 'start').mockReturnValue(mockContext as any);

      try {
        await middlewareManager.executeHandlerWithMiddleware(
          mockHandler,
          { test: 'data' },
          'test-event'
        );
      } catch (error) {
        // Expected error
      }

      // Verify timing context was properly ended even in error scenario
      expect(mockContext.end).toHaveBeenCalled();
    });

    test('should record timing for direct handler execution', async () => {
      // No middleware - should execute handler directly
      const mockMetricsCollector = middlewareManager['_metricsCollector'];
      const recordTimingSpy = jest.spyOn(mockMetricsCollector, 'recordTiming');

      await middlewareManager.executeHandlerWithMiddleware(
        mockHandler,
        { test: 'data' },
        'test-event'
      );

      expect(recordTimingSpy).toHaveBeenCalledWith(
        'handlerWithoutMiddleware',
        expect.objectContaining({
          duration: 2.5,
          reliable: true
        })
      );
    });

    test('should validate Jest fake timer compatibility', () => {
      jest.useFakeTimers();

      // Should not throw errors with fake timers
      expect(() => {
        new MiddlewareManager();
      }).not.toThrow();

      jest.useRealTimers();
    });

    test('should handle timing infrastructure failures gracefully', async () => {
      const middleware = createMockMiddleware('resilient-test-middleware', 1, {
        beforeHandler: async (_context) => true
      });

      middlewareManager.addMiddleware(middleware);

      // Mock timing failure
      const mockTimer = middlewareManager['_resilientTimer'];
      jest.spyOn(mockTimer, 'start').mockImplementation(() => {
        throw new Error('Timing infrastructure failure');
      });

      // Should handle timing failure gracefully and still execute
      await expect(
        middlewareManager.executeHandlerWithMiddleware(
          mockHandler,
          { test: 'data' },
          'test-event'
        )
      ).rejects.toThrow('Timing infrastructure failure');
    });
  });

  // ============================================================================
  // SECTION 9: METRICS AND MONITORING TESTS (Lines 852-920)
  // AI Context: "Middleware metrics collection and performance monitoring"
  // ============================================================================

  describe('Metrics and Monitoring', () => {
    test('should track middleware execution metrics', async () => {
      const middleware = createMockMiddleware('metrics-test-middleware', 1, {
        beforeHandler: async (_context) => true,
        afterHandler: async (_context, _result) => {}
      });

      middlewareManager.addMiddleware(middleware);

      await middlewareManager.executeHandlerWithMiddleware(
        mockHandler,
        { test: 'data' },
        'test-event'
      );

      const metrics = middlewareManager.getMiddlewareMetrics();
      expect(metrics.totalExecutions).toBe(1);
      expect(metrics.successfulExecutions).toBe(1);
      expect(metrics.failedExecutions).toBe(0);
      expect(metrics.skippedExecutions).toBe(0);
      expect(metrics.totalMiddleware).toBe(1);
      expect(metrics.averageExecutionTime).toBeGreaterThan(0);
    });

    test('should track skipped executions', async () => {
      const skipMiddleware = createMockMiddleware('skip-metrics-middleware', 1, {
        beforeHandler: async (_context) => false
      });

      middlewareManager.addMiddleware(skipMiddleware);

      await middlewareManager.executeHandlerWithMiddleware(
        mockHandler,
        { test: 'data' },
        'test-event'
      );

      const metrics = middlewareManager.getMiddlewareMetrics();
      expect(metrics.totalExecutions).toBe(1);
      expect(metrics.successfulExecutions).toBe(0);
      expect(metrics.skippedExecutions).toBe(1);
    });

    test('should track failed executions', async () => {
      const errorMiddleware = createMockMiddleware('error-metrics-middleware', 1, {
        beforeHandler: async (_context) => {
          throw new Error('Metrics test error');
        }
      });

      middlewareManager.addMiddleware(errorMiddleware);

      try {
        await middlewareManager.executeHandlerWithMiddleware(
          mockHandler,
          { test: 'data' },
          'test-event'
        );
      } catch (error) {
        // Expected error
      }

      const metrics = middlewareManager.getMiddlewareMetrics();
      expect(metrics.totalExecutions).toBe(1);
      expect(metrics.failedExecutions).toBe(1);
    });

    test('should calculate rolling average execution time', async () => {
      const middleware = createMockMiddleware('average-test-middleware', 1, {
        beforeHandler: async (_context) => true
      });

      middlewareManager.addMiddleware(middleware);

      // Execute multiple times
      for (let i = 0; i < 5; i++) {
        await middlewareManager.executeHandlerWithMiddleware(
          mockHandler,
          { test: `data-${i}` },
          'test-event'
        );
      }

      const metrics = middlewareManager.getMiddlewareMetrics();
      expect(metrics.totalExecutions).toBe(5);
      expect(metrics.averageExecutionTime).toBe(2.5); // Mock returns 2.5ms
    });

    test('should reset metrics correctly', async () => {
      const middleware = createMockMiddleware('reset-test-middleware', 1, {
        beforeHandler: async (_context) => true
      });

      middlewareManager.addMiddleware(middleware);

      await middlewareManager.executeHandlerWithMiddleware(
        mockHandler,
        { test: 'data' },
        'test-event'
      );

      middlewareManager.resetMiddlewareMetrics();

      const metrics = middlewareManager.getMiddlewareMetrics();
      expect(metrics.totalExecutions).toBe(0);
      expect(metrics.successfulExecutions).toBe(0);
      expect(metrics.failedExecutions).toBe(0);
      expect(metrics.skippedExecutions).toBe(0);
      expect(metrics.averageExecutionTime).toBe(0);
      expect(metrics.totalMiddleware).toBe(1); // Should preserve middleware count
    });

    test('should include metrics snapshot', async () => {
      const metrics = middlewareManager.getMiddlewareMetrics();

      expect(metrics.metricsSnapshot).toBeDefined();
      expect(metrics.metricsSnapshot.timestamp).toBeGreaterThan(0);
      expect(metrics.metricsSnapshot.reliable).toBe(true);
      expect(metrics.metricsSnapshot.metrics).toBeInstanceOf(Map);
      expect(metrics.metricsSnapshot.warnings).toEqual([]);
    });
  });

  // ============================================================================
  // SECTION 10: MEMORY SAFETY TESTS (Lines 921-980)
  // AI Context: "Memory-safe resource management and cleanup validation"
  // ============================================================================

  describe('Memory Safety', () => {
    test('should properly initialize and shutdown', async () => {
      const testManager = new MiddlewareManager();

      await testManager.initialize();
      expect(testManager['_resilientTimer']).toBeDefined();
      expect(testManager['_metricsCollector']).toBeDefined();

      await testManager.shutdown();
      expect(testManager.getMiddleware()).toHaveLength(0);

      const metrics = testManager.getMiddlewareMetrics();
      expect(metrics.totalExecutions).toBe(0);
      expect(metrics.totalMiddleware).toBe(0);
    });

    test('should clean up resources on shutdown', async () => {
      const testManager = new MiddlewareManager();
      await testManager.initialize();

      const middleware = createMockMiddleware('cleanup-test-middleware', 1);
      testManager.addMiddleware(middleware);

      await testManager.executeHandlerWithMiddleware(
        mockHandler,
        { test: 'data' },
        'test-event'
      );

      expect(testManager.getMiddleware()).toHaveLength(1);

      await testManager.shutdown();

      expect(testManager.getMiddleware()).toHaveLength(0);
      expect(testManager.getMiddlewareMetrics().totalMiddleware).toBe(0);
    });

    test('should handle multiple initialization calls safely', async () => {
      const testManager = new MiddlewareManager();

      await testManager.initialize();
      await testManager.initialize(); // Should not throw

      expect(testManager['_resilientTimer']).toBeDefined();
      expect(testManager['_metricsCollector']).toBeDefined();

      await testManager.shutdown();
    });

    test('should handle shutdown without initialization', async () => {
      const testManager = new MiddlewareManager();

      // Should not throw
      await expect(testManager.shutdown()).resolves.not.toThrow();
    });

    test('should maintain memory boundaries under load', async () => {
      const testManager = new MiddlewareManager();
      await testManager.initialize();

      // Add multiple middleware
      for (let i = 0; i < 10; i++) {
        const middleware = createMockMiddleware(`load-test-middleware-${i}`, i + 1, {
          beforeHandler: async (_context) => true
        });
        testManager.addMiddleware(middleware);
      }

      // Execute many operations
      for (let i = 0; i < 50; i++) {
        const handler = createMockHandler(`load-handler-${i}`, `load-client-${i}`);
        await testManager.executeHandlerWithMiddleware(
          handler,
          { test: `load-data-${i}` },
          'test-event'
        );
      }

      const metrics = testManager.getMiddlewareMetrics();
      expect(metrics.totalExecutions).toBe(50);
      expect(metrics.successfulExecutions).toBe(50);

      await testManager.shutdown();
    });
  });
});
