/**
 * ============================================================================
 * AI CONTEXT: ComplianceManager Test Suite - Governance & Enterprise Standards
 * Purpose: Comprehensive testing of compliance management with resilient timing integration
 * Complexity: High - Complex governance validation with enterprise-grade compliance testing
 * AI Navigation: 8 logical sections - Setup, Core, Validation, Reporting, Audit, Timing, Error, Memory
 * Dependencies: ResilientTiming, ResilientMetrics, MemorySafeResourceManager
 * Performance: <2ms per compliance operation validation
 * ============================================================================
 */

/**
 * @file ComplianceManager Test Suite
 * @filepath shared/src/base/__tests__/modules/event-handler-registry/ComplianceManager.test.ts
 * @task-id T-TSK-02.SUB-03.7.COM-01
 * @component event-handler-registry-enhanced
 * @reference foundation-context.MEMORY-SAFETY.004
 * @template enhanced-compliance-testing
 * @tier T0
 * @context foundation-context
 * @category Memory-Safety-Enhanced-Compliance-Testing
 * @created 2025-08-05
 * @modified 2025-08-05
 *
 * @description
 * Comprehensive test suite for ComplianceManager module:
 * - Enterprise governance and standards validation testing
 * - Anti-Simplification Policy compliance monitoring validation
 * - Comprehensive audit trails and reporting testing
 * - Real-time compliance tracking with resilient timing integration
 * - Memory-safe resource management validation for T0 compliance components
 */

// ============================================================================
// TABLE OF CONTENTS
// ============================================================================
// CLASSES:
//   ComplianceManager (Imported: 73)
//     - Properties:
//       - _resilientTimer (Mocked: 79)
//       - _metricsCollector (Mocked: 90)
//       - _config (Line 79)
//       - _violations (Line 82)
//       - _auditTrail (Line 83)
//       - _complianceScore (Line 84)
//     - Methods:
//       - constructor (Line 86)
//       - doInitialize (Line 103)
//       - doShutdown (Line 140)
//       - recordViolation (Line 155)
//       - addAuditEntry (Line 175)
//       - validateOperation (Line 192)
//       - generateComplianceReport (Line 237)
//       - getComplianceScore (Line 250)
//       - getRecentViolations (Line 257)
//       - clearViolations (Line 265)
//       - _performComplianceValidation (Line 275)
//       - _updateComplianceScore (Line 293)
//       - _generateRecommendations (Line 305)
//       - _generateAuditId (Line 319)
//       - _generateComplianceReport (Line 325)
//       - _cleanupAuditTrail (Line 340)
// INTERFACES:
//   IComplianceManagerConfig (Line 42)
//     - enableAuditTrail?: boolean (Line 43)
//     - auditRetentionMs?: number (Line 44)
//     - complianceReportingIntervalMs?: number (Line 45)
//     - enableRealTimeValidation?: boolean (Line 46)
//   IComplianceReport (Line 49)
//     - timestamp: Date (Line 50)
//     - complianceScore: number (Line 51)
//     - violations: IComplianceViolation[] (Line 52)
//     - recommendations: string[] (Line 53)
//     - auditTrail: IAuditEntry[] (Line 54)
//   IComplianceViolation (Line 57)
//     - type: string (Line 58)
//     - severity: string (Line 59)
//     - description: string (Line 60)
//     - timestamp: Date (Line 61)
//     - context?: Record<string, unknown> (Line 62)
//   IAuditEntry (Line 65)
//     - id: string (Line 66)
//     - timestamp: Date (Line 67)
//     - operation: string (Line 68)
//     - result: string (Line 69)
//     - details: Record<string, unknown> (Line 70)
// GLOBAL FUNCTIONS:
//   None
// IMPORTED:
//   ComplianceManager (Imported from '../../../event-handler-registry/modules/ComplianceManager')
//   IComplianceManagerConfig, IComplianceReport, IComplianceViolation, IAuditEntry (Imported from '../../../event-handler-registry/modules/ComplianceManager')
//   ResilientTimer (Imported from '../../../utils/ResilientTiming')
//   ResilientMetricsCollector (Imported from '../../../utils/ResilientMetrics')
// ============================================================================

import { 
  ComplianceManager,
  IComplianceManagerConfig,
  IComplianceReport,
  IComplianceViolation,
  IAuditEntry
} from '../../../event-handler-registry/modules/ComplianceManager';
import { ResilientTimer } from '../../../utils/ResilientTiming';
import { ResilientMetricsCollector } from '../../../utils/ResilientMetrics';

// Jest configuration for comprehensive testing
jest.setTimeout(30000); // 30 seconds for comprehensive tests

// Mock ResilientTimer and ResilientMetricsCollector for controlled testing
jest.mock('../../../utils/ResilientTiming');
jest.mock('../../../utils/ResilientMetrics');

const MockedResilientTimer = ResilientTimer as jest.MockedClass<typeof ResilientTimer>;
const MockedResilientMetricsCollector = ResilientMetricsCollector as jest.MockedClass<typeof ResilientMetricsCollector>;

describe('ComplianceManager - Comprehensive Testing Suite', () => {
  let complianceManager: ComplianceManager;
  let mockResilientTimer: jest.Mocked<ResilientTimer>;
  let mockMetricsCollector: jest.Mocked<ResilientMetricsCollector>;
  let mockTimingContext: { end: jest.Mock };

  // ============================================================================
  // SECTION 1: TEST SETUP AND CONFIGURATION (Lines 1-150)
  // AI Context: "Test environment setup with mocked dependencies"
  // ============================================================================

  beforeEach(async () => {
    // Clear all mocks
    jest.clearAllMocks();

    // Setup timing context mock
    mockTimingContext = {
      end: jest.fn().mockReturnValue({ duration: 1.5, reliable: true })
    };

    // Setup ResilientTimer mock
    mockResilientTimer = {
      start: jest.fn().mockReturnValue(mockTimingContext),
      isReliable: jest.fn().mockReturnValue(true),
      getReliabilityScore: jest.fn().mockReturnValue(0.98)
    } as any;

    // Setup ResilientMetricsCollector mock
    mockMetricsCollector = {
      recordTiming: jest.fn(),
      createSnapshot: jest.fn().mockReturnValue({
        totalMeasurements: 25,
        averageDuration: 1.2,
        reliability: 0.99
      }),
      getStatistics: jest.fn().mockReturnValue({
        count: 25,
        average: 1.2,
        min: 0.5,
        max: 2.8
      })
    } as any;

    // Configure mocked constructors
    MockedResilientTimer.mockImplementation(() => mockResilientTimer);
    MockedResilientMetricsCollector.mockImplementation(() => mockMetricsCollector);

    // Create fresh ComplianceManager instance
    complianceManager = new ComplianceManager();
    await (complianceManager as any).initialize();
  });

  afterEach(async () => {
    if (complianceManager) {
      await complianceManager.shutdown();
    }
  });

  // ============================================================================
  // SECTION 2: CORE FUNCTIONALITY TESTING (Lines 151-300)
  // AI Context: "Basic ComplianceManager functionality validation"
  // ============================================================================

  describe('Core Functionality', () => {
    test('should initialize with default configuration', async () => {
      const defaultManager = new ComplianceManager();
      await (defaultManager as any).initialize();

      expect(MockedResilientTimer).toHaveBeenCalledWith({
        enableFallbacks: true,
        maxExpectedDuration: 2000,
        unreliableThreshold: 3,
        estimateBaseline: 1
      });

      expect(MockedResilientMetricsCollector).toHaveBeenCalledWith({
        enableFallbacks: true,
        cacheUnreliableValues: false,
        maxMetricsAge: 86400000,
        defaultEstimates: expect.any(Map)
      });

      await defaultManager.shutdown();
    });

    test('should initialize with custom configuration', async () => {
      const customConfig: IComplianceManagerConfig = {
        enableAuditTrail: false,
        auditRetentionMs: 3600000, // 1 hour
        complianceReportingIntervalMs: 1800000, // 30 minutes
        enableRealTimeValidation: false
      };

      const customManager = new ComplianceManager(customConfig);
      await (customManager as any).initialize();

      expect(MockedResilientTimer).toHaveBeenCalled();
      expect(MockedResilientMetricsCollector).toHaveBeenCalledWith({
        enableFallbacks: true,
        cacheUnreliableValues: false,
        maxMetricsAge: 3600000,
        defaultEstimates: expect.any(Map)
      });

      await customManager.shutdown();
    });

    test('should properly initialize resilient timing infrastructure', async () => {
      expect(MockedResilientTimer).toHaveBeenCalledTimes(1);
      expect(MockedResilientMetricsCollector).toHaveBeenCalledTimes(1);
      expect(complianceManager['_resilientTimer']).toBeDefined();
      expect(complianceManager['_metricsCollector']).toBeDefined();
    });

    test('should inherit from MemorySafeResourceManager', () => {
      expect(complianceManager).toBeInstanceOf(require('../../../MemorySafeResourceManager').MemorySafeResourceManager);
    });

    test('should start with perfect compliance score', () => {
      expect(complianceManager.getComplianceScore()).toBe(100);
    });
  });

  // ============================================================================
  // SECTION 3: COMPLIANCE VIOLATION TESTING (Lines 301-450)
  // AI Context: "Compliance violation recording and scoring validation"
  // ============================================================================

  describe('Compliance Violation Management', () => {
    test('should record compliance violations correctly', () => {
      const violation: Omit<IComplianceViolation, 'timestamp'> = {
        type: 'test_violation',
        severity: 'medium',
        description: 'Test compliance violation',
        context: { testData: 'value' }
      };

      complianceManager.recordViolation(violation);

      const report = complianceManager.generateComplianceReport();
      expect(report.violations).toHaveLength(1);
      expect(report.violations[0].type).toBe('test_violation');
      expect(report.violations[0].severity).toBe('medium');
      expect(report.violations[0].description).toBe('Test compliance violation');
      expect(report.violations[0].timestamp).toBeInstanceOf(Date);
      expect(report.violations[0].context).toEqual({ testData: 'value' });
    });

    test('should update compliance score based on violation severity', () => {
      const initialScore = complianceManager.getComplianceScore();
      expect(initialScore).toBe(100);

      // Record low severity violation (penalty: 1)
      complianceManager.recordViolation({
        type: 'low_violation',
        severity: 'low',
        description: 'Low severity violation'
      });
      expect(complianceManager.getComplianceScore()).toBe(99);

      // Record medium severity violation (penalty: 5)
      complianceManager.recordViolation({
        type: 'medium_violation',
        severity: 'medium',
        description: 'Medium severity violation'
      });
      expect(complianceManager.getComplianceScore()).toBe(94);

      // Record high severity violation (penalty: 15)
      complianceManager.recordViolation({
        type: 'high_violation',
        severity: 'high',
        description: 'High severity violation'
      });
      expect(complianceManager.getComplianceScore()).toBe(79);

      // Record critical severity violation (penalty: 30)
      complianceManager.recordViolation({
        type: 'critical_violation',
        severity: 'critical',
        description: 'Critical severity violation'
      });
      expect(complianceManager.getComplianceScore()).toBe(49);
    });

    test('should not allow compliance score to go below zero', () => {
      // Record multiple critical violations to exceed 100 points
      for (let i = 0; i < 5; i++) {
        complianceManager.recordViolation({
          type: 'critical_violation',
          severity: 'critical',
          description: `Critical violation ${i}`
        });
      }

      expect(complianceManager.getComplianceScore()).toBe(0);
    });

    test('should handle unknown severity levels with default penalty', () => {
      const initialScore = complianceManager.getComplianceScore();

      complianceManager.recordViolation({
        type: 'unknown_violation',
        severity: 'unknown' as any,
        description: 'Unknown severity violation'
      });

      // Should apply default penalty of 5
      expect(complianceManager.getComplianceScore()).toBe(initialScore - 5);
    });

    test('should get recent violations within specified timeframe', () => {
      // Record violations
      complianceManager.recordViolation({
        type: 'recent_violation',
        severity: 'low',
        description: 'Recent violation'
      });

      const recentViolations = complianceManager.getRecentViolations(24);
      expect(recentViolations).toHaveLength(1);
      expect(recentViolations[0].type).toBe('recent_violation');
    });

    test('should clear violations and reset compliance score', () => {
      // Record some violations
      complianceManager.recordViolation({
        type: 'violation1',
        severity: 'high',
        description: 'First violation'
      });
      complianceManager.recordViolation({
        type: 'violation2',
        severity: 'medium',
        description: 'Second violation'
      });

      expect(complianceManager.getComplianceScore()).toBeLessThan(100);

      complianceManager.clearViolations();

      expect(complianceManager.getComplianceScore()).toBe(100);
      expect(complianceManager.generateComplianceReport().violations).toHaveLength(0);
    });
  });

  // ============================================================================
  // SECTION 4: AUDIT TRAIL TESTING (Lines 451-600)
  // AI Context: "Audit trail management and tracking validation"
  // ============================================================================

  describe('Audit Trail Management', () => {
    test('should add audit entries when audit trail is enabled', () => {
      complianceManager.addAuditEntry('test_operation', 'success', {
        operationId: 'op123',
        duration: 150
      });

      const report = complianceManager.generateComplianceReport();
      expect(report.auditTrail).toHaveLength(1);

      const entry = report.auditTrail[0];
      expect(entry.operation).toBe('test_operation');
      expect(entry.result).toBe('success');
      expect(entry.details).toEqual({
        operationId: 'op123',
        duration: 150
      });
      expect(entry.id).toMatch(/^audit:[a-z0-9]+:[a-z0-9]+$/);
      expect(entry.timestamp).toBeInstanceOf(Date);
    });

    test('should not add audit entries when audit trail is disabled', async () => {
      const disabledManager = new ComplianceManager({
        enableAuditTrail: false
      });
      await (disabledManager as any).initialize();

      disabledManager.addAuditEntry('test_operation', 'success', {
        operationId: 'op123'
      });

      const report = disabledManager.generateComplianceReport();
      expect(report.auditTrail).toHaveLength(0);

      await disabledManager.shutdown();
    });

    test('should generate unique audit IDs', () => {
      const ids = new Set<string>();

      for (let i = 0; i < 100; i++) {
        complianceManager.addAuditEntry(`operation_${i}`, 'success', {
          iteration: i
        });
      }

      const report = complianceManager.generateComplianceReport();
      report.auditTrail.forEach(entry => {
        expect(ids.has(entry.id)).toBe(false);
        ids.add(entry.id);
      });

      expect(ids.size).toBe(100);
    });

    test('should automatically add audit entries for violations', () => {
      complianceManager.recordViolation({
        type: 'test_violation',
        severity: 'medium',
        description: 'Test violation for audit'
      });

      const report = complianceManager.generateComplianceReport();

      // Should have both the violation and the audit entry
      expect(report.violations).toHaveLength(1);
      expect(report.auditTrail).toHaveLength(1);

      const auditEntry = report.auditTrail[0];
      expect(auditEntry.operation).toBe('compliance_violation');
      expect(auditEntry.result).toBe('warning');
      expect(auditEntry.details.violationType).toBe('test_violation');
      expect(auditEntry.details.severity).toBe('medium');
    });

    test('should handle different audit entry result types', () => {
      const resultTypes: Array<'success' | 'failure' | 'warning'> = ['success', 'failure', 'warning'];

      resultTypes.forEach((result, index) => {
        complianceManager.addAuditEntry(`operation_${index}`, result, {
          testType: result
        });
      });

      const report = complianceManager.generateComplianceReport();
      expect(report.auditTrail).toHaveLength(3);

      resultTypes.forEach((expectedResult, index) => {
        expect(report.auditTrail[index].result).toBe(expectedResult);
      });
    });
  });

  // ============================================================================
  // SECTION 5: OPERATION VALIDATION TESTING (Lines 601-750)
  // AI Context: "Real-time compliance validation and operation testing"
  // ============================================================================

  describe('Operation Validation', () => {
    test('should validate compliant operations successfully', () => {
      const result = complianceManager.validateOperation('valid_operation', {
        userId: 'user123',
        action: 'create',
        resource: 'document'
      });

      expect(result).toBe(true);
      expect(mockResilientTimer.start).toHaveBeenCalled();
      expect(mockTimingContext.end).toHaveBeenCalled();
      expect(mockMetricsCollector.recordTiming).toHaveBeenCalledWith(
        'complianceValidation',
        { duration: 1.5, reliable: true }
      );
    });

    test('should reject operations with empty context', () => {
      const result = complianceManager.validateOperation('test_operation', {});

      expect(result).toBe(false);

      // Should record a violation
      const report = complianceManager.generateComplianceReport();
      expect(report.violations).toHaveLength(1);
      expect(report.violations[0].type).toBe('operation_non_compliance');
      expect(report.violations[0].severity).toBe('medium');
    });

    test('should reject operations with short names', () => {
      const result = complianceManager.validateOperation('ab', {
        validContext: 'data'
      });

      expect(result).toBe(false);

      // Should record a violation
      const report = complianceManager.generateComplianceReport();
      expect(report.violations).toHaveLength(1);
      expect(report.violations[0].type).toBe('operation_non_compliance');
    });

    test('should handle validation errors gracefully', () => {
      // Mock timing context to throw error
      mockTimingContext.end.mockImplementationOnce(() => {
        throw new Error('Validation timing error');
      });

      const result = complianceManager.validateOperation('test_operation', {
        validContext: 'data'
      });

      expect(result).toBe(false);
      expect(mockMetricsCollector.recordTiming).toHaveBeenCalledWith(
        'complianceValidationError',
        { duration: 1.5, reliable: true }
      );

      // Should record a violation for the error
      const report = complianceManager.generateComplianceReport();
      expect(report.violations).toHaveLength(1);
      expect(report.violations[0].type).toBe('compliance_validation_error');
      expect(report.violations[0].severity).toBe('high');
    });

    test('should skip validation when real-time validation is disabled', async () => {
      const disabledManager = new ComplianceManager({
        enableRealTimeValidation: false
      });
      await (disabledManager as any).initialize();

      const result = disabledManager.validateOperation('any_operation', {});

      expect(result).toBe(true);

      // Should not record any violations
      const report = disabledManager.generateComplianceReport();
      expect(report.violations).toHaveLength(0);

      await disabledManager.shutdown();
    });

    test('should handle null or undefined operation names', () => {
      const result1 = complianceManager.validateOperation('', { validContext: 'data' });
      const result2 = complianceManager.validateOperation(null as any, { validContext: 'data' });

      expect(result1).toBe(false);
      expect(result2).toBe(false);

      const report = complianceManager.generateComplianceReport();
      expect(report.violations).toHaveLength(2);
    });
  });

  // ============================================================================
  // SECTION 6: COMPLIANCE REPORTING TESTING (Lines 751-900)
  // AI Context: "Compliance reporting and metrics generation validation"
  // ============================================================================

  describe('Compliance Reporting', () => {
    test('should generate comprehensive compliance reports', () => {
      // Add some test data
      complianceManager.recordViolation({
        type: 'test_violation',
        severity: 'medium',
        description: 'Test violation for reporting'
      });

      complianceManager.addAuditEntry('test_operation', 'success', {
        operationId: 'op123'
      });

      const report = complianceManager.generateComplianceReport();

      expect(report.timestamp).toBeInstanceOf(Date);
      expect(report.complianceScore).toBe(95); // 100 - 5 (medium penalty)
      expect(report.violations).toHaveLength(1);
      expect(report.auditTrail).toHaveLength(2); // violation audit + manual audit
      expect(Array.isArray(report.recommendations)).toBe(true);
    });

    test('should provide recommendations based on compliance score', () => {
      // Reduce compliance score below 80
      for (let i = 0; i < 5; i++) {
        complianceManager.recordViolation({
          type: 'test_violation',
          severity: 'medium',
          description: `Test violation ${i}`
        });
      }

      const report = complianceManager.generateComplianceReport();
      expect(report.complianceScore).toBe(75); // 100 - (5 * 5)
      expect(report.recommendations).toContain('Review and address compliance violations');
    });

    test('should recommend immediate action for critical violations', () => {
      complianceManager.recordViolation({
        type: 'critical_violation',
        severity: 'critical',
        description: 'Critical security violation'
      });

      const report = complianceManager.generateComplianceReport();
      expect(report.recommendations).toContain('Immediately address critical compliance violations');
    });

    test('should return immutable compliance reports', () => {
      const report1 = complianceManager.generateComplianceReport();
      const report2 = complianceManager.generateComplianceReport();

      expect(report1).not.toBe(report2); // Different objects
      expect(report1.violations).not.toBe(report2.violations); // Different arrays
      expect(report1.auditTrail).not.toBe(report2.auditTrail); // Different arrays

      // Modifying returned arrays should not affect internal state
      report1.violations.push({
        type: 'fake_violation',
        severity: 'low',
        description: 'Fake violation',
        timestamp: new Date()
      });

      const report3 = complianceManager.generateComplianceReport();
      expect(report3.violations).not.toContain(
        expect.objectContaining({ type: 'fake_violation' })
      );
    });

    test('should filter recent violations correctly', () => {
      // Record violations
      complianceManager.recordViolation({
        type: 'recent_violation',
        severity: 'low',
        description: 'Recent violation'
      });

      const recentViolations24h = complianceManager.getRecentViolations(24);
      const recentViolations1h = complianceManager.getRecentViolations(1);

      expect(recentViolations24h).toHaveLength(1);
      expect(recentViolations1h).toHaveLength(1); // Should still be within 1 hour

      // Test with very small time window (should still include recent violations)
      const recentViolationsSmall = complianceManager.getRecentViolations(0.001); // 3.6 seconds
      expect(recentViolationsSmall).toHaveLength(1);

      // Test default parameter
      const recentViolationsDefault = complianceManager.getRecentViolations();
      expect(recentViolationsDefault).toHaveLength(1);
    });
  });

  // ============================================================================
  // SECTION 7: MEMORY SAFETY AND PERFORMANCE (Lines 901-1050)
  // AI Context: "Memory safety and performance validation"
  // ============================================================================

  describe('Memory Safety and Performance', () => {
    test('should properly initialize and shutdown', async () => {
      const testManager = new ComplianceManager();

      await (testManager as any).initialize();
      expect(testManager['_resilientTimer']).toBeDefined();
      expect(testManager['_metricsCollector']).toBeDefined();

      await testManager.shutdown();

      // Verify state is reset after shutdown
      expect(testManager.getComplianceScore()).toBe(100);
      expect(testManager.generateComplianceReport().violations).toHaveLength(0);
      expect(testManager.generateComplianceReport().auditTrail).toHaveLength(0);
    });

    test('should handle resource cleanup correctly', async () => {
      // Generate some compliance data
      complianceManager.recordViolation({
        type: 'test_violation',
        severity: 'medium',
        description: 'Test violation'
      });
      complianceManager.addAuditEntry('test_operation', 'success', { test: 'data' });

      // Verify data exists
      let report = complianceManager.generateComplianceReport();
      expect(report.violations).toHaveLength(1);
      expect(report.auditTrail).toHaveLength(2); // violation audit + manual audit

      // Shutdown should clean up
      await complianceManager.shutdown();

      report = complianceManager.generateComplianceReport();
      expect(report.violations).toHaveLength(0);
      expect(report.auditTrail).toHaveLength(0);
      expect(complianceManager.getComplianceScore()).toBe(100);
    });

    test('should meet performance requirements for compliance operations', () => {
      const iterations = 1000;
      const startTime = process.hrtime.bigint();

      for (let i = 0; i < iterations; i++) {
        complianceManager.validateOperation(`operation_${i}`, {
          userId: `user_${i}`,
          action: 'test'
        });
        complianceManager.addAuditEntry(`audit_${i}`, 'success', { iteration: i });
      }

      const endTime = process.hrtime.bigint();
      const avgTime = Number(endTime - startTime) / iterations / 1000000; // Convert to ms

      // Should meet <2ms per operation requirement
      expect(avgTime).toBeLessThan(2);
    });

    test('should handle high-frequency operations efficiently', () => {
      const startTime = Date.now();

      // Simulate high-frequency operations
      for (let i = 0; i < 5000; i++) {
        complianceManager.recordViolation({
          type: 'high_freq_violation',
          severity: 'low',
          description: `High frequency violation ${i}`
        });
        complianceManager.validateOperation(`operation_${i}`, { iteration: i });
      }

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Should complete within reasonable time (less than 100ms for 5k operations)
      expect(duration).toBeLessThan(100);

      // Verify operations were tracked
      const report = complianceManager.generateComplianceReport();
      expect(report.violations).toHaveLength(5000);
    });

    test('should maintain memory efficiency with large datasets', () => {
      const initialMemory = process.memoryUsage().heapUsed;

      // Generate large amount of compliance data
      for (let i = 0; i < 10000; i++) {
        complianceManager.recordViolation({
          type: 'memory_test_violation',
          severity: 'low',
          description: `Memory test violation ${i}`,
          context: { iteration: i, data: 'test'.repeat(10) }
        });
        complianceManager.addAuditEntry(`memory_test_${i}`, 'success', {
          iteration: i,
          data: 'test'.repeat(10)
        });
      }

      const finalMemory = process.memoryUsage().heapUsed;
      const memoryGrowth = finalMemory - initialMemory;

      // Memory growth should be reasonable (less than 50MB for this test)
      expect(memoryGrowth).toBeLessThan(50 * 1024 * 1024);
    });
  });

  // ============================================================================
  // SECTION 8: INTERVAL CALLBACKS AND PRIVATE METHODS (Lines 1051-1200)
  // AI Context: "Testing interval callbacks and private method execution"
  // ============================================================================

  describe('Interval Callbacks and Private Methods', () => {
    test('should execute compliance reporting interval callback', async () => {
      // Create manager with compliance reporting enabled
      const reportingManager = new ComplianceManager({
        complianceReportingIntervalMs: 1000 // 1 second
      });
      await (reportingManager as any).initialize();

      // Spy on createSafeInterval to capture the callback
      let intervalCallback: (() => void) | undefined;
      const createSafeIntervalSpy = jest.spyOn(reportingManager as any, 'createSafeInterval')
        .mockImplementation((...args: any[]) => {
          if (args[2] === 'compliance-reporting') {
            intervalCallback = args[0] as () => void;
          }
          return 'mocked-interval-id';
        });

      // Re-initialize to trigger interval setup
      await (reportingManager as any).doInitialize();

      // Verify interval was created
      expect(createSafeIntervalSpy).toHaveBeenCalledWith(
        expect.any(Function),
        1000,
        'compliance-reporting'
      );

      // Execute the captured callback to test line 126
      expect(intervalCallback).toBeDefined();
      if (intervalCallback) {
        intervalCallback();
      }

      // Verify the private method was executed (indirectly through timing calls)
      expect(mockResilientTimer.start).toHaveBeenCalled();
      expect(mockTimingContext.end).toHaveBeenCalled();
      expect(mockMetricsCollector.recordTiming).toHaveBeenCalledWith(
        'complianceReporting',
        { duration: 1.5, reliable: true }
      );

      await reportingManager.shutdown();
    });

    test('should execute audit cleanup interval callback', async () => {
      // Create manager with audit trail enabled
      const cleanupManager = new ComplianceManager({
        enableAuditTrail: true,
        auditRetentionMs: 3600000 // 1 hour
      });
      await (cleanupManager as any).initialize();

      // Spy on createSafeInterval to capture the callback
      let intervalCallback: (() => void) | undefined;
      const createSafeIntervalSpy = jest.spyOn(cleanupManager as any, 'createSafeInterval')
        .mockImplementation((...args: any[]) => {
          if (args[2] === 'audit-cleanup') {
            intervalCallback = args[0] as () => void;
          }
          return 'mocked-interval-id';
        });

      // Re-initialize to trigger interval setup
      await (cleanupManager as any).doInitialize();

      // Verify interval was created
      expect(createSafeIntervalSpy).toHaveBeenCalledWith(
        expect.any(Function),
        900000, // auditRetentionMs / 4 = 3600000 / 4 = 900000
        'audit-cleanup'
      );

      // Add some test data that should be cleaned up
      cleanupManager.addAuditEntry('old_operation', 'success', { test: 'data' });
      cleanupManager.recordViolation({
        type: 'old_violation',
        severity: 'low',
        description: 'Old violation'
      });

      // Mock Date.now to simulate old timestamps
      const originalDateNow = Date.now;
      const mockDateNow = jest.fn().mockReturnValue(Date.now() + 7200000); // 2 hours later
      Date.now = mockDateNow;

      // Execute the captured callback to test line 134
      expect(intervalCallback).toBeDefined();
      if (intervalCallback) {
        intervalCallback();
      }

      // Verify cleanup occurred (audit trail and violations should be filtered)
      const report = cleanupManager.generateComplianceReport();
      expect(report.auditTrail).toHaveLength(0); // Should be cleaned up
      expect(report.violations).toHaveLength(0); // Should be cleaned up

      // Restore Date.now
      Date.now = originalDateNow;

      await cleanupManager.shutdown();
    });

    test('should handle errors in compliance reporting interval callback', async () => {
      const errorManager = new ComplianceManager({
        complianceReportingIntervalMs: 1000
      });
      await (errorManager as any).initialize();

      // Mock timing context to throw error in _generateComplianceReport
      mockTimingContext.end.mockImplementationOnce(() => {
        throw new Error('Compliance reporting error');
      });

      // Spy on createSafeInterval to capture the callback
      let intervalCallback: (() => void) | undefined;
      jest.spyOn(errorManager as any, 'createSafeInterval')
        .mockImplementation((...args: any[]) => {
          if (args[2] === 'compliance-reporting') {
            intervalCallback = args[0] as () => void;
          }
          return 'mocked-interval-id';
        });

      // Re-initialize to trigger interval setup
      await (errorManager as any).doInitialize();

      // Execute the callback which should trigger the error path
      expect(intervalCallback).toBeDefined();
      if (intervalCallback) {
        // Should not throw, error should be caught
        expect(() => intervalCallback!()).not.toThrow();
      }

      // Verify error handling metrics were recorded
      expect(mockMetricsCollector.recordTiming).toHaveBeenCalledWith(
        'complianceReportingError',
        { duration: 1.5, reliable: true }
      );

      await errorManager.shutdown();
    });

    test('should test private _cleanupAuditTrail method directly', () => {
      // Add test data with different timestamps
      const now = Date.now();
      const oldTimestamp = new Date(now - 25 * 60 * 60 * 1000); // 25 hours ago (older than default 24h retention)
      const recentTimestamp = new Date(now - 1800000); // 30 minutes ago

      // Manually add data to internal arrays (simulating old data)
      (complianceManager as any)._auditTrail.push({
        id: 'old-audit',
        timestamp: oldTimestamp,
        operation: 'old_operation',
        result: 'success',
        details: {}
      });

      (complianceManager as any)._violations.push({
        type: 'old_violation',
        severity: 'low',
        description: 'Old violation',
        timestamp: oldTimestamp
      });

      (complianceManager as any)._auditTrail.push({
        id: 'recent-audit',
        timestamp: recentTimestamp,
        operation: 'recent_operation',
        result: 'success',
        details: {}
      });

      (complianceManager as any)._violations.push({
        type: 'recent_violation',
        severity: 'low',
        description: 'Recent violation',
        timestamp: recentTimestamp
      });

      // Call the private method directly
      (complianceManager as any)._cleanupAuditTrail();

      // Verify old data was removed and recent data was kept
      const report = complianceManager.generateComplianceReport();

      // Should only have recent entries (default retention is 24 hours)
      expect(report.auditTrail.length).toBeGreaterThan(0);
      expect(report.violations.length).toBeGreaterThan(0);

      // Verify old entries are not present
      expect(report.auditTrail.find(entry => entry.id === 'old-audit')).toBeUndefined();
      expect(report.violations.find(v => v.type === 'old_violation')).toBeUndefined();

      // Verify recent entries are present
      expect(report.auditTrail.find(entry => entry.id === 'recent-audit')).toBeDefined();
      expect(report.violations.find(v => v.type === 'recent_violation')).toBeDefined();
    });

    test('should test private _generateComplianceReport method directly', () => {
      // Add some test data
      complianceManager.recordViolation({
        type: 'test_violation',
        severity: 'medium',
        description: 'Test violation for private method'
      });

      // Call the private method directly
      (complianceManager as any)._generateComplianceReport();

      // Verify timing was recorded
      expect(mockResilientTimer.start).toHaveBeenCalled();
      expect(mockTimingContext.end).toHaveBeenCalled();
      expect(mockMetricsCollector.recordTiming).toHaveBeenCalledWith(
        'complianceReporting',
        { duration: 1.5, reliable: true }
      );
    });
  });

  // ============================================================================
  // SECTION 9: INTEGRATION AND ERROR HANDLING (Lines 1201-1350)
  // AI Context: "Integration testing and comprehensive error handling"
  // ============================================================================

  describe('Integration and Error Handling', () => {
    test('should integrate properly with MemorySafeResourceManager', () => {
      expect(complianceManager).toBeInstanceOf(require('../../../MemorySafeResourceManager').MemorySafeResourceManager);

      // Should have resource management capabilities
      expect(typeof (complianceManager as any).createSafeInterval).toBe('function');
      expect(typeof (complianceManager as any).createSafeTimeout).toBe('function');
    });

    test('should handle concurrent operations safely', async () => {
      const promises: Promise<any>[] = [];

      // Create multiple concurrent operations
      for (let i = 0; i < 100; i++) {
        promises.push(
          Promise.resolve().then(() => {
            complianceManager.recordViolation({
              type: `concurrent_violation_${i}`,
              severity: 'low',
              description: `Concurrent violation ${i}`
            });
            complianceManager.addAuditEntry(`concurrent_operation_${i}`, 'success', { iteration: i });
            complianceManager.validateOperation(`concurrent_validation_${i}`, { iteration: i });
          })
        );
      }

      await Promise.all(promises);

      const report = complianceManager.generateComplianceReport();
      expect(report.violations).toHaveLength(100);
      expect(report.auditTrail.length).toBeGreaterThanOrEqual(100); // At least 100, may have more from violations
    });

    test('should provide consistent interface across all compliance methods', () => {
      // Test all public methods are accessible
      expect(typeof complianceManager.recordViolation).toBe('function');
      expect(typeof complianceManager.addAuditEntry).toBe('function');
      expect(typeof complianceManager.validateOperation).toBe('function');
      expect(typeof complianceManager.generateComplianceReport).toBe('function');
      expect(typeof complianceManager.getComplianceScore).toBe('function');
      expect(typeof complianceManager.getRecentViolations).toBe('function');
      expect(typeof complianceManager.clearViolations).toBe('function');
    });

    test('should handle resilient timing infrastructure correctly', () => {
      expect(MockedResilientTimer).toHaveBeenCalledWith({
        enableFallbacks: true,
        maxExpectedDuration: 2000,
        unreliableThreshold: 3,
        estimateBaseline: 1
      });

      expect(MockedResilientMetricsCollector).toHaveBeenCalledWith({
        enableFallbacks: true,
        cacheUnreliableValues: false,
        maxMetricsAge: 86400000,
        defaultEstimates: expect.any(Map)
      });

      // Verify default estimates map
      const call = MockedResilientMetricsCollector.mock.calls[0]?.[0];
      const defaultEstimates = call?.defaultEstimates;
      expect(defaultEstimates?.get('complianceValidation')).toBe(1);
      expect(defaultEstimates?.get('auditTrailUpdate')).toBe(1);
      expect(defaultEstimates?.get('complianceReporting')).toBe(2);
    });

    test('should handle edge cases in compliance validation', () => {
      const edgeCases = [
        { operation: 'min', context: { a: 1 } }, // Minimal valid case
        { operation: 'a'.repeat(100), context: { data: 'test' } }, // Long operation name
        { operation: 'test_operation', context: { complex: { nested: { data: 'value' } } } }, // Complex context
      ];

      for (const { operation, context } of edgeCases) {
        const result = complianceManager.validateOperation(operation, context);
        expect(typeof result).toBe('boolean');
      }
    });

    test('should handle configuration edge cases', async () => {
      const edgeConfigs = [
        { complianceReportingIntervalMs: 0 }, // Disabled reporting
        { auditRetentionMs: 1000 }, // Very short retention
        { enableAuditTrail: false, enableRealTimeValidation: false }, // All disabled
      ];

      for (const config of edgeConfigs) {
        const edgeManager = new ComplianceManager(config);
        await (edgeManager as any).initialize();

        // Should not throw errors
        edgeManager.recordViolation({
          type: 'edge_test',
          severity: 'low',
          description: 'Edge case test'
        });

        const report = edgeManager.generateComplianceReport();
        expect(report).toBeDefined();
        expect(typeof report.complianceScore).toBe('number');

        await edgeManager.shutdown();
      }
    });
  });
});
