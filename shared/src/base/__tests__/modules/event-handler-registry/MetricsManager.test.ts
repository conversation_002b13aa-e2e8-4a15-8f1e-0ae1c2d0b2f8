/**
 * ============================================================================
 * AI CONTEXT: MetricsManager Test Suite - Performance Monitoring & Analytics
 * Purpose: Comprehensive testing of metrics collection and monitoring with resilient timing
 * Complexity: High - Complex metrics aggregation with enterprise-grade timing validation
 * AI Navigation: 8 logical sections - Setup, Core, Collection, Performance, Error, Timing, Memory, Integration
 * Dependencies: ResilientTiming, ResilientMetrics, MemorySafeResourceManager
 * Performance: <1ms per metrics operation validation
 * ============================================================================
 */

/**
 * @file MetricsManager Test Suite
 * @filepath shared/src/base/__tests__/modules/event-handler-registry/MetricsManager.test.ts
 * @task-id T-TSK-02.SUB-03.5.MEM-01
 * @component event-handler-registry-enhanced
 * @reference foundation-context.MEMORY-SAFETY.003
 * @template enhanced-metrics-testing
 * @tier T0
 * @context foundation-context
 * @category Memory-Safety-Enhanced-Metrics-Testing
 * @created 2025-08-05
 * @modified 2025-08-05
 *
 * @description
 * Comprehensive test suite for MetricsManager module:
 * - Performance monitoring and analytics collection testing with resilient timing integration
 * - Enterprise-grade metrics aggregation and reporting testing with timing measurement
 * - Real-time performance tracking testing with comprehensive metrics coverage
 * - Anti-Simplification Policy compliance with comprehensive metrics validation
 * - Memory-safe resource management validation for T0 metrics components
 */

// ============================================================================
// TABLE OF CONTENTS
// ============================================================================
// CLASSES:
//   MetricsManager (Imported: 69)
//     - Properties:
//       - _resilientTimer (Mocked: 79)
//       - _metricsCollector (Mocked: 90)
//       - _config (Line 76)
//       - _emissionMetrics (Line 79)
//       - _performanceMetrics (Line 91)
//     - Methods:
//       - constructor (Line 100)
//       - initialize (Line 117)
//       - shutdown (Line 147)
//       - updateEmissionMetrics (Line 160)
//       - updatePerformanceMetrics (Line 183)
//       - incrementCounter (Line 207)
//       - recordHandlerTimeout (Line 216)
//       - getEmissionMetrics (Line 228)
//       - getPerformanceMetrics (Line 235)
//       - getComprehensiveMetrics (Line 242)
//       - resetAllMetrics (Line 255)
// INTERFACES:
//   IMetricsManagerConfig (Line 42)
//     - enableTiming?: boolean (Line 43)
//     - metricsRetentionMs?: number (Line 44)
//     - aggregationIntervalMs?: number (Line 45)
//     - enableReporting?: boolean (Line 46)
//   IEmissionMetrics (Line 49)
//     - totalEmissions: number (Line 50)
//     - successfulEmissions: number (Line 51)
//     - failedEmissions: number (Line 52)
//     - bufferedEvents: number (Line 53)
//     - totalMiddlewareExecutions: number (Line 54)
//     - duplicatesDetected: number (Line 55)
//     - averageEmissionTime: number (Line 56)
//     - totalRetries: number (Line 57)
//     - deadLetterEvents: number (Line 58)
//   IPerformanceMetrics (Line 61)
//     - averageHandlerExecutionTime: number (Line 62)
//     - maxHandlerExecutionTime: number (Line 63)
//     - minHandlerExecutionTime: number (Line 64)
//     - totalHandlerExecutions: number (Line 65)
//     - handlerTimeouts: number (Line 66)
//     - handlerErrors: number (Line 67)
// IMPORTED:
//   MetricsManager (Imported from '../../../event-handler-registry/modules/MetricsManager')
//   IMetricsManagerConfig (Imported from '../../../event-handler-registry/modules/MetricsManager')
//   ResilientTimer (Imported from '../../../utils/ResilientTiming')
//   ResilientMetricsCollector (Imported from '../../../utils/ResilientMetrics')
// ============================================================================

import {
  MetricsManager,
  IMetricsManagerConfig
} from '../../../event-handler-registry/modules/MetricsManager';
import { ResilientTimer } from '../../../utils/ResilientTiming';
import { ResilientMetricsCollector } from '../../../utils/ResilientMetrics';

// Jest configuration for comprehensive testing
jest.setTimeout(30000); // 30 seconds for comprehensive tests

// Mock ResilientTimer and ResilientMetricsCollector for controlled testing
jest.mock('../../../utils/ResilientTiming');
jest.mock('../../../utils/ResilientMetrics');

const MockedResilientTimer = ResilientTimer as jest.MockedClass<typeof ResilientTimer>;
const MockedResilientMetricsCollector = ResilientMetricsCollector as jest.MockedClass<typeof ResilientMetricsCollector>;

describe('MetricsManager - Comprehensive Testing Suite', () => {
  let metricsManager: MetricsManager;
  let mockResilientTimer: jest.Mocked<ResilientTimer>;
  let mockMetricsCollector: jest.Mocked<ResilientMetricsCollector>;
  let mockTimingContext: { end: jest.Mock };

  // ============================================================================
  // SECTION 1: TEST SETUP AND CONFIGURATION (Lines 1-150)
  // AI Context: "Test environment setup with mocked dependencies"
  // ============================================================================

  beforeEach(async () => {
    // Clear all mocks
    jest.clearAllMocks();

    // Setup timing context mock
    mockTimingContext = {
      end: jest.fn().mockReturnValue({ duration: 1.5, reliable: true })
    };

    // Setup ResilientTimer mock
    mockResilientTimer = {
      start: jest.fn().mockReturnValue(mockTimingContext),
      isReliable: jest.fn().mockReturnValue(true),
      getReliabilityScore: jest.fn().mockReturnValue(0.95)
    } as any;

    // Setup ResilientMetricsCollector mock
    mockMetricsCollector = {
      recordTiming: jest.fn(),
      createSnapshot: jest.fn().mockReturnValue({
        totalMeasurements: 10,
        averageDuration: 1.2,
        reliability: 0.98
      }),
      getStatistics: jest.fn().mockReturnValue({
        count: 10,
        average: 1.2,
        min: 0.5,
        max: 2.0
      })
    } as any;

    // Configure mocked constructors
    MockedResilientTimer.mockImplementation(() => mockResilientTimer);
    MockedResilientMetricsCollector.mockImplementation(() => mockMetricsCollector);

    // Create fresh MetricsManager instance
    metricsManager = new MetricsManager({
      enableTiming: true,
      metricsRetentionMs: 3600000,
      aggregationIntervalMs: 60000,
      enableReporting: true
    });

    await (metricsManager as any).initialize();
  });

  afterEach(async () => {
    if (metricsManager) {
      await metricsManager.shutdown();
    }
  });

  // ============================================================================
  // SECTION 2: CORE FUNCTIONALITY TESTING (Lines 151-250)
  // AI Context: "Basic MetricsManager functionality validation"
  // ============================================================================

  describe('Core Functionality', () => {
    test('should initialize with default configuration', async () => {
      const defaultManager = new MetricsManager();
      await (defaultManager as any).initialize();

      expect(MockedResilientTimer).toHaveBeenCalledWith({
        enableFallbacks: true,
        maxExpectedDuration: 5000,
        unreliableThreshold: 3,
        estimateBaseline: 1
      });

      expect(MockedResilientMetricsCollector).toHaveBeenCalledWith({
        enableFallbacks: true,
        cacheUnreliableValues: false,
        maxMetricsAge: 3600000,
        defaultEstimates: expect.any(Map)
      });

      await defaultManager.shutdown();
    });

    test('should initialize with custom configuration', async () => {
      const customConfig: IMetricsManagerConfig = {
        enableTiming: false,
        metricsRetentionMs: 1800000,
        aggregationIntervalMs: 30000,
        enableReporting: false
      };

      const customManager = new MetricsManager(customConfig);
      await (customManager as any).initialize();

      expect(MockedResilientMetricsCollector).toHaveBeenCalledWith({
        enableFallbacks: true,
        cacheUnreliableValues: false,
        maxMetricsAge: 1800000,
        defaultEstimates: expect.any(Map)
      });

      await customManager.shutdown();
    });

    test('should properly initialize resilient timing infrastructure', async () => {
      expect(MockedResilientTimer).toHaveBeenCalledTimes(1);
      expect(MockedResilientMetricsCollector).toHaveBeenCalledTimes(1);
      expect(metricsManager['_resilientTimer']).toBeDefined();
      expect(metricsManager['_metricsCollector']).toBeDefined();
    });
  });

  // ============================================================================
  // SECTION 3: EMISSION METRICS TESTING (Lines 251-400)
  // AI Context: "Emission metrics collection and calculation validation"
  // ============================================================================

  describe('Emission Metrics Collection', () => {
    test('should update emission metrics correctly', () => {
      const executionTime = 5.5;
      const successfulHandlers = 3;
      const failedHandlers = 0;

      metricsManager.updateEmissionMetrics(executionTime, successfulHandlers, failedHandlers);

      const metrics = metricsManager.getEmissionMetrics();
      expect(metrics.totalEmissions).toBe(1);
      expect(metrics.successfulEmissions).toBe(1);
      expect(metrics.failedEmissions).toBe(0);
      expect(metrics.averageEmissionTime).toBe(5.5);
    });

    test('should handle failed emissions correctly', () => {
      metricsManager.updateEmissionMetrics(3.0, 2, 1); // Mixed success/failure (has failures)
      metricsManager.updateEmissionMetrics(4.0, 0, 2); // All failures

      const metrics = metricsManager.getEmissionMetrics();
      expect(metrics.totalEmissions).toBe(2);
      expect(metrics.successfulEmissions).toBe(0); // Both had failures, so 0 successful
      expect(metrics.failedEmissions).toBe(2); // Both had failures
      expect(metrics.averageEmissionTime).toBe(3.5); // (3.0 + 4.0) / 2
    });

    test('should calculate rolling average emission time correctly', () => {
      metricsManager.updateEmissionMetrics(2.0, 1, 0);
      metricsManager.updateEmissionMetrics(4.0, 1, 0);
      metricsManager.updateEmissionMetrics(6.0, 1, 0);

      const metrics = metricsManager.getEmissionMetrics();
      expect(metrics.totalEmissions).toBe(3);
      expect(metrics.averageEmissionTime).toBe(4.0); // (2 + 4 + 6) / 3
    });

    test('should increment specific counters correctly', () => {
      metricsManager.incrementCounter('bufferedEvents');
      metricsManager.incrementCounter('duplicatesDetected');
      metricsManager.incrementCounter('totalRetries');
      metricsManager.incrementCounter('deadLetterEvents');

      const metrics = metricsManager.getEmissionMetrics();
      expect(metrics.bufferedEvents).toBe(1);
      expect(metrics.duplicatesDetected).toBe(1);
      expect(metrics.totalRetries).toBe(1);
      expect(metrics.deadLetterEvents).toBe(1);
    });

    test('should handle counter increments for numeric properties', () => {
      // First set up some baseline data
      const initialMetrics = metricsManager.getEmissionMetrics();
      const initialAverage = initialMetrics.averageEmissionTime;

      // Increment a numeric property (averageEmissionTime is numeric, so it will be incremented)
      metricsManager.incrementCounter('averageEmissionTime' as any);

      const metrics = metricsManager.getEmissionMetrics();
      expect(metrics.averageEmissionTime).toBe(initialAverage + 1); // Should be incremented
    });

    test('should return immutable emission metrics', () => {
      metricsManager.updateEmissionMetrics(1.0, 1, 0);
      const metrics1 = metricsManager.getEmissionMetrics();
      const metrics2 = metricsManager.getEmissionMetrics();

      expect(metrics1).not.toBe(metrics2); // Different objects
      expect(metrics1).toEqual(metrics2); // Same content

      // Modifying returned object should not affect internal state
      metrics1.totalEmissions = 999;
      const metrics3 = metricsManager.getEmissionMetrics();
      expect(metrics3.totalEmissions).toBe(1);
    });
  });

  // ============================================================================
  // SECTION 4: PERFORMANCE METRICS TESTING (Lines 401-550)
  // AI Context: "Performance metrics collection and calculation validation"
  // ============================================================================

  describe('Performance Metrics Collection', () => {
    test('should update performance metrics correctly', () => {
      const executionTime = 2.5;
      metricsManager.updatePerformanceMetrics(executionTime, false);

      const metrics = metricsManager.getPerformanceMetrics();
      expect(metrics.totalHandlerExecutions).toBe(1);
      expect(metrics.handlerErrors).toBe(0);
      expect(metrics.averageHandlerExecutionTime).toBe(2.5);
      expect(metrics.maxHandlerExecutionTime).toBe(2.5);
      expect(metrics.minHandlerExecutionTime).toBe(2.5);
    });

    test('should track handler errors correctly', () => {
      metricsManager.updatePerformanceMetrics(1.0, false);
      metricsManager.updatePerformanceMetrics(2.0, true);
      metricsManager.updatePerformanceMetrics(3.0, true);

      const metrics = metricsManager.getPerformanceMetrics();
      expect(metrics.totalHandlerExecutions).toBe(3);
      expect(metrics.handlerErrors).toBe(2);
      expect(metrics.averageHandlerExecutionTime).toBe(2.0); // (1 + 2 + 3) / 3
    });

    test('should calculate min/max execution times correctly', () => {
      metricsManager.updatePerformanceMetrics(5.0, false);
      metricsManager.updatePerformanceMetrics(1.0, false);
      metricsManager.updatePerformanceMetrics(8.0, false);
      metricsManager.updatePerformanceMetrics(3.0, false);

      const metrics = metricsManager.getPerformanceMetrics();
      expect(metrics.minHandlerExecutionTime).toBe(1.0);
      expect(metrics.maxHandlerExecutionTime).toBe(8.0);
      expect(metrics.averageHandlerExecutionTime).toBe(4.25); // (5 + 1 + 8 + 3) / 4
    });

    test('should record handler timeouts correctly', () => {
      metricsManager.recordHandlerTimeout();
      metricsManager.recordHandlerTimeout();

      const metrics = metricsManager.getPerformanceMetrics();
      expect(metrics.handlerTimeouts).toBe(2);
    });

    test('should handle initial min value correctly', () => {
      const metrics = metricsManager.getPerformanceMetrics();
      expect(metrics.minHandlerExecutionTime).toBe(Number.MAX_VALUE);

      metricsManager.updatePerformanceMetrics(2.5, false);
      const updatedMetrics = metricsManager.getPerformanceMetrics();
      expect(updatedMetrics.minHandlerExecutionTime).toBe(2.5);
    });

    test('should return immutable performance metrics', () => {
      metricsManager.updatePerformanceMetrics(1.0, false);
      const metrics1 = metricsManager.getPerformanceMetrics();
      const metrics2 = metricsManager.getPerformanceMetrics();

      expect(metrics1).not.toBe(metrics2); // Different objects
      expect(metrics1).toEqual(metrics2); // Same content

      // Modifying returned object should not affect internal state
      metrics1.totalHandlerExecutions = 999;
      const metrics3 = metricsManager.getPerformanceMetrics();
      expect(metrics3.totalHandlerExecutions).toBe(1);
    });
  });

  // ============================================================================
  // SECTION 5: COMPREHENSIVE METRICS REPORTING (Lines 551-700)
  // AI Context: "Comprehensive metrics reporting and aggregation validation"
  // ============================================================================

  describe('Comprehensive Metrics Reporting', () => {
    test('should provide comprehensive metrics report', () => {
      // Setup some metrics data
      metricsManager.updateEmissionMetrics(3.0, 2, 1);
      metricsManager.updatePerformanceMetrics(1.5, false);
      metricsManager.incrementCounter('bufferedEvents');

      const comprehensiveMetrics = metricsManager.getComprehensiveMetrics();

      expect(comprehensiveMetrics).toHaveProperty('emission');
      expect(comprehensiveMetrics).toHaveProperty('performance');
      expect(comprehensiveMetrics).toHaveProperty('resilientTiming');
      expect(comprehensiveMetrics).toHaveProperty('timestamp');
      expect(comprehensiveMetrics).toHaveProperty('uptime');

      expect(comprehensiveMetrics.emission.totalEmissions).toBe(1);
      expect(comprehensiveMetrics.performance.totalHandlerExecutions).toBe(1);
      expect(comprehensiveMetrics.resilientTiming).toEqual({
        totalMeasurements: 10,
        averageDuration: 1.2,
        reliability: 0.98
      });
      expect(comprehensiveMetrics.timestamp).toBeInstanceOf(Date);
      expect(comprehensiveMetrics.uptime).toBeGreaterThanOrEqual(0);
    });

    test('should include resilient timing snapshot in comprehensive metrics', () => {
      const comprehensiveMetrics = metricsManager.getComprehensiveMetrics();

      expect(mockMetricsCollector.createSnapshot).toHaveBeenCalled();
      expect(comprehensiveMetrics.resilientTiming).toBeDefined();
    });

    test('should calculate uptime correctly', () => {
      // Since we're using fake timers, just verify uptime is a number >= 0
      const comprehensiveMetrics = metricsManager.getComprehensiveMetrics();
      expect(typeof comprehensiveMetrics.uptime).toBe('number');
      expect(comprehensiveMetrics.uptime).toBeGreaterThanOrEqual(0);
    });

    test('should reset all metrics correctly', () => {
      // Setup some metrics data
      metricsManager.updateEmissionMetrics(3.0, 2, 1);
      metricsManager.updatePerformanceMetrics(1.5, false);
      metricsManager.incrementCounter('bufferedEvents');
      metricsManager.recordHandlerTimeout();

      // Verify data exists
      let emissionMetrics = metricsManager.getEmissionMetrics();
      let performanceMetrics = metricsManager.getPerformanceMetrics();
      expect(emissionMetrics.totalEmissions).toBe(1);
      expect(performanceMetrics.totalHandlerExecutions).toBe(1);

      // Reset all metrics
      metricsManager.resetAllMetrics();

      // Verify all metrics are reset
      emissionMetrics = metricsManager.getEmissionMetrics();
      performanceMetrics = metricsManager.getPerformanceMetrics();

      expect(emissionMetrics.totalEmissions).toBe(0);
      expect(emissionMetrics.successfulEmissions).toBe(0);
      expect(emissionMetrics.failedEmissions).toBe(0);
      expect(emissionMetrics.bufferedEvents).toBe(0);
      expect(emissionMetrics.averageEmissionTime).toBe(0);

      expect(performanceMetrics.totalHandlerExecutions).toBe(0);
      expect(performanceMetrics.handlerErrors).toBe(0);
      expect(performanceMetrics.handlerTimeouts).toBe(0);
      expect(performanceMetrics.averageHandlerExecutionTime).toBe(0);
      expect(performanceMetrics.maxHandlerExecutionTime).toBe(0);
      expect(performanceMetrics.minHandlerExecutionTime).toBe(Number.MAX_VALUE);
    });
  });

  // ============================================================================
  // SECTION 6: RESILIENT TIMING INTEGRATION (Lines 701-850)
  // AI Context: "Resilient timing infrastructure integration validation"
  // ============================================================================

  describe('Resilient Timing Integration', () => {
    test('should initialize resilient timing with correct configuration', () => {
      expect(MockedResilientTimer).toHaveBeenCalledWith({
        enableFallbacks: true,
        maxExpectedDuration: 5000,
        unreliableThreshold: 3,
        estimateBaseline: 1
      });
    });

    test('should initialize resilient metrics collector with correct configuration', () => {
      expect(MockedResilientMetricsCollector).toHaveBeenCalledWith({
        enableFallbacks: true,
        cacheUnreliableValues: false,
        maxMetricsAge: 3600000,
        defaultEstimates: expect.any(Map)
      });

      // Verify default estimates map
      const call = MockedResilientMetricsCollector.mock.calls[0]?.[0];
      const defaultEstimates = call?.defaultEstimates;
      expect(defaultEstimates?.get('metricsCollection')).toBe(1);
      expect(defaultEstimates?.get('metricsAggregation')).toBe(2);
      expect(defaultEstimates?.get('metricsReporting')).toBe(5);
    });

    test('should use resilient timing for metrics aggregation', () => {
      // Trigger metrics aggregation by calling private method
      const aggregateMethod = (metricsManager as any)._aggregateMetrics.bind(metricsManager);
      aggregateMethod();

      expect(mockResilientTimer.start).toHaveBeenCalled();
      expect(mockTimingContext.end).toHaveBeenCalled();
      expect(mockMetricsCollector.recordTiming).toHaveBeenCalledWith(
        'metricsAggregation',
        { duration: 1.5, reliable: true }
      );
    });

    test('should handle timing errors during aggregation', () => {
      // Mock timing context to throw error
      mockTimingContext.end.mockImplementationOnce(() => {
        throw new Error('Timing error');
      });

      const aggregateMethod = (metricsManager as any)._aggregateMetrics.bind(metricsManager);

      expect(() => aggregateMethod()).not.toThrow();
      expect(mockResilientTimer.start).toHaveBeenCalled();
      expect(mockMetricsCollector.recordTiming).toHaveBeenCalledWith(
        'metricsAggregationError',
        { duration: 1.5, reliable: true }
      );
    });

    test('should provide timing reliability assessment', () => {
      const comprehensiveMetrics = metricsManager.getComprehensiveMetrics();
      expect((comprehensiveMetrics.resilientTiming as any).reliability).toBe(0.98);
    });

    test('should trigger metrics aggregation via interval callback', () => {
      // Create a spy to track the actual interval callback execution
      let intervalCallback: (() => void) | undefined;

      // Create a new manager with reporting enabled and spy on createSafeInterval
      const reportingManager = new MetricsManager({
        enableReporting: true,
        aggregationIntervalMs: 1000
      });

      // Spy on createSafeInterval to capture the callback
      const createSafeIntervalSpy = jest.spyOn(reportingManager as any, 'createSafeInterval')
        .mockImplementation((...args: any[]) => {
          intervalCallback = args[0] as () => void;
          return 'mocked-interval-id';
        });

      // Initialize to trigger the interval creation
      (reportingManager as any).doInitialize();

      // Verify createSafeInterval was called
      expect(createSafeIntervalSpy).toHaveBeenCalledWith(
        expect.any(Function),
        1000,
        'metrics-aggregation'
      );

      // Clear previous calls to focus on the interval callback
      jest.clearAllMocks();

      // Manually trigger the captured interval callback to simulate the interval firing
      if (intervalCallback) {
        intervalCallback();
      }

      // Verify that the interval callback triggered _aggregateMetrics
      expect(mockResilientTimer.start).toHaveBeenCalled();
      expect(mockTimingContext.end).toHaveBeenCalled();
      expect(mockMetricsCollector.recordTiming).toHaveBeenCalledWith(
        'metricsAggregation',
        { duration: 1.5, reliable: true }
      );

      // Cleanup
      createSafeIntervalSpy.mockRestore();
    });
  });

  // ============================================================================
  // SECTION 7: ERROR HANDLING AND EDGE CASES (Lines 851-1000)
  // AI Context: "Error handling and edge case validation"
  // ============================================================================

  describe('Error Handling and Edge Cases', () => {
    test('should handle zero execution times correctly', () => {
      metricsManager.updateEmissionMetrics(0, 1, 0);
      metricsManager.updatePerformanceMetrics(0, false);

      const emissionMetrics = metricsManager.getEmissionMetrics();
      const performanceMetrics = metricsManager.getPerformanceMetrics();

      expect(emissionMetrics.averageEmissionTime).toBe(0);
      expect(performanceMetrics.averageHandlerExecutionTime).toBe(0);
      expect(performanceMetrics.minHandlerExecutionTime).toBe(0);
      expect(performanceMetrics.maxHandlerExecutionTime).toBe(0);
    });

    test('should handle negative execution times gracefully', () => {
      metricsManager.updateEmissionMetrics(-1, 1, 0);
      metricsManager.updatePerformanceMetrics(-2, false);

      const emissionMetrics = metricsManager.getEmissionMetrics();
      const performanceMetrics = metricsManager.getPerformanceMetrics();

      expect(emissionMetrics.averageEmissionTime).toBe(-1);
      expect(performanceMetrics.averageHandlerExecutionTime).toBe(-2);
      expect(performanceMetrics.minHandlerExecutionTime).toBe(-2);
      expect(performanceMetrics.maxHandlerExecutionTime).toBe(0); // Max starts at 0, so -2 won't update it
    });

    test('should handle very large execution times', () => {
      const largeTime = Number.MAX_SAFE_INTEGER;
      metricsManager.updateEmissionMetrics(largeTime, 1, 0);
      metricsManager.updatePerformanceMetrics(largeTime, false);

      const emissionMetrics = metricsManager.getEmissionMetrics();
      const performanceMetrics = metricsManager.getPerformanceMetrics();

      expect(emissionMetrics.averageEmissionTime).toBe(largeTime);
      expect(performanceMetrics.averageHandlerExecutionTime).toBe(largeTime);
      expect(performanceMetrics.maxHandlerExecutionTime).toBe(largeTime);
    });

    test('should handle multiple resets correctly', () => {
      metricsManager.updateEmissionMetrics(1, 1, 0);
      metricsManager.resetAllMetrics();
      metricsManager.resetAllMetrics(); // Double reset

      const emissionMetrics = metricsManager.getEmissionMetrics();
      expect(emissionMetrics.totalEmissions).toBe(0);
    });

    test('should handle shutdown and reinitialization', async () => {
      metricsManager.updateEmissionMetrics(1, 1, 0);

      await metricsManager.shutdown();

      // Metrics should be reset after shutdown
      const metricsAfterShutdown = metricsManager.getEmissionMetrics();
      expect(metricsAfterShutdown.totalEmissions).toBe(0);

      // Should be able to reinitialize
      await (metricsManager as any).initialize();
      expect(metricsManager['_resilientTimer']).toBeDefined();
    });
  });

  // ============================================================================
  // SECTION 8: MEMORY SAFETY AND PERFORMANCE (Lines 1001-1150)
  // AI Context: "Memory safety and performance validation"
  // ============================================================================

  describe('Memory Safety and Performance', () => {
    test('should properly initialize and shutdown', async () => {
      const testManager = new MetricsManager();

      await (testManager as any).initialize();
      expect(testManager['_resilientTimer']).toBeDefined();
      expect(testManager['_metricsCollector']).toBeDefined();

      await testManager.shutdown();

      // Verify metrics are reset after shutdown
      const metrics = testManager.getEmissionMetrics();
      expect(metrics.totalEmissions).toBe(0);
    });

    test('should handle resource cleanup correctly', async () => {
      // Setup some data
      metricsManager.updateEmissionMetrics(1, 1, 0);
      metricsManager.updatePerformanceMetrics(1, false);

      // Verify data exists
      let metrics = metricsManager.getEmissionMetrics();
      expect(metrics.totalEmissions).toBe(1);

      // Shutdown should clean up
      await metricsManager.shutdown();

      metrics = metricsManager.getEmissionMetrics();
      expect(metrics.totalEmissions).toBe(0);
    });

    test('should meet performance requirements for metrics operations', () => {
      const iterations = 1000;
      const startTime = process.hrtime.bigint();

      for (let i = 0; i < iterations; i++) {
        metricsManager.updateEmissionMetrics(1.0, 1, 0);
        metricsManager.updatePerformanceMetrics(1.0, false);
        metricsManager.incrementCounter('bufferedEvents');
      }

      const endTime = process.hrtime.bigint();
      const avgTime = Number(endTime - startTime) / iterations / 1000000; // Convert to ms

      // Should meet <1ms per operation requirement
      expect(avgTime).toBeLessThan(1);
    });

    test('should handle high-frequency metrics updates efficiently', () => {
      const startTime = Date.now();

      // Simulate high-frequency updates
      for (let i = 0; i < 10000; i++) {
        metricsManager.updateEmissionMetrics(Math.random() * 10, 1, 0);
        metricsManager.updatePerformanceMetrics(Math.random() * 5, false);
      }

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Should complete within reasonable time (less than 100ms for 10k operations)
      expect(duration).toBeLessThan(100);

      // Verify metrics are still accurate
      const metrics = metricsManager.getEmissionMetrics();
      expect(metrics.totalEmissions).toBe(10000);
    });

    test('should maintain memory efficiency with large datasets', () => {
      const initialMemory = process.memoryUsage().heapUsed;

      // Generate large amount of metrics data
      for (let i = 0; i < 50000; i++) {
        metricsManager.updateEmissionMetrics(i, 1, 0);
        metricsManager.updatePerformanceMetrics(i, false);
      }

      const finalMemory = process.memoryUsage().heapUsed;
      const memoryGrowth = finalMemory - initialMemory;

      // Memory growth should be reasonable (less than 10MB for this test)
      expect(memoryGrowth).toBeLessThan(10 * 1024 * 1024);
    });
  });

  // ============================================================================
  // SECTION 9: INTEGRATION AND CONFIGURATION TESTING (Lines 1151-1300)
  // AI Context: "Integration testing and configuration validation"
  // ============================================================================

  describe('Integration and Configuration Testing', () => {
    test('should work with disabled timing configuration', async () => {
      const disabledTimingManager = new MetricsManager({
        enableTiming: false,
        enableReporting: false
      });

      await (disabledTimingManager as any).initialize();

      // Should still function normally
      disabledTimingManager.updateEmissionMetrics(1, 1, 0);
      const metrics = disabledTimingManager.getEmissionMetrics();
      expect(metrics.totalEmissions).toBe(1);

      await disabledTimingManager.shutdown();
    });

    test('should handle custom retention and aggregation intervals', async () => {
      const customManager = new MetricsManager({
        metricsRetentionMs: 1800000, // 30 minutes
        aggregationIntervalMs: 30000, // 30 seconds
        enableReporting: true
      });

      await (customManager as any).initialize();

      expect(MockedResilientMetricsCollector).toHaveBeenCalledWith(
        expect.objectContaining({
          maxMetricsAge: 1800000
        })
      );

      await customManager.shutdown();
    });

    test('should integrate properly with MemorySafeResourceManager', async () => {
      // Verify inheritance and resource management
      expect(metricsManager).toBeInstanceOf(require('../../../MemorySafeResourceManager').MemorySafeResourceManager);

      // Should have resource management capabilities
      expect(typeof (metricsManager as any).createSafeInterval).toBe('function');
      expect(typeof (metricsManager as any).createSafeTimeout).toBe('function');
    });

    test('should provide consistent interface across all metric types', () => {
      // Test all metric interfaces are properly implemented
      const emissionMetrics = metricsManager.getEmissionMetrics();
      const performanceMetrics = metricsManager.getPerformanceMetrics();
      const comprehensiveMetrics = metricsManager.getComprehensiveMetrics();

      // Verify all expected properties exist
      expect(emissionMetrics).toHaveProperty('totalEmissions');
      expect(emissionMetrics).toHaveProperty('successfulEmissions');
      expect(emissionMetrics).toHaveProperty('failedEmissions');
      expect(emissionMetrics).toHaveProperty('averageEmissionTime');

      expect(performanceMetrics).toHaveProperty('totalHandlerExecutions');
      expect(performanceMetrics).toHaveProperty('averageHandlerExecutionTime');
      expect(performanceMetrics).toHaveProperty('maxHandlerExecutionTime');
      expect(performanceMetrics).toHaveProperty('minHandlerExecutionTime');

      expect(comprehensiveMetrics).toHaveProperty('emission');
      expect(comprehensiveMetrics).toHaveProperty('performance');
      expect(comprehensiveMetrics).toHaveProperty('resilientTiming');
    });

    test('should handle concurrent metrics updates safely', async () => {
      const promises: Promise<void>[] = [];

      // Create multiple concurrent update operations
      for (let i = 0; i < 100; i++) {
        promises.push(
          Promise.resolve().then(() => {
            metricsManager.updateEmissionMetrics(i, 1, 0);
            metricsManager.updatePerformanceMetrics(i, false);
            metricsManager.incrementCounter('bufferedEvents');
          })
        );
      }

      await Promise.all(promises);

      const emissionMetrics = metricsManager.getEmissionMetrics();
      expect(emissionMetrics.totalEmissions).toBe(100);
      expect(emissionMetrics.bufferedEvents).toBe(100);
    });

    test('should validate configuration parameters correctly', async () => {
      // Test with extreme values
      const extremeManager = new MetricsManager({
        metricsRetentionMs: 1, // Very short retention
        aggregationIntervalMs: 1, // Very short interval
        enableTiming: true,
        enableReporting: true
      });

      await (extremeManager as any).initialize();

      // Should still work with extreme values
      extremeManager.updateEmissionMetrics(1, 1, 0);
      const metrics = extremeManager.getEmissionMetrics();
      expect(metrics.totalEmissions).toBe(1);

      await extremeManager.shutdown();
    });
    test('should handle concurrent metrics updates safely', async () => {
      const promises: Promise<void>[] = [];

      // Create multiple concurrent update operations
      for (let i = 0; i < 100; i++) {
        promises.push(
          Promise.resolve().then(() => {
            metricsManager.updateEmissionMetrics(i, 1, 0);
            metricsManager.updatePerformanceMetrics(i, false);
            metricsManager.incrementCounter('bufferedEvents');
          })
        );
      }

      await Promise.all(promises);

      const emissionMetrics = metricsManager.getEmissionMetrics();
      expect(emissionMetrics.totalEmissions).toBe(100);
      expect(emissionMetrics.bufferedEvents).toBe(100);
    });

    test('should validate configuration parameters correctly', async () => {
      // Test with extreme values
      const extremeManager = new MetricsManager({
        metricsRetentionMs: 1, // Very short retention
        aggregationIntervalMs: 1, // Very short interval
        enableTiming: true,
        enableReporting: true
      });

      await (extremeManager as any).initialize();

      // Should still work with extreme values
      extremeManager.updateEmissionMetrics(1, 1, 0);
      const metrics = extremeManager.getEmissionMetrics();
      expect(metrics.totalEmissions).toBe(1);

      await extremeManager.shutdown();
    });
  });
});
