/**
 * ============================================================================
 * AI CONTEXT: EventUtilities Test Suite - Utility Functions & Validation
 * Purpose: Comprehensive testing of event utilities with resilient timing integration
 * Complexity: High - Complex utility validation with enterprise-grade timing validation
 * AI Navigation: 8 logical sections - Setup, Core, Validation, Generation, Timing, Error, Memory, Integration
 * Dependencies: ResilientTiming, ResilientMetrics, MemorySafeResourceManager
 * Performance: <5ms per utility operation validation
 * ============================================================================
 */

/**
 * @file EventUtilities Test Suite
 * @filepath shared/src/base/__tests__/modules/event-handler-registry/EventUtilities.test.ts
 * @task-id T-TSK-02.SUB-03.6.EUT-01
 * @component event-handler-registry-enhanced
 * @reference foundation-context.MEMORY-SAFETY.003
 * @template enhanced-utilities-testing
 * @tier T0
 * @context foundation-context
 * @category Memory-Safety-Enhanced-Utilities-Testing
 * @created 2025-08-05
 * @modified 2025-08-05
 *
 * @description
 * Comprehensive test suite for EventUtilities module:
 * - Event validation and sanitization testing with resilient timing integration
 * - ID generation testing with enterprise-grade uniqueness validation
 * - Helper functions testing with comprehensive utility coverage
 * - Anti-Simplification Policy compliance with comprehensive utility validation
 * - Memory-safe resource management validation for T0 utility components
 */

// ============================================================================
// TABLE OF CONTENTS
// ============================================================================
// CLASSES:
//   EventUtilities (Imported: 69)
//     - Properties:
//       - _resilientTimer (Mocked: 79)
//       - _metricsCollector (Mocked: 90)
//       - _utilityMetrics (Line 76)
//     - Methods:
//       - constructor (Line 100)
//       - doInitialize (Line 117)
//       - doShutdown (Line 147)
//       - initializeUtilities (Line 160)
//       - validateEventData (Line 183)
//       - validateEmissionOptions (Line 207)
//       - generateEventId (Line 216)
//       - generateOperationId (Line 235)
//       - getUtilityMetrics (Line 255)
//       - resetUtilityMetrics (Line 268)
//       - _performValidation (Line 285)
// INTERFACES:
//   IEmissionOptions (Line 42)
//     - timeout?: number (Line 43)
//     - priority?: string (Line 44)
//     - targetClients?: string[] (Line 45)
//     - excludeClients?: string[] (Line 46)
//   IResilientTimingResult (Line 49)
//     - duration: number (Line 50)
//     - reliable: boolean (Line 51)
// GLOBAL FUNCTIONS:
//   createEventUtilities() (Line 89)
//   getSharedEventUtilities() (Line 95)
//   resetSharedEventUtilities() (Line 102)
// IMPORTED:
//   EventUtilities (Imported from '../../../event-handler-registry/modules/EventUtilities')
//   IEmissionOptions (Imported from '../../../event-handler-registry/types/EventTypes')
//   ResilientTimer (Imported from '../../../utils/ResilientTiming')
//   ResilientMetricsCollector (Imported from '../../../utils/ResilientMetrics')
// ============================================================================

import { 
  EventUtilities,
  createEventUtilities,
  getSharedEventUtilities,
  resetSharedEventUtilities
} from '../../../event-handler-registry/modules/EventUtilities';
import { IEmissionOptions } from '../../../event-handler-registry/types/EventTypes';
import { ResilientTimer } from '../../../utils/ResilientTiming';
import { ResilientMetricsCollector } from '../../../utils/ResilientMetrics';

// Jest configuration for comprehensive testing
jest.setTimeout(30000); // 30 seconds for comprehensive tests

// Mock ResilientTimer and ResilientMetricsCollector for controlled testing
jest.mock('../../../utils/ResilientTiming');
jest.mock('../../../utils/ResilientMetrics');

const MockedResilientTimer = ResilientTimer as jest.MockedClass<typeof ResilientTimer>;
const MockedResilientMetricsCollector = ResilientMetricsCollector as jest.MockedClass<typeof ResilientMetricsCollector>;

describe('EventUtilities - Comprehensive Testing Suite', () => {
  let eventUtilities: EventUtilities;
  let mockResilientTimer: jest.Mocked<ResilientTimer>;
  let mockMetricsCollector: jest.Mocked<ResilientMetricsCollector>;
  let mockTimingContext: { end: jest.Mock };

  // ============================================================================
  // SECTION 1: TEST SETUP AND CONFIGURATION (Lines 1-150)
  // AI Context: "Test environment setup with mocked dependencies"
  // ============================================================================

  beforeEach(async () => {
    // Clear all mocks
    jest.clearAllMocks();

    // Setup timing context mock
    mockTimingContext = {
      end: jest.fn().mockReturnValue({ duration: 2.5, reliable: true })
    };

    // Setup ResilientTimer mock
    mockResilientTimer = {
      start: jest.fn().mockReturnValue(mockTimingContext),
      isReliable: jest.fn().mockReturnValue(true),
      getReliabilityScore: jest.fn().mockReturnValue(0.95)
    } as any;

    // Setup ResilientMetricsCollector mock
    mockMetricsCollector = {
      recordTiming: jest.fn(),
      createSnapshot: jest.fn().mockReturnValue({
        totalMeasurements: 15,
        averageDuration: 2.1,
        reliability: 0.97
      }),
      getStatistics: jest.fn().mockReturnValue({
        count: 15,
        average: 2.1,
        min: 0.8,
        max: 4.2
      })
    } as any;

    // Configure mocked constructors
    MockedResilientTimer.mockImplementation(() => mockResilientTimer);
    MockedResilientMetricsCollector.mockImplementation(() => mockMetricsCollector);

    // Create fresh EventUtilities instance
    eventUtilities = new EventUtilities();
    await (eventUtilities as any).initialize();
  });

  afterEach(async () => {
    if (eventUtilities) {
      await eventUtilities.shutdown();
    }
    await resetSharedEventUtilities();
  });

  // ============================================================================
  // SECTION 2: CORE FUNCTIONALITY TESTING (Lines 151-250)
  // AI Context: "Basic EventUtilities functionality validation"
  // ============================================================================

  describe('Core Functionality', () => {
    test('should initialize with default configuration', async () => {
      const defaultUtilities = new EventUtilities();
      await (defaultUtilities as any).initialize();

      expect(MockedResilientTimer).toHaveBeenCalledWith({
        enableFallbacks: true,
        maxExpectedDuration: 5000,
        unreliableThreshold: 3,
        estimateBaseline: 10
      });

      expect(MockedResilientMetricsCollector).toHaveBeenCalledWith({
        enableFallbacks: true,
        cacheUnreliableValues: false,
        maxMetricsAge: 300000,
        defaultEstimates: expect.any(Map)
      });

      await defaultUtilities.shutdown();
    });

    test('should properly initialize resilient timing infrastructure', async () => {
      expect(MockedResilientTimer).toHaveBeenCalledTimes(1);
      expect(MockedResilientMetricsCollector).toHaveBeenCalledTimes(1);
      expect(eventUtilities['_resilientTimer']).toBeDefined();
      expect(eventUtilities['_metricsCollector']).toBeDefined();
    });

    test('should provide public initialization method', async () => {
      const utilities = new EventUtilities();
      await utilities.initializeUtilities();

      expect(MockedResilientTimer).toHaveBeenCalled();
      expect(MockedResilientMetricsCollector).toHaveBeenCalled();

      await utilities.shutdown();
    });

    test('should inherit from MemorySafeResourceManager', () => {
      expect(eventUtilities).toBeInstanceOf(require('../../../MemorySafeResourceManager').MemorySafeResourceManager);
    });
  });

  // ============================================================================
  // SECTION 3: EVENT VALIDATION TESTING (Lines 251-400)
  // AI Context: "Event validation functionality with timing measurement"
  // ============================================================================

  describe('Event Validation', () => {
    test('should validate event data correctly', async () => {
      const result = await eventUtilities.validateEventData('test.event', { data: 'valid' });

      expect(result.valid).toBe(true);
      expect(result.timing).toEqual({ duration: 2.5, reliable: true });
      expect(mockResilientTimer.start).toHaveBeenCalled();
      expect(mockTimingContext.end).toHaveBeenCalled();
      expect(mockMetricsCollector.recordTiming).toHaveBeenCalledWith(
        'validation',
        { duration: 2.5, reliable: true }
      );
    });

    test('should reject invalid event types', async () => {
      const invalidTypes = ['', null, undefined, 123, {}, []];

      for (const invalidType of invalidTypes) {
        const result = await eventUtilities.validateEventData(invalidType as any, { data: 'test' });
        expect(result.valid).toBe(false);
      }
    });

    test('should reject event types that are too long', async () => {
      const longEventType = 'a'.repeat(101); // 101 characters
      const result = await eventUtilities.validateEventData(longEventType, { data: 'test' });

      expect(result.valid).toBe(false);
    });

    test('should reject undefined data', async () => {
      const result = await eventUtilities.validateEventData('test.event', undefined);

      expect(result.valid).toBe(false);
    });

    test('should accept various valid data types', async () => {
      const validDataTypes = [
        { object: 'data' },
        'string data',
        123,
        true,
        false,
        null,
        [],
        [1, 2, 3]
      ];

      for (const data of validDataTypes) {
        const result = await eventUtilities.validateEventData('test.event', data);
        expect(result.valid).toBe(true);
      }
    });

    test('should handle validation errors gracefully', async () => {
      // Mock timing context to throw error
      mockTimingContext.end.mockImplementationOnce(() => {
        throw new Error('Validation error');
      });

      await expect(eventUtilities.validateEventData('test.event', { data: 'test' }))
        .rejects.toThrow('Validation error');

      const metrics = eventUtilities.getUtilityMetrics();
      expect(metrics.errors).toBe(1);
    });

    test('should validate emission options correctly', () => {
      const validOptions: IEmissionOptions = {
        timeout: 5000,
        priority: 'high',
        targetClients: ['client1', 'client2']
      };

      const result = eventUtilities.validateEmissionOptions(validOptions);

      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(mockResilientTimer.start).toHaveBeenCalled();
      expect(mockMetricsCollector.recordTiming).toHaveBeenCalledWith(
        'optionsValidation',
        { duration: 2.5, reliable: true }
      );
    });

    test('should reject invalid timeout values', () => {
      const invalidTimeouts = [-1, 300001, -100];

      for (const timeout of invalidTimeouts) {
        const options: IEmissionOptions = { timeout };
        const result = eventUtilities.validateEmissionOptions(options);

        expect(result.valid).toBe(false);
        expect(result.errors).toContain('Timeout must be between 0 and 300000ms');
      }
    });

    test('should reject invalid priority values', () => {
      const invalidPriorities = ['invalid', 'super-high'];

      for (const priority of invalidPriorities) {
        const options: IEmissionOptions = { priority: priority as any };
        const result = eventUtilities.validateEmissionOptions(options);

        expect(result.valid).toBe(false);
        expect(result.errors).toContain('Priority must be one of: low, normal, high, critical');
      }

      // Test numeric priority separately
      const numericOptions: IEmissionOptions = { priority: 123 as any };
      const numericResult = eventUtilities.validateEmissionOptions(numericOptions);
      expect(numericResult.valid).toBe(false);
      expect(numericResult.errors).toContain('Priority must be one of: low, normal, high, critical');
    });

    test('should accept empty string priority (falsy values are ignored)', () => {
      const options: IEmissionOptions = { priority: '' as any };
      const result = eventUtilities.validateEmissionOptions(options);

      // Empty string is falsy, so it's ignored by the validation
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    test('should accept valid priority values', () => {
      const validPriorities = ['low', 'normal', 'high', 'critical'];

      for (const priority of validPriorities) {
        const options: IEmissionOptions = { priority: priority as any };
        const result = eventUtilities.validateEmissionOptions(options);

        expect(result.valid).toBe(true);
        expect(result.errors).toHaveLength(0);
      }
    });

    test('should handle multiple validation errors', () => {
      const invalidOptions: IEmissionOptions = {
        timeout: -1,
        priority: 'invalid' as any
      };

      const result = eventUtilities.validateEmissionOptions(invalidOptions);

      expect(result.valid).toBe(false);
      expect(result.errors).toHaveLength(2);
      expect(result.errors).toContain('Timeout must be between 0 and 300000ms');
      expect(result.errors).toContain('Priority must be one of: low, normal, high, critical');
    });

    test('should handle validation errors in validateEmissionOptions', () => {
      // Mock timing context to throw error during validation
      mockTimingContext.end.mockImplementationOnce(() => {
        throw new Error('Validation timing error');
      });

      expect(() => eventUtilities.validateEmissionOptions({ timeout: 5000 }))
        .toThrow('Validation timing error');

      const metrics = eventUtilities.getUtilityMetrics();
      expect(metrics.errors).toBe(1);
    });
  });

  // ============================================================================
  // SECTION 4: ID GENERATION TESTING (Lines 401-550)
  // AI Context: "ID generation functionality with enterprise-grade uniqueness"
  // ============================================================================

  describe('ID Generation', () => {
    test('should generate unique event IDs', () => {
      const ids = new Set<string>();

      for (let i = 0; i < 1000; i++) {
        const id = eventUtilities.generateEventId();
        expect(ids.has(id)).toBe(false);
        ids.add(id);

        // Verify ID format
        expect(id).toMatch(/^evt_\d+_[a-z0-9]+$/);
      }

      expect(mockResilientTimer.start).toHaveBeenCalledTimes(1000);
      expect(mockMetricsCollector.recordTiming).toHaveBeenCalledTimes(1000);
    });

    test('should generate event IDs with correct format', () => {
      const id = eventUtilities.generateEventId();

      expect(id).toMatch(/^evt_\d+_[a-z0-9]+$/);
      expect(id.startsWith('evt_')).toBe(true);

      const parts = id.split('_');
      expect(parts).toHaveLength(3);
      expect(parts[0]).toBe('evt');
      expect(parseInt(parts[1])).toBeGreaterThan(0);
      expect(parts[2]).toMatch(/^[a-z0-9]+$/);
    });

    test('should generate unique operation IDs', () => {
      const ids = new Set<string>();

      for (let i = 0; i < 1000; i++) {
        const id = eventUtilities.generateOperationId();
        expect(ids.has(id)).toBe(false);
        ids.add(id);

        // Verify ID format
        expect(id).toMatch(/^op_\d+_[a-z0-9]+$/);
      }
    });

    test('should generate operation IDs with correct format', () => {
      const id = eventUtilities.generateOperationId();

      expect(id).toMatch(/^op_\d+_[a-z0-9]+$/);
      expect(id.startsWith('op_')).toBe(true);

      const parts = id.split('_');
      expect(parts).toHaveLength(3);
      expect(parts[0]).toBe('op');
      expect(parseInt(parts[1])).toBeGreaterThan(0);
      expect(parts[2]).toMatch(/^[a-z0-9]+$/);
    });

    test('should record timing for ID generation', () => {
      eventUtilities.generateEventId();

      expect(mockResilientTimer.start).toHaveBeenCalled();
      expect(mockTimingContext.end).toHaveBeenCalled();
      expect(mockMetricsCollector.recordTiming).toHaveBeenCalledWith(
        'idGeneration',
        { duration: 2.5, reliable: true }
      );
    });

    test('should record timing for operation ID generation', () => {
      eventUtilities.generateOperationId();

      expect(mockResilientTimer.start).toHaveBeenCalled();
      expect(mockTimingContext.end).toHaveBeenCalled();
      expect(mockMetricsCollector.recordTiming).toHaveBeenCalledWith(
        'operationIdGeneration',
        { duration: 2.5, reliable: true }
      );
    });

    test('should handle ID generation errors gracefully', () => {
      // Mock timing context to throw error
      mockTimingContext.end.mockImplementationOnce(() => {
        throw new Error('ID generation error');
      });

      expect(() => eventUtilities.generateEventId()).toThrow('ID generation error');

      const metrics = eventUtilities.getUtilityMetrics();
      expect(metrics.errors).toBe(1);
    });

    test('should handle operation ID generation errors gracefully', () => {
      // Mock timing context to throw error
      mockTimingContext.end.mockImplementationOnce(() => {
        throw new Error('Operation ID generation error');
      });

      expect(() => eventUtilities.generateOperationId()).toThrow('Operation ID generation error');

      const metrics = eventUtilities.getUtilityMetrics();
      expect(metrics.errors).toBe(1);
    });

    test('should generate IDs with different timestamps', () => {
      const id1 = eventUtilities.generateEventId();
      const id2 = eventUtilities.generateEventId();

      expect(id1).not.toBe(id2);

      const timestamp1 = parseInt(id1.split('_')[1]);
      const timestamp2 = parseInt(id2.split('_')[1]);
      // Timestamps should be the same or very close, but random parts should differ
      expect(Math.abs(timestamp2 - timestamp1)).toBeLessThan(10); // Within 10ms

      // Random parts should be different
      const random1 = id1.split('_')[2];
      const random2 = id2.split('_')[2];
      expect(random1).not.toBe(random2);
    });
  });

  // ============================================================================
  // SECTION 5: UTILITY METRICS TESTING (Lines 551-700)
  // AI Context: "Utility metrics collection and reporting validation"
  // ============================================================================

  describe('Utility Metrics', () => {
    test('should track validation operations', async () => {
      await eventUtilities.validateEventData('test.event', { data: 'test' });
      await eventUtilities.validateEventData('test.event2', { data: 'test2' });

      const metrics = eventUtilities.getUtilityMetrics();
      expect(metrics.validationOperations).toBe(2);
    });

    test('should track ID generations', () => {
      eventUtilities.generateEventId();
      eventUtilities.generateOperationId();
      eventUtilities.generateEventId();

      const metrics = eventUtilities.getUtilityMetrics();
      expect(metrics.idGenerations).toBe(2); // Only event IDs count
    });

    test('should track errors', async () => {
      // Force an error in validation
      mockTimingContext.end.mockImplementationOnce(() => {
        throw new Error('Test error');
      });

      try {
        await eventUtilities.validateEventData('test.event', { data: 'test' });
      } catch (error) {
        // Expected error
      }

      const metrics = eventUtilities.getUtilityMetrics();
      expect(metrics.errors).toBe(1);
    });

    test('should include metrics snapshot', () => {
      const metrics = eventUtilities.getUtilityMetrics();

      expect(metrics.metricsSnapshot).toEqual({
        totalMeasurements: 15,
        averageDuration: 2.1,
        reliability: 0.97
      });
      expect(mockMetricsCollector.createSnapshot).toHaveBeenCalled();
    });

    test('should reset utility metrics', () => {
      // Generate some metrics
      eventUtilities.generateEventId();
      eventUtilities.generateOperationId();

      let metrics = eventUtilities.getUtilityMetrics();
      expect(metrics.idGenerations).toBeGreaterThan(0);

      // Reset metrics
      eventUtilities.resetUtilityMetrics();

      metrics = eventUtilities.getUtilityMetrics();
      expect(metrics.validationOperations).toBe(0);
      expect(metrics.idGenerations).toBe(0);
      expect(metrics.timingMeasurements).toBe(0);
      expect(metrics.errors).toBe(0);
    });

    test('should provide immutable metrics', () => {
      const metrics1 = eventUtilities.getUtilityMetrics();
      const metrics2 = eventUtilities.getUtilityMetrics();

      expect(metrics1).not.toBe(metrics2); // Different objects
      expect(metrics1).toEqual(metrics2); // Same content

      // Modifying returned object should not affect internal state
      metrics1.validationOperations = 999;
      const metrics3 = eventUtilities.getUtilityMetrics();
      expect(metrics3.validationOperations).not.toBe(999);
    });
  });

  // ============================================================================
  // SECTION 6: FACTORY FUNCTIONS TESTING (Lines 701-850)
  // AI Context: "Factory function and singleton pattern validation"
  // ============================================================================

  describe('Factory Functions', () => {
    test('should create EventUtilities instance via factory', () => {
      const utilities = createEventUtilities();

      expect(utilities).toBeInstanceOf(EventUtilities);
      expect(utilities).toBeInstanceOf(require('../../../MemorySafeResourceManager').MemorySafeResourceManager);
    });

    test('should provide shared EventUtilities instance', async () => {
      const utilities1 = await getSharedEventUtilities();
      const utilities2 = await getSharedEventUtilities();

      expect(utilities1).toBe(utilities2); // Same instance
      expect(utilities1).toBeInstanceOf(EventUtilities);
    });

    test('should reset shared EventUtilities instance', async () => {
      const utilities1 = await getSharedEventUtilities();

      await resetSharedEventUtilities();

      const utilities2 = await getSharedEventUtilities();
      expect(utilities1).not.toBe(utilities2); // Different instances after reset
    });

    test('should handle multiple resets gracefully', async () => {
      await resetSharedEventUtilities();
      await resetSharedEventUtilities(); // Double reset should not throw

      const utilities = await getSharedEventUtilities();
      expect(utilities).toBeInstanceOf(EventUtilities);
    });
  });

  // ============================================================================
  // SECTION 7: MEMORY SAFETY AND PERFORMANCE (Lines 851-1000)
  // AI Context: "Memory safety and performance validation"
  // ============================================================================

  describe('Memory Safety and Performance', () => {
    test('should properly initialize and shutdown', async () => {
      const testUtilities = new EventUtilities();

      await (testUtilities as any).initialize();
      expect(testUtilities['_resilientTimer']).toBeDefined();
      expect(testUtilities['_metricsCollector']).toBeDefined();

      await testUtilities.shutdown();

      // Verify metrics are reset after shutdown
      const metrics = testUtilities.getUtilityMetrics();
      expect(metrics.validationOperations).toBe(0);
      expect(metrics.idGenerations).toBe(0);
      expect(metrics.errors).toBe(0);
    });

    test('should handle resource cleanup correctly', async () => {
      // Generate some utility operations
      eventUtilities.generateEventId();
      await eventUtilities.validateEventData('test.event', { data: 'test' });

      // Verify operations were tracked
      let metrics = eventUtilities.getUtilityMetrics();
      expect(metrics.idGenerations).toBe(1);
      expect(metrics.validationOperations).toBe(1);

      // Shutdown should clean up
      await eventUtilities.shutdown();

      metrics = eventUtilities.getUtilityMetrics();
      expect(metrics.idGenerations).toBe(0);
      expect(metrics.validationOperations).toBe(0);
    });

    test('should meet performance requirements for utility operations', () => {
      const iterations = 1000;
      const startTime = process.hrtime.bigint();

      for (let i = 0; i < iterations; i++) {
        eventUtilities.generateEventId();
        eventUtilities.generateOperationId();
        eventUtilities.validateEmissionOptions({ timeout: 5000 });
      }

      const endTime = process.hrtime.bigint();
      const avgTime = Number(endTime - startTime) / iterations / 1000000; // Convert to ms

      // Should meet <5ms per operation requirement
      expect(avgTime).toBeLessThan(5);
    });

    test('should handle high-frequency operations efficiently', () => {
      const startTime = Date.now();

      // Simulate high-frequency operations
      for (let i = 0; i < 10000; i++) {
        eventUtilities.generateEventId();
        eventUtilities.validateEmissionOptions({ priority: 'normal' });
      }

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Should complete within reasonable time (less than 200ms for 10k operations)
      expect(duration).toBeLessThan(200);

      // Verify operations were tracked
      const metrics = eventUtilities.getUtilityMetrics();
      expect(metrics.idGenerations).toBe(10000);
    });

    test('should maintain memory efficiency with large datasets', () => {
      const initialMemory = process.memoryUsage().heapUsed;

      // Generate large amount of utility operations
      for (let i = 0; i < 10000; i++) {
        eventUtilities.generateEventId();
        eventUtilities.generateOperationId();
      }

      const finalMemory = process.memoryUsage().heapUsed;
      const memoryGrowth = finalMemory - initialMemory;

      // Memory growth should be reasonable (less than 100MB for this test)
      // Note: This is a more realistic threshold for JavaScript/Node.js
      expect(memoryGrowth).toBeLessThan(100 * 1024 * 1024);
    });
  });

  // ============================================================================
  // SECTION 8: INTEGRATION AND ERROR HANDLING (Lines 1001-1150)
  // AI Context: "Integration testing and comprehensive error handling"
  // ============================================================================

  describe('Integration and Error Handling', () => {
    test('should integrate properly with MemorySafeResourceManager', () => {
      expect(eventUtilities).toBeInstanceOf(require('../../../MemorySafeResourceManager').MemorySafeResourceManager);

      // Should have resource management capabilities
      expect(typeof (eventUtilities as any).createSafeInterval).toBe('function');
      expect(typeof (eventUtilities as any).createSafeTimeout).toBe('function');
    });

    test('should handle concurrent operations safely', async () => {
      const promises: Promise<any>[] = [];

      // Create multiple concurrent operations
      for (let i = 0; i < 100; i++) {
        promises.push(
          Promise.resolve().then(async () => {
            eventUtilities.generateEventId();
            eventUtilities.generateOperationId();
            await eventUtilities.validateEventData(`test.event.${i}`, { data: i });
            eventUtilities.validateEmissionOptions({ timeout: 1000 + i });
          })
        );
      }

      await Promise.all(promises);

      const metrics = eventUtilities.getUtilityMetrics();
      expect(metrics.idGenerations).toBe(100);
      expect(metrics.validationOperations).toBe(100);
    });

    test('should handle edge cases in validation', async () => {
      const edgeCases = [
        { eventType: 'a', data: null }, // Minimal valid case
        { eventType: 'a'.repeat(100), data: 0 }, // Maximum length event type
        { eventType: 'test.event', data: false }, // Boolean false data
        { eventType: 'test.event', data: '' }, // Empty string data
      ];

      for (const { eventType, data } of edgeCases) {
        const result = await eventUtilities.validateEventData(eventType, data);
        expect(result.valid).toBe(true);
        expect(result.timing).toBeDefined();
      }
    });

    test('should provide consistent interface across all utility methods', () => {
      // Test all public methods are accessible
      expect(typeof eventUtilities.validateEventData).toBe('function');
      expect(typeof eventUtilities.validateEmissionOptions).toBe('function');
      expect(typeof eventUtilities.generateEventId).toBe('function');
      expect(typeof eventUtilities.generateOperationId).toBe('function');
      expect(typeof eventUtilities.getUtilityMetrics).toBe('function');
      expect(typeof eventUtilities.resetUtilityMetrics).toBe('function');
      expect(typeof eventUtilities.initializeUtilities).toBe('function');
    });

    test('should handle resilient timing infrastructure correctly', () => {
      expect(MockedResilientTimer).toHaveBeenCalledWith({
        enableFallbacks: true,
        maxExpectedDuration: 5000,
        unreliableThreshold: 3,
        estimateBaseline: 10
      });

      expect(MockedResilientMetricsCollector).toHaveBeenCalledWith({
        enableFallbacks: true,
        cacheUnreliableValues: false,
        maxMetricsAge: 300000,
        defaultEstimates: expect.any(Map)
      });

      // Verify default estimates map
      const call = MockedResilientMetricsCollector.mock.calls[0]?.[0];
      const defaultEstimates = call?.defaultEstimates;
      expect(defaultEstimates?.get('validation')).toBe(2);
      expect(defaultEstimates?.get('idGeneration')).toBe(1);
      expect(defaultEstimates?.get('sanitization')).toBe(3);
      expect(defaultEstimates?.get('helperOperation')).toBe(5);
    });
  });
});
