/**
 * ============================================================================
 * AI CONTEXT: Performance Validation Tests - Enterprise Performance Requirements
 * Purpose: Validate <100ms template execution and comprehensive performance metrics
 * Complexity: Complex - Multi-dimensional performance testing with benchmarks
 * AI Navigation: 4 logical sections, 2 major domains (Template Performance, System Performance)
 * ============================================================================
 */

/**
 * Document Type: Performance Test Suite
 * Version: 1.0.0
 * Created: 2025-07-25
 * Authority: President & CEO, E.Z. Consultancy
 * Classification: Quality Assurance - Performance Validation
 */

// ============================================================================
// SECTION 1: IMPORTS & DEPENDENCIES (Lines 1-40)
// AI Context: "Performance testing dependencies and utilities"
// ============================================================================

import { performance } from 'perf_hooks';
import { CleanupTemplateManager } from '../../../modules/cleanup/CleanupTemplateManager';
import { TemplateWorkflowExecutor } from '../../../modules/cleanup/TemplateWorkflows';
import { JestCompatibilityUtils } from '../../../utils/JestCompatibilityUtils';
import {
  ICleanupTemplate,
  ICleanupTemplateStep,
  IEnhancedCleanupConfig,
  IComponentRegistry,
  IRetryPolicy
} from '../../../types/CleanupTypes';
import { CleanupOperationType, CleanupPriority } from '../../../CleanupCoordinator';

// ============================================================================
// SECTION 2: TEST CONFIGURATION & SETUP (Lines 41-120)
// AI Context: "Performance test configuration and mock setup"
// ============================================================================

/**
 * Performance requirements from compliance review
 */
const PERFORMANCE_REQUIREMENTS = {
  TEMPLATE_EXECUTION_MAX_MS: 100,
  TEST_SUITE_TARGET_MS: 2600, // 2.5-2.6 seconds
  POOL_OPERATION_MAX_MS: 5,
  SCHEDULING_MAX_MS: 10,
  SYNCHRONIZATION_MAX_MS: 20,
  MEMORY_OVERHEAD_MAX_PERCENT: 2
};

/**
 * Performance test configuration
 */
const PERFORMANCE_TEST_CONFIG: Partial<IEnhancedCleanupConfig> = {
  templateValidationEnabled: true,
  testMode: true,
  maxRetries: 1,
  defaultTimeout: 3000
};

/**
 * Create mock component registry for performance testing
 */
function createPerformanceComponentRegistry(): IComponentRegistry {
  // CRITICAL FIX: Create sufficient components for concurrent testing
  // LESSON LEARNED: Mock component registry must have enough components to satisfy
  // all concurrent execution requests to prevent step failures
  const availableComponents = Array.from({ length: 10 }, (_, i) => `test-component-${i + 1}`);

  return {
    findComponents: jest.fn().mockResolvedValue(availableComponents),
    getCleanupOperation: jest.fn().mockReturnValue(undefined),
    registerOperation: jest.fn().mockReturnValue(true),
    hasOperation: jest.fn().mockReturnValue(false),
    listOperations: jest.fn().mockReturnValue([]),
    getOperationMetrics: jest.fn().mockReturnValue({
      totalOperations: 0,
      executionCount: 0,
      averageExecutionTime: 0
    })
  };
}

/**
 * Create performance test template
 */
function createPerformanceTestTemplate(): ICleanupTemplate {
  const steps: ICleanupTemplateStep[] = [];
  
  // Create multiple steps for comprehensive testing
  for (let i = 1; i <= 10; i++) {
    const retryPolicy: IRetryPolicy = {
      maxRetries: 2,
      retryDelay: 100,
      backoffMultiplier: 2,
      maxRetryDelay: 1000,
      retryOnErrors: ['TIMEOUT']
    };

    steps.push({
      id: `step-${i}`,
      type: CleanupOperationType.RESOURCE_CLEANUP,
      componentPattern: 'test-.*',
      operationName: 'performance-cleanup',
      parameters: { testParam: `value-${i}` },
      timeout: 3000,
      retryPolicy,
      dependsOn: i > 1 ? [`step-${i-1}`] : [],
      priority: CleanupPriority.NORMAL,
      estimatedDuration: 50,
      description: `Performance Test Step ${i}`,
      rollbackOperation: 'restore'
    });
  }

  return {
    id: 'performance-test-template',
    name: 'Performance Test Template',
    description: 'Template for performance validation testing',
    version: '1.0.0',
    operations: steps,
    conditions: [],
    rollbackSteps: [],
    metadata: {
      category: 'performance-test',
      estimatedDuration: 500
    },
    tags: ['performance', 'test'],
    createdAt: new Date(),
    modifiedAt: new Date(),
    author: 'performance-test-suite',
    validationRules: []
  };
}

// ============================================================================
// SECTION 3: PERFORMANCE TEST SUITE (Lines 121-250)
// AI Context: "Core performance validation tests"
// ============================================================================

describe('Performance Validation Tests', () => {
  let templateManager: CleanupTemplateManager;
  let mockComponentRegistry: IComponentRegistry;
  let performanceMetrics: Map<string, number>;

  beforeAll(() => {
    // Configure Jest compatibility for performance testing
    JestCompatibilityUtils.configure({
      forceTestMode: true,
      maxDelaySteps: 10,
      baseStepDuration: 1
    });

    JestCompatibilityUtils.configurePerformance({
      testModeMultiplier: 0.01, // Very fast for performance tests
      productionModeMultiplier: 1.0,
      maxExecutionTime: PERFORMANCE_REQUIREMENTS.TEMPLATE_EXECUTION_MAX_MS
    });
  });

  beforeEach(async () => {
    mockComponentRegistry = createPerformanceComponentRegistry();
    templateManager = new CleanupTemplateManager(PERFORMANCE_TEST_CONFIG, mockComponentRegistry);
    performanceMetrics = new Map();
    
    // Register performance test template
    const template = createPerformanceTestTemplate();
    await templateManager.registerTemplate(template);
  });

  afterEach(async () => {
    await templateManager.shutdown();
  });

  describe('Template Execution Performance', () => {
    it('should execute template within 100ms requirement', async () => {
      const startTime = performance.now();
      
      const result = await templateManager.executeTemplate(
        'performance-test-template',
        ['test-component-1', 'test-component-2'],
        {}
      );
      
      const executionTime = performance.now() - startTime;
      performanceMetrics.set('template-execution', executionTime);

      expect(result.status).toBe('success');
      expect(executionTime).toBeLessThan(PERFORMANCE_REQUIREMENTS.TEMPLATE_EXECUTION_MAX_MS);
      
      console.log(`Template execution time: ${executionTime.toFixed(2)}ms (requirement: <${PERFORMANCE_REQUIREMENTS.TEMPLATE_EXECUTION_MAX_MS}ms)`);
    });

    it('should maintain performance with multiple concurrent executions', async () => {
      const concurrentExecutions = 5;
      const startTime = performance.now();

      // LESSON LEARNED: Ensure sufficient mock components for concurrent testing

      const promises = Array.from({ length: concurrentExecutions }, (_, i) =>
        templateManager.executeTemplate(
          'performance-test-template',
          [`test-component-${i + 1}`],
          { executionId: `concurrent-${i}` }
        )
      );
      
      const results = await Promise.all(promises);
      const totalTime = performance.now() - startTime;
      const averageTime = totalTime / concurrentExecutions;

      performanceMetrics.set('concurrent-execution-average', averageTime);
      performanceMetrics.set('concurrent-execution-total', totalTime);

      // LESSON LEARNED: Validate all concurrent executions succeed
      const failedResults = results.filter(r => r.status !== 'success');
      if (failedResults.length > 0) {
        // Log failure details for debugging if needed
        console.warn(`${failedResults.length} concurrent executions failed:`,
          failedResults.map(r => ({ status: r.status, errors: r.errors?.length || 0 })));
      }

      // All executions should succeed
      expect(results.every(r => r.status === 'success')).toBe(true);
      
      // Average execution time should still be within limits
      expect(averageTime).toBeLessThan(PERFORMANCE_REQUIREMENTS.TEMPLATE_EXECUTION_MAX_MS);
      
      console.log(`Concurrent execution average: ${averageTime.toFixed(2)}ms, total: ${totalTime.toFixed(2)}ms`);
    });

    it('should validate memory overhead stays within 2% limit', async () => {
      const initialMemory = process.memoryUsage().heapUsed;
      
      // Execute multiple templates to test memory usage
      for (let i = 0; i < 10; i++) {
        await templateManager.executeTemplate(
          'performance-test-template',
          ['test-component-1'],
          { executionId: `memory-test-${i}` }
        );
      }
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }
      
      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;
      const memoryIncreasePercent = (memoryIncrease / initialMemory) * 100;
      
      performanceMetrics.set('memory-overhead-percent', memoryIncreasePercent);
      
      expect(memoryIncreasePercent).toBeLessThan(PERFORMANCE_REQUIREMENTS.MEMORY_OVERHEAD_MAX_PERCENT);
      
      console.log(`Memory overhead: ${memoryIncreasePercent.toFixed(2)}% (requirement: <${PERFORMANCE_REQUIREMENTS.MEMORY_OVERHEAD_MAX_PERCENT}%)`);
    });
  });

  describe('System Performance Metrics', () => {
    it('should validate Jest compatibility performance', async () => {
      const startTime = performance.now();
      
      // Test Jest-compatible operations
      for (let i = 0; i < 100; i++) {
        await JestCompatibilityUtils.compatibleDelay(10);
      }
      
      const jestCompatibilityTime = performance.now() - startTime;
      performanceMetrics.set('jest-compatibility-overhead', jestCompatibilityTime);
      
      // Jest compatibility should not add significant overhead
      expect(jestCompatibilityTime).toBeLessThan(500); // 500ms for 100 operations
      
      console.log(`Jest compatibility overhead: ${jestCompatibilityTime.toFixed(2)}ms for 100 operations`);
    });

    it('should validate batched async yield performance', async () => {
      const startTime = performance.now();
      
      // Test batched async yields
      await JestCompatibilityUtils.batchedAsyncYield(100, 5);
      
      const batchedYieldTime = performance.now() - startTime;
      performanceMetrics.set('batched-yield-time', batchedYieldTime);
      
      // Batched yields should be very fast
      expect(batchedYieldTime).toBeLessThan(50); // 50ms for 100 operations in batches of 5
      
      console.log(`Batched async yield time: ${batchedYieldTime.toFixed(2)}ms for 100 operations`);
    });
  });

  describe('Performance Regression Detection', () => {
    it('should detect performance regressions in template execution', async () => {
      const executionTimes: number[] = [];
      
      // Run multiple executions to get baseline
      for (let i = 0; i < 5; i++) {
        const startTime = performance.now();
        
        await templateManager.executeTemplate(
          'performance-test-template',
          ['test-component-1'],
          { executionId: `regression-test-${i}` }
        );
        
        executionTimes.push(performance.now() - startTime);
      }
      
      const averageTime = executionTimes.reduce((sum, time) => sum + time, 0) / executionTimes.length;
      const maxTime = Math.max(...executionTimes);
      const minTime = Math.min(...executionTimes);
      const variance = executionTimes.reduce((sum, time) => sum + Math.pow(time - averageTime, 2), 0) / executionTimes.length;
      
      performanceMetrics.set('execution-average', averageTime);
      performanceMetrics.set('execution-max', maxTime);
      performanceMetrics.set('execution-min', minTime);
      performanceMetrics.set('execution-variance', variance);
      
      // Performance should be consistent
      expect(maxTime - minTime).toBeLessThan(50); // Variance should be less than 50ms
      expect(averageTime).toBeLessThan(PERFORMANCE_REQUIREMENTS.TEMPLATE_EXECUTION_MAX_MS);
      
      console.log(`Performance metrics - Avg: ${averageTime.toFixed(2)}ms, Max: ${maxTime.toFixed(2)}ms, Min: ${minTime.toFixed(2)}ms, Variance: ${variance.toFixed(2)}`);
    });
  });

  afterAll(() => {
    // Log all performance metrics for analysis
    console.log('\n=== PERFORMANCE VALIDATION SUMMARY ===');
    performanceMetrics.forEach((value, key) => {
      console.log(`${key}: ${value.toFixed(2)}ms`);
    });
    console.log('=====================================\n');
  });
});
