/**
 * SystemOrchestrator Test Suite
 * Tests system orchestration and coordination functionality
 */

import { SystemOrchestrator } from '../../../modules/cleanup/SystemOrchestrator';
import { ITemplateExecution } from '../../../types/CleanupTypes';

describe('SystemOrchestrator', () => {
  let orchestrator: SystemOrchestrator;

  beforeEach(async () => {
    orchestrator = new SystemOrchestrator({
      maxConcurrentOperations: 5,
      defaultTimeout: 30000,
      performanceMonitoringEnabled: true,
      testMode: true // Enable test mode for Jest compatibility
    });
  });

  afterEach(async () => {
    if (orchestrator) {
      await orchestrator.shutdown();
    }
  });

  describe('Initialization and Configuration', () => {
    it('should initialize with default configuration', async () => {
      const defaultOrchestrator = new SystemOrchestrator();
      expect(defaultOrchestrator).toBeDefined();
      await defaultOrchestrator.shutdown();
    });

    it('should initialize with custom configuration', () => {
      expect(orchestrator).toBeDefined();
    });
  });

  describe('System Status Management', () => {
    it('should get system status', () => {
      const status = orchestrator.getSystemStatus();
      
      expect(status).toBeDefined();
      expect(typeof status).toBe('object');
      expect(status.phaseIntegration).toBeDefined();
      expect(status.executions).toBeDefined();
      expect(status.monitoring).toBeDefined();
      expect(status.system).toBeDefined();
    });

    it('should perform health check', async () => {
      const healthCheck = await orchestrator.performHealthCheck();
      
      expect(healthCheck).toBeDefined();
      expect(typeof healthCheck.healthy).toBe('boolean');
      expect(Array.isArray(healthCheck.issues)).toBe(true);
      expect(typeof healthCheck.metrics).toBe('object');
    });

    it('should create system snapshot', async () => {
      const snapshotId = 'test-snapshot';
      const snapshot = await orchestrator.createSystemSnapshot(snapshotId);
      
      expect(snapshot).toBeDefined();
      expect(typeof snapshot).toBe('object');
      expect(snapshot.timestamp).toBeDefined();
      expect(snapshot.activeOperations).toBeDefined();
      expect(snapshot.systemMetrics).toBeDefined();
    });
  });

  describe('Template Execution Tracking', () => {
    it('should register template execution', () => {
      const mockExecution: ITemplateExecution = {
        id: 'test-execution-1',
        templateId: 'test-template',
        targetComponents: ['component1', 'component2'],
        parameters: {},
        startTime: new Date(),
        status: 'running',
        stepResults: new Map(),
        rollbackExecuted: false,
        metrics: {
          totalSteps: 2,
          executedSteps: 0,
          failedSteps: 0,
          skippedSteps: 0,
          averageStepTime: 0,
          longestStepTime: 0,
          dependencyResolutionTime: 0,
          validationTime: 0,
          totalExecutionTime: 0
        }
      };
      
      expect(() => {
        orchestrator.registerTemplateExecution(mockExecution);
      }).not.toThrow();
    });

    it('should update template execution status', () => {
      const mockExecution: ITemplateExecution = {
        id: 'test-execution-2',
        templateId: 'test-template',
        targetComponents: ['component1'],
        parameters: {},
        startTime: new Date(),
        status: 'running',
        stepResults: new Map(),
        rollbackExecuted: false,
        metrics: {
          totalSteps: 1,
          executedSteps: 0,
          failedSteps: 0,
          skippedSteps: 0,
          averageStepTime: 0,
          longestStepTime: 0,
          dependencyResolutionTime: 0,
          validationTime: 0,
          totalExecutionTime: 0
        }
      };
      
      orchestrator.registerTemplateExecution(mockExecution);
      
      expect(() => {
        orchestrator.updateTemplateExecution('test-execution-2', { 
          status: 'completed',
          endTime: new Date()
        });
      }).not.toThrow();
      
      const status = orchestrator.getSystemStatus();
      expect(status.executions.active).toBeGreaterThanOrEqual(0);
    });

    it('should get template metrics', () => {
      const templateId = 'metrics-template';
      const metrics = orchestrator.getTemplateMetrics(templateId);
      
      expect(metrics).toBeDefined();
      expect(typeof metrics).toBe('object');
      expect(metrics.totalSteps).toBeDefined();
      expect(metrics.executedSteps).toBeDefined();
      expect(metrics.failedSteps).toBeDefined();
    });
  });

  describe('System Snapshot Management', () => {
    it('should create and retrieve system snapshot', async () => {
      const snapshotId = 'retrieve-test-snapshot';
      const snapshot = await orchestrator.createSystemSnapshot(snapshotId);
      
      expect(snapshot).toBeDefined();
      
      const retrievedSnapshot = orchestrator.getSystemSnapshot(snapshotId);
      expect(retrievedSnapshot).toBeDefined();
      expect(retrievedSnapshot?.timestamp).toEqual(snapshot.timestamp);
    });

    it('should list system snapshots', async () => {
      await orchestrator.createSystemSnapshot('snapshot-1');
      await orchestrator.createSystemSnapshot('snapshot-2');
      
      const snapshots = orchestrator.listSystemSnapshots();
      expect(Array.isArray(snapshots)).toBe(true);
      expect(snapshots.length).toBeGreaterThanOrEqual(2);
    });

    it('should cleanup old system snapshots', async () => {
      await orchestrator.createSystemSnapshot('old-snapshot');
      
      const oldDate = new Date(Date.now() + 1000); // 1 second in the future to ensure cleanup
      const cleanedCount = await orchestrator.cleanupSystemSnapshots(oldDate);
      
      expect(typeof cleanedCount).toBe('number');
      expect(cleanedCount).toBeGreaterThanOrEqual(0);
    });
  });

  describe('Performance Monitoring', () => {
    it('should monitor system performance', async () => {
      const healthCheck = await orchestrator.performHealthCheck();
      
      expect(healthCheck.metrics).toBeDefined();
      expect(typeof healthCheck.metrics).toBe('object');
    });

    it('should track resource utilization', () => {
      const status = orchestrator.getSystemStatus();
      
      expect(status).toBeDefined();
      expect(status.system).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    it('should handle template execution failures gracefully', () => {
      const mockExecution: ITemplateExecution = {
        id: 'failing-execution',
        templateId: 'failing-template',
        targetComponents: ['component1'],
        parameters: {},
        startTime: new Date(),
        status: 'running',
        stepResults: new Map(),
        rollbackExecuted: false,
        metrics: {
          totalSteps: 1,
          executedSteps: 0,
          failedSteps: 0,
          skippedSteps: 0,
          averageStepTime: 0,
          longestStepTime: 0,
          dependencyResolutionTime: 0,
          validationTime: 0,
          totalExecutionTime: 0
        }
      };
      
      orchestrator.registerTemplateExecution(mockExecution);
      
      expect(() => {
        orchestrator.updateTemplateExecution('failing-execution', { 
          status: 'failed',
          endTime: new Date()
        });
      }).not.toThrow();
    });

    it('should handle shutdown gracefully', async () => {
      await expect(orchestrator.shutdown()).resolves.not.toThrow();
    });
  });
}); 