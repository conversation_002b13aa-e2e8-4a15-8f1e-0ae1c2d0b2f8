/**
 * CleanupConfiguration Test Suite
 * Tests configuration management and defaults
 */

import { 
  DEFAULT_ENHANCED_CLEANUP_CONFIG,
  validateConfig,
  mergeConfigs,
  IConfigValidationResult
} from '../../../modules/cleanup/CleanupConfiguration';

describe('CleanupConfiguration', () => {
  describe('DEFAULT_ENHANCED_CLEANUP_CONFIG', () => {
    it('should provide valid default configuration', () => {
      expect(DEFAULT_ENHANCED_CLEANUP_CONFIG).toBeDefined();
      expect(typeof DEFAULT_ENHANCED_CLEANUP_CONFIG).toBe('object');
    });

    it('should have required fields', () => {
      const config = DEFAULT_ENHANCED_CLEANUP_CONFIG;
      
      // Base configuration properties
      expect(config.maxConcurrentOperations).toBeDefined();
      expect(config.defaultTimeout).toBeDefined();
      expect(config.maxRetries).toBeDefined();
      expect(config.conflictDetectionEnabled).toBeDefined();
      expect(config.metricsEnabled).toBeDefined();
      expect(config.cleanupIntervalMs).toBeDefined();
      expect(config.testMode).toBeDefined();
      
      // Enhanced configuration properties
      expect(config.templateValidationEnabled).toBeDefined();
      expect(config.dependencyOptimizationEnabled).toBeDefined();
      expect(config.rollbackEnabled).toBeDefined();
      expect(config.maxCheckpoints).toBeDefined();
      expect(config.checkpointRetentionDays).toBeDefined();
      expect(config.phaseIntegrationEnabled).toBeDefined();
      expect(config.performanceMonitoringEnabled).toBeDefined();
    });

    it('should have valid timeout configuration', () => {
      const config = DEFAULT_ENHANCED_CLEANUP_CONFIG;
      
      expect(config.defaultTimeout).toBeGreaterThan(0);
      expect(config.maxRetries).toBeGreaterThanOrEqual(0);
      expect(config.cleanupIntervalMs).toBeGreaterThan(0);
      expect(config.maxCheckpoints).toBeGreaterThan(0);
      expect(config.checkpointRetentionDays).toBeGreaterThan(0);
    });
  });

  describe('validateConfig', () => {
    it('should validate valid configuration', () => {
      const validConfig = {
        maxConcurrentOperations: 5,
        defaultTimeout: 30000,
        maxRetries: 3,
        conflictDetectionEnabled: true,
        metricsEnabled: true,
        cleanupIntervalMs: 300000,
        testMode: false,
        templateValidationEnabled: true,
        dependencyOptimizationEnabled: true,
        rollbackEnabled: true,
        maxCheckpoints: 100,
        checkpointRetentionDays: 7,
        phaseIntegrationEnabled: true,
        performanceMonitoringEnabled: true
      };

      const result: IConfigValidationResult = validateConfig(validConfig);
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect invalid timeout values', () => {
      const invalidConfig = {
        maxConcurrentOperations: 5,
        defaultTimeout: -1000,
        maxRetries: 3
      };

      const result: IConfigValidationResult = validateConfig(invalidConfig);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors).toContain('defaultTimeout must be greater than 0');
    });

    it('should detect invalid retry configuration', () => {
      const invalidConfig = {
        maxConcurrentOperations: 5,
        defaultTimeout: 30000,
        maxRetries: -1
      };

      const result: IConfigValidationResult = validateConfig(invalidConfig);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors).toContain('maxRetries must be greater than or equal to 0');
    });

    it('should detect invalid checkpoint configuration', () => {
      const invalidConfig = {
        maxCheckpoints: -5,
        checkpointRetentionDays: 0
      };

      const result: IConfigValidationResult = validateConfig(invalidConfig);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors).toContain('maxCheckpoints must be greater than 0');
      expect(result.errors).toContain('checkpointRetentionDays must be greater than 0');
    });
  });

  describe('mergeConfigs', () => {
    it('should merge configurations correctly', () => {
      const baseConfig = DEFAULT_ENHANCED_CLEANUP_CONFIG;
      const overrides = {
        maxConcurrentOperations: 10,
        defaultTimeout: 60000
      };

      const merged = mergeConfigs(baseConfig, overrides);
      
      expect(merged.maxConcurrentOperations).toBe(10);
      expect(merged.defaultTimeout).toBe(60000);
      expect(merged.maxRetries).toBe(baseConfig.maxRetries);
      expect(merged.templateValidationEnabled).toBe(baseConfig.templateValidationEnabled);
    });

    it('should handle nested configuration merging', () => {
      const baseConfig = {
        maxConcurrentOperations: 5,
        nestedConfig: {
          timeout: 30000,
          retries: 3
        }
      };
      const overrides: Partial<typeof baseConfig> = {
        nestedConfig: {
          timeout: 60000,
          retries: 3 // Include all required properties for TypeScript
        }
      };

      const merged = mergeConfigs(baseConfig, overrides);
      
      expect(merged.nestedConfig.timeout).toBe(60000);
      expect(merged.nestedConfig.retries).toBe(3);
    });

    it('should handle empty overrides', () => {
      const baseConfig = DEFAULT_ENHANCED_CLEANUP_CONFIG;
      const merged = mergeConfigs(baseConfig, {});
      
      expect(merged).toEqual(baseConfig);
    });

    it('should handle undefined values in overrides', () => {
      const baseConfig = DEFAULT_ENHANCED_CLEANUP_CONFIG;
      const overrides = {
        maxConcurrentOperations: undefined,
        defaultTimeout: 60000
      };

      const merged = mergeConfigs(baseConfig, overrides);
      
      expect(merged.maxConcurrentOperations).toBe(baseConfig.maxConcurrentOperations);
      expect(merged.defaultTimeout).toBe(60000);
    });
  });
}); 