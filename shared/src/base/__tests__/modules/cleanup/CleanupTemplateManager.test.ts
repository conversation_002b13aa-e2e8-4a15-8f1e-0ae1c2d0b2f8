/**
 * @file CleanupTemplateManager Test Suite
 * @filepath shared/src/base/__tests__/modules/cleanup/CleanupTemplateManager.test.ts
 * @task-id M-TSK-01.SUB-01.REF-01.TEMPLATE-TESTS
 * @component cleanup-template-manager-tests
 * @created 2025-07-25 02:49:40 +03
 * 
 * @description
 * Comprehensive test suite for CleanupTemplateManager providing validation of:
 * - Template registration and validation workflows
 * - Template execution with target component integration
 * - Template metrics collection and performance monitoring
 * - Error handling and recovery scenarios
 * - Integration with extracted validation and workflow modules
 * - Memory safety compliance and Jest compatibility patterns
 * 
 * PHASE B COMPLIANCE:
 * - 100% test preservation mandate from refactoring plan
 * - Performance requirements: <100ms template execution
 * - Jest compatibility with proven Phase 5 async yielding patterns
 * - File size target: ≤400 lines per refactoring specifications
 * 
 * LESSONS LEARNED INTEGRATION:
 * - Jest compatibility: Async yielding instead of setTimeout patterns
 * - Memory safety: Proper resource cleanup in tests
 * - Performance: Optimized test execution with timing validation
 */

import { 
  CleanupTemplateManager 
} from '../../../modules/cleanup/CleanupTemplateManager';
import { 
  TemplateValidator,
  validateTemplate 
} from '../../../modules/cleanup/TemplateValidation';
import { 
  TemplateWorkflowExecutor,
  createWorkflowExecutor 
} from '../../../modules/cleanup/TemplateWorkflows';
import { 
  DependencyGraph,
  createDependencyGraphFromOperations 
} from '../../../modules/cleanup/TemplateDependencies';
import {
  ICleanupTemplate,
  ITemplateExecution,
  ITemplateExecutionResult,
  ICleanupTemplateStep,
  IEnhancedCleanupConfig,
  IComponentRegistry
} from '../../../types/CleanupTypes';
import { CleanupOperationType, CleanupPriority } from '../../../CleanupCoordinatorEnhanced';

/**
 * ============================================================================
 * AI CONTEXT: CleanupTemplateManager Test Suite
 * Purpose: Comprehensive testing of template management functionality
 * Complexity: Moderate - Template lifecycle and integration testing
 * AI Navigation: 6 logical sections - Setup, Registration, Execution, Metrics, Integration, Performance
 * ============================================================================
 */

/**
 * ============================================================================
 * SECTION 1: TEST SETUP & UTILITIES (Lines 1-80)
 * AI Context: "Test configuration, mocks, and helper functions"
 * ============================================================================
 */

describe('CleanupTemplateManager', () => {
  let templateManager: CleanupTemplateManager;
  let mockComponentRegistry: IComponentRegistry;

  // Test configuration with Jest compatibility
  const testConfig: Partial<IEnhancedCleanupConfig> = {
    templateValidationEnabled: true,
    performanceMonitoringEnabled: true,
    rollbackEnabled: true
  };

  // Mock component registry for testing
  const createMockComponentRegistry = (): IComponentRegistry => ({
    findComponents: jest.fn().mockResolvedValue(['component1', 'component2', 'test-component', 'test-component-1', 'test-component-2']),
    getCleanupOperation: jest.fn().mockReturnValue(jest.fn()),
    registerOperation: jest.fn().mockReturnValue(true),
    hasOperation: jest.fn().mockReturnValue(true),
    listOperations: jest.fn().mockReturnValue(['cleanup', 'validation']),
    getOperationMetrics: jest.fn().mockReturnValue({
      totalOperations: 10,
      successfulOperations: 9,
      failedOperations: 1,
      averageExecutionTime: 50
    })
  });

  // Helper to create test template
  const createTestTemplate = (overrides: Partial<ICleanupTemplate> = {}): ICleanupTemplate => ({
    id: 'test-template-001',
    name: 'Test Cleanup Template',
    description: 'Test template for validation',
    version: '1.0.0',
    operations: [
      {
        id: 'step-001',
        type: CleanupOperationType.RESOURCE_CLEANUP,
        componentPattern: 'test-.*',
        operationName: 'test-cleanup',
        parameters: { testParam: 'testValue' },
        timeout: 5000,
        retryPolicy: {
          maxRetries: 3,
          retryDelay: 1000,
          backoffMultiplier: 2,
          maxRetryDelay: 10000,
          retryOnErrors: ['TIMEOUT', 'NETWORK_ERROR']
        },
        dependsOn: [],
        priority: CleanupPriority.NORMAL,
        estimatedDuration: 1000,
        description: 'Test cleanup operation'
      }
    ],
    conditions: [],
    rollbackSteps: [],
    metadata: { testKey: 'testValue' },
    tags: ['test', 'cleanup'],
    createdAt: new Date(),
    modifiedAt: new Date(),
    author: 'Test Suite',
    validationRules: [],
    ...overrides
  });

  beforeEach(async () => {
    // LESSON LEARNED: Async yielding for Jest compatibility
    await Promise.resolve();

    mockComponentRegistry = createMockComponentRegistry();
    templateManager = new CleanupTemplateManager(testConfig, mockComponentRegistry);
    // Note: initialize() is protected, so we'll work with the manager as-is for testing
  });

  afterEach(async () => {
    // LESSON LEARNED: Proper resource cleanup
    await Promise.resolve();
    
    if (templateManager) {
      await templateManager.shutdown();
    }
    
    // Clear all mocks
    jest.clearAllMocks();
  });

  /**
   * ============================================================================
   * SECTION 2: TEMPLATE REGISTRATION TESTS (Lines 81-160)
   * AI Context: "Template registration, validation, and storage functionality"
   * ============================================================================
   */

  describe('Template Registration', () => {
    it('should register valid template successfully', async () => {
      // LESSON LEARNED: Performance timing validation
      const startTime = performance.now();
      
      const template = createTestTemplate();
      await templateManager.registerTemplate(template);
      
      const executionTime = performance.now() - startTime;
      expect(executionTime).toBeLessThan(100); // <100ms requirement
      
      const templates = templateManager.getTemplates();
      expect(templates).toHaveLength(1);
      expect(templates[0].id).toBe('test-template-001');
    });

    it('should validate template structure during registration', async () => {
      const invalidTemplate = createTestTemplate({
        id: '', // Invalid empty ID
        operations: [] // No operations
      });

      await expect(templateManager.registerTemplate(invalidTemplate))
        .rejects
        .toThrow('Template validation failed');
    });

    it('should detect circular dependencies in template operations', async () => {
      const cyclicTemplate = createTestTemplate({
        operations: [
          {
            id: 'step-001',
            type: CleanupOperationType.RESOURCE_CLEANUP,
            componentPattern: 'test-.*',
            operationName: 'cleanup-1',
            parameters: {},
            timeout: 5000,
            retryPolicy: {
              maxRetries: 3,
              retryDelay: 1000,
              backoffMultiplier: 2,
              maxRetryDelay: 10000,
              retryOnErrors: []
            },
            dependsOn: ['step-002'],
            priority: CleanupPriority.NORMAL,
            estimatedDuration: 1000,
            description: 'First step'
          },
          {
            id: 'step-002',
            type: CleanupOperationType.RESOURCE_CLEANUP,
            componentPattern: 'test-.*',
            operationName: 'cleanup-2',
            parameters: {},
            timeout: 5000,
            retryPolicy: {
              maxRetries: 3,
              retryDelay: 1000,
              backoffMultiplier: 2,
              maxRetryDelay: 10000,
              retryOnErrors: []
            },
            dependsOn: ['step-001'], // Creates cycle
            priority: CleanupPriority.NORMAL,
            estimatedDuration: 1000,
            description: 'Second step'
          }
        ]
      });

      await expect(templateManager.registerTemplate(cyclicTemplate))
        .rejects
        .toThrow('Dependency cycles detected');
    });

    it('should initialize template metrics upon registration', async () => {
      const template = createTestTemplate();
      await templateManager.registerTemplate(template);

      const metrics = templateManager.getTemplateMetrics('test-template-001');
      expect(metrics).toBeDefined();
      expect(metrics.totalSteps).toBe(1);
      expect(metrics.executedSteps).toBe(0);
      expect(metrics.failedSteps).toBe(0);
    });
  });

  /**
   * ============================================================================
   * SECTION 3: TEMPLATE EXECUTION TESTS (Lines 161-240)
   * AI Context: "Template execution workflows and result processing"
   * ============================================================================
   */

  describe('Template Execution', () => {
    beforeEach(async () => {
      const template = createTestTemplate();
      await templateManager.registerTemplate(template);
    });

    it('should execute template with target components', async () => {
      const startTime = performance.now();
      
      const result = await templateManager.executeTemplate(
        'test-template-001',
        ['test-component-1', 'test-component-2'],
        { executionParam: 'testValue' }
      );

      const executionTime = performance.now() - startTime;
      expect(executionTime).toBeLessThan(100); // <100ms requirement

      expect(result.status).toBe('success');
      expect(result.templateId).toBe('test-template-001');
      expect(result.executedSteps).toBeGreaterThan(0);
      expect(result.errors).toHaveLength(0);
    });

    it('should handle template execution errors gracefully', async () => {
      // Create a new template manager with a failing component registry
      const failingComponentRegistry = createMockComponentRegistry();
      failingComponentRegistry.findComponents = jest.fn().mockRejectedValue(new Error('Component error'));

      const failingTemplateManager = new CleanupTemplateManager(testConfig, failingComponentRegistry);

      // Register template on the failing manager
      const template = createTestTemplate();
      await failingTemplateManager.registerTemplate(template);

      // CRITICAL FIX: Use target components that match the pattern 'test-.*'
      const result = await failingTemplateManager.executeTemplate(
        'test-template-001',
        ['test-component-1', 'test-component-2'], // These match the pattern 'test-.*'
        {}
      );

      expect(result.status).toBe('failure');
      expect(result.errors.length).toBeGreaterThan(0);

      // Clean up
      await failingTemplateManager.shutdown();
    });

    it('should generate unique execution IDs', async () => {
      const result1 = await templateManager.executeTemplate(
        'test-template-001',
        ['test-component'],
        {}
      );

      const result2 = await templateManager.executeTemplate(
        'test-template-001',
        ['test-component'],
        {}
      );

      expect(result1.executionId).toBeDefined();
      expect(result2.executionId).toBeDefined();
      expect(result1.executionId).not.toBe(result2.executionId);
    });

    it('should reject execution of non-existent template', async () => {
      await expect(templateManager.executeTemplate(
        'non-existent-template',
        ['test-component'],
        {}
      )).rejects.toThrow('Template non-existent-template not found');
    });
  });

  /**
   * ============================================================================
   * SECTION 4: METRICS COLLECTION TESTS (Lines 241-320)
   * AI Context: "Template metrics tracking and performance monitoring"
   * ============================================================================
   */

  describe('Template Metrics', () => {
    beforeEach(async () => {
      const template = createTestTemplate();
      await templateManager.registerTemplate(template);
    });

    it('should collect execution metrics during template execution', async () => {
      await templateManager.executeTemplate(
        'test-template-001',
        ['test-component'],
        {}
      );

      const metrics = templateManager.getTemplateMetrics('test-template-001');
      expect(metrics.executedSteps).toBeGreaterThan(0);
      expect(metrics.totalExecutionTime).toBeGreaterThan(0);
      expect(metrics.averageStepTime).toBeGreaterThan(0);
    });

    it('should return empty metrics for non-existent template', () => {
      const metrics = templateManager.getTemplateMetrics('non-existent');
      expect(metrics.totalSteps).toBe(0);
      expect(metrics.executedSteps).toBe(0);
      expect(metrics.failedSteps).toBe(0);
    });

    it('should return all template metrics when no template ID specified', async () => {
      // Register multiple templates
      await templateManager.registerTemplate(createTestTemplate({ id: 'template-2' }));

      const allMetrics = templateManager.getTemplateMetrics();
      expect(typeof allMetrics).toBe('object');
      expect(Object.keys(allMetrics)).toContain('test-template-001');
      expect(Object.keys(allMetrics)).toContain('template-2');
    });
  });

  /**
   * ============================================================================
   * SECTION 5: INTEGRATION TESTS (Lines 321-380)
   * AI Context: "Integration with extracted validation and workflow modules"
   * ============================================================================
   */

  describe('Module Integration', () => {
    it('should integrate with TemplateValidator for validation', async () => {
      const template = createTestTemplate();
      
      // Template should be validated during registration
      await expect(templateManager.registerTemplate(template))
        .resolves
        .not.toThrow();
    });

    it('should integrate with TemplateWorkflowExecutor for execution', async () => {
      const template = createTestTemplate();
      await templateManager.registerTemplate(template);

      const result = await templateManager.executeTemplate(
        'test-template-001',
        ['test-component'],
        {}
      );

      // Verify workflow executor integration
      expect(result.results).toBeDefined();
      expect(Array.isArray(result.results)).toBe(true);
    });

    it('should integrate with DependencyGraph for dependency management', async () => {
      const template = createTestTemplate({
        operations: [
          {
            id: 'step-001',
            type: CleanupOperationType.RESOURCE_CLEANUP,
            componentPattern: 'test-.*',
            operationName: 'cleanup-1',
            parameters: {},
            timeout: 5000,
            retryPolicy: {
              maxRetries: 3,
              retryDelay: 1000,
              backoffMultiplier: 2,
              maxRetryDelay: 10000,
              retryOnErrors: []
            },
            dependsOn: [],
            priority: CleanupPriority.HIGH,
            estimatedDuration: 1000,
            description: 'First step'
          },
          {
            id: 'step-002',
            type: CleanupOperationType.MEMORY_CLEANUP,
            componentPattern: 'test-.*',
            operationName: 'cleanup-2',
            parameters: {},
            timeout: 5000,
            retryPolicy: {
              maxRetries: 3,
              retryDelay: 1000,
              backoffMultiplier: 2,
              maxRetryDelay: 10000,
              retryOnErrors: []
            },
            dependsOn: ['step-001'],
            priority: CleanupPriority.NORMAL,
            estimatedDuration: 1000,
            description: 'Second step'
          }
        ]
      });

      // Should register successfully with proper dependency order
      await expect(templateManager.registerTemplate(template))
        .resolves
        .not.toThrow();
    });
  });

  /**
   * ============================================================================
   * SECTION 6: PERFORMANCE & MEMORY SAFETY TESTS (Lines 381-400)
   * AI Context: "Performance validation and memory safety compliance testing"
   * ============================================================================
   */

  describe('Performance & Memory Safety', () => {
    it('should maintain performance requirements during template operations', async () => {
      const template = createTestTemplate();
      await templateManager.registerTemplate(template);

      const startTime = performance.now();
      await templateManager.executeTemplate(
        'test-template-001',
        ['test-component'],
        {}
      );
      const executionTime = performance.now() - startTime;

      // Performance requirement: <100ms template execution
      expect(executionTime).toBeLessThan(100);
    });

    it('should properly cleanup resources during shutdown', async () => {
      const template = createTestTemplate();
      await templateManager.registerTemplate(template);

      // Should shutdown without errors
      await expect(templateManager.shutdown())
        .resolves
        .not.toThrow();

      // Templates should be cleared after shutdown
      expect(templateManager.getTemplates()).toHaveLength(0);
    });
  });
}); 