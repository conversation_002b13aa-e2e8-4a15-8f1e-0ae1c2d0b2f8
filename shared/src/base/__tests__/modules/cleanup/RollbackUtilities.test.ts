/**
 * ============================================================================
 * RollbackUtilities Test Suite
 * Comprehensive testing for rollback utility functions with enterprise-grade validation
 * ============================================================================
 */

import {
  generateCheckpointId,
  deepClone,
  calculateCheckpointChecksum,
  sortRollbackActions,
  assessRollbackComplexity,
  estimateRollbackTime,
  assessRollbackRisk,
  identifyRollbackLimitations,
  validateCheckpointIntegrity,
  validateRollbackAction,
  RollbackUtils
} from '../../../modules/cleanup/RollbackUtilities';

import {
  ICheckpoint,
  IRollbackAction,
  ISystemSnapshot
} from '../../../types/CleanupTypes';

describe('RollbackUtilities', () => {
  // ES6 Constants for test configuration
  const TEST_OPERATION_IDS = ['test-op', 'complex-operation', 'simple-op', 'priority-test'];
  const TIMESTAMP_TOLERANCE_MS = 100;
  const MAX_EXECUTION_TIME_MS = 5000;
  const CHECKPOINT_ID_PATTERN = /^checkpoint-[a-zA-Z0-9-]+-\d+-[a-z0-9]{6}$/;

  // ES6 Test Data Factory using enhanced object literals
  const createMockRollbackAction = (overrides: Partial<IRollbackAction> = {}): IRollbackAction => ({
    type: 'cleanup_resource',
    parameters: { resource: 'test-resource' },
    timeout: 5000,
    priority: 5,
    estimatedDuration: 1000,
    critical: false,
    description: 'Mock rollback action for testing',
    ...overrides
  });

  const createMockCheckpoint = (overrides: Partial<ICheckpoint> = {}): ICheckpoint => ({
    id: 'test-checkpoint-123',
    operationId: TEST_OPERATION_IDS[0],
    timestamp: new Date(),
    state: { mock: 'state' },
    rollbackActions: [createMockRollbackAction()],
    metadata: { test: true },
    dependencies: [],
    systemSnapshot: createMockSystemSnapshot(),
    checksum: 'mock-checksum-abc123',
    ...overrides
  });

  const createMockSystemSnapshot = (overrides: Partial<ISystemSnapshot> = {}): ISystemSnapshot => ({
    timestamp: new Date(),
    componentStates: new Map(),
    resourceStates: new Map(),
    configurationStates: new Map(),
    activeOperations: [],
    systemMetrics: {
      memoryUsage: 1024 * 1024,
      timestamp: Date.now()
    },
    version: '1.0.0',
    ...overrides
  });

  describe('ID Generation Utilities', () => {
    describe('generateCheckpointId', () => {
      it('should generate unique checkpoint ID with enterprise pattern validation', () => {
        const operationId = TEST_OPERATION_IDS[0];
        const checkpointId = generateCheckpointId(operationId);
        
        // Enterprise-grade validation using ES6 features
        const idValidation = {
          isString: typeof checkpointId === 'string',
          isNonEmpty: checkpointId.length > 0,
          matchesPattern: CHECKPOINT_ID_PATTERN.test(checkpointId),
          includesOperationId: checkpointId.includes(operationId),
          hasTimestamp: /\d{13}/.test(checkpointId), // 13-digit timestamp
          hasRandomSuffix: /[a-z0-9]{6}$/.test(checkpointId)
        };

        // Validate using ES6 Object.entries
        Object.entries(idValidation).forEach(([check, isValid]) => {
          expect(isValid).toBe(true);
          if (!isValid) {
            throw new Error(`ID validation failed: ${check}. Generated ID: ${checkpointId}`);
          }
        });
      });

      it('should generate unique IDs for concurrent operations', () => {
        // Enterprise concurrency testing with ES6 Promise.all
        const concurrentOperations = Array.from({ length: 100 }, (_, i) => `op-${i}`);
        const generatedIds = concurrentOperations.map(opId => generateCheckpointId(opId));

        // Validate uniqueness using ES6 Set
        const uniqueIds = new Set(generatedIds);
        expect(uniqueIds.size).toBe(generatedIds.length);

        // Validate all IDs follow pattern
        generatedIds.forEach(id => {
          expect(CHECKPOINT_ID_PATTERN.test(id)).toBe(true);
        });
      });

      it('should handle edge case operation IDs', () => {
        // Enterprise edge case testing
        const edgeCaseIds = [
          '', 
          'very-long-operation-id-'.repeat(10),
          'special!@#$%^&*()chars',
          '123456789',
          'single'
        ];

        edgeCaseIds.forEach(operationId => {
          const checkpointId = generateCheckpointId(operationId);
          expect(typeof checkpointId).toBe('string');
          expect(checkpointId.length).toBeGreaterThan(0);
          expect(checkpointId.startsWith('checkpoint-')).toBe(true);
        });
      });
    });
  });

  describe('Data Manipulation Utilities', () => {
    describe('deepClone', () => {
      it('should perform deep cloning with enterprise validation', () => {
        // Complex nested object for thorough testing
        const complexObject = {
          primitive: 'string',
          number: 42,
          boolean: true,
          nullValue: null,
          nested: {
            array: [1, 2, { deep: 'value' }],
            date: new Date('2023-01-01'),
            map: { key1: 'value1', key2: 'value2' }
          },
          arrayOfObjects: [
            { id: 1, name: 'first' },
            { id: 2, name: 'second' }
          ]
        };

        const cloned = deepClone(complexObject);

        // Enterprise validation using ES6 features
        const cloneValidation = {
          isNotSameReference: cloned !== complexObject,
          hasAllProperties: Object.keys(cloned).length === Object.keys(complexObject).length,
          primitiveValuesMatch: cloned.primitive === complexObject.primitive,
          nestedObjectCloned: cloned.nested !== complexObject.nested,
          arrayCloned: cloned.nested.array !== complexObject.nested.array,
          deepArrayItemCloned: cloned.nested.array[2] !== complexObject.nested.array[2],
          arrayOfObjectsCloned: cloned.arrayOfObjects !== complexObject.arrayOfObjects
        };

        Object.entries(cloneValidation).forEach(([check, isValid]) => {
          expect(isValid).toBe(true);
          if (!isValid) {
            throw new Error(`Deep clone validation failed: ${check}`);
          }
        });

        // Verify deep equality of values
        expect(JSON.stringify(cloned)).toEqual(JSON.stringify(complexObject));
      });

      it('should handle primitive types and edge cases', () => {
        // ES6 test cases with proper typing
        const testCases = [
          { name: 'string', value: 'test' },
          { name: 'number', value: 42 },
          { name: 'boolean', value: true },
          { name: 'null', value: null },
          { name: 'array', value: [1, 2, 3] },
          { name: 'empty-object', value: {} },
          { name: 'empty-array', value: [] }
        ];

        testCases.forEach(({ name, value }) => {
          const cloned = deepClone(value);
          expect(cloned).toEqual(value);
          if (typeof value === 'object' && value !== null) {
            expect(cloned).not.toBe(value);
          }
        });
      });
    }); // Close deepClone describe block

    describe('calculateCheckpointChecksum', () => {
      it('should calculate consistent checksums with enterprise validation', async () => {
        const state = { key: 'value', count: 42 };
        const actions = [createMockRollbackAction(), createMockRollbackAction({ type: 'restore_state' })];
        const snapshot = createMockSystemSnapshot();

        const checksum1 = await calculateCheckpointChecksum(state, actions, snapshot);
        const checksum2 = await calculateCheckpointChecksum(state, actions, snapshot);

        // Enterprise checksum validation
        const checksumValidation = {
          isString: typeof checksum1 === 'string',
          isConsistent: checksum1 === checksum2,
          hasCorrectLength: checksum1.length === 16,
          isBase64: /^[A-Za-z0-9+/=]+$/.test(checksum1),
          isNonEmpty: checksum1.length > 0
        };

        Object.entries(checksumValidation).forEach(([check, isValid]) => {
          expect(isValid).toBe(true);
          if (!isValid) {
            throw new Error(`Checksum validation failed: ${check}. Checksum: ${checksum1}`);
          }
        });
      });

      it('should generate different checksums for different data', async () => {
        const baseState = { key: 'value' };
        const baseActions = [createMockRollbackAction()];
        
        // Create base snapshot with explicit timestamp for consistency
        const baseTimestamp = new Date('2024-01-01T10:00:00.000Z');
        const baseSnapshot = createMockSystemSnapshot({
          timestamp: baseTimestamp,
          systemMetrics: {
            memoryUsage: 1024 * 1024,
            timestamp: baseTimestamp.getTime()
          }
        });

        // Create significantly different data variations with distinct timestamps for reliable differentiation
        const testVariations = [
          {
            name: 'base-data',
            state: baseState,
            actions: baseActions,
            snapshot: baseSnapshot
          },
          {
            name: 'modified-state',
            state: { ...baseState, modified: true, additionalKey: 'newValue', nestedObject: { deep: 'value' } },
            actions: baseActions,
            snapshot: baseSnapshot
          },
          {
            name: 'different-actions',
            state: baseState,
            actions: [
              ...baseActions, 
              createMockRollbackAction({ type: 'restore_state', priority: 8, description: 'Different action 1' }),
              createMockRollbackAction({ type: 'notify_component', priority: 3, description: 'Different action 2' })
            ],
            snapshot: baseSnapshot
          },
          {
            name: 'different-snapshot',
            state: baseState,
            actions: baseActions,
            snapshot: createMockSystemSnapshot({ 
              timestamp: new Date('2024-01-01T11:00:00.000Z'), // 1 hour later
              version: '2.0.0',
              systemMetrics: { 
                memoryUsage: 2048, 
                timestamp: new Date('2024-01-01T11:00:00.000Z').getTime(),
                cpuUsage: 75 
              },
              activeOperations: ['different-op-1', 'different-op-2']
            })
          },
          {
            name: 'comprehensive-difference',
            state: { completely: 'different', data: 'structure', complexObject: { nested: { deep: 'change' } } },
            actions: [
              createMockRollbackAction({ type: 'execute_operation', priority: 9, description: 'Execute comprehensive' }),
              createMockRollbackAction({ type: 'revert_configuration', priority: 1, description: 'Revert comprehensive' })
            ],
            snapshot: createMockSystemSnapshot({ 
              timestamp: new Date('2024-01-01T12:00:00.000Z'), // 2 hours later
              version: '3.0.0',
              activeOperations: ['comprehensive-op-1', 'comprehensive-op-2', 'comprehensive-op-3'],
              systemMetrics: { 
                memoryUsage: 4096, 
                timestamp: new Date('2024-01-01T12:00:00.000Z').getTime(),
                cpuUsage: 25, 
                diskUsage: 85 
              }
            })
          },
          {
            name: 'map-states-difference',
            state: baseState,
            actions: baseActions,
            snapshot: (() => {
              const snapshot = createMockSystemSnapshot({
                timestamp: new Date('2024-01-01T13:00:00.000Z'), // 3 hours later
                systemMetrics: {
                  memoryUsage: 1024 * 1024,
                  timestamp: new Date('2024-01-01T13:00:00.000Z').getTime()
                }
              });
              snapshot.componentStates.set('component1', { status: 'active' });
              snapshot.componentStates.set('component2', { status: 'inactive' });
              snapshot.resourceStates.set('resource1', { allocated: true });
              snapshot.configurationStates.set('config1', { enabled: false });
              return snapshot;
            })()
          }
        ];

        // Generate checksums for all variations with detailed tracking
        const checksumResults = await Promise.all(
          testVariations.map(async ({ name, state, actions, snapshot }, index) => {
            const checksum = await calculateCheckpointChecksum(state, actions, snapshot);
            return { 
              index, 
              name, 
              checksum,
              stateSize: JSON.stringify(state).length,
              actionsCount: actions.length,
              snapshotVersion: snapshot.version,
              activeOpsCount: snapshot.activeOperations.length,
              snapshotTimestamp: snapshot.timestamp.getTime()
            };
          })
        );

        // Enterprise validation with comprehensive reporting
        const checksums = checksumResults.map(result => result.checksum);
        const uniqueChecksums = new Set(checksums);
        
        // Enhanced debugging information
        const checksumFrequency = checksums.reduce((freq, checksum) => {
          freq[checksum] = (freq[checksum] || 0) + 1;
          return freq;
        }, {} as Record<string, number>);

        const duplicates = Object.entries(checksumFrequency)
          .filter(([_, count]) => count > 1)
          .map(([checksum, count]) => ({ checksum, count }));

        // Enhanced validation with detailed reporting
        const uniquenessValidation = {
          totalVariations: testVariations.length,
          uniqueChecksumsGenerated: uniqueChecksums.size,
          duplicateChecksums: duplicates.length,
          allChecksumsValid: checksums.every(checksum => 
            typeof checksum === 'string' && 
            checksum.length === 16 && 
            /^[A-Za-z0-9+/=]+$/.test(checksum)
          ),
          hasReasonableUniqueness: uniqueChecksums.size >= Math.floor(testVariations.length * 0.85) // Increased to 85% for stricter validation
        };

        // Enhanced error reporting for debugging
        if (!uniquenessValidation.hasReasonableUniqueness) {
          const detailedReport = {
            summary: `Checksum uniqueness insufficient: ${uniqueChecksums.size}/${testVariations.length} unique`,
            duplicates: duplicates,
            allResults: checksumResults,
            checksumFrequency: checksumFrequency
          };
          
          console.error('CHECKSUM UNIQUENESS FAILURE:', JSON.stringify(detailedReport, null, 2));
        }

        // Validate with enterprise expectations
        Object.entries(uniquenessValidation).forEach(([check, result]) => {
          if (check === 'hasReasonableUniqueness') {
            expect(result).toBe(true);
            if (!result) {
              throw new Error(`Checksum uniqueness insufficient: ${uniqueChecksums.size}/${testVariations.length} unique (need ≥85%). Duplicates: ${JSON.stringify(duplicates)}`);
            }
          } else if (typeof result === 'boolean') {
            expect(result).toBe(true);
            if (!result) {
              throw new Error(`Validation failed for ${check}: ${result}`);
            }
          }
        });

        // Additional enterprise validation: ensure no empty checksums
        checksums.forEach((checksum, index) => {
          expect(checksum).toBeTruthy();
          expect(checksum.length).toBe(16);
          if (!checksum || checksum.length !== 16) {
            throw new Error(`Invalid checksum at index ${index}: "${checksum}"`);
          }
        });

        // Log success for debugging if needed (development only)
        if (process.env.NODE_ENV === 'development') {
          console.log('Checksum uniqueness SUCCESS:', {
            uniqueCount: uniqueChecksums.size,
            totalCount: testVariations.length,
            successRate: `${Math.round((uniqueChecksums.size / testVariations.length) * 100)}%`
          });
        }
      });
    });
  });

  describe('Rollback Action Utilities', () => {
    describe('sortRollbackActions', () => {
      it('should sort actions by priority and duration with enterprise validation', () => {
        // Create diverse set of actions for comprehensive testing using valid types
        const actions = [
          createMockRollbackAction({ priority: 3, estimatedDuration: 1000, type: 'cleanup_resource' }),
          createMockRollbackAction({ priority: 8, estimatedDuration: 500, type: 'execute_operation' }),
          createMockRollbackAction({ priority: 8, estimatedDuration: 2000, type: 'restore_state' }),
          createMockRollbackAction({ priority: 5, estimatedDuration: 1500, type: 'notify_component' }),
          createMockRollbackAction({ priority: 1, estimatedDuration: 100, type: 'revert_configuration' })
        ];

        const sorted = sortRollbackActions(actions);

        // Enterprise sorting validation using ES6 features
        const sortingValidation = {
          lengthPreserved: sorted.length === actions.length,
          originalUnmodified: actions.length === 5, // Original array not mutated
          priorityOrdering: sorted.every((action, index) => {
            if (index === 0) return true;
            return action.priority <= sorted[index - 1].priority;
          }),
          durationOrderingWithinPriority: () => {
            // Group by priority and check duration ordering within groups
            const priorityGroups = new Map<number, IRollbackAction[]>();
            sorted.forEach(action => {
              if (!priorityGroups.has(action.priority)) {
                priorityGroups.set(action.priority, []);
              }
              priorityGroups.get(action.priority)!.push(action);
            });

            return Array.from(priorityGroups.values()).every(group => {
              if (group.length <= 1) return true;
              return group.every((action: IRollbackAction, i: number) => {
                if (i === 0) return true;
                return action.estimatedDuration <= group[i - 1].estimatedDuration;
              });
            });
          }
        };

        Object.entries(sortingValidation).forEach(([check, result]) => {
          const isValid = typeof result === 'function' ? result() : result;
          expect(isValid).toBe(true);
          if (!isValid) {
            throw new Error(`Sorting validation failed: ${check}`);
          }
        });

        // Validate specific order expectations
        expect(sorted[0].priority).toBe(8); // Highest priority first
        expect(sorted[0].estimatedDuration).toBe(2000); // Longer duration first within same priority
      });

      it('should handle edge cases with enterprise patterns', () => {
        // ES6 test cases using Map with proper types
        const edgeCases = new Map<string, IRollbackAction[]>([
          ['empty-array', []],
          ['single-action', [createMockRollbackAction()]],
          ['identical-actions', Array(3).fill(createMockRollbackAction())],
          ['negative-priority', [createMockRollbackAction({ priority: -1 })]],
          ['zero-duration', [createMockRollbackAction({ estimatedDuration: 0 })]]
        ]);

        edgeCases.forEach((actions, testCase) => {
          const sorted = sortRollbackActions(actions);
          expect(Array.isArray(sorted)).toBe(true);
          expect(sorted.length).toBe(actions.length);
        });
      });
    });
  });

  describe('Assessment Utilities', () => {
    describe('assessRollbackComplexity', () => {
      it('should assess complexity levels with enterprise accuracy', () => {
        // ES6 Map for test scenarios
        const complexityScenarios = new Map([
          ['simple', { actionCount: 1, expected: 'simple' }],
          ['simple-max', { actionCount: 3, expected: 'simple' }],
          ['moderate-min', { actionCount: 4, expected: 'moderate' }],
          ['moderate-max', { actionCount: 10, expected: 'moderate' }],
          ['complex-min', { actionCount: 11, expected: 'complex' }],
          ['complex-high', { actionCount: 50, expected: 'complex' }]
        ]);

        complexityScenarios.forEach(({ actionCount, expected }, scenarioName) => {
          const actions = Array.from({ length: actionCount }, () => createMockRollbackAction());
          const checkpoint = createMockCheckpoint({ rollbackActions: actions });
          
          const complexity = assessRollbackComplexity(checkpoint);
          expect(complexity).toBe(expected);
          
          if (complexity !== expected) {
            throw new Error(`Complexity assessment failed for ${scenarioName}: expected ${expected}, got ${complexity}`);
          }
        });
      });

      it('should handle zero actions gracefully', () => {
        const checkpoint = createMockCheckpoint({ rollbackActions: [] });
        const complexity = assessRollbackComplexity(checkpoint);
        expect(complexity).toBe('simple');
      });
    });

    describe('estimateRollbackTime', () => {
      it('should calculate accurate time estimates with enterprise precision', () => {
        // Enterprise test scenarios with ES6 features
        const timeScenarios = [
          {
            name: 'single-action',
            actions: [createMockRollbackAction({ estimatedDuration: 1000 })],
            expected: 1000
          },
          {
            name: 'multiple-actions',
            actions: [
              createMockRollbackAction({ estimatedDuration: 500 }),
              createMockRollbackAction({ estimatedDuration: 1500 }),
              createMockRollbackAction({ estimatedDuration: 2000 })
            ],
            expected: 4000
          },
          {
            name: 'zero-duration',
            actions: [createMockRollbackAction({ estimatedDuration: 0 })],
            expected: 0
          },
          {
            name: 'empty-actions',
            actions: [],
            expected: 0
          }
        ];

        timeScenarios.forEach(({ name, actions, expected }) => {
          const checkpoint = createMockCheckpoint({ rollbackActions: actions });
          const estimatedTime = estimateRollbackTime(checkpoint);
          
          expect(estimatedTime).toBe(expected);
          expect(typeof estimatedTime).toBe('number');
          expect(estimatedTime).toBeGreaterThanOrEqual(0);
          
          if (estimatedTime !== expected) {
            throw new Error(`Time estimation failed for ${name}: expected ${expected}, got ${estimatedTime}`);
          }
        });
      });

      it('should handle large-scale operations efficiently', () => {
        // Performance test with many actions
        const manyActions = Array.from({ length: 1000 }, (_, i) => 
          createMockRollbackAction({ estimatedDuration: i + 1 })
        );
        const checkpoint = createMockCheckpoint({ rollbackActions: manyActions });
        
        const startTime = Date.now();
        const estimatedTime = estimateRollbackTime(checkpoint);
        const executionTime = Date.now() - startTime;
        
        expect(executionTime).toBeLessThan(100); // Should be fast
        expect(estimatedTime).toBe(500500); // Sum of 1 to 1000
      });
    });

    describe('assessRollbackRisk', () => {
      it('should assess risk levels with comprehensive enterprise criteria', () => {
        const now = new Date();
        
        // ES6 risk assessment scenarios
        const riskScenarios = [
          {
            name: 'low-risk-recent',
            checkpoint: createMockCheckpoint({
              timestamp: new Date(now.getTime() - 30 * 60 * 1000), // 30 minutes ago
              rollbackActions: [createMockRollbackAction({ critical: false })]
            }),
            expected: 'low'
          },
          {
            name: 'medium-risk-some-critical',
            checkpoint: createMockCheckpoint({
              timestamp: new Date(now.getTime() - 2 * 60 * 60 * 1000), // 2 hours ago
              rollbackActions: [
                createMockRollbackAction({ critical: true }),
                createMockRollbackAction({ critical: false })
              ]
            }),
            expected: 'medium'
          },
          {
            name: 'high-risk-old',
            checkpoint: createMockCheckpoint({
              timestamp: new Date(now.getTime() - 25 * 60 * 60 * 1000), // 25 hours ago
              rollbackActions: [createMockRollbackAction({ critical: false })]
            }),
            expected: 'high'
          },
          {
            name: 'high-risk-many-critical',
            checkpoint: createMockCheckpoint({
              timestamp: new Date(now.getTime() - 30 * 60 * 1000), // Recent
              rollbackActions: Array(5).fill(createMockRollbackAction({ critical: true }))
            }),
            expected: 'high'
          }
        ];

        riskScenarios.forEach(({ name, checkpoint, expected }) => {
          const risk = assessRollbackRisk(checkpoint);
          expect(risk).toBe(expected);
          
          if (risk !== expected) {
            throw new Error(`Risk assessment failed for ${name}: expected ${expected}, got ${risk}`);
          }
        });
      });
    });

    describe('identifyRollbackLimitations', () => {
      it('should identify comprehensive limitations with enterprise detail', () => {
        const now = new Date();
        
        // Test old checkpoint limitation
        const oldCheckpoint = createMockCheckpoint({
          timestamp: new Date(now.getTime() - 25 * 60 * 60 * 1000), // 25 hours ago
          rollbackActions: [createMockRollbackAction()]
        });
        
        const oldLimitations = identifyRollbackLimitations(oldCheckpoint);
        expect(oldLimitations).toContain('Checkpoint is older than 24 hours - may have stale state');
        
        // Test many critical actions limitation
        const criticalCheckpoint = createMockCheckpoint({
          rollbackActions: Array(6).fill(createMockRollbackAction({ critical: true }))
        });
        
        const criticalLimitations = identifyRollbackLimitations(criticalCheckpoint);
        expect(criticalLimitations).toContain('High number of critical actions - increased failure risk');
        
        // Test recent checkpoint with few critical actions (no limitations)
        const goodCheckpoint = createMockCheckpoint({
          timestamp: new Date(now.getTime() - 30 * 60 * 1000), // 30 minutes ago
          rollbackActions: [
            createMockRollbackAction({ critical: true }),
            createMockRollbackAction({ critical: false })
          ]
        });
        
        const goodLimitations = identifyRollbackLimitations(goodCheckpoint);
        expect(goodLimitations).toEqual([]);
      });
    });
  });

  describe('Validation Utilities', () => {
    describe('validateCheckpointIntegrity', () => {
      it('should validate checkpoint integrity with enterprise thoroughness', () => {
        // Valid checkpoint
        const validCheckpoint = createMockCheckpoint();
        expect(validateCheckpointIntegrity(validCheckpoint)).toBe(true);
        
        // ES6 Map for invalid checkpoint scenarios
        const invalidScenarios = new Map([
          ['missing-id', { ...validCheckpoint, id: '' }],
          ['null-id', { ...validCheckpoint, id: null as any }],
          ['missing-operation-id', { ...validCheckpoint, operationId: '' }],
          ['null-operation-id', { ...validCheckpoint, operationId: null as any }],
          ['missing-timestamp', { ...validCheckpoint, timestamp: null as any }],
          ['future-timestamp', { ...validCheckpoint, timestamp: new Date(Date.now() + 24 * 60 * 60 * 1000) }],
          ['invalid-actions-null', { ...validCheckpoint, rollbackActions: null as any }],
          ['invalid-actions-not-array', { ...validCheckpoint, rollbackActions: {} as any }]
        ]);

        invalidScenarios.forEach((invalidCheckpoint, scenarioName) => {
          const isValid = validateCheckpointIntegrity(invalidCheckpoint as ICheckpoint);
          expect(isValid).toBe(false);
          
          if (isValid) {
            throw new Error(`Validation should have failed for scenario: ${scenarioName}`);
          }
        });
      });
    });

    describe('validateRollbackAction', () => {
      it('should validate rollback actions with comprehensive enterprise rules', () => {
        // Valid action
        const validAction = createMockRollbackAction();
        expect(validateRollbackAction(validAction)).toBe(true);
        
        // ES6 invalid action scenarios
        const invalidActionScenarios = new Map([
          ['missing-type', { ...validAction, type: '' }],
          ['null-type', { ...validAction, type: null as any }],
          ['missing-parameters', { ...validAction, parameters: null as any }],
          ['negative-priority', { ...validAction, priority: -1 }],
          ['excessive-priority', { ...validAction, priority: 11 }],
          ['negative-duration', { ...validAction, estimatedDuration: -100 }]
        ]);

        invalidActionScenarios.forEach((invalidAction, scenarioName) => {
          const isValid = validateRollbackAction(invalidAction as IRollbackAction);
          expect(isValid).toBe(false);
          
          if (isValid) {
            throw new Error(`Action validation should have failed for scenario: ${scenarioName}`);
          }
        });
      });

      it('should validate edge case priorities and durations', () => {
        // Test boundary conditions
        const boundaryTests = [
          { priority: 0, estimatedDuration: 0, shouldBeValid: true },
          { priority: 10, estimatedDuration: 1, shouldBeValid: true },
          { priority: 5, estimatedDuration: Number.MAX_SAFE_INTEGER, shouldBeValid: true }
        ];

        boundaryTests.forEach(({ priority, estimatedDuration, shouldBeValid }) => {
          const action = createMockRollbackAction({ priority, estimatedDuration });
          const isValid = validateRollbackAction(action);
          expect(isValid).toBe(shouldBeValid);
        });
      });
    });
  });

  describe('RollbackUtils Collection', () => {
    it('should export all utility functions with enterprise validation', () => {
      // ES6 Map for function validation
      const expectedFunctions = new Map([
        ['generateCheckpointId', 'function'],
        ['deepClone', 'function'],
        ['calculateCheckpointChecksum', 'function'],
        ['sortRollbackActions', 'function'],
        ['assessRollbackComplexity', 'function'],
        ['estimateRollbackTime', 'function'],
        ['assessRollbackRisk', 'function'],
        ['identifyRollbackLimitations', 'function'],
        ['validateCheckpointIntegrity', 'function'],
        ['validateRollbackAction', 'function']
      ]);

      expectedFunctions.forEach((expectedType, functionName) => {
        expect(typeof (RollbackUtils as any)[functionName]).toBe(expectedType);
        expect((RollbackUtils as any)[functionName]).toBeDefined();
        
        if (typeof (RollbackUtils as any)[functionName] !== expectedType) {
          throw new Error(`RollbackUtils.${functionName} should be a ${expectedType}`);
        }
      });

      // Validate RollbackUtils object structure
      expect(typeof RollbackUtils).toBe('object');
      expect(Object.keys(RollbackUtils).length).toBe(expectedFunctions.size);
    });

    it('should provide functional equivalency between direct imports and RollbackUtils', async () => {
      // Test functional equivalency using ES6 features with enterprise-grade validation
      const testCheckpoint = createMockCheckpoint();
      const testAction = createMockRollbackAction();
      const testState = { test: 'data' };
      const testSnapshot = createMockSystemSnapshot();

      // Enhanced equivalency testing with proper handling of time-sensitive functions
      const functionalEquivalencyTests = [
        {
          name: 'generateCheckpointId',
          directFn: () => generateCheckpointId(TEST_OPERATION_IDS[0]),
          utilFn: () => RollbackUtils.generateCheckpointId(TEST_OPERATION_IDS[0]),
          validator: (direct: string, util: string) => {
            // Validate pattern equivalency instead of exact equality
            const pattern = CHECKPOINT_ID_PATTERN;
            return pattern.test(direct) && pattern.test(util) && 
                   direct.includes(TEST_OPERATION_IDS[0]) && util.includes(TEST_OPERATION_IDS[0]);
          }
        },
        {
          name: 'deepClone',
          directFn: () => deepClone(testState),
          utilFn: () => RollbackUtils.deepClone(testState),
          validator: (direct: any, util: any) => JSON.stringify(direct) === JSON.stringify(util)
        },
        {
          name: 'sortRollbackActions',
          directFn: () => sortRollbackActions([testAction]),
          utilFn: () => RollbackUtils.sortRollbackActions([testAction]),
          validator: (direct: any[], util: any[]) => JSON.stringify(direct) === JSON.stringify(util)
        },
        {
          name: 'assessRollbackComplexity',
          directFn: () => assessRollbackComplexity(testCheckpoint),
          utilFn: () => RollbackUtils.assessRollbackComplexity(testCheckpoint),
          validator: (direct: string, util: string) => direct === util
        },
        {
          name: 'estimateRollbackTime',
          directFn: () => estimateRollbackTime(testCheckpoint),
          utilFn: () => RollbackUtils.estimateRollbackTime(testCheckpoint),
          validator: (direct: number, util: number) => direct === util
        },
        {
          name: 'assessRollbackRisk',
          directFn: () => assessRollbackRisk(testCheckpoint),
          utilFn: () => RollbackUtils.assessRollbackRisk(testCheckpoint),
          validator: (direct: string, util: string) => direct === util
        },
        {
          name: 'identifyRollbackLimitations',
          directFn: () => identifyRollbackLimitations(testCheckpoint),
          utilFn: () => RollbackUtils.identifyRollbackLimitations(testCheckpoint),
          validator: (direct: string[], util: string[]) => JSON.stringify(direct) === JSON.stringify(util)
        },
        {
          name: 'validateCheckpointIntegrity',
          directFn: () => validateCheckpointIntegrity(testCheckpoint),
          utilFn: () => RollbackUtils.validateCheckpointIntegrity(testCheckpoint),
          validator: (direct: boolean, util: boolean) => direct === util
        },
        {
          name: 'validateRollbackAction',
          directFn: () => validateRollbackAction(testAction),
          utilFn: () => RollbackUtils.validateRollbackAction(testAction),
          validator: (direct: boolean, util: boolean) => direct === util
        }
      ];

      // Execute functional equivalency tests with enterprise validation
      const equivalencyResults = await Promise.all(
        functionalEquivalencyTests.map(async ({ name, directFn, utilFn, validator }) => {
          const directResult = await Promise.resolve(directFn());
          const utilResult = await Promise.resolve(utilFn());
          
          const isEquivalent = validator(directResult, utilResult);
          return { name, isEquivalent, directType: typeof directResult, utilType: typeof utilResult };
        })
      );

      // Validate all functions show proper equivalency
      equivalencyResults.forEach(({ name, isEquivalent, directType, utilType }) => {
        expect(isEquivalent).toBe(true);
        expect(directType).toBe(utilType);
        
        if (!isEquivalent) {
          throw new Error(`Functional equivalency failed for ${name}`);
        }
      });

      // Additional test for calculateCheckpointChecksum with controlled timing
      const checksumDirect = await calculateCheckpointChecksum(testState, [testAction], testSnapshot);
      const checksumUtil = await RollbackUtils.calculateCheckpointChecksum(testState, [testAction], testSnapshot);
      
      // Both should be valid base64 strings of length 16
      expect(typeof checksumDirect).toBe('string');
      expect(typeof checksumUtil).toBe('string');
      expect(checksumDirect.length).toBe(16);
      expect(checksumUtil.length).toBe(16);
      expect(/^[A-Za-z0-9+/=]+$/.test(checksumDirect)).toBe(true);
      expect(/^[A-Za-z0-9+/=]+$/.test(checksumUtil)).toBe(true);
    });
  });

  describe('Enterprise Performance and Scalability', () => {
    it('should maintain performance under high-load scenarios', () => {
      const startTime = Date.now();
      
      // High-volume operations
      const operationResults = Array.from({ length: 1000 }, (_, i) => {
        const operationId = `bulk-op-${i}`;
        const checkpointId = generateCheckpointId(operationId);
        const checkpoint = createMockCheckpoint({ 
          id: checkpointId, 
          operationId,
          rollbackActions: Array.from({ length: 10 }, () => createMockRollbackAction())
        });
        
        return {
          id: checkpointId,
          complexity: assessRollbackComplexity(checkpoint),
          estimatedTime: estimateRollbackTime(checkpoint),
          risk: assessRollbackRisk(checkpoint),
          isValid: validateCheckpointIntegrity(checkpoint)
        };
      });
      
      const executionTime = Date.now() - startTime;
      
      // Performance validation
      expect(executionTime).toBeLessThan(MAX_EXECUTION_TIME_MS);
      expect(operationResults.length).toBe(1000);
      expect(operationResults.every(result => result.isValid)).toBe(true);
      
      // Validate result diversity
      const complexities = new Set(operationResults.map(r => r.complexity));
      const risks = new Set(operationResults.map(r => r.risk));
      expect(complexities.size).toBeGreaterThan(0);
      expect(risks.size).toBeGreaterThan(0);
    });

    it('should handle concurrent utility operations safely', async () => {
      // Concurrent operations simulation
      const concurrentTasks = Array.from({ length: 50 }, async (_, i) => {
        const operationId = `concurrent-${i}`;
        const checkpoint = createMockCheckpoint({ operationId });
        const action = createMockRollbackAction({ priority: i % 10 });
        
        const [checkpointId, clonedData, complexity, risk] = await Promise.all([
          Promise.resolve(generateCheckpointId(operationId)),
          Promise.resolve(deepClone({ index: i, data: 'test' })),
          Promise.resolve(assessRollbackComplexity(checkpoint)),
          Promise.resolve(assessRollbackRisk(checkpoint))
        ]);
        
        return { checkpointId, clonedData, complexity, risk, index: i };
      });

      const results = await Promise.all(concurrentTasks);
      
      // Validate all operations completed successfully
      expect(results.length).toBe(50);
      results.forEach((result, index) => {
        expect(result.index).toBe(index);
        expect(typeof result.checkpointId).toBe('string');
        expect(result.clonedData.index).toBe(index);
        expect(['simple', 'moderate', 'complex']).toContain(result.complexity);
        expect(['low', 'medium', 'high']).toContain(result.risk);
      });
    });
  });
});