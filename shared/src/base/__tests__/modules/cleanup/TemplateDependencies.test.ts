/**
 * @file Template Dependencies Test Suite
 * @filepath shared/src/base/__tests__/modules/cleanup/TemplateDependencies.test.ts
 * @task-id M-TSK-01.SUB-01.REF-01.TEMPLATE-DEPS-TESTS
 * @component template-dependencies-tests
 * @created 2025-07-25 02:49:40 +03
 * 
 * @description
 * Comprehensive test suite for TemplateDependencies providing validation of:
 * - DependencyGraph class functionality and manipulation
 * - Cycle detection algorithms with various graph structures
 * - Topological sorting accuracy and completeness
 * - Critical path analysis and calculation
 * - Parallel execution group identification and optimization
 * - Graph metrics, validation utilities, and performance monitoring
 * 
 * PHASE B COMPLIANCE:
 * - 100% test preservation mandate from refactoring plan
 * - Performance requirements: <50ms dependency analysis
 * - Jest compatibility with proven Phase 5 async yielding patterns
 * - File size target: ≤400 lines per refactoring specifications
 * 
 * LESSONS LEARNED INTEGRATION:
 * - ES6+ compliance: Modern forEach patterns throughout testing
 * - Memory safety: Proper graph cleanup and resource management
 * - Performance: Optimized algorithm testing with timing validation
 */

import { 
  DependencyGraph,
  createDependencyGraphFromOperations,
  validateDependencyGraph
} from '../../../modules/cleanup/TemplateDependencies';
import { CleanupOperationType, CleanupPriority } from '../../../CleanupCoordinator';

/**
 * ============================================================================
 * AI CONTEXT: Template Dependencies Test Suite
 * Purpose: Comprehensive testing of dependency graph algorithms and utilities
 * Complexity: Moderate - Graph algorithm validation and performance testing
 * AI Navigation: 6 logical sections - Setup, Basic Operations, Cycle Detection, Sorting, Analysis, Performance
 * ============================================================================
 */

/**
 * ============================================================================
 * SECTION 1: TEST SETUP & UTILITIES (Lines 1-60)
 * AI Context: "Test configuration, graph builders, and helper functions"
 * ============================================================================
 */

describe('TemplateDependencies', () => {
  let dependencyGraph: DependencyGraph;

  // Helper to create test operations
  const createTestOperations = (config: { id: string; dependsOn?: string[] }[]) => {
    return config.map(op => ({
      id: op.id,
      type: CleanupOperationType.RESOURCE_CLEANUP,
      componentPattern: '.*',
      operationName: `operation-${op.id}`,
      parameters: {},
      timeout: 5000,
      retryPolicy: {
        maxRetries: 3,
        retryDelay: 1000,
        backoffMultiplier: 2,
        maxRetryDelay: 10000,
        retryOnErrors: []
      },
      dependsOn: op.dependsOn || [],
      priority: CleanupPriority.NORMAL,
      estimatedDuration: 1000,
      description: `Test operation ${op.id}`
    }));
  };

  beforeEach(async () => {
    // LESSON LEARNED: Async yielding for Jest compatibility
    await Promise.resolve();
    dependencyGraph = new DependencyGraph();
  });

  afterEach(async () => {
    // LESSON LEARNED: Proper resource cleanup
    await Promise.resolve();
    dependencyGraph.clear();
  });

  /**
   * ============================================================================
   * SECTION 2: BASIC GRAPH OPERATIONS (Lines 61-120)
   * AI Context: "Node and edge management, basic graph construction"
   * ============================================================================
   */

  describe('Basic Graph Operations', () => {
    it('should add nodes to the graph', () => {
      dependencyGraph.addNode('node1');
      dependencyGraph.addNode('node2');

      expect(dependencyGraph.nodes.size).toBe(2);
      expect(dependencyGraph.nodes.has('node1')).toBe(true);
      expect(dependencyGraph.nodes.has('node2')).toBe(true);
    });

    it('should add edges between nodes', () => {
      dependencyGraph.addEdge('node1', 'node2');

      expect(dependencyGraph.nodes.has('node1')).toBe(true);
      expect(dependencyGraph.nodes.has('node2')).toBe(true);
      expect(dependencyGraph.edges.get('node1')?.has('node2')).toBe(true);
    });

    it('should add dependencies correctly', () => {
      dependencyGraph.addDependency('main', ['dep1', 'dep2']);

      expect(dependencyGraph.nodes.size).toBe(3);
      expect(dependencyGraph.edges.get('main')?.has('dep1')).toBe(true);
      expect(dependencyGraph.edges.get('main')?.has('dep2')).toBe(true);
    });

    it('should handle duplicate node additions gracefully', () => {
      dependencyGraph.addNode('node1');
      dependencyGraph.addNode('node1'); // Duplicate

      expect(dependencyGraph.nodes.size).toBe(1);
      expect(dependencyGraph.nodes.has('node1')).toBe(true);
    });

    it('should clear the graph completely', () => {
      dependencyGraph.addNode('node1');
      dependencyGraph.addEdge('node1', 'node2');

      dependencyGraph.clear();

      expect(dependencyGraph.nodes.size).toBe(0);
      expect(dependencyGraph.edges.size).toBe(0);
    });
  });

  /**
   * ============================================================================
   * SECTION 3: CYCLE DETECTION TESTS (Lines 121-180)
   * AI Context: "Cycle detection algorithms with various graph structures"
   * ============================================================================
   */

  describe('Cycle Detection', () => {
    it('should detect no cycles in acyclic graph', () => {
      dependencyGraph.addDependency('A', ['B', 'C']);
      dependencyGraph.addDependency('B', ['D']);
      dependencyGraph.addDependency('C', ['D']);

      expect(dependencyGraph.hasCycles()).toBe(false);
    });

    it('should detect simple two-node cycle', () => {
      dependencyGraph.addDependency('A', ['B']);
      dependencyGraph.addDependency('B', ['A']);

      expect(dependencyGraph.hasCycles()).toBe(true);
    });

    it('should detect complex multi-node cycle', () => {
      dependencyGraph.addDependency('A', ['B']);
      dependencyGraph.addDependency('B', ['C']);
      dependencyGraph.addDependency('C', ['D']);
      dependencyGraph.addDependency('D', ['A']); // Creates cycle

      expect(dependencyGraph.hasCycles()).toBe(true);
    });

    it('should find all cycles in graph', () => {
      dependencyGraph.addDependency('A', ['B']);
      dependencyGraph.addDependency('B', ['A']); // Cycle 1: A -> B -> A
      dependencyGraph.addDependency('C', ['D']);
      dependencyGraph.addDependency('D', ['C']); // Cycle 2: C -> D -> C

      const cycles = dependencyGraph.findCycles();
      expect(cycles.length).toBeGreaterThan(0);
      expect(cycles.some(cycle => cycle.includes('A') && cycle.includes('B'))).toBe(true);
    });

    it('should handle self-referencing nodes', () => {
      dependencyGraph.addDependency('A', ['A']); // Self-cycle

      expect(dependencyGraph.hasCycles()).toBe(true);
    });
  });

  /**
   * ============================================================================
   * SECTION 4: TOPOLOGICAL SORTING TESTS (Lines 181-240)
   * AI Context: "Topological sorting accuracy and dependency order validation"
   * ============================================================================
   */

  describe('Topological Sorting', () => {
    it('should perform topological sort on simple DAG', () => {
      dependencyGraph.addDependency('A', ['B']);
      dependencyGraph.addDependency('B', ['C']);

      const sorted = dependencyGraph.topologicalSort();
      
      expect(sorted).toHaveLength(3);
      expect(sorted.indexOf('A')).toBeLessThan(sorted.indexOf('B'));
      expect(sorted.indexOf('B')).toBeLessThan(sorted.indexOf('C'));
    });

    it('should handle complex dependency relationships', () => {
      dependencyGraph.addDependency('main', ['lib1', 'lib2']);
      dependencyGraph.addDependency('lib1', ['core']);
      dependencyGraph.addDependency('lib2', ['core']);

      const sorted = dependencyGraph.topologicalSort();
      
      expect(sorted).toHaveLength(4);
      expect(sorted.indexOf('main')).toBeLessThan(sorted.indexOf('lib1'));
      expect(sorted.indexOf('main')).toBeLessThan(sorted.indexOf('lib2'));
      expect(sorted.indexOf('lib1')).toBeLessThan(sorted.indexOf('core'));
      expect(sorted.indexOf('lib2')).toBeLessThan(sorted.indexOf('core'));
    });

    it('should return empty array for cyclic graph', () => {
      dependencyGraph.addDependency('A', ['B']);
      dependencyGraph.addDependency('B', ['A']); // Creates cycle

      const sorted = dependencyGraph.topologicalSort();
      
      // Should detect incomplete sort due to cycle
      expect(sorted.length).toBeLessThan(dependencyGraph.nodes.size);
    });

    it('should handle disconnected components', () => {
      dependencyGraph.addNode('isolated1');
      dependencyGraph.addNode('isolated2');
      dependencyGraph.addDependency('connected1', ['connected2']);

      const sorted = dependencyGraph.topologicalSort();
      
      expect(sorted).toHaveLength(4);
      expect(sorted).toContain('isolated1');
      expect(sorted).toContain('isolated2');
      expect(sorted).toContain('connected1');
      expect(sorted).toContain('connected2');
    });
  });

  /**
   * ============================================================================
   * SECTION 5: CRITICAL PATH & PARALLEL ANALYSIS (Lines 241-320)
   * AI Context: "Critical path calculation and parallel execution planning"
   * ============================================================================
   */

  describe('Critical Path & Parallel Analysis', () => {
    it('should calculate critical path correctly', () => {
      dependencyGraph.addDependency('A', ['B', 'C']);
      dependencyGraph.addDependency('B', ['D']);
      dependencyGraph.addDependency('C', ['E']);
      dependencyGraph.addDependency('D', ['F']);
      dependencyGraph.addDependency('E', ['F']);

      const criticalPath = dependencyGraph.getCriticalPath();
      
      expect(criticalPath.length).toBeGreaterThan(0);
      expect(criticalPath).toContain('F'); // Should be end of path
    });

    it('should identify parallel execution groups', () => {
      dependencyGraph.addDependency('main', ['parallel1', 'parallel2']);
      dependencyGraph.addDependency('parallel1', ['final']);
      dependencyGraph.addDependency('parallel2', ['final']);

      const parallelGroups = dependencyGraph.getParallelGroups();
      
      expect(parallelGroups.length).toBeGreaterThan(1);
      // Find group with parallel1 and parallel2
      const parallelGroup = parallelGroups.find(group => 
        group.includes('parallel1') && group.includes('parallel2'));
      expect(parallelGroup).toBeDefined();
    });

    it('should calculate graph metrics accurately', () => {
      dependencyGraph.addDependency('A', ['B', 'C']);
      dependencyGraph.addDependency('B', ['D']);

      const metrics = dependencyGraph.getGraphMetrics();
      
      expect(metrics.nodeCount).toBe(4);
      expect(metrics.edgeCount).toBeGreaterThan(0);
      expect(metrics.maxDepth).toBeGreaterThan(0);
      expect(metrics.avgDependencies).toBeGreaterThan(0);
      expect(metrics.cycleCount).toBe(0);
    });

    it('should detect transitive dependencies', () => {
      dependencyGraph.addDependency('A', ['B']);
      dependencyGraph.addDependency('B', ['C']);

      expect(dependencyGraph.hasTransitiveDependency('A', 'C')).toBe(true);
      expect(dependencyGraph.hasTransitiveDependency('C', 'A')).toBe(false);
    });
  });

  /**
   * ============================================================================
   * SECTION 6: UTILITY FUNCTIONS & PERFORMANCE (Lines 321-400)
   * AI Context: "Graph utilities, factory functions, and performance validation"
   * ============================================================================
   */

  describe('Utility Functions & Performance', () => {
    it('should create dependency graph from operations', async () => {
      // LESSON LEARNED: Performance timing validation
      const startTime = performance.now();
      
      const operations = createTestOperations([
        { id: 'op1', dependsOn: ['op2'] },
        { id: 'op2', dependsOn: ['op3'] },
        { id: 'op3', dependsOn: [] }
      ]);

      const graph = createDependencyGraphFromOperations(operations);
      
      const executionTime = performance.now() - startTime;
      expect(executionTime).toBeLessThan(50); // <50ms requirement

      expect(graph.nodes.size).toBe(3);
      expect(graph.hasCycles()).toBe(false);
    });

    it('should validate dependency graph for issues', () => {
      dependencyGraph.addDependency('A', ['B']);
      dependencyGraph.addDependency('B', ['C']);

      const validation = validateDependencyGraph(dependencyGraph);
      
      expect(validation.valid).toBe(true);
      expect(validation.issues).toHaveLength(0);
      expect(validation.metrics).toBeDefined();
      expect(validation.metrics.nodeCount).toBe(3);
    });

    it('should detect validation issues in problematic graphs', () => {
      dependencyGraph.addDependency('A', ['B']);
      dependencyGraph.addDependency('B', ['A']); // Creates cycle

      const validation = validateDependencyGraph(dependencyGraph);
      
      expect(validation.valid).toBe(false);
      expect(validation.issues.length).toBeGreaterThan(0);
      expect(validation.issues[0]).toContain('cycle');
    });

    it('should clone dependency graph accurately', () => {
      dependencyGraph.addDependency('A', ['B', 'C']);
      dependencyGraph.addDependency('B', ['D']);

      const cloned = dependencyGraph.clone();
      
      expect(cloned.nodes.size).toBe(dependencyGraph.nodes.size);
      expect(cloned.edges.size).toBe(dependencyGraph.edges.size);
      expect(cloned.hasCycles()).toBe(dependencyGraph.hasCycles());
      
      // Verify independence
      cloned.addNode('newNode');
      expect(dependencyGraph.nodes.has('newNode')).toBe(false);
    });

    it('should maintain performance requirements for complex graphs', async () => {
      // LESSON LEARNED: Performance testing with larger graphs
      const startTime = performance.now();
      
      // Create complex graph with 50 nodes
      for (let i = 0; i < 50; i++) {
        const dependencies = i > 0 ? [`node-${i-1}`] : [];
        dependencyGraph.addDependency(`node-${i}`, dependencies);
      }
      
      // Perform complex operations
      const hasCycles = dependencyGraph.hasCycles();
      const sorted = dependencyGraph.topologicalSort();
      const criticalPath = dependencyGraph.getCriticalPath();
      const parallelGroups = dependencyGraph.getParallelGroups();
      
      const executionTime = performance.now() - startTime;
      expect(executionTime).toBeLessThan(50); // <50ms requirement
      
      expect(hasCycles).toBe(false);
      expect(sorted).toHaveLength(50);
      expect(criticalPath.length).toBeGreaterThan(0);
      expect(parallelGroups.length).toBeGreaterThan(0);
    });

    it('should handle edge cases gracefully', () => {
      // Empty graph
      expect(dependencyGraph.topologicalSort()).toHaveLength(0);
      expect(dependencyGraph.getCriticalPath()).toHaveLength(0);
      expect(dependencyGraph.getParallelGroups()).toHaveLength(0);
      expect(dependencyGraph.hasCycles()).toBe(false);
      
      // Single node graph
      dependencyGraph.addNode('single');
      expect(dependencyGraph.topologicalSort()).toEqual(['single']);
      expect(dependencyGraph.getCriticalPath()).toEqual(['single']);
    });
  });
}); 