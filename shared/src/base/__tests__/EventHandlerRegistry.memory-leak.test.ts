/**
 * @file Event Handler Registry Memory Leak Validation Test Suite
 * @component event-handler-registry-memory-leak-tests
 * @authority-level critical-memory-safety
 * 
 * 🚨 PHASE 3 MEMORY LEAK VALIDATION: Final M0 Blocker Resolution
 * 
 * This test suite validates that EventHandlerRegistry integrations with
 * RealTimeManager and GovernanceRuleEventManager do not cause memory leaks:
 * - Rapid subscription/unsubscription cycles
 * - Client disconnection scenarios  
 * - Handler timeout and orphan cleanup
 * - High-volume concurrent operations
 * - toString() pattern elimination validation
 */

import { EventHandlerRegistry, getEventHandlerRegistry, resetEventHandlerRegistry } from '../EventHandlerRegistry';

// Memory tracking utilities
interface MemorySnapshot {
  totalHandlers: number;
  handlersByClient: Record<string, number>;
  handlersByType: Record<string, number>;
  orphanedHandlers: number;
  heapUsed?: number;
}

function takeMemorySnapshot(registry: EventHandlerRegistry): MemorySnapshot {
  const metrics = registry.getMetrics();
  return {
    totalHandlers: metrics.totalHandlers,
    handlersByClient: { ...metrics.handlersByClient },
    handlersByType: { ...metrics.handlersByType },
    orphanedHandlers: metrics.orphanedHandlers,
    heapUsed: process.memoryUsage().heapUsed
  };
}

function compareMemorySnapshots(before: MemorySnapshot, after: MemorySnapshot): {
  handlersLeaked: number;
  heapGrowth: number;
  hasLeaks: boolean;
} {
  const handlersLeaked = after.totalHandlers - before.totalHandlers;
  const heapGrowth = (after.heapUsed || 0) - (before.heapUsed || 0);
  
  return {
    handlersLeaked,
    heapGrowth,
    hasLeaks: handlersLeaked > 0 || after.orphanedHandlers > 0
  };
}

// Mock console for testing
const mockConsole = {
  log: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn()
};

// Configure Jest timeout for memory tests
jest.setTimeout(30000); // 30 second timeout for memory tests

describe('EventHandlerRegistry Memory Leak Validation', () => {
  let registry: EventHandlerRegistry;

  beforeEach(async () => {
    // Ensure clean state before each test
    await resetEventHandlerRegistry();

    // Reset console mocks
    Object.values(mockConsole).forEach(mock => mock.mockClear());

    // Override console methods for logging tests
    global.console = {
      ...global.console,
      ...mockConsole
    };

    // Force garbage collection if available
    if (global.gc) {
      global.gc();
    }

    // Get registry instance with test configuration
    registry = EventHandlerRegistry.getInstance({
      maxHandlersPerClient: 1000,
      maxGlobalHandlers: 5000,
      orphanDetectionIntervalMs: 100, // Fast detection for testing
      handlerTimeoutMs: 1000 // 1 second timeout for testing
    });

    await registry.initialize();
  });

  afterEach(async () => {
    if (registry) {
      await registry.shutdown();
    }
    await resetEventHandlerRegistry();
    
    // Force garbage collection after each test
    if (global.gc) {
      global.gc();
    }
  });

  describe('Rapid Subscription/Unsubscription Cycles', () => {
    it('should not leak memory during rapid handler registration/unregistration', async () => {
      const initialSnapshot = takeMemorySnapshot(registry);
      const clientId = 'rapid-cycle-client';
      
      // Perform rapid cycles
      for (let cycle = 0; cycle < 100; cycle++) {
        const handlerIds: string[] = [];
        
        // Register handlers
        for (let i = 0; i < 10; i++) {
          const callback = jest.fn();
          const handlerId = registry.registerHandler(
            clientId,
            `rapid-event-${cycle}-${i}`,
            callback,
            { cycle, iteration: i }
          );
          handlerIds.push(handlerId);
        }
        
        // Immediately unregister all handlers
        for (const handlerId of handlerIds) {
          registry.unregisterHandler(handlerId);
        }
      }
      
      // Force garbage collection
      if (global.gc) {
        global.gc();
      }
      
      const finalSnapshot = takeMemorySnapshot(registry);
      const comparison = compareMemorySnapshots(initialSnapshot, finalSnapshot);
      
      // Verify no memory leaks
      expect(comparison.hasLeaks).toBe(false);
      expect(comparison.handlersLeaked).toBe(0);
      expect(finalSnapshot.totalHandlers).toBe(0);
      expect(finalSnapshot.orphanedHandlers).toBe(0);
    });

    it('should handle partial cleanup scenarios without leaks', async () => {
      const initialSnapshot = takeMemorySnapshot(registry);
      const clientId = 'partial-cleanup-client';
      
      // Create handlers and only clean up some
      for (let cycle = 0; cycle < 50; cycle++) {
        const handlerIds: string[] = [];
        
        // Register 10 handlers
        for (let i = 0; i < 10; i++) {
          const callback = jest.fn();
          const handlerId = registry.registerHandler(
            clientId,
            `partial-event-${cycle}-${i}`,
            callback
          );
          handlerIds.push(handlerId);
        }
        
        // Only unregister 8 handlers (leaving 2 per cycle)
        for (let i = 0; i < 8; i++) {
          registry.unregisterHandler(handlerIds[i]);
        }
      }
      
      // Clean up remaining handlers using client cleanup
      const removedCount = registry.unregisterClientHandlers(clientId);
      expect(removedCount).toBe(100); // 50 cycles × 2 remaining handlers
      
      const finalSnapshot = takeMemorySnapshot(registry);
      const comparison = compareMemorySnapshots(initialSnapshot, finalSnapshot);
      
      // Verify complete cleanup
      expect(comparison.hasLeaks).toBe(false);
      expect(finalSnapshot.totalHandlers).toBe(0);
    });
  });

  describe('Client Disconnection Scenarios', () => {
    it('should completely clean up all client handlers on disconnection', async () => {
      const initialSnapshot = takeMemorySnapshot(registry);
      const clients = ['client-1', 'client-2', 'client-3'];
      const allHandlerIds: string[] = [];
      
      // Register handlers for multiple clients
      for (const clientId of clients) {
        for (let i = 0; i < 50; i++) {
          const callback = jest.fn();
          // Add potential circular reference to test cleanup
          (callback as any).clientRef = { clientId, handlerIndex: i };
          
          const handlerId = registry.registerHandler(
            clientId,
            `client-event-${i}`,
            callback,
            { clientId, handlerIndex: i }
          );
          allHandlerIds.push(handlerId);
        }
      }
      
      // Verify handlers are registered
      const midSnapshot = takeMemorySnapshot(registry);
      expect(midSnapshot.totalHandlers).toBe(150); // 3 clients × 50 handlers
      
      // Disconnect all clients
      for (const clientId of clients) {
        const removedCount = registry.unregisterClientHandlers(clientId);
        expect(removedCount).toBe(50);
      }
      
      // Verify complete cleanup
      const finalSnapshot = takeMemorySnapshot(registry);
      const comparison = compareMemorySnapshots(initialSnapshot, finalSnapshot);
      
      expect(comparison.hasLeaks).toBe(false);
      expect(finalSnapshot.totalHandlers).toBe(0);
      
      // Verify individual handlers are gone
      for (const handlerId of allHandlerIds) {
        expect(registry.getHandler(handlerId)).toBeUndefined();
      }
    });

    it('should handle concurrent client disconnections without leaks', async () => {
      const initialSnapshot = takeMemorySnapshot(registry);
      const clientCount = 20;
      const handlersPerClient = 25;
      
      // Register handlers for multiple clients
      const clientPromises = Array(clientCount).fill(null).map(async (_, clientIndex) => {
        const clientId = `concurrent-client-${clientIndex}`;
        const handlerIds: string[] = [];
        
        for (let i = 0; i < handlersPerClient; i++) {
          const callback = jest.fn();
          const handlerId = registry.registerHandler(
            clientId,
            `concurrent-event-${clientIndex}-${i}`,
            callback
          );
          handlerIds.push(handlerId);
        }
        
        return { clientId, handlerIds };
      });
      
      const clientData = await Promise.all(clientPromises);
      
      // Verify all handlers registered
      const midSnapshot = takeMemorySnapshot(registry);
      expect(midSnapshot.totalHandlers).toBe(clientCount * handlersPerClient);
      
      // Disconnect all clients concurrently
      const disconnectPromises = clientData.map(async ({ clientId }) => {
        return registry.unregisterClientHandlers(clientId);
      });
      
      const removedCounts = await Promise.all(disconnectPromises);
      
      // Verify all handlers were removed
      const totalRemoved = removedCounts.reduce((sum, count) => sum + count, 0);
      expect(totalRemoved).toBe(clientCount * handlersPerClient);
      
      const finalSnapshot = takeMemorySnapshot(registry);
      const comparison = compareMemorySnapshots(initialSnapshot, finalSnapshot);
      
      expect(comparison.hasLeaks).toBe(false);
      expect(finalSnapshot.totalHandlers).toBe(0);
    });
  });

  describe('Handler Timeout and Orphan Cleanup', () => {
    it('should detect and clean up orphaned handlers', async () => {
      const clientId = 'orphan-test-client';
      const handlerIds: string[] = [];
      
      // Register handlers
      for (let i = 0; i < 10; i++) {
        const callback = jest.fn();
        const handlerId = registry.registerHandler(
          clientId,
          `orphan-event-${i}`,
          callback
        );
        handlerIds.push(handlerId);
      }
      
      // Manually age some handlers to simulate orphans
      for (let i = 0; i < 5; i++) {
        const handler = registry.getHandler(handlerIds[i]);
        if (handler) {
          // Set lastUsed to 2 seconds ago (beyond 1 second timeout)
          handler.lastUsed = new Date(Date.now() - 2000);
        }
      }
      
      // Verify handlers can be identified as aged
      let agedHandlers = 0;
      for (let i = 0; i < 5; i++) {
        const handler = registry.getHandler(handlerIds[i]);
        if (handler) {
          const age = Date.now() - handler.lastUsed.getTime();
          if (age > 1000) {
            agedHandlers++;
          }
        }
      }
      expect(agedHandlers).toBe(5);
      
      // Clean up all handlers
      const removedCount = registry.unregisterClientHandlers(clientId);
      expect(removedCount).toBe(10);
      
      // Verify complete cleanup
      const finalMetrics = registry.getMetrics();
      expect(finalMetrics.totalHandlers).toBe(0);
    });
  });

  describe('High-Volume Concurrent Operations', () => {
    it('should handle high-volume operations without memory leaks', async () => {
      const initialSnapshot = takeMemorySnapshot(registry);
      const operationCount = 100;
      const handlersPerOperation = 10;
      
      // Perform concurrent registration and unregistration
      const operations = Array(operationCount).fill(null).map(async (_, opIndex) => {
        const clientId = `volume-client-${opIndex}`;
        const handlerIds: string[] = [];
        
        // Register handlers
        for (let i = 0; i < handlersPerOperation; i++) {
          const callback = jest.fn();
          const handlerId = registry.registerHandler(
            clientId,
            `volume-event-${opIndex}-${i}`,
            callback
          );
          handlerIds.push(handlerId);
        }
        
        // Unregister half immediately
        for (let i = 0; i < Math.floor(handlersPerOperation / 2); i++) {
          registry.unregisterHandler(handlerIds[i]);
        }
        
        // Return remaining handler count for cleanup
        return Math.ceil(handlersPerOperation / 2);
      });
      
      const remainingCounts = await Promise.all(operations);
      const expectedRemaining = remainingCounts.reduce((sum, count) => sum + count, 0);
      
      // Verify expected handlers remain
      const midSnapshot = takeMemorySnapshot(registry);
      expect(midSnapshot.totalHandlers).toBe(expectedRemaining);
      
      // Clean up all remaining handlers
      for (let opIndex = 0; opIndex < operationCount; opIndex++) {
        const clientId = `volume-client-${opIndex}`;
        registry.unregisterClientHandlers(clientId);
      }
      
      const finalSnapshot = takeMemorySnapshot(registry);
      const comparison = compareMemorySnapshots(initialSnapshot, finalSnapshot);
      
      expect(comparison.hasLeaks).toBe(false);
      expect(finalSnapshot.totalHandlers).toBe(0);
    });
  });

  describe('Circular Reference Prevention', () => {
    it('should not retain circular references after cleanup', async () => {
      const clientId = 'circular-ref-client';
      const handlerIds: string[] = [];
      
      // Create handlers with potential circular references
      for (let i = 0; i < 20; i++) {
        const callback = jest.fn();
        const metadata = { index: i, callback };
        
        // Create circular reference
        (callback as any).metadata = metadata;
        metadata.callback = callback;
        
        const handlerId = registry.registerHandler(
          clientId,
          `circular-event-${i}`,
          callback,
          metadata
        );
        handlerIds.push(handlerId);
      }
      
      // Verify handlers exist
      expect(registry.getMetrics().totalHandlers).toBe(20);
      
      // Clean up all handlers
      const removedCount = registry.unregisterClientHandlers(clientId);
      expect(removedCount).toBe(20);
      
      // Verify complete cleanup
      const finalMetrics = registry.getMetrics();
      expect(finalMetrics.totalHandlers).toBe(0);
      
      // Verify handlers are truly gone (no circular references retained)
      for (const handlerId of handlerIds) {
        expect(registry.getHandler(handlerId)).toBeUndefined();
      }
    });
  });

  describe('toString() Pattern Elimination Validation', () => {
    it('should use deterministic handler IDs instead of toString() patterns', async () => {
      const clientId = 'deterministic-test-client';
      const callbacks: Array<() => void> = [];
      const handlerIds: string[] = [];

      // Create multiple handlers with identical function signatures
      for (let i = 0; i < 5; i++) {
        const callback = () => { /* identical function body */ };
        callbacks.push(callback);

        const handlerId = registry.registerHandler(
          clientId,
          'deterministic-event',
          callback as any
        );
        handlerIds.push(handlerId);
      }

      // Verify all handlers have unique IDs despite identical toString() output
      const uniqueIds = new Set(handlerIds);
      expect(uniqueIds.size).toBe(5);

      // Verify each handler can be individually retrieved and removed
      for (let i = 0; i < handlerIds.length; i++) {
        const handlerId = handlerIds[i];
        const handler = registry.getHandler(handlerId);
        expect(handler).toBeDefined();
        expect(handler!.callback).toBe(callbacks[i]);

        // Remove specific handler
        const removed = registry.unregisterHandler(handlerId);
        expect(removed).toBe(true);

        // Verify it's gone
        expect(registry.getHandler(handlerId)).toBeUndefined();
      }

      // Verify all handlers removed
      expect(registry.getMetrics().totalHandlers).toBe(0);
    });

    it('should handle function references without relying on string representation', async () => {
      const clientId = 'function-ref-client';

      // Create anonymous functions that would have identical toString() output
      const createIdenticalFunction = () => () => console.log('test');

      const callback1 = createIdenticalFunction();
      const callback2 = createIdenticalFunction();

      // Verify functions have identical string representation but are different objects
      expect(callback1.toString()).toBe(callback2.toString());
      expect(callback1).not.toBe(callback2);

      // Register both handlers
      const handlerId1 = registry.registerHandler(clientId, 'test-event', callback1 as any);
      const handlerId2 = registry.registerHandler(clientId, 'test-event', callback2 as any);

      // Verify both are registered with unique IDs
      expect(handlerId1).not.toBe(handlerId2);
      expect(registry.getMetrics().totalHandlers).toBe(2);

      // Verify we can remove them individually
      expect(registry.unregisterHandler(handlerId1)).toBe(true);
      expect(registry.getMetrics().totalHandlers).toBe(1);

      expect(registry.unregisterHandler(handlerId2)).toBe(true);
      expect(registry.getMetrics().totalHandlers).toBe(0);
    });
  });

  describe('Memory Metrics Accuracy Validation', () => {
    it('should accurately track memory state in metrics', async () => {
      const clientId = 'metrics-accuracy-client';
      const handlerIds: string[] = [];

      // Register handlers and verify metrics accuracy
      for (let i = 0; i < 25; i++) {
        const callback = jest.fn();
        const handlerId = registry.registerHandler(
          clientId,
          `metrics-event-${i}`,
          callback
        );
        handlerIds.push(handlerId);

        // Verify metrics are accurate after each registration
        const metrics = registry.getMetrics();
        expect(metrics.totalHandlers).toBe(i + 1);
        expect(metrics.handlersByClient[clientId]).toBe(i + 1);
      }

      // Remove handlers one by one and verify metrics
      for (let i = 0; i < handlerIds.length; i++) {
        registry.unregisterHandler(handlerIds[i]);

        const metrics = registry.getMetrics();
        const expectedRemaining = handlerIds.length - (i + 1);
        expect(metrics.totalHandlers).toBe(expectedRemaining);

        if (expectedRemaining === 0) {
          expect(metrics.handlersByClient[clientId]).toBeUndefined();
        } else {
          expect(metrics.handlersByClient[clientId]).toBe(expectedRemaining);
        }
      }

      // Final verification
      const finalMetrics = registry.getMetrics();
      expect(finalMetrics.totalHandlers).toBe(0);
      expect(Object.keys(finalMetrics.handlersByClient)).toHaveLength(0);
    });
  });
});
