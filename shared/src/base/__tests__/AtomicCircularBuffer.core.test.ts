/**
 * @file AtomicCircularBuffer Core Test Suite
 * @component atomic-circular-buffer-core-tests
 * @authority-level critical-memory-safety-testing
 * @governance-adr ADR-security-002-atomic-operations-testing
 */

// Configure Jest timeout
jest.setTimeout(10000); // 10 second timeout

// CRITICAL: Rely on global Jest setup mocking (jest.setup.js) for timer and module mocking
// This ensures mocks are applied before ANY module loading occurs (Lesson 04 pattern)

// Mock timer functions to prevent real timer creation
const mockSetInterval = jest.fn((callback: Function, ms: number) => {
  console.log(`[MOCK] setInterval called with ${ms}ms - MOCKED, NO REAL INTERVAL CREATED`);
  return 'mock-interval-id';
});

const mockClearInterval = jest.fn((id: any) => {
  console.log(`[MOCK] clearInterval called for ${id} - MOCKED`);
});

const mockSetTimeout = jest.fn((callback: Function, ms: number) => {
  console.log(`[MOCK] setTimeout called with ${ms}ms - MOCKED, NO REAL TIMEOUT CREATED`);
  return 'mock-timeout-id';
});

const mockClearTimeout = jest.fn((id: any) => {
  console.log(`[MOCK] clearTimeout called for ${id} - MOCKED`);
});

// Apply global mocks
(global as any).setInterval = mockSetInterval;
(global as any).clearInterval = mockClearInterval;
(global as any).setTimeout = mockSetTimeout;
(global as any).clearTimeout = mockClearTimeout;

// CRITICAL: Mock MemorySafeResourceManager to eliminate ALL resource allocation (Lesson 04)
jest.mock('../MemorySafeResourceManager', () => {
  // Track mock calls for debugging
  const mockCalls = {
    createSafeInterval: jest.fn((_callback?: any, _intervalMs?: number, _name?: string) => 'mock-interval-id'),
    createSafeTimeout: jest.fn((_callback?: any, _timeoutMs?: number, _name?: string) => 'mock-timeout-id'),
    clearSafeInterval: jest.fn((_id: string) => {}),
    clearSafeTimeout: jest.fn((_id: string) => {})
  };

  class MockMemorySafeResourceManager {
    protected _isInitialized = false;
    protected _isShuttingDown = false;
    protected _limits: any;
    protected _intervals = new Map();
    protected _timeouts = new Map();

    constructor(limits?: any) {
      // CRITICAL: Completely empty constructor - no side effects (Lesson 04 pattern)
      this._limits = limits || {};
      console.log('[MOCK] MemorySafeResourceManager constructor called - NO RESOURCES CREATED');
    }

    // CRITICAL: Mock initialization to prevent resource creation (Lesson 04 pattern)
    protected async doInitialize(): Promise<void> {
      console.log('[MOCK] doInitialize called - MOCKED, NO REAL INITIALIZATION');
      this._isInitialized = true;
    }

    protected async doShutdown(): Promise<void> {
      console.log('[MOCK] doShutdown called - MOCKED, NO REAL SHUTDOWN');
      this._isShuttingDown = true;
    }

    public async initialize(): Promise<void> {
      console.log('[MOCK] initialize called - MOCKED');
      await this.doInitialize();
    }

    public async shutdown(): Promise<void> {
      console.log('[MOCK] shutdown called - MOCKED');
      this._isShuttingDown = true;
      await this.doShutdown();
    }

    public isHealthy(): boolean {
      console.log(`[MOCK] isHealthy called - shutting down: ${this._isShuttingDown}, returning: ${!this._isShuttingDown}`);
      return !this._isShuttingDown;
    }

    public getResourceMetrics(): any {
      console.log('[MOCK] getResourceMetrics called - returning mock metrics');
      return {
        totalResources: 0,
        intervals: 0,
        timeouts: 0,
        cacheSize: 0,
        connections: 0
      };
    }

    // CRITICAL: Mock ALL resource creation methods (Lesson 04 pattern)
    protected createSafeInterval(callback?: any, intervalMs?: number, name?: string): string {
      console.log(`[MOCK] createSafeInterval called with ${intervalMs}ms interval - MOCKED, NO REAL INTERVAL CREATED`);
      return mockCalls.createSafeInterval(callback, intervalMs, name);
    }

    protected createSafeTimeout(callback?: any, timeoutMs?: number, name?: string): string {
      console.log(`[MOCK] createSafeTimeout called with ${timeoutMs}ms timeout - MOCKED, NO REAL TIMEOUT CREATED`);
      return mockCalls.createSafeTimeout(callback, timeoutMs, name);
    }

    protected clearSafeInterval(id: string): void {
      console.log(`[MOCK] clearSafeInterval called for ${id} - MOCKED`);
      mockCalls.clearSafeInterval(id);
    }

    protected clearSafeTimeout(id: string): void {
      console.log(`[MOCK] clearSafeTimeout called for ${id} - MOCKED`);
      mockCalls.clearSafeTimeout(id);
    }

    // CRITICAL: Mock static methods (Lesson 05 pattern)
    public static forceGlobalCleanup(): void {
      console.log('[MOCK] MemorySafeResourceManager.forceGlobalCleanup() called - MOCKED');
    }

    // Expose mock calls for testing
    public static getMockCalls() {
      return mockCalls;
    }
  }

  return {
    MemorySafeResourceManager: MockMemorySafeResourceManager,
    // Export mock calls for verification
    __mockCalls: mockCalls
  };
});

// Mock console methods for logging tests
const mockConsole = {
  log: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn()
};

// CRITICAL FIX: Mock LoggingMixin with functional logger for console testing (Lesson 04)
jest.mock('../LoggingMixin', () => {
  return {
    SimpleLogger: class MockSimpleLogger {
      private _name: string;

      constructor(name: string) {
        this._name = name;
      }

      logInfo(message: string, details?: Record<string, unknown>) {
        // Call the mocked console.log for testing
        mockConsole.log(`[INFO] ${this._name}: ${message}`, details || '');
      }

      logWarning(message: string, details?: Record<string, unknown>) {
        // Call the mocked console.warn for testing
        mockConsole.warn(`[WARNING] ${this._name}: ${message}`, details || '');
      }

      logError(message: string, error: unknown, details?: Record<string, unknown>) {
        // CRITICAL FIX: Format error message consistently with real SimpleLogger
        const errorMessage = error instanceof Error ? error.message : String(error);
        mockConsole.error(`[ERROR] ${this._name}: ${message} - ${errorMessage}`, details || '');
      }

      logDebug(message: string, details?: Record<string, unknown>) {
        // Only log in development environment
        if (process.env.NODE_ENV === 'development') {
          mockConsole.debug(`[DEBUG] ${this._name}: ${message}`, details || '');
        }
      }
    },
    ILoggingService: {} // Interface export
  };
});

console.log('[TEST] AtomicCircularBuffer.core.test.ts starting - relying on global mocks');

// Import AFTER global mocks are established (Lesson 04 pattern)
import { AtomicCircularBuffer } from '../AtomicCircularBuffer';

describe('AtomicCircularBuffer - Core Functionality', () => {
  let buffer: AtomicCircularBuffer<string>;
  const maxSize = 5;

  beforeEach(async () => {
    console.log('[TEST] Creating new AtomicCircularBuffer for test');
    buffer = new AtomicCircularBuffer<string>(maxSize);
    await buffer.initialize();
    console.log('[TEST] AtomicCircularBuffer initialized successfully');
  });

  afterEach(async () => {
    console.log('[TEST] Cleaning up AtomicCircularBuffer');
    if (buffer) {
      try {
        await buffer.shutdown();
        console.log('[TEST] AtomicCircularBuffer shutdown completed');
      } catch (error) {
        console.warn('[TEST] Error during buffer shutdown:', error);
      }
    }
  }, 10000); // 10 second timeout for cleanup

  describe('Constructor and Initialization', () => {
    it('should create buffer with valid max size', () => {
      const testBuffer = new AtomicCircularBuffer<string>(10);
      expect(testBuffer).toBeInstanceOf(AtomicCircularBuffer);
      expect(testBuffer.getSize()).toBe(0);
    });

    it('should initialize with empty state', () => {
      expect(buffer.getSize()).toBe(0);
      expect(buffer.getAllItems().size).toBe(0);
    });

    it('should handle zero max size', () => {
      const zeroBuffer = new AtomicCircularBuffer<string>(0);
      expect(zeroBuffer.getSize()).toBe(0);
    });

    it('should handle large max size', () => {
      const largeBuffer = new AtomicCircularBuffer<string>(10000);
      expect(largeBuffer.getSize()).toBe(0);
    });
  });

  describe('Basic Add Operations', () => {
    it('should add single item correctly', async () => {
      await buffer.addItem('key1', 'value1');
      expect(buffer.getSize()).toBe(1);
      expect(buffer.getItem('key1')).toBe('value1');
    });

    it('should add multiple items correctly', async () => {
      await buffer.addItem('key1', 'value1');
      await buffer.addItem('key2', 'value2');
      await buffer.addItem('key3', 'value3');
      
      expect(buffer.getSize()).toBe(3);
      expect(buffer.getItem('key1')).toBe('value1');
      expect(buffer.getItem('key2')).toBe('value2');
      expect(buffer.getItem('key3')).toBe('value3');
    });

    it('should handle duplicate keys by updating values', async () => {
      await buffer.addItem('key1', 'value1');
      await buffer.addItem('key1', 'updated_value1');
      
      expect(buffer.getSize()).toBe(1);
      expect(buffer.getItem('key1')).toBe('updated_value1');
    });

    it('should handle empty string keys', async () => {
      await buffer.addItem('', 'empty_key_value');
      expect(buffer.getItem('')).toBe('empty_key_value');
    });

    it('should handle special character keys', async () => {
      const specialKeys = ['key with spaces', 'key-with-dashes', 'key_with_underscores', 'key.with.dots'];
      
      for (const key of specialKeys) {
        await buffer.addItem(key, `value_for_${key}`);
        expect(buffer.getItem(key)).toBe(`value_for_${key}`);
      }
    });
  });

  describe('Basic Remove Operations', () => {
    beforeEach(async () => {
      // Add some test data
      await buffer.addItem('key1', 'value1');
      await buffer.addItem('key2', 'value2');
      await buffer.addItem('key3', 'value3');
    });

    it('should remove existing item correctly', async () => {
      const removed = await buffer.removeItem('key2');
      expect(removed).toBe(true);
      expect(buffer.getSize()).toBe(2);
      expect(buffer.getItem('key2')).toBeUndefined();
    });

    it('should return false for non-existent key', async () => {
      const removed = await buffer.removeItem('nonexistent');
      expect(removed).toBe(false);
      expect(buffer.getSize()).toBe(3); // Size should remain unchanged
    });

    it('should handle removing all items', async () => {
      await buffer.removeItem('key1');
      await buffer.removeItem('key2');
      await buffer.removeItem('key3');
      
      expect(buffer.getSize()).toBe(0);
      expect(buffer.getAllItems().size).toBe(0);
    });
  });

  describe('Basic Get Operations', () => {
    beforeEach(async () => {
      await buffer.addItem('key1', 'value1');
      await buffer.addItem('key2', 'value2');
    });

    it('should retrieve existing items correctly', () => {
      expect(buffer.getItem('key1')).toBe('value1');
      expect(buffer.getItem('key2')).toBe('value2');
    });

    it('should return undefined for non-existent keys', () => {
      expect(buffer.getItem('nonexistent')).toBeUndefined();
      expect(buffer.getItem('')).toBeUndefined();
      expect(buffer.getItem('null')).toBeUndefined();
    });

    it('should handle getAllItems correctly', () => {
      const allItems = buffer.getAllItems();
      expect(allItems).toBeInstanceOf(Map);
      expect(allItems.size).toBe(2);
      expect(allItems.get('key1')).toBe('value1');
      expect(allItems.get('key2')).toBe('value2');
    });

    it('should return defensive copy of all items', () => {
      const allItems1 = buffer.getAllItems();
      const allItems2 = buffer.getAllItems();
      
      expect(allItems1).not.toBe(allItems2); // Different instances
      expect(allItems1.size).toBe(allItems2.size);
    });
  });

  describe('Size Management', () => {
    it('should track size accurately', async () => {
      expect(buffer.getSize()).toBe(0);
      
      await buffer.addItem('key1', 'value1');
      expect(buffer.getSize()).toBe(1);
      
      await buffer.addItem('key2', 'value2');
      expect(buffer.getSize()).toBe(2);
      
      await buffer.removeItem('key1');
      expect(buffer.getSize()).toBe(1);
    });

    it('should enforce maximum size limits', async () => {
      // Add items up to max size
      for (let i = 0; i < maxSize; i++) {
        await buffer.addItem(`key${i}`, `value${i}`);
      }
      expect(buffer.getSize()).toBe(maxSize);
      
      // Add one more item - should trigger circular behavior
      await buffer.addItem('overflow', 'overflow_value');
      expect(buffer.getSize()).toBe(maxSize); // Size should not exceed maximum
    });

    it('should handle size correctly with duplicate keys', async () => {
      await buffer.addItem('key1', 'value1');
      expect(buffer.getSize()).toBe(1);
      
      await buffer.addItem('key1', 'updated_value1');
      expect(buffer.getSize()).toBe(1); // Size should remain the same
    });
  });

  describe('Circular Buffer Behavior', () => {
    it('should implement circular behavior when full', async () => {
      // Fill buffer to capacity
      for (let i = 0; i < maxSize; i++) {
        await buffer.addItem(`key${i}`, `value${i}`);
      }
      
      // Add one more item
      await buffer.addItem('overflow', 'overflow_value');
      
      // First item should be removed (circular behavior)
      expect(buffer.getItem('key0')).toBeUndefined();
      expect(buffer.getItem('overflow')).toBe('overflow_value');
      expect(buffer.getSize()).toBe(maxSize);
    });

    it('should maintain insertion order in circular behavior', async () => {
      // Fill buffer
      for (let i = 0; i < maxSize; i++) {
        await buffer.addItem(`key${i}`, `value${i}`);
      }
      
      // Add more items to trigger multiple circular removals
      await buffer.addItem('overflow1', 'overflow_value1');
      await buffer.addItem('overflow2', 'overflow_value2');
      
      // Check that oldest items are removed
      expect(buffer.getItem('key0')).toBeUndefined();
      expect(buffer.getItem('key1')).toBeUndefined();
      expect(buffer.getItem('overflow1')).toBe('overflow_value1');
      expect(buffer.getItem('overflow2')).toBe('overflow_value2');
    });
  });

  describe('Resource Management Integration', () => {
    it('should integrate with MemorySafeResourceManager', () => {
      expect(buffer.isHealthy()).toBe(true);
      
      const metrics = buffer.getResourceMetrics();
      expect(metrics).toBeDefined();
      expect(typeof metrics.totalResources).toBe('number');
    });

    it('should handle initialization and shutdown properly', async () => {
      const testBuffer = new AtomicCircularBuffer<string>(5);
      
      // Should be able to initialize
      await expect(testBuffer.initialize()).resolves.not.toThrow();
      expect(testBuffer.isHealthy()).toBe(true);
      
      // Should be able to shutdown
      await expect(testBuffer.shutdown()).resolves.not.toThrow();
    });
  });

  describe('Basic Metrics', () => {
    it('should initialize metrics with zero values', () => {
      const metrics = buffer.getMetrics();
      expect(metrics.totalOperations).toBe(0);
      expect(metrics.addOperations).toBe(0);
      expect(metrics.removeOperations).toBe(0);
    });

    it('should track basic operations in metrics', async () => {
      await buffer.addItem('key1', 'value1');
      await buffer.addItem('key2', 'value2');
      await buffer.removeItem('key1');
      
      const metrics = buffer.getMetrics();
      expect(metrics.addOperations).toBe(2);
      expect(metrics.removeOperations).toBe(1);
      expect(metrics.totalOperations).toBe(3);
    });
  });
});
