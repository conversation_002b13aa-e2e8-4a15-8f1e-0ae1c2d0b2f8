/**
 * ============================================================================
 * JestCompatibilityUtils Test Suite
 * Comprehensive testing for Jest compatibility and test environment utilities
 * ============================================================================
 */

import {
  JestCompatibilityUtils,
  IJestCompatibilityConfig,
  IPerformanceConfig,
  jestCompatibleYield,
  jestCompatibleDelay,
  performanceOptimizedExecution
} from '../../utils/JestCompatibilityUtils';

describe('JestCompatibilityUtils', () => {
  // Store original environment variables
  const originalEnv = process.env;

  beforeEach(() => {
    // Reset configuration before each test
    JestCompatibilityUtils.configure({});
    JestCompatibilityUtils.configurePerformance({});
    
    // Reset environment variables
    process.env = { ...originalEnv };
    
    // Clear console mocks
    jest.clearAllMocks();
  });

  afterAll(() => {
    // Restore original environment
    process.env = originalEnv;
  });

  describe('Configuration Management', () => {
    it('should configure Jest compatibility settings', () => {
      const config: Partial<IJestCompatibilityConfig> = {
        forceTestMode: true,
        maxDelaySteps: 50,
        baseStepDuration: 5
      };

      JestCompatibilityUtils.configure(config);
      
      const currentConfig = JestCompatibilityUtils.getConfiguration();
      expect(currentConfig.jestConfig.forceTestMode).toBe(true);
      expect(currentConfig.jestConfig.maxDelaySteps).toBe(50);
      expect(currentConfig.jestConfig.baseStepDuration).toBe(5);
    });

    it('should configure performance settings', () => {
      const perfConfig: Partial<IPerformanceConfig> = {
        testModeMultiplier: 0.05,
        productionModeMultiplier: 1.5,
        maxExecutionTime: 200
      };

      JestCompatibilityUtils.configurePerformance(perfConfig);
      
      const currentConfig = JestCompatibilityUtils.getConfiguration();
      expect(currentConfig.performanceConfig.testModeMultiplier).toBe(0.05);
      expect(currentConfig.performanceConfig.productionModeMultiplier).toBe(1.5);
      expect(currentConfig.performanceConfig.maxExecutionTime).toBe(200);
    });

    it('should merge configuration with defaults', () => {
      JestCompatibilityUtils.configure({ forceTestMode: true });
      
      const config = JestCompatibilityUtils.getConfiguration();
      expect(config.jestConfig.forceTestMode).toBe(true);
      expect(config.jestConfig.maxDelaySteps).toBe(100); // Default value
      expect(config.jestConfig.baseStepDuration).toBe(10); // Default value
    });
  });

  describe('Environment Detection', () => {
    it('should detect test environment through NODE_ENV', () => {
      process.env.NODE_ENV = 'test';
      expect(JestCompatibilityUtils.isTestEnvironment()).toBe(true);
    });

    it('should detect test environment through JEST_WORKER_ID', () => {
      process.env.NODE_ENV = 'production';
      process.env.JEST_WORKER_ID = '1';
      expect(JestCompatibilityUtils.isTestEnvironment()).toBe(true);
    });

    it('should force test mode when configured', () => {
      JestCompatibilityUtils.configure({ forceTestMode: true });
      expect(JestCompatibilityUtils.isTestEnvironment()).toBe(true);
    });

    it('should handle configuration correctly', () => {
      JestCompatibilityUtils.configure({ forceTestMode: false });
      const config = JestCompatibilityUtils.getConfiguration();
      expect(config.jestConfig.forceTestMode).toBe(false);
    });
  });

  describe('Compatible Delay Function', () => {
    it('should complete quickly in test environment', async () => {
      const startTime = Date.now();
      await JestCompatibilityUtils.compatibleDelay(1000);
      const duration = Date.now() - startTime;
      expect(duration).toBeLessThan(100);
    });

    it('should handle zero delays', async () => {
      await expect(JestCompatibilityUtils.compatibleDelay(0)).resolves.not.toThrow();
    });

    it('should handle negative delays', async () => {
      await expect(JestCompatibilityUtils.compatibleDelay(-100)).resolves.not.toThrow();
    });
  });

  describe('Performance Optimization', () => {
    it('should calculate test mode steps correctly', () => {
      JestCompatibilityUtils.configurePerformance({ testModeMultiplier: 0.1 });
      const steps = JestCompatibilityUtils.calculateTestModeSteps(1000);
      expect(steps).toBe(100);
    });

    it('should respect maxDelaySteps in step calculation', () => {
      JestCompatibilityUtils.configure({ maxDelaySteps: 50 });
      JestCompatibilityUtils.configurePerformance({ testModeMultiplier: 0.2 });
      const steps = JestCompatibilityUtils.calculateTestModeSteps(1000);
      expect(steps).toBe(50);
    });

    it('should handle batched async yield', async () => {
      await expect(JestCompatibilityUtils.batchedAsyncYield(25, 5)).resolves.not.toThrow();
    });
  });

  describe('Performance Optimized Execution', () => {
    let consoleWarnSpy: jest.SpyInstance;
    let consoleErrorSpy: jest.SpyInstance;

    beforeEach(() => {
      consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation();
      consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();
    });

    afterEach(() => {
      consoleWarnSpy.mockRestore();
      consoleErrorSpy.mockRestore();
    });

    it('should execute operation successfully', async () => {
      const mockOperation = jest.fn().mockResolvedValue('test-result');
      const result = await JestCompatibilityUtils.performanceOptimizedExecution(mockOperation, 'test-operation');
      
      expect(result).toBe('test-result');
      expect(mockOperation).toHaveBeenCalledTimes(1);
    });

    it('should handle operation errors', async () => {
      const failingOperation = jest.fn().mockRejectedValue(new Error('Operation failed'));
      
      await expect(
        JestCompatibilityUtils.performanceOptimizedExecution(failingOperation, 'failing-operation')
      ).rejects.toThrow('Operation failed');
      
      expect(consoleErrorSpy).toHaveBeenCalled();
    });
  });

  describe('Compatible Timeout', () => {
    it('should resolve immediately in test environment', async () => {
      const startTime = Date.now();
      await JestCompatibilityUtils.createCompatibleTimeout(5000);
      const duration = Date.now() - startTime;
      expect(duration).toBeLessThan(10);
    });

    it('should return a Promise', () => {
      const timeoutPromise = JestCompatibilityUtils.createCompatibleTimeout(1000);
      expect(timeoutPromise).toBeInstanceOf(Promise);
    });
  });

  describe('Configuration Retrieval', () => {
    it('should return complete configuration object', () => {
      JestCompatibilityUtils.configure({ forceTestMode: true, maxDelaySteps: 75 });
      JestCompatibilityUtils.configurePerformance({ testModeMultiplier: 0.2 });
      
      const config = JestCompatibilityUtils.getConfiguration();
      
      expect(config.jestConfig.forceTestMode).toBe(true);
      expect(config.jestConfig.maxDelaySteps).toBe(75);
      expect(config.performanceConfig.testModeMultiplier).toBe(0.2);
      expect(typeof config.isTestEnvironment).toBe('boolean');
    });

    it('should return defensive copies of configuration', () => {
      const config1 = JestCompatibilityUtils.getConfiguration();
      const config2 = JestCompatibilityUtils.getConfiguration();
      
      config1.jestConfig.forceTestMode = true;
      expect(config2.jestConfig.forceTestMode).toBe(false);
    });
  });

  describe('Convenience Functions', () => {
    it('should provide jestCompatibleYield function', async () => {
      await expect(jestCompatibleYield()).resolves.not.toThrow();
    });

    it('should provide jestCompatibleDelay function', async () => {
      const startTime = Date.now();
      await jestCompatibleDelay(500);
      const duration = Date.now() - startTime;
      expect(duration).toBeLessThan(50);
    });

    it('should provide performanceOptimizedExecution function', async () => {
      const mockOperation = jest.fn().mockResolvedValue('convenience-result');
      const result = await performanceOptimizedExecution(mockOperation, 'convenience-test');
      
      expect(result).toBe('convenience-result');
      expect(mockOperation).toHaveBeenCalledTimes(1);
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle edge cases gracefully', async () => {
      await expect(JestCompatibilityUtils.compatibleDelay(0)).resolves.not.toThrow();
      await expect(JestCompatibilityUtils.batchedAsyncYield(0, 5)).resolves.not.toThrow();
      
      expect(JestCompatibilityUtils.calculateTestModeSteps(0)).toBeGreaterThanOrEqual(0);
      expect(JestCompatibilityUtils.calculateTestModeSteps(-100)).toBeGreaterThanOrEqual(0);
    });

    it('should handle zero batch size', async () => {
      await expect(JestCompatibilityUtils.batchedAsyncYield(10, 0)).resolves.not.toThrow();
    });
  });

  describe('Integration', () => {
    it('should work in realistic scenarios', async () => {
      JestCompatibilityUtils.configure({ baseStepDuration: 5, maxDelaySteps: 20 });
      JestCompatibilityUtils.configurePerformance({ testModeMultiplier: 0.1 });

      const operation = async () => {
        await JestCompatibilityUtils.compatibleDelay(50);
        await jestCompatibleYield();
        return 'integration-result';
      };

      const result = await JestCompatibilityUtils.performanceOptimizedExecution(operation, 'integration-test');
      expect(result).toBe('integration-result');
    });
  });
}); 