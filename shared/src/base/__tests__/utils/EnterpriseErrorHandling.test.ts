/**
 * ============================================================================
 * EnterpriseErrorHandling Test Suite
 * Comprehensive testing for enterprise-grade error handling patterns
 * ============================================================================
 */

import {
  CircuitBreaker,
  CircuitBreakerState,
  EnterpriseErrorHandler,
  ICircuitBreakerMetrics,
  IEnterpriseErrorMetrics,
  IEnterpriseOperationContext,
  IEnterpriseRetryConfig,
  IErrorClassification,
  IEnterpriseRetryResult
} from '../../utils/EnterpriseErrorHandling';

// Mock JestCompatibilityUtils to control delays in tests
jest.mock('../../utils/JestCompatibilityUtils', () => ({
  JestCompatibilityUtils: {
    compatibleDelay: jest.fn().mockResolvedValue(undefined)
  }
}));

describe('EnterpriseErrorHandling', () => {
  beforeEach(() => {
    // Reset all state before each test
    EnterpriseErrorHandler.clearAllState();
    jest.clearAllMocks();
  });

  describe('CircuitBreaker', () => {
    let circuitBreaker: CircuitBreaker;

    beforeEach(() => {
      circuitBreaker = new CircuitBreaker({
        circuitBreakerThreshold: 3,
        circuitBreakerTimeoutMs: 1000,
        circuitBreakerRecoveryAttempts: 2
      });
    });

    describe('State Management', () => {
      it('should start in CLOSED state', () => {
        expect(circuitBreaker.canExecute()).toBe(true);
        
        const metrics = circuitBreaker.getMetrics();
        expect(metrics.state).toBe(CircuitBreakerState.CLOSED);
        expect(metrics.failureCount).toBe(0);
        expect(metrics.successCount).toBe(0);
      });

      it('should transition to OPEN state after threshold failures', () => {
        // Record failures up to threshold
        circuitBreaker.recordFailure();
        circuitBreaker.recordFailure();
        expect(circuitBreaker.canExecute()).toBe(true);
        
        // Third failure should open circuit
        circuitBreaker.recordFailure();
        expect(circuitBreaker.canExecute()).toBe(false);
        
        const metrics = circuitBreaker.getMetrics();
        expect(metrics.state).toBe(CircuitBreakerState.OPEN);
        expect(metrics.failureCount).toBe(3);
      });

      it('should transition to HALF_OPEN after timeout', () => {
        // Open circuit with failures
        circuitBreaker.recordFailure();
        circuitBreaker.recordFailure();
        circuitBreaker.recordFailure();
        expect(circuitBreaker.canExecute()).toBe(false);
        
        // Mock timeout passage by manipulating the circuit breaker's internal state
        // We need to simulate time passage for the timeout check
        const perfNowSpy = jest.spyOn(performance, 'now');
        
        // First call - record failure time (0)
        perfNowSpy.mockReturnValueOnce(0);
        circuitBreaker.recordFailure(); // This sets the lastFailureTime
        
        // Second call - check if timeout has passed (simulate 2000ms later)
        perfNowSpy.mockReturnValueOnce(2000);
        
        expect(circuitBreaker.canExecute()).toBe(true);
        const metrics = circuitBreaker.getMetrics();
        expect(metrics.state).toBe(CircuitBreakerState.HALF_OPEN);
        
        perfNowSpy.mockRestore();
      });

      it('should transition from HALF_OPEN to CLOSED after successful recovery', () => {
        // Set up OPEN state
        circuitBreaker.recordFailure();
        circuitBreaker.recordFailure();
        circuitBreaker.recordFailure();
        
        // Mock transition to HALF_OPEN
        const perfNowSpy = jest.spyOn(performance, 'now');
        perfNowSpy.mockReturnValueOnce(0); // failure time
        circuitBreaker.recordFailure(); // This sets lastFailureTime
        perfNowSpy.mockReturnValueOnce(2000); // timeout check
        
        circuitBreaker.canExecute(); // Transition to HALF_OPEN
        
        // Record successful recovery attempts (need 2 based on config)
        circuitBreaker.recordSuccess();
        circuitBreaker.recordSuccess();
        
        const metrics = circuitBreaker.getMetrics();
        expect(metrics.state).toBe(CircuitBreakerState.CLOSED);
        expect(metrics.failureCount).toBe(0);
        
        perfNowSpy.mockRestore();
      });

      it('should transition from HALF_OPEN back to OPEN on failure', () => {
        // Set up OPEN state
        circuitBreaker.recordFailure();
        circuitBreaker.recordFailure();
        circuitBreaker.recordFailure();
        
        // Mock transition to HALF_OPEN
        const perfNowSpy = jest.spyOn(performance, 'now');
        perfNowSpy.mockReturnValueOnce(0);
        circuitBreaker.recordFailure();
        perfNowSpy.mockReturnValueOnce(2000);
        
        circuitBreaker.canExecute(); // Transition to HALF_OPEN
        
        // Record failure during recovery
        circuitBreaker.recordFailure();
        
        const metrics = circuitBreaker.getMetrics();
        expect(metrics.state).toBe(CircuitBreakerState.OPEN);
        expect(circuitBreaker.canExecute()).toBe(false);
        
        perfNowSpy.mockRestore();
      });
    });

    describe('Metrics Tracking', () => {
      it('should track failure and success rates accurately', () => {
        // Record mixed results
        circuitBreaker.recordSuccess();
        circuitBreaker.recordSuccess();
        circuitBreaker.recordFailure();
        circuitBreaker.recordSuccess();
        
        const metrics = circuitBreaker.getMetrics();
        expect(metrics.totalRequests).toBe(4);
        expect(metrics.successCount).toBe(3);
        expect(metrics.failureCount).toBe(1);
        expect(metrics.failureRate).toBe(0.25);
      });

      it('should update timestamps correctly', () => {
        const mockTime = 12345;
        jest.spyOn(global.performance, 'now').mockReturnValue(mockTime);
        
        circuitBreaker.recordSuccess();
        let metrics = circuitBreaker.getMetrics();
        expect(metrics.lastSuccessTime).toBe(mockTime);
        
        circuitBreaker.recordFailure();
        metrics = circuitBreaker.getMetrics();
        expect(metrics.lastFailureTime).toBe(mockTime);
      });
    });

    describe('Reset Functionality', () => {
      it('should reset all metrics and state', () => {
        circuitBreaker.recordFailure();
        circuitBreaker.recordFailure();
        circuitBreaker.recordFailure();
        
        circuitBreaker.reset();
        
        const metrics = circuitBreaker.getMetrics();
        expect(metrics.state).toBe(CircuitBreakerState.CLOSED);
        expect(metrics.failureCount).toBe(0);
        expect(metrics.successCount).toBe(0);
        expect(metrics.totalRequests).toBe(0);
        expect(circuitBreaker.canExecute()).toBe(true);
      });
    });
  });

  describe('Error Classification', () => {
    it('should classify transient errors correctly', () => {
      const error = new Error('Connection timeout occurred');
      const classification = EnterpriseErrorHandler.classifyError(error);
      
      expect(classification.type).toBe('transient');
      expect(classification.retryable).toBe(true);
      expect(classification.severity).toBe('medium');
      expect(classification.recoveryStrategy).toBe('retry');
      expect(classification.recommendedDelay).toBe(1000);
    });

    it('should classify resource errors correctly', () => {
      const error = new Error('Memory limit exceeded');
      const classification = EnterpriseErrorHandler.classifyError(error);
      
      expect(classification.type).toBe('resource');
      expect(classification.retryable).toBe(true);
      expect(classification.severity).toBe('high');
      expect(classification.recoveryStrategy).toBe('circuit-breaker');
      expect(classification.recommendedDelay).toBe(5000);
    });

    it('should classify authentication errors correctly', () => {
      const error = new Error('Unauthorized access token');
      const classification = EnterpriseErrorHandler.classifyError(error);
      
      expect(classification.type).toBe('authentication');
      expect(classification.retryable).toBe(false);
      expect(classification.severity).toBe('critical');
      expect(classification.recoveryStrategy).toBe('escalate');
      expect(classification.recommendedDelay).toBe(0);
    });

    it('should classify permanent errors correctly', () => {
      const error = new Error('Invalid request format');
      const classification = EnterpriseErrorHandler.classifyError(error);
      
      expect(classification.type).toBe('permanent');
      expect(classification.retryable).toBe(false);
      expect(classification.severity).toBe('high');
      expect(classification.recoveryStrategy).toBe('fallback');
      expect(classification.recommendedDelay).toBe(0);
    });

    it('should classify unknown errors with default handling', () => {
      const error = new Error('Something unexpected happened');
      const classification = EnterpriseErrorHandler.classifyError(error);
      
      expect(classification.type).toBe('unknown');
      expect(classification.retryable).toBe(true);
      expect(classification.severity).toBe('medium');
      expect(classification.recoveryStrategy).toBe('retry');
      expect(classification.recommendedDelay).toBe(2000);
    });
  });

  describe('Operation Context Management', () => {
    it('should create operation context with proper structure', () => {
      const operationId = 'test-operation';
      const correlationId = 'corr-123';
      const metadata = { userId: 'user-456' };
      
      const context = EnterpriseErrorHandler.createOperationContext(
        operationId, 
        correlationId, 
        metadata
      );
      
      expect(context.operationId).toBe(operationId);
      expect(context.correlationId).toBe(correlationId);
      expect(context.executionId).toMatch(new RegExp(`^${operationId}-\\d+-[a-z0-9]+$`));
      expect(context.startTime).toBeGreaterThan(0);
      expect(context.metadata.userId).toBe('user-456');
      expect(context.metadata.createdAt).toBeDefined();
      expect(context.metadata.nodeEnv).toBeDefined();
    });

    it('should track active operations', () => {
      const context1 = EnterpriseErrorHandler.createOperationContext('op1');
      const context2 = EnterpriseErrorHandler.createOperationContext('op2');
      
      const activeOps = EnterpriseErrorHandler.getActiveOperations();
      expect(activeOps.size).toBe(2);
      expect(activeOps.has(context1.executionId)).toBe(true);
      expect(activeOps.has(context2.executionId)).toBe(true);
    });

    it('should cleanup old operations', () => {
      // Create operations with mocked old timestamps
      jest.spyOn(global.performance, 'now')
        .mockReturnValueOnce(1000) // Old operation
        .mockReturnValueOnce(100000); // Recent operation
      
      const oldContext = EnterpriseErrorHandler.createOperationContext('old-op');
      const recentContext = EnterpriseErrorHandler.createOperationContext('recent-op');
      
      // Mock current time for cleanup
      jest.spyOn(global.performance, 'now').mockReturnValue(400000); // 300+ seconds later
      
      const cleanedCount = EnterpriseErrorHandler.cleanupCompletedOperations(300000);
      
      expect(cleanedCount).toBe(1);
      const activeOps = EnterpriseErrorHandler.getActiveOperations();
      expect(activeOps.has(oldContext.executionId)).toBe(false);
      expect(activeOps.has(recentContext.executionId)).toBe(true);
    });
  });

  describe('Retry Logic with executeWithRetry', () => {
    it('should execute successful operation without retries', async () => {
      const mockOperation = jest.fn().mockResolvedValue('success');
      const operationId = 'test-operation';
      
      const result = await EnterpriseErrorHandler.executeWithRetry(
        mockOperation,
        operationId
      );
      
      expect(result.success).toBe(true);
      expect(result.result).toBe('success');
      expect(result.attempts).toBe(1);
      expect(result.circuitBreakerTriggered).toBe(false);
      expect(mockOperation).toHaveBeenCalledTimes(1);
    });

    it('should retry transient failures with exponential backoff', async () => {
      const mockOperation = jest.fn()
        .mockRejectedValueOnce(new Error('Connection timeout'))
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValue('success');
      
      const result = await EnterpriseErrorHandler.executeWithRetry(
        mockOperation,
        'retry-test',
        { maxRetries: 3 }
      );
      
      expect(result.success).toBe(true);
      expect(result.result).toBe('success');
      expect(result.attempts).toBe(3);
      expect(mockOperation).toHaveBeenCalledTimes(3);
    });

    it('should fail after max retries for persistent errors', async () => {
      const mockOperation = jest.fn().mockRejectedValue(new Error('Persistent error'));
      
      const result = await EnterpriseErrorHandler.executeWithRetry(
        mockOperation,
        'fail-test',
        { maxRetries: 2 }
      );
      
      expect(result.success).toBe(false);
      expect(result.attempts).toBe(3); // Initial + 2 retries
      expect(result.error?.message).toBe('Persistent error');
      expect(result.errorClassification?.type).toBe('unknown');
      expect(mockOperation).toHaveBeenCalledTimes(3);
    });

    it('should not retry non-retryable errors', async () => {
      const mockOperation = jest.fn().mockRejectedValue(new Error('Unauthorized access'));
      
      const result = await EnterpriseErrorHandler.executeWithRetry(
        mockOperation,
        'auth-fail-test',
        { maxRetries: 3 }
      );
      
      expect(result.success).toBe(false);
      expect(result.attempts).toBe(1);
      expect(result.error?.message).toBe('Unauthorized access');
      expect(result.errorClassification?.retryable).toBe(false);
      expect(mockOperation).toHaveBeenCalledTimes(1);
    });

    it('should trigger circuit breaker after threshold failures', async () => {
      const mockOperation = jest.fn().mockRejectedValue(new Error('Service failure'));
      const operationId = 'circuit-test';
      
      // Fill circuit breaker threshold
      for (let i = 0; i < 5; i++) {
        await EnterpriseErrorHandler.executeWithRetry(mockOperation, operationId, { maxRetries: 0 });
      }
      
      // Next operation should be blocked by circuit breaker
      const result = await EnterpriseErrorHandler.executeWithRetry(mockOperation, operationId);
      
      expect(result.success).toBe(false);
      expect(result.circuitBreakerTriggered).toBe(true);
      expect(result.attempts).toBe(0);
      expect(result.error?.message).toContain('Circuit breaker open');
    });
  });

  describe('Metrics Tracking', () => {
    it('should track operation metrics correctly', async () => {
      // Mock performance.now to return meaningful execution times
      const perfNowSpy = jest.spyOn(performance, 'now');
      let timeCounter = 1000;
      perfNowSpy.mockImplementation(() => timeCounter += 100);

      const mockOperation = jest.fn()
        .mockResolvedValueOnce('success1')
        .mockRejectedValueOnce(new Error('failure'))
        .mockResolvedValueOnce('success2');
      
      const operationId = 'metrics-test';
      
      // Execute operations
      await EnterpriseErrorHandler.executeWithRetry(mockOperation, operationId);
      await EnterpriseErrorHandler.executeWithRetry(mockOperation, operationId, { maxRetries: 0 });
      await EnterpriseErrorHandler.executeWithRetry(mockOperation, operationId);
      
      const metrics = EnterpriseErrorHandler.getOperationMetrics(operationId) as IEnterpriseErrorMetrics;
      
      expect(metrics.operationId).toBe(operationId);
      expect(metrics.totalExecutions).toBe(3);
      expect(metrics.successfulExecutions).toBe(2);
      expect(metrics.failedExecutions).toBe(1);
      expect(metrics.averageExecutionTime).toBeGreaterThanOrEqual(0);
      expect(metrics.averageRetryAttempts).toBeGreaterThanOrEqual(0);

      perfNowSpy.mockRestore();
    });

    it('should track error type distribution', async () => {
      const mockOperation = jest.fn()
        .mockRejectedValueOnce(new Error('Connection timeout'))
        .mockRejectedValueOnce(new Error('Memory limit exceeded'))
        .mockRejectedValueOnce(new Error('Unauthorized access'));
      
      const operationId = 'error-types-test';
      
      // Execute operations with different error types
      await EnterpriseErrorHandler.executeWithRetry(mockOperation, operationId, { maxRetries: 0 });
      await EnterpriseErrorHandler.executeWithRetry(mockOperation, operationId, { maxRetries: 0 });
      await EnterpriseErrorHandler.executeWithRetry(mockOperation, operationId, { maxRetries: 0 });
      
      const metrics = EnterpriseErrorHandler.getOperationMetrics(operationId) as IEnterpriseErrorMetrics;
      
      expect(metrics.errorTypes.get('transient')).toBe(1);
      expect(metrics.errorTypes.get('resource')).toBe(1);
      expect(metrics.errorTypes.get('authentication')).toBe(1);
    });

    it('should get all operation metrics', async () => {
      const mockOp1 = jest.fn().mockResolvedValue('success');
      const mockOp2 = jest.fn().mockRejectedValue(new Error('failure'));
      
      await EnterpriseErrorHandler.executeWithRetry(mockOp1, 'op1');
      await EnterpriseErrorHandler.executeWithRetry(mockOp2, 'op2', { maxRetries: 0 });
      
      const allMetrics = EnterpriseErrorHandler.getOperationMetrics() as Map<string, IEnterpriseErrorMetrics>;
      
      expect(allMetrics.size).toBe(2);
      expect(allMetrics.has('op1')).toBe(true);
      expect(allMetrics.has('op2')).toBe(true);
    });
  });

  describe('Health Reporting', () => {
    beforeEach(async () => {
      // Mock performance.now for consistent execution times
      const perfNowSpy = jest.spyOn(performance, 'now');
      let timeCounter = 1000;
      perfNowSpy.mockImplementation(() => timeCounter += 50);

      // Set up test data
      const successOp = jest.fn().mockResolvedValue('success');
      const failOp = jest.fn().mockRejectedValue(new Error('failure'));
      
      await EnterpriseErrorHandler.executeWithRetry(successOp, 'healthy-op');
      await EnterpriseErrorHandler.executeWithRetry(successOp, 'healthy-op');
      await EnterpriseErrorHandler.executeWithRetry(failOp, 'failing-op', { maxRetries: 0 });

      perfNowSpy.mockRestore();
    });

    it('should generate comprehensive health report', () => {
      const healthReport = EnterpriseErrorHandler.getHealthReport();
      
      expect(healthReport.operationMetrics).toBeDefined();
      expect(healthReport.circuitBreakers).toBeDefined();
      expect(healthReport.systemHealth).toBeDefined();
      
      expect(healthReport.systemHealth.totalOperations).toBe(3);
      expect(healthReport.systemHealth.overallSuccessRate).toBeCloseTo(2/3, 2);
      expect(healthReport.systemHealth.averageExecutionTime).toBeGreaterThanOrEqual(0);
    });

    it('should identify most failed operations', () => {
      const healthReport = EnterpriseErrorHandler.getHealthReport();
      
      expect(healthReport.systemHealth.mostFailedOperations.length).toBeGreaterThan(0);
      expect(healthReport.systemHealth.mostFailedOperations[0].operationId).toBe('failing-op');
      expect(healthReport.systemHealth.mostFailedOperations[0].failureRate).toBe(1);
    });

    it('should export metrics for monitoring', () => {
      const exportedMetrics = EnterpriseErrorHandler.exportMetricsForMonitoring();
      
      expect(exportedMetrics.timestamp).toBeDefined();
      expect(exportedMetrics.operations).toBeDefined();
      expect(exportedMetrics.circuitBreakers).toBeDefined();
      expect(exportedMetrics.systemSummary).toBeDefined();
      expect(exportedMetrics.systemSummary.overallHealth).toMatch(/healthy|degraded|critical/);
    });
  });

  describe('State Management', () => {
    it('should reset circuit breakers', async () => {
      // Create circuit breaker with failures
      const failOp = jest.fn().mockRejectedValue(new Error('failure'));
      await EnterpriseErrorHandler.executeWithRetry(failOp, 'reset-test', { maxRetries: 0 });
      
      expect(EnterpriseErrorHandler.getAllCircuitBreakerMetrics().size).toBe(1);
      
      EnterpriseErrorHandler.resetAllCircuitBreakers();
      
      expect(EnterpriseErrorHandler.getAllCircuitBreakerMetrics().size).toBe(0);
    });

    it('should reset operation metrics', async () => {
      const successOp = jest.fn().mockResolvedValue('success');
      await EnterpriseErrorHandler.executeWithRetry(successOp, 'reset-metrics-test');
      
      EnterpriseErrorHandler.resetOperationMetrics('reset-metrics-test');
      
      const metrics = EnterpriseErrorHandler.getOperationMetrics('reset-metrics-test') as IEnterpriseErrorMetrics;
      expect(metrics.totalExecutions).toBe(0);
    });

    it('should clear all state', async () => {
      const successOp = jest.fn().mockResolvedValue('success');
      await EnterpriseErrorHandler.executeWithRetry(successOp, 'clear-test');
      
      EnterpriseErrorHandler.clearAllState();
      
      const allMetrics = EnterpriseErrorHandler.getOperationMetrics() as Map<string, IEnterpriseErrorMetrics>;
      const allCircuitBreakers = EnterpriseErrorHandler.getAllCircuitBreakerMetrics();
      const activeOps = EnterpriseErrorHandler.getActiveOperations();
      
      expect(allMetrics.size).toBe(0);
      expect(allCircuitBreakers.size).toBe(0);
      expect(activeOps.size).toBe(0);
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle non-Error objects gracefully', async () => {
      const mockOperation = jest.fn().mockRejectedValue('string error');
      
      const result = await EnterpriseErrorHandler.executeWithRetry(
        mockOperation,
        'non-error-test',
        { maxRetries: 0 }
      );
      
      expect(result.success).toBe(false);
      expect(result.error).toBeInstanceOf(Error);
      expect(result.error?.message).toBe('string error');
    });

    it('should handle operations with custom correlation ID and metadata', async () => {
      const mockOperation = jest.fn().mockResolvedValue('success');
      const correlationId = 'custom-correlation-123';
      const metadata = { customField: 'customValue' };
      
      const result = await EnterpriseErrorHandler.executeWithRetry(
        mockOperation,
        'custom-context-test',
        {},
        correlationId,
        metadata
      );
      
      expect(result.success).toBe(true);
      // Context should be cleaned up after successful execution
      const activeOps = EnterpriseErrorHandler.getActiveOperations();
      expect(activeOps.size).toBe(0);
    });

    it('should handle zero retry configuration', async () => {
      const mockOperation = jest.fn().mockRejectedValue(new Error('immediate failure'));
      
      const result = await EnterpriseErrorHandler.executeWithRetry(
        mockOperation,
        'zero-retry-test',
        { maxRetries: 0 }
      );
      
      expect(result.success).toBe(false);
      expect(result.attempts).toBe(1);
      expect(mockOperation).toHaveBeenCalledTimes(1);
    });
  });
}); 