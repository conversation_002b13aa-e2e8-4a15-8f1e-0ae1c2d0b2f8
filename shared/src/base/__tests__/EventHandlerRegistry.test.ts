/**
 * @file Event Handler Registry Test Suite
 * @component event-handler-registry-tests
 * @authority-level critical-memory-safety
 */

import { EventHandlerRegistry, getEventHandlerRegistry, resetEventHandlerRegistry } from '../EventHandlerRegistry';

// Mock console for testing
const mockConsole = {
  log: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn()
};

// Configure Jest timeout
jest.setTimeout(30000); // 30 second timeout

describe('EventHandlerRegistry', () => {
  let registry: EventHandlerRegistry;
  const maxHandlersPerClient = 5;
  const maxGlobalHandlers = 20;

  beforeEach(async () => {
    // Ensure clean state before each test
    await resetEventHandlerRegistry();

    // Reset console mocks
    mockConsole.log.mockClear();
    mockConsole.warn.mockClear();
    mockConsole.error.mockClear();
    mockConsole.debug.mockClear();

    // Override console methods for logging tests
    global.console = {
      ...global.console,
      log: mockConsole.log,
      warn: mockConsole.warn,
      error: mockConsole.error,
      debug: mockConsole.debug
    };

    // Create fresh registry instance with test configuration
    registry = EventHandlerRegistry.getInstance({
      maxHandlersPerClient,
      maxGlobalHandlers,
      orphanDetectionIntervalMs: 1000, // 1 second for testing
      handlerTimeoutMs: 5000 // 5 seconds for testing
    });

    // Initialize registry (MemorySafeResourceManager will auto-initialize)
  });

  afterEach(async () => {
    try {
      // Reset global registry first to ensure clean state
      await resetEventHandlerRegistry();
    } catch (error) {
      console.warn('Error during registry reset:', error);
    }

    // Clear Jest timers and mocks
    jest.clearAllTimers();
    jest.clearAllMocks();

    // Single GC cycle to prevent timeout
    if (typeof (global as any).gc === 'function') {
      (global as any).gc();
    }
  });

  describe('Basic Functionality', () => {
    it('should create registry with valid configuration', () => {
      expect(registry).toBeDefined();
      expect(registry).toBeInstanceOf(EventHandlerRegistry);
    });

    it('should register handlers successfully', () => {
      const callback = jest.fn();
      const handlerId = registry.registerHandler('client1', 'test-event', callback);
      
      expect(handlerId).toBeDefined();
      expect(handlerId).toMatch(/^client1:test-event:\d+:[a-z0-9]+$/);
    });

    it('should unregister handlers successfully', () => {
      const callback = jest.fn();
      const handlerId = registry.registerHandler('client1', 'test-event', callback);
      
      const result = registry.unregisterHandler(handlerId);
      expect(result).toBe(true);
    });

    it('should return false when unregistering non-existent handler', () => {
      const result = registry.unregisterHandler('non-existent-id');
      expect(result).toBe(false);
    });
  });

  describe('Handler Retrieval', () => {
    it('should retrieve handlers for specific event type', () => {
      const callback1 = jest.fn();
      const callback2 = jest.fn();
      
      registry.registerHandler('client1', 'test-event', callback1);
      registry.registerHandler('client2', 'test-event', callback2);
      registry.registerHandler('client1', 'other-event', callback1);
      
      const handlers = registry.getHandlersForEvent('test-event');
      expect(handlers).toHaveLength(2);
      expect(handlers.every(h => h.eventType === 'test-event')).toBe(true);
    });

    it('should return empty array for non-existent event type', () => {
      const handlers = registry.getHandlersForEvent('non-existent-event');
      expect(handlers).toHaveLength(0);
    });

    it('should update lastUsed timestamp when retrieving handlers', () => {
      const callback = jest.fn();
      const handlerId = registry.registerHandler('client1', 'test-event', callback);

      // Get initial timestamp
      const initialHandlers = registry.getHandlersForEvent('test-event');
      const initialTimestamp = initialHandlers[0].lastUsed.getTime();

      // Wait a millisecond to ensure timestamp difference
      const startTime = Date.now();
      while (Date.now() - startTime < 2) {
        // Busy wait for 2ms
      }

      // Get handlers again to update timestamp
      const updatedHandlers = registry.getHandlersForEvent('test-event');
      expect(updatedHandlers).toHaveLength(1);
      expect(updatedHandlers[0].lastUsed.getTime()).toBeGreaterThanOrEqual(initialTimestamp);
    });
  });

  describe('Client Management', () => {
    it('should unregister all handlers for a client', () => {
      const callback = jest.fn();
      
      registry.registerHandler('client1', 'event1', callback);
      registry.registerHandler('client1', 'event2', callback);
      registry.registerHandler('client2', 'event1', callback);
      
      const removedCount = registry.unregisterClientHandlers('client1');
      expect(removedCount).toBe(2);
      
      const event1Handlers = registry.getHandlersForEvent('event1');
      expect(event1Handlers).toHaveLength(1);
      expect(event1Handlers[0].clientId).toBe('client2');
      
      const event2Handlers = registry.getHandlersForEvent('event2');
      expect(event2Handlers).toHaveLength(0);
    });

    it('should return 0 when unregistering handlers for non-existent client', () => {
      const removedCount = registry.unregisterClientHandlers('non-existent-client');
      expect(removedCount).toBe(0);
    });

    it('should enforce client handler limits', () => {
      const callback = jest.fn();
      
      // Register up to the limit
      for (let i = 0; i < maxHandlersPerClient; i++) {
        registry.registerHandler('client1', `event${i}`, callback);
      }
      
      // Attempt to exceed the limit
      expect(() => {
        registry.registerHandler('client1', 'excess-event', callback);
      }).toThrow('Client client1 has exceeded maximum handler limit');
    });
  });

  describe('Metrics and Monitoring', () => {
    it('should track handler metrics correctly', () => {
      const callback = jest.fn();
      
      registry.registerHandler('client1', 'event1', callback);
      registry.registerHandler('client1', 'event2', callback);
      registry.registerHandler('client2', 'event1', callback);
      
      const metrics = registry.getMetrics();
      expect(metrics.totalHandlers).toBe(3);
      expect(metrics.handlersByType['event1']).toBe(2);
      expect(metrics.handlersByType['event2']).toBe(1);
      expect(metrics.handlersByClient['client1']).toBe(2);
      expect(metrics.handlersByClient['client2']).toBe(1);
    });

    it('should initialize metrics with zero values', () => {
      const metrics = registry.getMetrics();
      expect(metrics.totalHandlers).toBe(0);
      expect(metrics.orphanedHandlers).toBe(0);
      expect(metrics.cleanupOperations).toBe(0);
      expect(metrics.lastCleanup).toBeNull();
    });
  });

  describe('Error Handling', () => {
    it('should throw error for invalid registration parameters', () => {
      const callback = jest.fn();
      
      expect(() => {
        registry.registerHandler('', 'event', callback);
      }).toThrow('Invalid handler registration parameters');
      
      expect(() => {
        registry.registerHandler('client', '', callback);
      }).toThrow('Invalid handler registration parameters');
      
      expect(() => {
        registry.registerHandler('client', 'event', null as any);
      }).toThrow('Invalid handler registration parameters');
    });

    it('should handle registration with metadata', () => {
      const callback = jest.fn();
      const metadata = { priority: 1, source: 'test' };
      
      const handlerId = registry.registerHandler('client1', 'test-event', callback, metadata);
      expect(handlerId).toBeDefined();
      
      const handlers = registry.getHandlersForEvent('test-event');
      expect(handlers).toHaveLength(1);
      expect(handlers[0].metadata).toEqual(metadata);
    });
  });

  describe('Memory Safety', () => {
    it('should perform emergency cleanup when global limit exceeded', () => {
      const callback = jest.fn();
      
      // Fill up to the global limit
      for (let i = 0; i < maxGlobalHandlers; i++) {
        registry.registerHandler(`client${i}`, 'test-event', callback);
      }
      
      // This should trigger emergency cleanup
      const handlerId = registry.registerHandler('excess-client', 'test-event', callback);
      expect(handlerId).toBeDefined();
      
      // Should have performed cleanup
      const metrics = registry.getMetrics();
      expect(metrics.totalHandlers).toBeLessThanOrEqual(maxGlobalHandlers);
    });

    it('should not leak memory during normal operations', async () => {
      const callback = jest.fn();
      
      // Register and unregister many handlers
      for (let i = 0; i < 100; i++) {
        const handlerId = registry.registerHandler(`client${i}`, 'test-event', callback);
        registry.unregisterHandler(handlerId);
      }
      
      const metrics = registry.getMetrics();
      expect(metrics.totalHandlers).toBe(0);
    });
  });

  describe('Logging Interface', () => {
    it('should log info messages correctly', () => {
      registry.logInfo('Test info message', { test: 'data' });
      expect(mockConsole.log).toHaveBeenCalledWith(
        '[INFO] EventHandlerRegistry: Test info message',
        { test: 'data' }
      );
    });

    it('should log warning messages correctly', () => {
      registry.logWarning('Test warning message', { test: 'data' });
      expect(mockConsole.warn).toHaveBeenCalledWith(
        '[WARNING] EventHandlerRegistry: Test warning message',
        { test: 'data' }
      );
    });

    it('should log error messages correctly', () => {
      const error = new Error('Test error');
      registry.logError('Test error message', error, { test: 'data' });
      expect(mockConsole.error).toHaveBeenCalledWith(
        '[ERROR] EventHandlerRegistry: Test error message - Test error',
        { test: 'data' }
      );
    });

    it('should log debug messages correctly', () => {
      // Set environment to enable debug logging
      const originalEnv = process.env.NODE_ENV;
      const originalDebug = process.env.DEBUG;

      process.env.NODE_ENV = 'development';
      process.env.DEBUG = 'true';

      registry.logDebug('Test debug message', { test: 'data' });
      expect(mockConsole.debug).toHaveBeenCalledWith(
        '[DEBUG] EventHandlerRegistry: Test debug message',
        { test: 'data' }
      );

      // Restore environment
      process.env.NODE_ENV = originalEnv;
      process.env.DEBUG = originalDebug;
    });
  });

  describe('Global Instance Management', () => {
    it('should provide global instance access', () => {
      const globalRegistry = getEventHandlerRegistry();
      expect(globalRegistry).toBeInstanceOf(EventHandlerRegistry);
    });

    it('should return same instance on multiple calls', () => {
      const registry1 = getEventHandlerRegistry();
      const registry2 = getEventHandlerRegistry();
      expect(registry1).toBe(registry2);
    });

    it('should reset global instance correctly', async () => {
      const registry1 = getEventHandlerRegistry();
      await resetEventHandlerRegistry();
      const registry2 = getEventHandlerRegistry();
      expect(registry1).not.toBe(registry2);
    });
  });
});
