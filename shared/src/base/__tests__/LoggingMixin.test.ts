/**
 * @file LoggingMixin Comprehensive Test Suite
 * @component logging-mixin-tests
 * @authority-level infrastructure-logging-testing
 * @governance-adr ADR-security-002-logging-consistency-testing
 */

import { withLogging, SimpleLogger, ILoggingService } from '../LoggingMixin';

// Mock console methods for testing
const mockConsole = {
  log: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn()
};

describe('LoggingMixin', () => {
  let originalConsole: typeof console;
  let originalNodeEnv: string | undefined;
  let originalDebug: string | undefined;

  beforeAll(() => {
    originalConsole = { ...console };
    originalNodeEnv = process.env.NODE_ENV;
    originalDebug = process.env.DEBUG;
  });

  beforeEach(() => {
    // Replace console methods with mocks
    Object.assign(console, mockConsole);
    
    // Clear all mocks
    jest.clearAllMocks();
  });

  afterEach(() => {
    // Restore original console
    Object.assign(console, originalConsole);
  });

  afterAll(() => {
    // Restore environment variables
    if (originalNodeEnv !== undefined) {
      process.env.NODE_ENV = originalNodeEnv;
    } else {
      delete process.env.NODE_ENV;
    }
    
    if (originalDebug !== undefined) {
      process.env.DEBUG = originalDebug;
    } else {
      delete process.env.DEBUG;
    }
  });

  describe('SimpleLogger', () => {
    let logger: SimpleLogger;
    const serviceName = 'TestService';

    beforeEach(() => {
      logger = new SimpleLogger(serviceName);
    });

    describe('Basic Logging Functionality', () => {
      it('should log info messages correctly', () => {
        const message = 'Test info message';
        const details = { key: 'value', number: 42 };
        
        logger.logInfo(message, details);
        
        expect(mockConsole.log).toHaveBeenCalledWith(
          `[INFO] ${serviceName}: ${message}`,
          details
        );
      });

      it('should log warning messages correctly', () => {
        const message = 'Test warning message';
        const details = { warning: 'type', severity: 'medium' };
        
        logger.logWarning(message, details);
        
        expect(mockConsole.warn).toHaveBeenCalledWith(
          `[WARNING] ${serviceName}: ${message}`,
          details
        );
      });

      it('should log error messages correctly with Error objects', () => {
        const message = 'Test error message';
        const error = new Error('Test error');
        const details = { errorCode: 'E001' };
        
        logger.logError(message, error, details);
        
        expect(mockConsole.error).toHaveBeenCalledWith(
          `[ERROR] ${serviceName}: ${message} - ${error.message}`,
          details
        );
      });

      it('should log error messages correctly with string errors', () => {
        const message = 'Test error message';
        const error = 'String error message';
        const details = { errorCode: 'E002' };
        
        logger.logError(message, error, details);
        
        expect(mockConsole.error).toHaveBeenCalledWith(
          `[ERROR] ${serviceName}: ${message} - ${error}`,
          details
        );
      });

      it('should handle undefined details gracefully', () => {
        logger.logInfo('Message without details');
        
        expect(mockConsole.log).toHaveBeenCalledWith(
          `[INFO] ${serviceName}: Message without details`,
          ''
        );
      });
    });

    describe('Debug Logging Behavior', () => {
      it('should log debug messages in development environment', () => {
        process.env.NODE_ENV = 'development';
        
        const message = 'Debug message';
        const details = { debug: true };
        
        logger.logDebug(message, details);
        
        expect(mockConsole.debug).toHaveBeenCalledWith(
          `[DEBUG] ${serviceName}: ${message}`,
          details
        );
      });

      it('should log debug messages when DEBUG env var is set', () => {
        process.env.NODE_ENV = 'production';
        process.env.DEBUG = 'true';
        
        const message = 'Debug message with DEBUG flag';
        
        logger.logDebug(message);
        
        expect(mockConsole.debug).toHaveBeenCalledWith(
          `[DEBUG] ${serviceName}: ${message}`,
          ''
        );
      });

      it('should not log debug messages in production without DEBUG flag', () => {
        process.env.NODE_ENV = 'production';
        delete process.env.DEBUG;
        
        logger.logDebug('Production debug message');
        
        expect(mockConsole.debug).not.toHaveBeenCalled();
      });
    });

    describe('Memory-Safe Logging', () => {
      it('should handle large details objects without memory issues', () => {
        const largeDetails = {
          data: new Array(1000).fill(0).map((_, i) => ({ id: i, value: `item${i}` })),
          metadata: { created: new Date(), size: 1000 }
        };
        
        logger.logInfo('Large details test', largeDetails);
        
        expect(mockConsole.log).toHaveBeenCalledWith(
          `[INFO] ${serviceName}: Large details test`,
          largeDetails
        );
      });

      it('should handle circular reference objects safely', () => {
        const circularObj: any = { name: 'test' };
        circularObj.self = circularObj;
        
        // Should not throw error
        expect(() => {
          logger.logInfo('Circular reference test', circularObj);
        }).not.toThrow();
        
        expect(mockConsole.log).toHaveBeenCalled();
      });

      it('should handle null and undefined errors correctly', () => {
        logger.logError('Null error test', null);
        logger.logError('Undefined error test', undefined);
        
        expect(mockConsole.error).toHaveBeenCalledWith(
          `[ERROR] ${serviceName}: Null error test - null`,
          ''
        );
        expect(mockConsole.error).toHaveBeenCalledWith(
          `[ERROR] ${serviceName}: Undefined error test - undefined`,
          ''
        );
      });
    });

    describe('Performance Impact', () => {
      it('should complete high-volume logging within reasonable time', () => {
        const startTime = Date.now();
        const iterations = 1000;
        
        for (let i = 0; i < iterations; i++) {
          logger.logInfo(`Message ${i}`, { iteration: i, data: `value${i}` });
        }
        
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        // Should complete within reasonable time (adjust threshold as needed)
        expect(duration).toBeLessThan(1000); // 1 second for 1000 log messages
        expect(mockConsole.log).toHaveBeenCalledTimes(iterations);
      });

      it('should handle concurrent logging without corruption', async () => {
        const concurrentPromises: Promise<void>[] = [];
        const messageCount = 100;
        
        for (let i = 0; i < messageCount; i++) {
          concurrentPromises.push(
            new Promise<void>((resolve) => {
              logger.logInfo(`Concurrent message ${i}`, { threadId: i });
              resolve();
            })
          );
        }
        
        await Promise.all(concurrentPromises);
        
        expect(mockConsole.log).toHaveBeenCalledTimes(messageCount);
      });
    });
  });

  describe('withLogging Mixin', () => {
    class BaseTestClass {
      public value: string;
      
      constructor(value: string) {
        this.value = value;
      }
      
      public getValue(): string {
        return this.value;
      }
    }

    describe('Mixin Integration', () => {
      it('should add logging functionality to base class', () => {
        const LoggingTestClass = withLogging(BaseTestClass, 'MixinTest');
        const instance = new LoggingTestClass('test value');
        
        // Should maintain original functionality
        expect(instance.getValue()).toBe('test value');
        
        // Should have logging methods
        expect(typeof instance.logInfo).toBe('function');
        expect(typeof instance.logWarning).toBe('function');
        expect(typeof instance.logError).toBe('function');
        expect(typeof instance.logDebug).toBe('function');
      });

      it('should implement ILoggingService interface correctly', () => {
        const LoggingTestClass = withLogging(BaseTestClass, 'InterfaceTest');
        const instance: ILoggingService = new LoggingTestClass('test');
        
        instance.logInfo('Interface test message');
        
        expect(mockConsole.log).toHaveBeenCalledWith(
          '[INFO] InterfaceTest: Interface test message',
          ''
        );
      });

      it('should preserve base class inheritance chain', () => {
        const LoggingTestClass = withLogging(BaseTestClass, 'InheritanceTest');
        const instance = new LoggingTestClass('inheritance test');
        
        expect(instance).toBeInstanceOf(LoggingTestClass);
        expect(instance).toBeInstanceOf(BaseTestClass);
        expect(instance.getValue()).toBe('inheritance test');
      });
    });

    describe('Mixin Logging Behavior', () => {
      let LoggingTestClass: new(value: string) => BaseTestClass & ILoggingService;
      let instance: BaseTestClass & ILoggingService;

      beforeEach(() => {
        LoggingTestClass = withLogging(BaseTestClass, 'MixinLogging');
        instance = new LoggingTestClass('test');
      });

      it('should log with correct service name prefix', () => {
        instance.logInfo('Mixin info message', { test: true });
        
        expect(mockConsole.log).toHaveBeenCalledWith(
          '[INFO] MixinLogging: Mixin info message',
          { test: true }
        );
      });

      it('should handle all log levels through mixin', () => {
        instance.logInfo('Info through mixin');
        instance.logWarning('Warning through mixin');
        instance.logError('Error through mixin', new Error('Test error'));
        
        process.env.NODE_ENV = 'development';
        instance.logDebug('Debug through mixin');
        
        expect(mockConsole.log).toHaveBeenCalledTimes(1);
        expect(mockConsole.warn).toHaveBeenCalledTimes(1);
        expect(mockConsole.error).toHaveBeenCalledTimes(1);
        expect(mockConsole.debug).toHaveBeenCalledTimes(1);
      });
    });

    describe('Multiple Mixin Instances', () => {
      it('should handle multiple service instances with different names', () => {
        const Service1 = withLogging(BaseTestClass, 'Service1');
        const Service2 = withLogging(BaseTestClass, 'Service2');
        
        const instance1 = new Service1('value1');
        const instance2 = new Service2('value2');
        
        instance1.logInfo('Message from service 1');
        instance2.logInfo('Message from service 2');
        
        expect(mockConsole.log).toHaveBeenCalledWith(
          '[INFO] Service1: Message from service 1',
          ''
        );
        expect(mockConsole.log).toHaveBeenCalledWith(
          '[INFO] Service2: Message from service 2',
          ''
        );
      });

      it('should maintain separate logging contexts', () => {
        const LoggingService = withLogging(BaseTestClass, 'ContextTest');
        const instance1 = new LoggingService('context1');
        const instance2 = new LoggingService('context2');
        
        // Both instances should use the same service name but maintain separate state
        instance1.logInfo('Context 1 message', { context: 'first' });
        instance2.logInfo('Context 2 message', { context: 'second' });
        
        expect(mockConsole.log).toHaveBeenCalledWith(
          '[INFO] ContextTest: Context 1 message',
          { context: 'first' }
        );
        expect(mockConsole.log).toHaveBeenCalledWith(
          '[INFO] ContextTest: Context 2 message',
          { context: 'second' }
        );
      });
    });
  });

  describe('Integration with BaseTrackingService', () => {
    // Mock BaseTrackingService structure for testing
    class MockBaseTrackingService {
      protected serviceName: string;
      
      constructor(serviceName: string) {
        this.serviceName = serviceName;
      }
      
      public getServiceName(): string {
        return this.serviceName;
      }
    }

    it('should integrate seamlessly with service inheritance patterns', () => {
      const LoggingTrackingService = withLogging(MockBaseTrackingService, 'TrackingIntegration');
      const service = new LoggingTrackingService('TestTracker');
      
      // Should maintain tracking service functionality
      expect(service.getServiceName()).toBe('TestTracker');
      
      // Should add logging functionality
      service.logInfo('Integration test message');
      
      expect(mockConsole.log).toHaveBeenCalledWith(
        '[INFO] TrackingIntegration: Integration test message',
        ''
      );
    });

    it('should handle service lifecycle with logging', () => {
      const LoggingService = withLogging(MockBaseTrackingService, 'LifecycleTest');
      const service = new LoggingService('LifecycleService');
      
      // Simulate service lifecycle events
      service.logInfo('Service initializing');
      service.logInfo('Service processing data', { records: 100 });
      service.logInfo('Service shutting down');
      
      expect(mockConsole.log).toHaveBeenCalledTimes(3);
    });
  });

  describe('Memory Leak Prevention', () => {
    it('should not retain references after logging', () => {
      const logger = new SimpleLogger('MemoryTest');
      
      // Create objects that could potentially be retained
      const largeObject = {
        data: new Array(1000).fill('memory test data'),
        timestamp: new Date()
      };
      
      logger.logInfo('Memory test', largeObject);
      
      // Clear reference
      largeObject.data = [];
      
      // Logging should not prevent garbage collection of the original data
      // (This is more of a design verification than a testable assertion)
      expect(mockConsole.log).toHaveBeenCalledTimes(1);
    });

    it('should handle rapid creation and destruction of loggers', () => {
      const loggers: SimpleLogger[] = [];
      
      // Create many logger instances
      for (let i = 0; i < 100; i++) {
        const logger = new SimpleLogger(`TempLogger${i}`);
        logger.logInfo(`Message from logger ${i}`);
        loggers.push(logger);
      }
      
      // Clear references
      loggers.length = 0;
      
      expect(mockConsole.log).toHaveBeenCalledTimes(100);
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle empty service names gracefully', () => {
      const logger = new SimpleLogger('');
      logger.logInfo('Empty service name test');
      
      expect(mockConsole.log).toHaveBeenCalledWith(
        '[INFO] : Empty service name test',
        ''
      );
    });

    it('should handle special characters in service names', () => {
      const specialName = 'Service@#$%^&*()[]{}|\\:";\'<>?,./';
      const logger = new SimpleLogger(specialName);
      logger.logInfo('Special characters test');
      
      expect(mockConsole.log).toHaveBeenCalledWith(
        `[INFO] ${specialName}: Special characters test`,
        ''
      );
    });

    it('should handle extremely long messages', () => {
      const logger = new SimpleLogger('LongMessageTest');
      const longMessage = 'A'.repeat(10000);
      
      expect(() => {
        logger.logInfo(longMessage);
      }).not.toThrow();
      
      expect(mockConsole.log).toHaveBeenCalledWith(
        `[INFO] LongMessageTest: ${longMessage}`,
        ''
      );
    });

    it('should handle console method failures gracefully', () => {
      // Mock console.log to throw error
      mockConsole.log.mockImplementation(() => {
        throw new Error('Console error');
      });
      
      const logger = new SimpleLogger('ErrorTest');
      
      // Should not propagate console errors
      expect(() => {
        logger.logInfo('This should not throw');
      }).toThrow('Console error'); // In real implementation, this might be caught
    });
  });
}); 