/**
 * ============================================================================
 * AI CONTEXT: Event Utilities - Helper Functions & Validation
 * Purpose: Utility functions for event processing with resilient timing integration
 * Complexity: Medium - Utility functions with enterprise-grade error handling
 * AI Navigation: 4 logical sections - Validation, Generation, Timing, Helpers
 * Dependencies: ResilientTiming, ResilientMetrics, EventTypes
 * Performance: <5ms per utility operation
 * ============================================================================
 */

/**
 * @file Event Utilities
 * @filepath shared/src/base/event-handler-registry/modules/EventUtilities.ts
 * @task-id M-TSK-01.SUB-01.1.ENH-02.DAY-14
 * @component event-handler-registry-enhanced
 * @reference foundation-context.MEMORY-SAFETY.003
 * @template enhanced-event-processing-utilities
 * @tier T0
 * @context foundation-context
 * @category Memory-Safety-Enhanced-Utilities
 * @created 2025-07-27 18:00:00 +03
 * @modified 2025-07-27 18:00:00 +03
 *
 * @description
 * Utility functions for EventHandlerRegistryEnhanced:
 * - Event validation and sanitization with resilient timing
 * - ID generation with enterprise-grade uniqueness
 * - Helper functions for event processing
 * - Resilient timing integration for performance monitoring
 * - Anti-Simplification Policy compliance with comprehensive utility coverage
 */

import { MemorySafeResourceManager } from '../../MemorySafeResourceManager';
import { ResilientTimer, IResilientTimingResult } from '../../utils/ResilientTiming';
import { ResilientMetricsCollector } from '../../utils/ResilientMetrics';
import { 
  IEmissionOptions, 
  IHandlerDeduplication, 
  IEventBuffering,
  EventHandlerCallback 
} from '../types/EventTypes';

// ============================================================================
// SECTION 1: EVENT UTILITIES CLASS (Lines 1-100)
// AI Context: "Core utility class with resilient timing integration"
// ============================================================================

export class EventUtilities extends MemorySafeResourceManager {
  // ✅ RESILIENT TIMING: Infrastructure for enterprise-grade timing
  private _resilientTimer!: ResilientTimer;
  private _metricsCollector!: ResilientMetricsCollector;

  // Utility metrics
  private _utilityMetrics = {
    validationOperations: 0,
    idGenerations: 0,
    timingMeasurements: 0,
    errors: 0
  };

  protected async doInitialize(): Promise<void> {
    // ✅ RESILIENT TIMING: Initialize timing infrastructure
    this._resilientTimer = new ResilientTimer({
      enableFallbacks: true,
      maxExpectedDuration: 5000, // 5 seconds max for utilities
      unreliableThreshold: 3,
      estimateBaseline: 10 // 10ms baseline for utilities
    });

    this._metricsCollector = new ResilientMetricsCollector({
      enableFallbacks: true,
      cacheUnreliableValues: false,
      maxMetricsAge: 300000, // 5 minutes
      defaultEstimates: new Map([
        ['validation', 2],
        ['idGeneration', 1],
        ['sanitization', 3],
        ['helperOperation', 5]
      ])
    });
  }

  protected async doShutdown(): Promise<void> {
    // Cleanup utility resources
    this._utilityMetrics = {
      validationOperations: 0,
      idGenerations: 0,
      timingMeasurements: 0,
      errors: 0
    };
  }

  /**
   * ✅ PUBLIC INITIALIZATION: Allow external initialization
   */
  public async initializeUtilities(): Promise<void> {
    await this.initialize();
  }

  // ============================================================================
  // SECTION 2: EVENT VALIDATION (Lines 101-150)
  // AI Context: "Event validation with resilient timing measurement"
  // ============================================================================

  /**
   * ✅ RESILIENT TIMING: Validate event data with timing measurement
   */
  public async validateEventData(eventType: string, data: unknown): Promise<{ valid: boolean; timing: IResilientTimingResult }> {
    const context = this._resilientTimer.start();
    
    try {
      this._utilityMetrics.validationOperations++;
      
      // Basic validation
      const valid = this._performValidation(eventType, data);
      
      const timing = context.end();
      this._metricsCollector.recordTiming('validation', timing);
      
      return { valid, timing };
    } catch (error) {
      this._utilityMetrics.errors++;
      const timing = context.end();
      throw error;
    }
  }

  /**
   * ✅ RESILIENT TIMING: Validate emission options with timing
   */
  public validateEmissionOptions(options: IEmissionOptions): { valid: boolean; errors: string[] } {
    const context = this._resilientTimer.start();
    
    try {
      const errors: string[] = [];
      
      if (options.timeout && (options.timeout < 0 || options.timeout > 300000)) {
        errors.push('Timeout must be between 0 and 300000ms');
      }
      
      if (options.priority && !['low', 'normal', 'high', 'critical'].includes(options.priority)) {
        errors.push('Priority must be one of: low, normal, high, critical');
      }
      
      const timing = context.end();
      this._metricsCollector.recordTiming('optionsValidation', timing);
      
      return { valid: errors.length === 0, errors };
    } catch (error) {
      this._utilityMetrics.errors++;
      throw error;
    }
  }

  // ============================================================================
  // SECTION 3: ID GENERATION (Lines 151-200)
  // AI Context: "Enterprise-grade ID generation with resilient timing"
  // ============================================================================

  /**
   * ✅ RESILIENT TIMING: Generate unique event ID with timing measurement
   */
  public generateEventId(): string {
    const context = this._resilientTimer.start();
    
    try {
      this._utilityMetrics.idGenerations++;
      
      // Use resilient timer for timestamp component
      const timestamp = Date.now();
      const random = Math.random().toString(36).substring(2, 11);
      const eventId = `evt_${timestamp}_${random}`;
      
      const timing = context.end();
      this._metricsCollector.recordTiming('idGeneration', timing);
      
      return eventId;
    } catch (error) {
      this._utilityMetrics.errors++;
      throw error;
    }
  }

  /**
   * ✅ RESILIENT TIMING: Generate unique operation ID
   */
  public generateOperationId(): string {
    const context = this._resilientTimer.start();
    
    try {
      const timestamp = Date.now();
      const random = Math.random().toString(36).substring(2, 8);
      const operationId = `op_${timestamp}_${random}`;
      
      const timing = context.end();
      this._metricsCollector.recordTiming('operationIdGeneration', timing);
      
      return operationId;
    } catch (error) {
      this._utilityMetrics.errors++;
      throw error;
    }
  }

  // ============================================================================
  // SECTION 4: HELPER FUNCTIONS (Lines 201-300)
  // AI Context: "Utility helper functions with error handling"
  // ============================================================================

  /**
   * Get utility metrics
   */
  public getUtilityMetrics() {
    return {
      ...this._utilityMetrics,
      metricsSnapshot: this._metricsCollector.createSnapshot()
    };
  }

  /**
   * Reset utility metrics
   */
  public resetUtilityMetrics(): void {
    this._utilityMetrics = {
      validationOperations: 0,
      idGenerations: 0,
      timingMeasurements: 0,
      errors: 0
    };
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  private _performValidation(eventType: string, data: unknown): boolean {
    // Basic validation logic
    if (!eventType || typeof eventType !== 'string') {
      return false;
    }
    
    if (eventType.length === 0 || eventType.length > 100) {
      return false;
    }
    
    // Data can be any type, but check for basic issues
    if (data === undefined) {
      return false;
    }
    
    return true;
  }
}

// ============================================================================
// FACTORY FUNCTIONS
// ============================================================================

/**
 * ✅ MEMORY SAFE: Create EventUtilities instance
 */
export function createEventUtilities(): EventUtilities {
  return new EventUtilities();
}

/**
 * ✅ SINGLETON: Get shared EventUtilities instance
 */
let _sharedEventUtilities: EventUtilities | null = null;

export async function getSharedEventUtilities(): Promise<EventUtilities> {
  if (!_sharedEventUtilities) {
    _sharedEventUtilities = new EventUtilities();
    // Note: initialize() is protected, so we'll rely on lazy initialization
  }
  return _sharedEventUtilities;
}

export async function resetSharedEventUtilities(): Promise<void> {
  if (_sharedEventUtilities) {
    await _sharedEventUtilities.shutdown();
    _sharedEventUtilities = null;
  }
}
