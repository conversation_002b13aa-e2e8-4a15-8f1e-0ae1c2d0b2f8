/**
 * ============================================================================
 * AI CONTEXT: Event Types - Core Event System Type Definitions
 * Purpose: Comprehensive type definitions for enhanced event handling system
 * Complexity: Medium - Central type definitions with resilient timing integration
 * AI Navigation: 4 logical sections - Emission, Middleware, Deduplication, Buffering
 * Dependencies: None (pure types)
 * Performance: No runtime impact - compile-time only
 * ============================================================================
 */

/**
 * @file Event Types
 * @filepath shared/src/base/event-handler-registry/types/EventTypes.ts
 * @task-id M-TSK-01.SUB-01.1.ENH-02.DAY-11
 * @component event-handler-registry-enhanced
 * @reference foundation-context.MEMORY-SAFETY.003
 * @template enhanced-event-processing-types
 * @tier T0
 * @context foundation-context
 * @category Memory-Safety-Enhanced-Types
 * @created 2025-01-27 12:00:00 +03
 * @modified 2025-01-27 12:00:00 +03
 *
 * @description
 * Core type definitions for EventHandlerRegistryEnhanced:
 * - Event emission system interfaces with resilient timing support
 * - Priority-based middleware system type definitions
 * - Advanced handler deduplication strategy types
 * - Event buffering and queuing configuration types
 * - Resilient timing integration for enterprise-grade performance monitoring
 * - Anti-Simplification Policy compliance with comprehensive type coverage
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-002-event-emission-architecture
 * @governance-dcr DCR-foundation-002-event-emission-development
 * @governance-status approved
 * @governance-compliance authority-validated
 */

// ============================================================================
// SECTION 1: BASE EVENT HANDLER TYPES (Lines 1-80)
// AI Context: "Core event handler callback and registration types"
// ============================================================================

export type EventHandlerCallback = (
  event: unknown,
  context?: {
    eventType: string;
    clientId: string;
    timestamp: Date;
    metadata?: Record<string, unknown>;
  }
) => unknown | Promise<unknown>;

export interface IRegisteredHandler {
  id: string;
  clientId: string;
  eventType: string;
  callback: EventHandlerCallback;
  registeredAt: Date;
  lastUsed: Date;
  metadata?: Record<string, unknown>;
}

// ============================================================================
// SECTION 2: EVENT EMISSION SYSTEM TYPES (Lines 81-200)
// AI Context: "Event emission interfaces with resilient timing integration"
// ============================================================================

export interface IEventEmissionSystem {
  emitEvent(eventType: string, data: unknown, options?: IEmissionOptions): Promise<IEmissionResult>;
  emitEventToClient(clientId: string, eventType: string, data: unknown): Promise<IClientEmissionResult>;
  emitEventBatch(events: IEventBatch[]): Promise<IBatchEmissionResult>;
  emitEventWithTimeout(eventType: string, data: unknown, timeoutMs: number): Promise<IEmissionResult>;
}

export interface IEmissionOptions {
  targetClients?: string[];
  excludeClients?: string[];
  priority?: 'low' | 'normal' | 'high' | 'critical';
  timeout?: number;
  requireAcknowledgment?: boolean;
  retryPolicy?: IRetryPolicy;
}

export interface IEmissionResult {
  eventId: string;
  eventType: string;
  targetHandlers: number;
  successfulHandlers: number;
  failedHandlers: number;
  executionTime: number;
  handlerResults: IHandlerResult[];
  errors: IHandlerError[];
  // ✅ RESILIENT TIMING: Enhanced with timing reliability metadata
  timingReliability?: number;
  measurementMethod?: 'performance' | 'date' | 'process' | 'estimate';
  fallbackUsed?: boolean;
}

export interface IHandlerResult {
  handlerId: string;
  clientId: string;
  result: unknown;
  executionTime: number;
  success: boolean;
  skippedByMiddleware?: string;
  // ✅ RESILIENT TIMING: Enhanced with timing reliability
  timingReliable?: boolean;
  measurementMethod?: string;
}

export interface IHandlerError {
  handlerId: string;
  clientId: string;
  error: Error;
  timestamp: Date;
  // ✅ RESILIENT TIMING: Enhanced with timing context
  timingContext?: {
    duration?: number;
    reliable?: boolean;
    method?: string;
  };
}

export interface IClientEmissionResult extends IEmissionResult {
  targetClientId: string;
}

export interface IEventBatch {
  eventType: string;
  data: unknown;
  options?: IEmissionOptions;
}

export interface IBatchEmissionResult {
  batchId: string;
  totalEvents: number;
  successfulEvents: number;
  failedEvents: number;
  executionTime: number;
  results: IEmissionResult[];
  // ✅ RESILIENT TIMING: Enhanced with batch timing reliability
  batchTimingReliability?: number;
  measurementMethod?: string;
}

export interface IRetryPolicy {
  maxRetries: number;
  retryDelayMs: number;
  backoffMultiplier: number;
  maxBackoffDelayMs?: number;
  retryableErrorTypes?: string[];
  nonRetryableErrorTypes?: string[];
}

export interface IErrorClassification {
  category: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  retryable: boolean;
}

// ============================================================================
// SECTION 3: MIDDLEWARE SYSTEM TYPES (Lines 201-280)
// AI Context: "Handler middleware interfaces with execution context"
// ============================================================================

export interface IHandlerMiddleware {
  name: string;
  priority: number; // Higher priority executes first
  beforeHandlerExecution?(context: IHandlerExecutionContext): Promise<boolean>; // false = skip handler
  afterHandlerExecution?(context: IHandlerExecutionContext, result: unknown): Promise<void>;
  onHandlerError?(context: IHandlerExecutionContext, error: Error): Promise<boolean>; // true = error handled
}

export interface IHandlerExecutionContext {
  handlerId: string;
  clientId: string;
  eventType: string;
  eventData: unknown;
  timestamp: Date;
  metadata: Record<string, unknown>;
  executionAttempt: number;
  // ✅ RESILIENT TIMING: Enhanced with timing context
  timingContext?: {
    startTime?: number;
    method?: string;
    expectedDuration?: number;
  };
}

export interface IMiddlewareResult {
  success: boolean;
  chainTiming: {
    duration: number;
    reliable: boolean;
    method: string;
  };
  stepResults?: Array<{
    middlewareName: string;
    duration: number;
    reliable: boolean;
    success: boolean;
  }>;
}

// ============================================================================
// SECTION 4: DEDUPLICATION & BUFFERING TYPES (Lines 281-380)
// AI Context: "Advanced deduplication strategies and event buffering configuration"
// ============================================================================

export interface IHandlerDeduplication {
  enabled: boolean;
  strategy: 'signature' | 'reference' | 'custom';
  customDeduplicationFn?: (handler1: EventHandlerCallback, handler2: EventHandlerCallback) => boolean;
  autoMergeMetadata: boolean;
  onDuplicateDetected?: (existing: IRegisteredHandler, duplicate: IRegisteredHandler) => void;
}

export interface IEventBuffering {
  enabled: boolean;
  bufferSize: number;
  flushInterval: number; // milliseconds
  bufferStrategy: 'fifo' | 'lifo' | 'priority' | 'time_window';
  priorityFn?: (context: IEventPriorityContext) => number;
  autoFlushThreshold: number; // 0.0-1.0, flush when buffer is X% full
  onBufferOverflow: 'drop_oldest' | 'drop_newest' | 'force_flush' | 'error';
  deadLetterQueueHandler?: (event: any) => Promise<void>;
}

export interface IEventPriorityContext {
  eventType: string;
  data: unknown;
  options: IEmissionOptions;
  timestamp: Date;
  systemLoad: ISystemLoad;
  queueDepth: number;
  targetHandlerCount: number;
}

export interface ISystemLoad {
  memoryUtilization: number;
  cpuUtilization: number;
  eventQueueDepth: number;
  activeHandlers: number;
}

export interface IBufferedEvent {
  id: string;
  type: string;
  data: unknown;
  options: IEmissionOptions;
  timestamp: Date;
  priority: number;
  retryCount: number;
  metadata?: Record<string, unknown>;
  // ✅ RESILIENT TIMING: Enhanced with timing requirements
  expectedExecutionTime?: number;
  timingRequirements?: {
    maxDuration: number;
    requireReliableTiming: boolean;
    fallbackAcceptable: boolean;
  };
}

// ============================================================================
// SECTION 5: CONFIGURATION & METRICS TYPES (Lines 381-450)
// AI Context: "Enhanced configuration and metrics interfaces with resilient timing"
// ============================================================================

export interface IEventHandlerRegistryEnhancedConfig {
  deduplication?: IHandlerDeduplication;
  buffering?: IEventBuffering;
  maxMiddleware?: number;
  emissionTimeoutMs?: number;
  // ✅ RESILIENT TIMING: Enhanced configuration options
  resilientTiming?: {
    enableFallbacks: boolean;
    maxExpectedDuration: number;
    unreliableThreshold: number;
    estimateBaseline: number;
    enableDetailedLogging?: boolean;
  };
}

export interface IEmissionMetrics {
  totalEmissions: number;
  successfulEmissions: number;
  failedEmissions: number;
  averageEmissionTime: number;
  totalMiddlewareExecutions: number;
  duplicatesDetected: number;
  bufferedEvents: number;
  totalRetries: number;
  deadLetterEvents: number;
  // ✅ RESILIENT TIMING: Enhanced metrics with reliability tracking
  timingReliabilityScore?: number;
  fallbackUsageCount?: number;
  performanceFailures?: number;
  estimatedDurationAccuracy?: number;
}

export interface IEmissionSummary {
  eventId: string;
  eventType: string;
  targetHandlers: number;
  successfulHandlers: number;
  failedHandlers: number;
  totalExecutionTime: number;
  timingReliability: number;
  measurementMethod: string;
  handlerSummary: Array<{
    handlerId: string;
    success: boolean;
    executionTime: number;
    timingReliable: boolean;
  }>;
} 