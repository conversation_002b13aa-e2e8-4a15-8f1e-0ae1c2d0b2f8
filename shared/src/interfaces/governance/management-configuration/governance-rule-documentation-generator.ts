/**
 * @file GovernanceRuleDocumentationGeneratorInterfaces
 * @filepath shared/src/interfaces/governance/management-configuration/governance-rule-documentation-generator.ts
 * @reference governance-rule-documentation-generator-interfaces
 * @tier T2
 * @context foundation-context
 * @category Foundation
 * @created 2025-07-04
 * @modified 2025-07-04 22:21:57 +03
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "E.Z. Consultancy"
 * @governance-adr ADR-foundation-009-hybrid-security-architecture
 * @governance-dcr DCR-foundation-008-security-governance-foundation
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on governance-rule-configuration-manager, governance-rule-template-engine
 * @enables governance-rule-environment-manager, governance-analytics-reporting
 * @related-contexts foundation-context, governance-context
 * @governance-impact governance-documentation, compliance-reporting
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type governance-interface
 * @lifecycle-stage implementation
 * @testing-status unit-tested, integration-tested
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/contexts/foundation-context/interfaces/governance-rule-documentation-generator.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 */

import { TDocumentationGenerationOptions, TDocumentationAuditTrail } from '../../../types/platform/governance/management-configuration/documentation-generator-types';

/**
 * 📚 GOVERNANCE RULE DOCUMENTATION GENERATOR INTERFACE
 * 
 * Core interface for enterprise-grade documentation generation system.
 * Provides comprehensive documentation automation with authority-driven governance,
 * multi-format output support, and enterprise compliance requirements.
 */
export interface IGovernanceRuleDocumentationGenerator {
  /**
   * Generate comprehensive documentation for governance rules
   * @param context - Documentation generation context
   * @param options - Generation options and configuration
   * @returns Promise<IDocumentationOutput> - Generated documentation output
   */
  generateDocumentation(
    context: IDocumentationGenerationContext,
    options?: TDocumentationGenerationOptions
  ): Promise<IDocumentationOutput>;

  /**
   * Generate documentation for multiple governance contexts
   * @param contexts - Array of documentation generation contexts
   * @param options - Generation options and configuration
   * @returns Promise<IDocumentationOutput[]> - Array of generated documentation outputs
   */
  generateBatchDocumentation(
    contexts: IDocumentationGenerationContext[],
    options?: TDocumentationGenerationOptions
  ): Promise<IDocumentationOutput[]>;
}

/**
 * 📋 DOCUMENTATION GENERATION CONTEXT INTERFACE
 * 
 * Defines the context and data required for documentation generation.
 * Includes governance rules, metadata, and configuration information.
 */
export interface IDocumentationGenerationContext {
  /** Unique identifier for the documentation context */
  id: string;

  /** Governance rules to be documented */
  rules: IGovernanceRule[];

  /** Context metadata and configuration */
  metadata: IDocumentationContextMetadata;

  /** Security level for documentation generation */
  securityLevel?: string;

  /** Authority level required for documentation access */
  authorityLevel?: string;

  /** Additional context-specific data */
  additionalData?: Record<string, any>;
}

/**
 * 🎨 DOCUMENTATION TEMPLATE INTERFACE
 * 
 * Defines the structure and configuration for documentation templates.
 * Supports multiple formats and customization options.
 */
export interface IDocumentationTemplate {
  /** Unique template identifier */
  id: string;

  /** Template name and description */
  name: string;
  description?: string;

  /** Template format (markdown, html, pdf, json) */
  format: string;

  /** Template content and structure */
  content: string;

  /** Template variables and placeholders */
  variables: Record<string, any>;

  /** Template metadata */
  metadata: IDocumentationTemplateMetadata;

  /** Template validation rules */
  validationRules?: IDocumentationTemplateValidation[];
}

/**
 * 📄 DOCUMENTATION OUTPUT INTERFACE
 * 
 * Defines the structure of generated documentation output.
 * Includes content, metadata, and audit information.
 */
export interface IDocumentationOutput {
  /** Unique output identifier */
  id: string;

  /** Source context identifier */
  context: string;

  /** Output format */
  format: string;

  /** Generated documentation content */
  content: string;

  /** Output metadata */
  metadata: IDocumentationMetadata;

  /** Generation timestamp */
  generatedAt: string;

  /** Documentation version */
  version: string;

  /** Audit trail for the generation process */
  auditTrail: TDocumentationAuditTrail[];
}

/**
 * 📊 DOCUMENTATION METADATA INTERFACE
 * 
 * Comprehensive metadata for documentation output including
 * generation details, validation status, and compliance information.
 */
export interface IDocumentationMetadata {
  /** Source context identifier */
  contextId: string;

  /** Generation timestamp */
  generatedAt: string;

  /** Documentation format */
  format: string;

  /** Documentation version */
  version: string;

  /** Authority validator */
  authority: string;

  /** Compliance level */
  complianceLevel: string;

  /** Security level */
  securityLevel: string;

  /** Number of rules documented */
  rulesCount: number;

  /** Number of sections included */
  sectionsCount: number;

  /** Validation status */
  validationStatus: string;

  /** Audit trail entries */
  auditTrail: TDocumentationAuditTrail[];
}

/**
 * 🔍 DOCUMENTATION VALIDATION INTERFACE
 * 
 * Defines validation rules and results for documentation generation.
 * Ensures compliance with authority requirements and quality standards.
 */
export interface IDocumentationValidation {
  /** Validation identifier */
  id: string;

  /** Validation type */
  type: string;

  /** Validation rules */
  rules: IDocumentationValidationRule[];

  /** Validation results */
  results: IDocumentationValidationResult[];

  /** Validation status */
  status: 'passed' | 'failed' | 'warning';

  /** Validation timestamp */
  timestamp: string;
}

/**
 * 📋 GOVERNANCE RULE INTERFACE
 * 
 * Defines the structure of governance rules for documentation.
 * Includes rule definition, conditions, actions, and dependencies.
 */
export interface IGovernanceRule {
  /** Unique rule identifier */
  id: string;

  /** Rule name */
  name: string;

  /** Rule type */
  type: string;

  /** Rule description */
  description?: string;

  /** Rule priority */
  priority?: string;

  /** Rule conditions */
  conditions?: string[];

  /** Rule actions */
  actions?: string[];

  /** Rule dependencies */
  dependencies?: string[];

  /** Rule metadata */
  metadata?: Record<string, any>;

  /** Rule validation status */
  validationStatus?: string;

  /** Rule authority level */
  authorityLevel?: string;
}

/**
 * 📝 DOCUMENTATION CONTEXT METADATA INTERFACE
 * 
 * Metadata and configuration for documentation generation context.
 * Includes context information, settings, and requirements.
 */
export interface IDocumentationContextMetadata {
  /** Context title */
  title?: string;

  /** Context description */
  description?: string;

  /** Context version */
  version?: string;

  /** Context authority */
  authority?: string;

  /** Context creation timestamp */
  created?: string;

  /** Context last modified timestamp */
  modified?: string;

  /** Context architecture overview */
  architecture?: string;

  /** Context key features */
  features?: string[];

  /** Environment variables documentation */
  environmentVariables?: Record<string, any>;

  /** API endpoints documentation */
  apiEndpoints?: IDocumentationAPIEndpoint[];

  /** Compliance level */
  complianceLevel?: string;

  /** Last validation timestamp */
  lastValidated?: string;

  /** Last audit timestamp */
  lastAudit?: string;

  /** Audit status */
  auditStatus?: string;

  /** Next audit timestamp */
  nextAudit?: string;
}

/**
 * 🔌 DOCUMENTATION API ENDPOINT INTERFACE
 * 
 * Defines API endpoint documentation structure.
 * Includes endpoint details, parameters, and examples.
 */
export interface IDocumentationAPIEndpoint {
  /** HTTP method */
  method: string;

  /** Endpoint path */
  path: string;

  /** Endpoint description */
  description?: string;

  /** Endpoint parameters */
  parameters?: IDocumentationAPIParameter[];

  /** Request example */
  example?: any;

  /** Response example */
  response?: any;

  /** Authentication requirements */
  authentication?: string;

  /** Authorization requirements */
  authorization?: string[];
}

/**
 * 📋 DOCUMENTATION API PARAMETER INTERFACE
 * 
 * Defines API parameter documentation structure.
 * Includes parameter details, types, and validation rules.
 */
export interface IDocumentationAPIParameter {
  /** Parameter name */
  name: string;

  /** Parameter type */
  type: string;

  /** Parameter description */
  description: string;

  /** Parameter required flag */
  required?: boolean;

  /** Parameter default value */
  default?: any;

  /** Parameter validation rules */
  validation?: string[];

  /** Parameter examples */
  examples?: any[];
}

/**
 * 🎨 DOCUMENTATION TEMPLATE METADATA INTERFACE
 * 
 * Metadata for documentation templates including
 * template information, configuration, and validation rules.
 */
export interface IDocumentationTemplateMetadata {
  /** Template author */
  author?: string;

  /** Template version */
  version?: string;

  /** Template creation timestamp */
  created?: string;

  /** Template last modified timestamp */
  modified?: string;

  /** Template description */
  description?: string;

  /** Template tags */
  tags?: string[];

  /** Template category */
  category?: string;

  /** Template compatibility */
  compatibility?: string[];

  /** Template requirements */
  requirements?: string[];
}

/**
 * ✅ DOCUMENTATION TEMPLATE VALIDATION INTERFACE
 * 
 * Validation rules for documentation templates.
 * Ensures template compliance and quality standards.
 */
export interface IDocumentationTemplateValidation {
  /** Validation rule identifier */
  id: string;

  /** Validation rule name */
  name: string;

  /** Validation rule type */
  type: string;

  /** Validation rule description */
  description?: string;

  /** Validation rule pattern */
  pattern?: string;

  /** Validation rule severity */
  severity: 'error' | 'warning' | 'info';

  /** Validation rule message */
  message: string;
}

/**
 * 🔍 DOCUMENTATION VALIDATION RULE INTERFACE
 * 
 * Individual validation rule for documentation generation.
 * Defines validation criteria and requirements.
 */
export interface IDocumentationValidationRule {
  /** Rule identifier */
  id: string;

  /** Rule name */
  name: string;

  /** Rule type */
  type: string;

  /** Rule description */
  description?: string;

  /** Rule criteria */
  criteria: Record<string, any>;

  /** Rule severity */
  severity: 'error' | 'warning' | 'info';

  /** Rule enabled flag */
  enabled: boolean;
}

/**
 * 📊 DOCUMENTATION VALIDATION RESULT INTERFACE
 * 
 * Result of documentation validation process.
 * Includes validation outcomes and recommendations.
 */
export interface IDocumentationValidationResult {
  /** Result identifier */
  id: string;

  /** Validation rule identifier */
  ruleId: string;

  /** Validation status */
  status: 'passed' | 'failed' | 'warning';

  /** Validation message */
  message: string;

  /** Validation details */
  details?: Record<string, any>;

  /** Validation timestamp */
  timestamp: string;

  /** Validation recommendations */
  recommendations?: string[];
} 