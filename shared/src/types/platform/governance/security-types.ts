/**
 * @file Security Types for Governance Rule Management
 * @filepath shared/src/types/platform/governance/security-types.ts
 * @reference G-TSK-07.SUB-07.2.TYPES
 * @template templates/contexts/foundation-context/governance/security-types.template
 * @tier T2
 * @context foundation-context
 * @category Foundation
 * @created 2025-01-27
 * @modified 2025-07-04
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-009-template-security-architecture
 * @governance-dcr DCR-foundation-008-template-security-standards
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @enables server/src/platform/governance/management-configuration/GovernanceRuleTemplateSecurity
 * @enables server/src/platform/governance/management-configuration/GovernanceRuleCSRFManager
 * @enables server/src/platform/governance/management-configuration/GovernanceRuleSecurityPolicy
 * @enables server/src/platform/governance/management-configuration/GovernanceRuleInputValidator
 * @related-contexts foundation-context, governance-context, security-context
 * @governance-impact framework-foundation, security-architecture
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type governance-security-types
 * @lifecycle-stage implementation
 * @testing-status type-validated
 * @deployment-ready true
 * @monitoring-enabled false
 * @documentation docs/contexts/foundation-context/governance/security-types.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 */

/**
 * Security validation result type
 */
export type TSecurityValidationResult = {
  isSecure: boolean;
  threats: string[];
  warnings: string[];
  recommendedActions: string[];
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  scanTimestamp: Date;
  validationContext: string;
  threatCategories: string[];
  securityScore: number; // 0-100
  complianceStatus: 'compliant' | 'non-compliant' | 'partial';
};

/**
 * CSRF token type
 */
export type TCSRFToken = {
  token: string;
  sessionId: string;
  action: string;
  expires: Date;
  created: Date;
  used: boolean;
  ipAddress?: string;
  userAgent?: string;
  context: string;
  nonce: string;
};

/**
 * Security configuration type
 */
export type TSecurityConfig = {
  xssProtection: boolean;
  csrfProtection: boolean;
  contentSecurityPolicy: boolean;
  inputSanitization: boolean;
  threatDetection: boolean;
  auditLogging: boolean;
  maxTokenAge: number; // in milliseconds
  tokenCleanupInterval: number; // in milliseconds
  maxFailedAttempts: number;
  lockoutDuration: number; // in milliseconds
  securityHeaders: Record<string, string>;
  allowedOrigins: string[];
  blockedPatterns: string[];
};

/**
 * Security violation type
 */
export type TSecurityViolation = {
  violationId: string;
  type: 'xss' | 'csrf' | 'injection' | 'policy' | 'unauthorized' | 'brute_force' | 'malformed_input';
  severity: 'low' | 'medium' | 'high' | 'critical';
  source: string;
  description: string;
  timestamp: Date;
  blocked: boolean;
  actionTaken: string;
  clientInfo?: {
    ip: string;
    userAgent: string;
    sessionId?: string;
    userId?: string;
  };
  requestInfo?: {
    method: string;
    url: string;
    headers: Record<string, string>;
    body?: string;
  };
  context: string;
  riskScore: number; // 0-100
};

/**
 * Injection threat type
 */
export type TInjectionThreat = {
  threatId: string;
  type: 'sql' | 'xss' | 'template' | 'command' | 'code' | 'ldap' | 'xpath';
  pattern: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  detectedAt: Date;
  input: string;
  sanitizedInput?: string;
  blocked: boolean;
  confidence: number; // 0-100
};

/**
 * Data schema validation type
 */
export type TDataSchema = {
  schemaId: string;
  version: string;
  fields: TSchemaField[];
  required: string[];
  additionalProperties: boolean;
  maxProperties?: number;
  minProperties?: number;
  validationRules: TValidationRule[];
};

/**
 * Schema field type
 */
export type TSchemaField = {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'array' | 'object' | 'date' | 'email' | 'url';
  required: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: string;
  format?: string;
  enum?: any[];
  default?: any;
  description?: string;
  sanitize?: boolean;
  validation?: TValidationRule[];
};

/**
 * Validation rule type
 */
export type TValidationRule = {
  ruleId: string;
  name: string;
  type: 'format' | 'length' | 'range' | 'pattern' | 'custom';
  parameters: Record<string, any>;
  errorMessage: string;
  severity: 'error' | 'warning' | 'info';
  enabled: boolean;
};

/**
 * Security audit log entry type
 */
export type TSecurityAuditLogEntry = {
  logId: string;
  timestamp: Date;
  eventType: 'access' | 'violation' | 'configuration' | 'authentication' | 'authorization' | 'data_access';
  userId?: string;
  sessionId?: string;
  action: string;
  resource: string;
  result: 'success' | 'failure' | 'blocked' | 'warning';
  details: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  correlationId?: string;
};

/**
 * Security policy definition type
 */
export type TSecurityPolicyDefinition = {
  policyId: string;
  name: string;
  version: string;
  description: string;
  enabled: boolean;
  rules: TSecurityPolicyRule[];
  enforcement: 'block' | 'warn' | 'log' | 'audit';
  scope: 'global' | 'template' | 'api' | 'ui' | 'data';
  priority: number;
  createdAt: Date;
  updatedAt: Date;
  author: string;
};

/**
 * Security policy rule type
 */
export type TSecurityPolicyRule = {
  ruleId: string;
  name: string;
  condition: string;
  action: 'allow' | 'deny' | 'sanitize' | 'log' | 'alert';
  parameters: Record<string, any>;
  enabled: boolean;
  priority: number;
  description?: string;
};

/**
 * Template security context type
 */
export type TTemplateSecurityContext = {
  templateId: string;
  securityLevel: 'minimal' | 'standard' | 'strict' | 'paranoid';
  allowedHelpers: string[];
  blockedHelpers: string[];
  allowedVariables: string[];
  blockedVariables: string[];
  sanitizationRules: TSanitizationRule[];
  validationRules: TValidationRule[];
  cspDirectives: Record<string, string>;
  trustedSources: string[];
};

/**
 * Sanitization rule type
 */
export type TSanitizationRule = {
  ruleId: string;
  name: string;
  type: 'html' | 'javascript' | 'css' | 'url' | 'sql' | 'regex' | 'custom';
  pattern: string;
  replacement: string;
  flags: string;
  enabled: boolean;
  priority: number;
  description?: string;
};

/**
 * Security metrics type
 */
export type TSecurityMetrics = {
  timestamp: Date;
  period: 'hour' | 'day' | 'week' | 'month';
  totalRequests: number;
  blockedRequests: number;
  threatsDetected: number;
  violationsLogged: number;
  securityScore: number; // 0-100
  threatsByType: Record<string, number>;
  violationsByType: Record<string, number>;
  topThreats: string[];
  riskTrends: TRiskTrend[];
};

/**
 * Risk trend type
 */
export type TRiskTrend = {
  timestamp: Date;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  threatCount: number;
  violationCount: number;
  securityScore: number;
  primaryThreats: string[];
};

/**
 * Enhanced template render result with security
 */
export type TTemplateRenderResult = {
  templateId: string;
  renderId: string;
  output: string;
  renderTime: number;
  dataContext: Record<string, any>;
  errors: string[];
  warnings: string[];
  securityHeaders: Record<string, string>;
  metadata: {
    templateVersion: string;
    renderEngine: string;
    variablesUsed: string[];
    helpersUsed: string[];
    partialsUsed: string[];
    conditionalBranches: string[];
    loopIterations: number;
    securityLevel: 'raw' | 'sanitized' | 'secure';
    xssProtected: boolean;
    csrfTokenIncluded?: string;
    threatsDetected: number;
    violationsBlocked: number;
    securityScore: number;
    sanitizationApplied: string[];
    validationsPassed: number;
    validationsFailed: number;
  };
};

/**
 * Security service interface base
 */
export interface ISecurityService {
  getSecurityConfig(): TSecurityConfig;
  updateSecurityConfig(config: Partial<TSecurityConfig>): Promise<void>;
  validateSecurityCompliance(): Promise<boolean>;
  generateSecurityReport(): Promise<TSecurityMetrics>;
  auditSecurityEvent(event: TSecurityAuditLogEntry): Promise<void>;
}

/**
 * Template security service interface
 */
export interface ITemplateSecurity extends ISecurityService {
  validateTemplateSecurity(source: string): Promise<TSecurityValidationResult>;
  sanitizeTemplateOutput(output: string): Promise<string>;
  generateCSPHeaders(context: TTemplateSecurityContext): Promise<Record<string, string>>;
  detectThreats(input: string): Promise<TInjectionThreat[]>;
  applySecurityPolicy(templateId: string, policy: TSecurityPolicyDefinition): Promise<void>;
}

/**
 * CSRF management service interface
 */
export interface ICSRFManager extends ISecurityService {
  generateToken(sessionId: string, action: string, context: string): Promise<string>;
  validateToken(token: string, sessionId: string, action: string, context: string): Promise<boolean>;
  revokeToken(token: string): Promise<void>;
  cleanupExpiredTokens(): Promise<number>;
  getTokenForTemplate(templateId: string, sessionId: string): Promise<string>;
  validateTokenForTemplate(token: string, templateId: string, sessionId: string): Promise<boolean>;
}

/**
 * Security policy service interface
 */
export interface ISecurityPolicy extends ISecurityService {
  enforceXSSProtection(content: string, context: TTemplateSecurityContext): Promise<string>;
  validateContentSecurityPolicy(headers: Record<string, string>): Promise<boolean>;
  generateSecurityHeaders(context: 'template' | 'api' | 'ui'): Promise<Record<string, string>>;
  auditSecurityViolation(violation: TSecurityViolation): Promise<void>;
  applySecurityPolicy(policyId: string, resource: string): Promise<TSecurityPolicyDefinition>;
  evaluateSecurityRisk(context: string, data: any): Promise<number>;
}

/**
 * Input validation service interface
 */
export interface IInputValidator extends ISecurityService {
  validateTemplateInput(input: string, schema?: TDataSchema): Promise<TSecurityValidationResult>;
  sanitizeUserInput(input: any, rules?: TSanitizationRule[]): Promise<any>;
  validateDataSchema(data: any, schema: TDataSchema): Promise<TSecurityValidationResult>;
  detectInjectionAttempts(input: string): Promise<TInjectionThreat[]>;
  applyInputSanitization(input: string, rules: TSanitizationRule[]): Promise<string>;
  validateInputCompliance(input: any, rules: TValidationRule[]): Promise<TSecurityValidationResult>;
} 