/**
 * @file DocumentationGeneratorTypes
 * @filepath shared/src/types/platform/governance/management-configuration/documentation-generator-types.ts
 * @reference documentation-generator-types
 * @tier T2
 * @context foundation-context
 * @category Foundation
 * @created 2025-07-04
 * @modified 2025-07-04 22:21:57 +03
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "E.Z. Consultancy"
 * @governance-adr ADR-foundation-009-hybrid-security-architecture
 * @governance-dcr DCR-foundation-008-security-governance-foundation
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on governance-rule-configuration-manager, governance-rule-template-engine
 * @enables governance-rule-environment-manager, governance-analytics-reporting
 * @related-contexts foundation-context, governance-context
 * @governance-impact governance-documentation, compliance-reporting
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type governance-types
 * @lifecycle-stage implementation
 * @testing-status unit-tested, integration-tested
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/contexts/foundation-context/types/documentation-generator-types.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 */

/**
 * 📚 DOCUMENTATION GENERATOR DATA TYPE
 * 
 * Core data structure for documentation generator operations.
 * Contains documentation-specific properties and service metadata.
 */
export type TDocumentationGeneratorData = {
  /** Service identifier */
  serviceId: string;

  /** Service name */
  serviceName: string;

  /** Service version */
  version: string;

  /** Service status */
  status: string;

  /** Service timestamp */
  timestamp: string;
  /** Documentation generator identifier */
  generatorId: string;

  /** Documentation generation context */
  context: string;

  /** Documentation format */
  format: TDocumentationFormat;

  /** Documentation sections */
  sections: TDocumentationSection[];

  /** Documentation metadata */
  metadata: TDocumentationMetadata;

  /** Documentation validation status */
  validationStatus: TDocumentationValidationStatus;

  /** Documentation audit trail */
  auditTrail: TDocumentationAuditTrail[];

  /** Documentation performance metrics */
  performanceMetrics: TDocumentationPerformanceMetrics;

  /** Documentation cache information */
  cacheInfo: TDocumentationCacheInfo;
};

/**
 * 📄 DOCUMENTATION FORMAT TYPE
 * 
 * Supported documentation output formats.
 * Defines the available formats for documentation generation.
 */
export type TDocumentationFormat = 
  | 'markdown'
  | 'html'
  | 'pdf'
  | 'json'
  | 'xml'
  | 'docx'
  | 'latex'
  | 'confluence'
  | 'notion'
  | 'custom';

/**
 * 📋 DOCUMENTATION SECTION TYPE
 * 
 * Structure for individual documentation sections.
 * Defines the content and metadata for each section.
 */
export type TDocumentationSection = {
  /** Section identifier */
  id: string;

  /** Section title */
  title: string;

  /** Section content */
  content: string;

  /** Section order */
  order: number;

  /** Section type */
  type: TDocumentationSectionType;

  /** Section metadata */
  metadata: TDocumentationSectionMetadata;

  /** Section subsections */
  subsections: TDocumentationSubsection[];

  /** Section validation status */
  validationStatus: TDocumentationValidationStatus;

  /** Section generation timestamp */
  generatedAt: string;
};

/**
 * 📝 DOCUMENTATION SECTION TYPE ENUM
 * 
 * Types of documentation sections available.
 * Defines the categorization of documentation content.
 */
export type TDocumentationSectionType = 
  | 'overview'
  | 'rules'
  | 'configuration'
  | 'compliance'
  | 'api'
  | 'troubleshooting'
  | 'appendices'
  | 'glossary'
  | 'references'
  | 'changelog'
  | 'migration'
  | 'examples'
  | 'custom';

/**
 * 📊 DOCUMENTATION SECTION METADATA TYPE
 * 
 * Metadata for documentation sections including
 * section information, configuration, and validation details.
 */
export type TDocumentationSectionMetadata = {
  /** Section author */
  author?: string;

  /** Section version */
  version?: string;

  /** Section creation timestamp */
  created?: string;

  /** Section last modified timestamp */
  modified?: string;

  /** Section description */
  description?: string;

  /** Section tags */
  tags?: string[];

  /** Section category */
  category?: string;

  /** Section priority */
  priority?: number;

  /** Section dependencies */
  dependencies?: string[];

  /** Section cross-references */
  crossReferences?: string[];

  /** Section validation rules */
  validationRules?: string[];

  /** Section custom properties */
  customProperties?: Record<string, any>;
};

/**
 * 📄 DOCUMENTATION SUBSECTION TYPE
 * 
 * Structure for documentation subsections.
 * Provides hierarchical organization of documentation content.
 */
export type TDocumentationSubsection = {
  /** Subsection identifier */
  id: string;

  /** Subsection title */
  title: string;

  /** Subsection content */
  content: string;

  /** Subsection order */
  order: number;

  /** Subsection level */
  level: number;

  /** Subsection metadata */
  metadata: TDocumentationSectionMetadata;

  /** Subsection validation status */
  validationStatus: TDocumentationValidationStatus;
};

/**
 * 📊 DOCUMENTATION METADATA TYPE
 * 
 * Comprehensive metadata for documentation output.
 * Includes generation details, validation status, and compliance information.
 */
export type TDocumentationMetadata = {
  /** Documentation identifier */
  id: string;

  /** Documentation title */
  title: string;

  /** Documentation description */
  description?: string;

  /** Documentation version */
  version: string;

  /** Documentation author */
  author: string;

  /** Documentation authority */
  authority: string;

  /** Documentation creation timestamp */
  created: string;

  /** Documentation last modified timestamp */
  modified: string;

  /** Documentation generation timestamp */
  generated: string;

  /** Documentation format */
  format: TDocumentationFormat;

  /** Documentation language */
  language: string;

  /** Documentation compliance level */
  complianceLevel: string;

  /** Documentation security level */
  securityLevel: string;

  /** Documentation classification */
  classification: string;

  /** Documentation tags */
  tags: string[];

  /** Documentation categories */
  categories: string[];

  /** Documentation keywords */
  keywords: string[];

  /** Documentation cross-references */
  crossReferences: string[];

  /** Documentation dependencies */
  dependencies: string[];

  /** Documentation validation status */
  validationStatus: TDocumentationValidationStatus;

  /** Documentation audit information */
  auditInfo: TDocumentationAuditInfo;

  /** Documentation performance metrics */
  performanceMetrics: TDocumentationPerformanceMetrics;

  /** Documentation custom properties */
  customProperties: Record<string, any>;
};

/**
 * ✅ DOCUMENTATION VALIDATION STATUS TYPE
 * 
 * Status of documentation validation process.
 * Indicates the validation state and compliance level.
 */
export type TDocumentationValidationStatus = 
  | 'pending'
  | 'validating'
  | 'passed'
  | 'failed'
  | 'warning'
  | 'error'
  | 'skipped'
  | 'unknown';

/**
 * 📋 DOCUMENTATION AUDIT INFO TYPE
 * 
 * Audit information for documentation generation.
 * Tracks audit status, timestamps, and compliance details.
 */
export type TDocumentationAuditInfo = {
  /** Last audit timestamp */
  lastAudit?: string;

  /** Audit status */
  auditStatus?: string;

  /** Next audit timestamp */
  nextAudit?: string;

  /** Audit findings */
  auditFindings?: string[];

  /** Audit recommendations */
  auditRecommendations?: string[];

  /** Audit compliance score */
  complianceScore?: number;

  /** Audit authority */
  auditAuthority?: string;

  /** Audit trail entries */
  auditTrail?: TDocumentationAuditTrail[];
};

/**
 * 📊 DOCUMENTATION AUDIT TRAIL TYPE
 * 
 * Individual audit trail entry for documentation operations.
 * Tracks all documentation generation and modification activities.
 */
export type TDocumentationAuditTrail = {
  /** Audit entry identifier */
  id: string;

  /** Operation identifier */
  operationId: string;

  /** Audit timestamp */
  timestamp: string;

  /** Audit action */
  action: string;

  /** Context identifier */
  contextId?: string;

  /** Documentation format */
  format?: string;

  /** User identifier */
  user: string;

  /** Authority validator */
  authority: string;

  /** Audit details */
  details?: Record<string, any>;

  /** Audit result */
  result?: string;

  /** Audit message */
  message?: string;

  /** Audit severity */
  severity?: 'info' | 'warning' | 'error' | 'critical';

  /** Audit source */
  source?: string;

  /** Audit correlation ID */
  correlationId?: string;
};

/**
 * 📈 DOCUMENTATION PERFORMANCE METRICS TYPE
 * 
 * Performance metrics for documentation generation operations.
 * Tracks generation time, resource usage, and optimization metrics.
 */
export type TDocumentationPerformanceMetrics = {
  /** Generation start timestamp */
  startTime: string;

  /** Generation end timestamp */
  endTime: string;

  /** Generation duration in milliseconds */
  duration: number;

  /** Memory usage in bytes */
  memoryUsage: number;

  /** CPU usage percentage */
  cpuUsage: number;

  /** Number of sections generated */
  sectionsGenerated: number;

  /** Number of rules processed */
  rulesProcessed: number;

  /** Number of templates applied */
  templatesApplied: number;

  /** Number of validations performed */
  validationsPerformed: number;

  /** Cache hit ratio */
  cacheHitRatio: number;

  /** Processing rate (items per second) */
  processingRate: number;

  /** Throughput (bytes per second) */
  throughput: number;

  /** Error count */
  errorCount: number;

  /** Warning count */
  warningCount: number;

  /** Optimization score */
  optimizationScore: number;
};

/**
 * 💾 DOCUMENTATION CACHE INFO TYPE
 * 
 * Cache information for documentation generation.
 * Tracks cache usage, hit rates, and optimization metrics.
 */
export type TDocumentationCacheInfo = {
  /** Cache enabled flag */
  enabled: boolean;

  /** Cache size in bytes */
  size: number;

  /** Cache entry count */
  entryCount: number;

  /** Cache hit count */
  hitCount: number;

  /** Cache miss count */
  missCount: number;

  /** Cache hit ratio */
  hitRatio: number;

  /** Cache eviction count */
  evictionCount: number;

  /** Cache last cleanup timestamp */
  lastCleanup: string;

  /** Cache TTL in seconds */
  ttl: number;

  /** Cache max size in bytes */
  maxSize: number;

  /** Cache strategy */
  strategy: 'lru' | 'fifo' | 'lfu' | 'ttl' | 'custom';

  /** Cache custom properties */
  customProperties: Record<string, any>;
};

/**
 * ⚙️ DOCUMENTATION GENERATION OPTIONS TYPE
 * 
 * Configuration options for documentation generation.
 * Defines generation parameters, formatting options, and output settings.
 */
export type TDocumentationGenerationOptions = {
  /** Output format */
  format?: TDocumentationFormat;

  /** Include table of contents */
  includeTableOfContents?: boolean;

  /** Sections to include */
  includeSections?: {
    overview?: boolean;
    rules?: boolean;
    configuration?: boolean;
    compliance?: boolean;
    api?: boolean;
    troubleshooting?: boolean;
    appendices?: boolean;
    glossary?: boolean;
    references?: boolean;
    changelog?: boolean;
    migration?: boolean;
    examples?: boolean;
  };

  /** Template identifier */
  templateId?: string;

  /** Custom template */
  customTemplate?: string;

  /** Output language */
  language?: string;

  /** Security level */
  securityLevel?: string;

  /** Authority level */
  authorityLevel?: string;

  /** Validation level */
  validationLevel?: 'none' | 'basic' | 'standard' | 'strict' | 'enterprise';

  /** Cache enabled */
  cacheEnabled?: boolean;

  /** Cache TTL in seconds */
  cacheTtl?: number;

  /** Batch processing */
  batchProcessing?: boolean;

  /** Concurrency level */
  concurrency?: number;

  /** Parallel processing */
  parallelProcessing?: boolean;

  /** Performance optimization */
  performanceOptimization?: boolean;

  /** Error handling strategy */
  errorHandling?: 'strict' | 'lenient' | 'skip' | 'log';

  /** Output compression */
  compression?: boolean;

  /** Output encoding */
  encoding?: string;

  /** Custom properties */
  customProperties?: Record<string, any>;

  /** Callback functions */
  callbacks?: {
    onStart?: (context: any) => void;
    onProgress?: (progress: number) => void;
    onComplete?: (result: any) => void;
    onError?: (error: any) => void;
  };
};

/**
 * 🎨 DOCUMENTATION TEMPLATE OPTIONS TYPE
 * 
 * Options for documentation template processing.
 * Defines template configuration, variables, and customization settings.
 */
export type TDocumentationTemplateOptions = {
  /** Template identifier */
  templateId: string;

  /** Template variables */
  variables: Record<string, any>;

  /** Template format */
  format: TDocumentationFormat;

  /** Template language */
  language?: string;

  /** Template theme */
  theme?: string;

  /** Template style */
  style?: string;

  /** Template custom CSS */
  customCss?: string;

  /** Template custom JavaScript */
  customJs?: string;

  /** Template preprocessing */
  preprocessing?: boolean;

  /** Template postprocessing */
  postprocessing?: boolean;

  /** Template validation */
  validation?: boolean;

  /** Template optimization */
  optimization?: boolean;

  /** Template caching */
  caching?: boolean;

  /** Template custom properties */
  customProperties?: Record<string, any>;
};

/**
 * 📊 DOCUMENTATION STATISTICS TYPE
 * 
 * Statistics and metrics for documentation generation.
 * Provides insights into generation performance and quality.
 */
export type TDocumentationStatistics = {
  /** Total documents generated */
  totalDocuments: number;

  /** Total sections generated */
  totalSections: number;

  /** Total rules processed */
  totalRules: number;

  /** Total templates used */
  totalTemplates: number;

  /** Total validation errors */
  totalValidationErrors: number;

  /** Total validation warnings */
  totalValidationWarnings: number;

  /** Average generation time */
  averageGenerationTime: number;

  /** Average document size */
  averageDocumentSize: number;

  /** Cache hit ratio */
  cacheHitRatio: number;

  /** Success rate */
  successRate: number;

  /** Error rate */
  errorRate: number;

  /** Performance score */
  performanceScore: number;

  /** Quality score */
  qualityScore: number;

  /** Compliance score */
  complianceScore: number;

  /** Statistics timestamp */
  timestamp: string;

  /** Statistics period */
  period: string;

  /** Statistics custom metrics */
  customMetrics: Record<string, number>;
};

/**
 * 🔍 DOCUMENTATION SEARCH OPTIONS TYPE
 * 
 * Options for searching and filtering documentation.
 * Defines search criteria, filters, and result formatting.
 */
export type TDocumentationSearchOptions = {
  /** Search query */
  query?: string;

  /** Search filters */
  filters?: {
    format?: TDocumentationFormat[];
    sections?: TDocumentationSectionType[];
    tags?: string[];
    categories?: string[];
    authors?: string[];
    dateRange?: {
      start: string;
      end: string;
    };
    validationStatus?: TDocumentationValidationStatus[];
    complianceLevel?: string[];
    securityLevel?: string[];
  };

  /** Search sorting */
  sorting?: {
    field: string;
    order: 'asc' | 'desc';
  };

  /** Search pagination */
  pagination?: {
    page: number;
    limit: number;
  };

  /** Search result format */
  resultFormat?: 'summary' | 'detailed' | 'full';

  /** Search highlighting */
  highlighting?: boolean;

  /** Search fuzzy matching */
  fuzzyMatching?: boolean;

  /** Search custom properties */
  customProperties?: Record<string, any>;
}; 