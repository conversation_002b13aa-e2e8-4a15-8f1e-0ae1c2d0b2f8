/**
 * @file Rule Management Types
 * @filepath shared/src/types/platform/governance/rule-management-types.ts
 * @task-id G-TSK-01.SUB-01.1.TYP-01
 * @component rule-management-types
 * @reference foundation-context.GOVERNANCE.002
 * @template on-demand-creation-with-latest-standards
 * @tier T1
 * @context foundation-context
 * @category Foundation
 * @created 2025-06-24
 * @modified 2025-06-24 18:33:55 +03
 * 
 * @description
 * Comprehensive type definitions for enterprise governance rule management system providing:
 * - Rule execution context types for environment state management
 * - Validator factory types for rule validation configuration
 * - Rule engine core types for central processing management
 * - Compliance checker types for governance validation structures
 * - Authority validator types for permission and access control
 * - Cache manager types for performance optimization structures
 * - Metrics collector types for monitoring and analytics data
 * - Audit logger types for comprehensive tracking and reporting
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-governance-types
 * @governance-dcr DCR-foundation-001-governance-development
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on foundation-context.TRACKING.tracking-types
 * @enables governance-interfaces, governance-rule-management-system
 * @related-contexts foundation-context, enterprise-context
 * @governance-impact framework-foundation, governance-infrastructure
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type type-definitions
 * @lifecycle-stage implementation
 * @testing-status type-validated
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/governance/types/rule-management-types.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 * 
 * 📝 VERSION HISTORY
 * @version-history
 * v1.0.0 (2025-06-24) - Initial comprehensive rule management types with enterprise governance capabilities
 */

// ============================================================================
// CORE GOVERNANCE RULE TYPES
// ============================================================================

/**
 * Governance rule type enumeration
 */
export type TGovernanceRuleType = 
  | 'authority-validation'
  | 'compliance-check'
  | 'security-policy'
  | 'access-control'
  | 'data-governance'
  | 'process-compliance'
  | 'audit-requirement'
  | 'quality-standard'
  | 'performance-benchmark'
  | 'integration-policy'
  | 'custom-rule';

/**
 * Governance rule severity levels
 */
export type TGovernanceRuleSeverity = 'info' | 'warning' | 'error' | 'critical';

/**
 * Rule execution status
 */
export type TRuleExecutionStatus = 'pending' | 'running' | 'completed' | 'failed' | 'timeout' | 'cancelled';

/**
 * Rule criteria definition
 */
export type TRuleCriteria = {
  /** Criteria type */
  type: 'condition' | 'validation' | 'policy' | 'constraint';
  
  /** Criteria expression */
  expression: string;
  
  /** Expected values */
  expectedValues: unknown[];
  
  /** Comparison operators */
  operators: string[];
  
  /** Criteria weight */
  weight: number;
};

/**
 * Rule action definition
 */
export type TRuleAction = {
  /** Action type */
  type: 'log' | 'alert' | 'block' | 'redirect' | 'transform' | 'approve' | 'reject';
  
  /** Action configuration */
  configuration: Record<string, unknown>;
  
  /** Action priority */
  priority: number;
  
  /** Conditional execution */
  conditions?: TRuleCriteria[];
};

/**
 * Retry configuration
 */
export type TRetryConfiguration = {
  /** Maximum retry attempts */
  maxAttempts: number;
  
  /** Retry delay in milliseconds */
  delayMs: number;
  
  /** Backoff strategy */
  backoffStrategy: 'fixed' | 'linear' | 'exponential';
  
  /** Maximum delay */
  maxDelayMs: number;
};

/**
 * Governance rule definition
 */
export type TGovernanceRule = {
  /** Unique rule identifier */
  ruleId: string;
  
  /** Rule name and description */
  name: string;
  description: string;
  
  /** Rule type and category */
  type: TGovernanceRuleType;
  category: string;
  
  /** Rule severity and priority */
  severity: TGovernanceRuleSeverity;
  priority: number;
  
  /** Rule configuration */
  configuration: {
    /** Rule parameters */
    parameters: Record<string, unknown>;
    
    /** Validation criteria */
    criteria: TRuleCriteria;
    
    /** Actions to take */
    actions: TRuleAction[];
    
    /** Rule dependencies */
    dependencies: string[];
  };
  
  /** Rule metadata */
  metadata: {
    /** Rule version */
    version: string;
    
    /** Author information */
    author: string;
    
    /** Creation timestamp */
    createdAt: Date;
    
    /** Last modified timestamp */
    modifiedAt: Date;
    
    /** Rule tags */
    tags: string[];
    
    /** Documentation references */
    documentation: string[];
  };
  
  /** Rule status and lifecycle */
  status: {
    /** Current status */
    current: 'draft' | 'active' | 'inactive' | 'deprecated';
    
    /** Activation date */
    activatedAt?: Date;
    
    /** Expiration date */
    expiresAt?: Date;
    
    /** Rule effectiveness */
    effectiveness: number;
  };
};

/**
 * Governance rule set
 */
export type TGovernanceRuleSet = {
  /** Rule set identifier */
  ruleSetId: string;
  
  /** Rule set name and description */
  name: string;
  description: string;
  
  /** Rules in the set */
  rules: TGovernanceRule[];
  
  /** Rule set configuration */
  configuration: {
    /** Execution order */
    executionOrder: 'sequential' | 'parallel' | 'priority-based';
    
    /** Failure handling */
    failureHandling: 'stop-on-first' | 'continue-on-failure' | 'best-effort';
    
    /** Timeout settings */
    timeout: number;
    
    /** Retry configuration */
    retryConfig: TRetryConfiguration;
  };
  
  /** Rule set metadata */
  metadata: {
    version: string;
    author: string;
    createdAt: Date;
    modifiedAt: Date;
    tags: string[];
    executionOrder?: 'sequential' | 'parallel' | 'priority-based';
  };
};

// ============================================================================
// EXECUTION CONTEXT TYPES
// ============================================================================

/**
 * Execution environment configuration
 */
export type TExecutionEnvironment = {
  /** Environment identifier */
  environmentId: string;
  
  /** Environment name */
  name: string;
  
  /** Environment type */
  type: 'development' | 'testing' | 'staging' | 'production';
  
  /** Environment configuration */
  configuration: {
    /** Resource limits */
    resourceLimits: {
      memory: number;
      cpu: number;
      storage: number;
      networkBandwidth: number;
    };
    
    /** Security settings */
    security: {
      encryption: boolean;
      authentication: boolean;
      authorization: boolean;
      auditLogging: boolean;
    };
    
    /** Performance settings */
    performance: {
      caching: boolean;
      compression: boolean;
      optimization: boolean;
    };
  };
  
  /** Environment variables */
  variables: Record<string, string>;
  
  /** Environment metadata */
  metadata: {
    version: string;
    createdAt: Date;
    modifiedAt: Date;
    owner: string;
  };
};

/**
 * Execution context
 */
export type TExecutionContext = {
  /** Context identifier */
  contextId: string;
  
  /** Context name */
  name: string;
  
  /** Associated rule set */
  ruleSetId: string;
  
  /** Execution environment */
  environment: TExecutionEnvironment;
  
  /** Context state */
  state: {
    /** Current status */
    status: 'initializing' | 'ready' | 'executing' | 'completed' | 'failed' | 'cleanup';
    
    /** Start timestamp */
    startedAt: Date;
    
    /** Completion timestamp */
    completedAt?: Date;
    
    /** Progress percentage */
    progress: number;
    
    /** Current step */
    currentStep?: string;
  };
  
  /** Context data */
  data: {
    /** Input data */
    input: Record<string, unknown>;
    
    /** Output data */
    output: Record<string, unknown>;
    
    /** Intermediate results */
    intermediate: Record<string, unknown>;
    
    /** Context variables */
    variables: Record<string, unknown>;
  };
  
  /** Context configuration */
  configuration: {
    /** Execution timeout */
    timeout: number;
    
    /** Error handling */
    errorHandling: 'strict' | 'lenient' | 'best-effort';
    
    /** Logging level */
    loggingLevel: 'debug' | 'info' | 'warn' | 'error';
    
    /** Monitoring enabled */
    monitoring: boolean;
  };
  
  /** Context metadata */
  metadata: Record<string, unknown>;
};

/**
 * Context status information
 */
export type TContextStatus = {
  /** Context identifier */
  contextId: string;
  
  /** Current status */
  status: TExecutionContext['state']['status'];
  
  /** Health indicators */
  health: {
    /** Overall health score */
    score: number;
    
    /** Resource utilization */
    resourceUtilization: {
      memory: number;
      cpu: number;
      storage: number;
    };
    
    /** Performance metrics */
    performance: {
      throughput: number;
      latency: number;
      errorRate: number;
    };
  };
  
  /** Status timestamp */
  timestamp: Date;
  
  /** Status metadata */
  metadata: Record<string, unknown>;
};

// ============================================================================
// VALIDATOR CONFIGURATION TYPES
// ============================================================================

/**
 * Validator configuration
 */
export type TValidatorConfiguration = {
  /** Validator type */
  type: TGovernanceRuleType;
  
  /** Validator parameters */
  parameters: {
    /** Validation strictness */
    strictness: 'strict' | 'moderate' | 'lenient';
    
    /** Timeout settings */
    timeout: number;
    
    /** Retry configuration */
    retry: TRetryConfiguration;
    
    /** Custom validation rules */
    customRules: Record<string, unknown>;
  };
  
  /** Validator options */
  options: {
    /** Enable caching */
    caching: boolean;
    
    /** Enable parallel validation */
    parallel: boolean;
    
    /** Maximum concurrent validations */
    maxConcurrent: number;
    
    /** Enable detailed logging */
    detailedLogging: boolean;
  };
  
  /** Validator metadata */
  metadata: Record<string, unknown>;
};

// ============================================================================
// RULE EXECUTION RESULT TYPES
// ============================================================================

/**
 * Rule validation result
 */
export type TRuleValidationResult = {
  /** Validation identifier */
  validationId: string;
  
  /** Rule criteria identifier */
  criteriaId: string;
  
  /** Validation status */
  status: 'passed' | 'failed' | 'warning' | 'skipped';
  
  /** Validation score */
  score: number;
  
  /** Validation message */
  message: string;
  
  /** Validation details */
  details: {
    /** Expected value */
    expected: unknown;
    
    /** Actual value */
    actual: unknown;
    
    /** Comparison operator */
    operator: string;
    
    /** Additional context */
    context: Record<string, unknown>;
  };
  
  /** Validation timestamp */
  timestamp: Date;
};

/**
 * Rule action result
 */
export type TRuleActionResult = {
  /** Action identifier */
  actionId: string;
  
  /** Action type */
  type: TRuleAction['type'];
  
  /** Action status */
  status: 'executed' | 'failed' | 'skipped' | 'pending';
  
  /** Action result */
  result: {
    /** Success indicator */
    success: boolean;
    
    /** Result data */
    data: unknown;
    
    /** Side effects */
    sideEffects: string[];
  };
  
  /** Action timing */
  timing: {
    startedAt: Date;
    endedAt?: Date;
    durationMs: number;
  };
  
  /** Error information */
  error?: {
    code: string;
    message: string;
    details: Record<string, unknown>;
  };
};

/**
 * Rule execution result
 */
export type TRuleExecutionResult = {
  /** Execution identifier */
  executionId: string;
  
  /** Rule identifier */
  ruleId: string;
  
  /** Rule name */
  ruleName?: string;
  
  /** Context identifier */
  contextId: string;
  
  /** Execution status */
  status: TRuleExecutionStatus;
  
  /** Status message */
  message?: string;
  
  /** Execution timing */
  timing: {
    /** Start timestamp */
    startedAt: Date;
    
    /** End timestamp */
    endedAt?: Date;
    
    /** Duration in milliseconds */
    durationMs: number;
  };
  
  /** Execution results */
  result: {
    /** Success indicator */
    success: boolean;
    
    /** Result data */
    data: unknown;
    
    /** Validation results */
    validations: TRuleValidationResult[];
    
    /** Actions taken */
    actions: TRuleActionResult[];
  };
  
  /** Error information */
  error?: {
    /** Error code */
    code: string;
    
    /** Error message */
    message: string;
    
    /** Error details */
    details: Record<string, unknown>;
    
    /** Stack trace */
    stackTrace?: string;
  };
  
  /** Execution metadata */
  metadata?: {
    /** Execution environment */
    environment: string;
    
    /** Rule type */
    ruleType?: string;
    
    /** Resource usage */
    resourceUsage?: {
      memory: number;
      cpu: number;
      networkCalls: number;
    };
    
    /** Performance metrics */
    performance: {
      throughput: number;
      latency: number;
    };
  };
};

// ============================================================================
// PROCESSING TYPES
// ============================================================================

/**
 * Processing context
 */
export type TProcessingContext = {
  /** Processing identifier */
  processingId: string;
  
  /** Target data to process */
  targetData: Record<string, unknown>;
  
  /** Processing configuration */
  configuration: {
    /** Processing mode */
    mode: 'validate' | 'transform' | 'audit' | 'enforce';
    
    /** Parallel processing */
    parallel: boolean;
    
    /** Batch size */
    batchSize: number;
    
    /** Timeout per rule */
    ruleTimeout: number;
  };
  
  /** Processing metadata */
  metadata: Record<string, unknown>;
};

/**
 * Rule processing result
 */
export type TRuleProcessingResult = {
  /** Processing identifier */
  processingId: string;
  
  /** Rule set identifier */
  ruleSetId: string;
  
  /** Processing status */
  status: 'completed' | 'partial' | 'failed' | 'cancelled';
  
  /** Overall results */
  overall: {
    /** Total rules processed */
    totalRules: number;
    
    /** Successful rules */
    successfulRules: number;
    
    /** Failed rules */
    failedRules: number;
    
    /** Overall score */
    score: number;
    
    /** Compliance status */
    compliant: boolean;
  };
  
  /** Individual rule results */
  ruleResults: TRuleExecutionResult[];
  
  /** Processing timing */
  timing: {
    startedAt: Date;
    endedAt?: Date;
    durationMs: number;
  };
  
  /** Processing metadata */
  metadata: Record<string, unknown>;
};

// ============================================================================
// COMPLIANCE TYPES
// ============================================================================

/**
 * Compliance standard types
 */
export type TComplianceStandard = 
  | 'sarbanes-oxley' 
  | 'gdpr' 
  | 'hipaa' 
  | 'pci-dss' 
  | 'iso-27001' 
  | 'nist-framework' 
  | 'custom-standard';

/**
 * Compliance level enumeration
 */
export type TComplianceLevel = 
  | 'excellent' 
  | 'good' 
  | 'adequate' 
  | 'poor' 
  | 'failing';

/**
 * Compliance result type
 */
export type TComplianceResult = {
  checkId: string;
  targetId: string;
  timestamp: Date;
  overallScore: number;
  level: TComplianceLevel;
  compliant: boolean;
  standards: TComplianceStandard[];
  violations: string[];
  recommendations: string[];
  metadata: Record<string, unknown>;
};

/**
 * Compliance report type
 */
export type TComplianceReport = {
  reportId: string;
  generatedAt: Date;
  standards: TComplianceStandard[];
  summary: {
    totalTargets: number;
    overallScore: number;
    totalViolations: number;
    complianceRate: number;
  };
  results: TComplianceResult[];
  metadata: Record<string, unknown>;
};

// ============================================================================
// AUTHORITY TYPES
// ============================================================================

/**
 * Authority level enumeration
 */
export type TAuthorityLevel = 
  | 'system-authority'
  | 'enterprise-authority'
  | 'architectural-authority'
  | 'security-authority'
  | 'experience-authority'
  | 'operational-authority'
  | 'user-authority';

/**
 * Permission scope enumeration
 */
export type TPermissionScope = 
  | 'global'
  | 'context'
  | 'component'
  | 'resource'
  | 'action';

/**
 * Authority validation result type
 */
export type TAuthorityValidationResult = {
  contextId: string;
  requesterId: string;
  granted: boolean;
  authorityLevel: TAuthorityLevel;
  permissionScope: TPermissionScope;
  reason: string;
  restrictions: string[];
  validUntil: Date;
  timestamp: Date;
  metadata: Record<string, unknown>;
};

// ============================================================================
// CACHE TYPES
// ============================================================================

/**
 * Cache configuration type
 */
export type TCacheConfiguration = {
  maxSize: number;
  ttl: number;
  evictionStrategy: string;
  compressionEnabled: boolean;
  encryptionEnabled: boolean;
  distributedEnabled: boolean;
};

// ============================================================================
// METRICS TYPES
// ============================================================================

/**
 * Rule metrics type
 */
export type TRuleMetrics = {
  ruleId: string;
  executionCount: number;
  successCount: number;
  failureCount: number;
  avgExecutionTime: number;
  lastExecuted: Date;
  metadata: Record<string, unknown>;
};

/**
 * Metrics configuration type
 */
export type TMetricsConfiguration = {
  enabled: boolean;
  retentionDays: number;
  aggregationInterval: number;
  alertThresholds: Record<string, number>;
  customMetrics: Record<string, unknown>;
};

// ============================================================================
// AUDIT TYPES
// ============================================================================

/**
 * Audit entry type
 */
export type TAuditEntry = {
  level: string;
  category: string;
  source: string;
  action: string;
  actor: {
    userId?: string;
    systemId?: string;
    service: string;
    ipAddress?: string;
    userAgent?: string;
  };
  target: {
    type: string;
    id: string;
    name?: string;
    metadata?: Record<string, unknown>;
  };
  result: {
    status: 'success' | 'failure' | 'partial';
    code?: string;
    message?: string;
    details?: Record<string, unknown>;
  };
  context: {
    sessionId?: string;
    requestId?: string;
    correlationId?: string;
    environment: string;
    version: string;
  };
  security: {
    classification: 'public' | 'internal' | 'confidential' | 'restricted';
    sensitivity: 'low' | 'medium' | 'high' | 'critical';
    retention: string;
  };
  tags: string[];
  metadata: Record<string, unknown>;
};

/**
 * Audit configuration type
 */
export type TAuditConfiguration = {
  enabled: boolean;
  retentionDays: number;
  encryptionEnabled: boolean;
  compressionEnabled: boolean;
  realTimeAlertsEnabled: boolean;
  complianceStandards: string[];
};

// ============================================================================
// LEGACY PLACEHOLDER TYPES
// ============================================================================

// ============================================================================
// COMPLIANCE INFRASTRUCTURE TYPES
// ============================================================================

/**
 * Governance context type
 */
export type TGovernanceContext = {
  contextId: string;
  name: string;
  type: string;
  metadata: Record<string, unknown>;
};

/**
 * Governance component type
 */
export type TGovernanceComponent = {
  componentId: string;
  name: string;
  type: string;
  version: string;
  metadata: Record<string, unknown>;
};

/**
 * Governance service type
 */
export type TGovernanceService = {
  serviceId: string;
  name: string;
  version: string;
  type: string;
  status: string;
  metadata: Record<string, unknown>;
};

/**
 * Compliance checker data type
 */
export type TComplianceCheckerData = {
  checkerId: string;
  name: string;
  configuration: Record<string, unknown>;
  metrics: Record<string, unknown>;
  metadata: Record<string, unknown>;
};

/**
 * Compliance framework data type
 */
export type TComplianceFrameworkData = {
  frameworkId: string;
  name: string;
  policies: Record<string, unknown>[];
  configuration: Record<string, unknown>;
  metadata: Record<string, unknown>;
};

/**
 * Quality framework data type
 */
export type TQualityFrameworkData = {
  frameworkId: string;
  name: string;
  metrics: TQualityMetric[];
  benchmarks: TQualityBenchmark[];
  assessments: TQualityAssessment[];
  metadata: Record<string, unknown>;
};

/**
 * Quality metrics type
 */
export type TQualityMetrics = {
  componentId: string;
  assessmentId: string;
  timestamp: Date;
  overallScore: number;
  dimensions: {
    reliability: number;
    performance: number;
    security: number;
    maintainability: number;
    usability: number;
    compliance: number;
  };
  benchmarks: TQualityBenchmark[];
  violations: string[];
  recommendations: string[];
  trends: {
    improvement: number;
    degradation: number;
    stability: number;
  };
  metadata: Record<string, unknown>;
};

/**
 * Quality metric type
 */
export type TQualityMetric = {
  metricId: string;
  name: string;
  value: number;
  threshold: number;
  unit: string;
  timestamp: Date;
};

/**
 * Quality benchmark type
 */
export type TQualityBenchmark = {
  benchmarkId: string;
  name: string;
  description: string;
  target: number;
  actual: number;
  status: 'met' | 'not-met' | 'exceeded';
  metadata: Record<string, unknown>;
};

/**
 * Quality assessment type
 */
export type TQualityAssessment = {
  assessmentId: string;
  componentId: string;
  timestamp: Date;
  score: number;
  status: 'passed' | 'failed' | 'warning';
  findings: string[];
  recommendations: string[];
  metadata: Record<string, unknown>;
};

// ============================================================================
// PLACEHOLDER TYPES (TO BE EXPANDED)
// ============================================================================

export type TComplianceRequirements = {
  standards: TComplianceStandard[];
  thresholds: Record<string, number>;
  metadata: Record<string, unknown>;
};
export type TComplianceScope = Record<string, unknown>;
export type TScheduleConfiguration = Record<string, unknown>;
export type TGovernanceData = Record<string, unknown>;
export type TGovernanceAction = Record<string, unknown>;
export type TAuthorityContext = Record<string, unknown>;
export type TAuthoritySubject = Record<string, unknown>;
export type TGovernanceOperation = Record<string, unknown>;
export type TGovernanceResource = Record<string, unknown>;
export type TPermissionResult = Record<string, unknown>;
export type TAuthorityHierarchyResult = Record<string, unknown>;
export type TPermissionSet = Record<string, unknown>;
export type TCacheStatistics = Record<string, unknown>;
export type TRuleExecutionMetrics = Record<string, unknown>;
export type TComplianceMetrics = Record<string, unknown>;
export type TTimeRange = Record<string, unknown>;
export type TRulePerformanceMetrics = Record<string, unknown>;
export type TSystemMetrics = Record<string, unknown>;
export type TMetricsDashboard = Record<string, unknown>;
export type TExportFormat = string;
export type TExportResult = Record<string, unknown>;
export type TGovernanceAuditEvent = Record<string, unknown>;
export type TRuleExecutionAudit = Record<string, unknown>;
export type TComplianceCheckAudit = Record<string, unknown>;
export type TAuthorityValidationAudit = Record<string, unknown>;
export type TAuditTrailFilters = Record<string, unknown>;
export type TAuditTrailResult = Record<string, unknown>;
export type TAuditReportConfig = Record<string, unknown>;
export type TAuditReport = Record<string, unknown>;
export type TAuditRetentionPolicy = Record<string, unknown>;

// ============================================================================
// TESTING FRAMEWORK TYPES
// ============================================================================

/**
 * Testing framework data type
 */
export type TTestingFrameworkData = {
  frameworkId: string;
  componentId: string;
  testSuiteId: string;
  testType: TTestType;
  configuration: TTestConfiguration;
  executionContext: TTestExecutionContext;
  results: TTestResults;
  metadata: Record<string, unknown>;
};

/**
 * Test type enumeration
 */
export type TTestType = 
  | 'unit'
  | 'integration'
  | 'compliance'
  | 'performance'
  | 'load'
  | 'security'
  | 'regression'
  | 'acceptance';

/**
 * Test configuration type
 */
export type TTestConfiguration = {
  configurationId: string;
  testType: TTestType;
  parameters: Record<string, unknown>;
  environment: TTestEnvironment;
  timeout: number;
  retries: number;
  parallel: boolean;
  coverage: boolean;
  reporting: boolean;
  metadata: Record<string, unknown>;
};

/**
 * Test environment type
 */
export type TTestEnvironment = {
  environmentId: string;
  name: string;
  type: 'development' | 'testing' | 'staging' | 'production';
  configuration: Record<string, unknown>;
  resources: TTestResources;
  constraints: Record<string, unknown>;
  metadata: Record<string, unknown>;
};

/**
 * Test resources type
 */
export type TTestResources = {
  cpu: string;
  memory: string;
  storage: string;
  network: string;
  instances: number;
  timeout: number;
  metadata: Record<string, unknown>;
};

/**
 * Test execution context type
 */
export type TTestExecutionContext = {
  contextId: string;
  testSuiteId: string;
  environment: TTestEnvironment;
  configuration: TTestConfiguration;
  startTime: Date;
  endTime?: Date;
  status: TTestExecutionStatus;
  progress: number;
  metadata: Record<string, unknown>;
};

/**
 * Test execution status enumeration
 */
export type TTestExecutionStatus = 
  | 'pending'
  | 'running'
  | 'completed'
  | 'failed'
  | 'cancelled'
  | 'timeout';

/**
 * Test suite type
 */
export type TTestSuite = {
  suiteId: string;
  name: string;
  description: string;
  componentId: string;
  testCases: TTestCase[];
  configuration: TTestConfiguration;
  dependencies: string[];
  tags: string[];
  metadata: Record<string, unknown>;
};

/**
 * Test case type
 */
export type TTestCase = {
  caseId: string;
  name: string;
  description: string;
  testType: TTestType;
  preconditions: string[];
  steps: TTestStep[];
  expectedResults: string[];
  assertions: TTestAssertion[];
  timeout: number;
  priority: TTestPriority;
  tags: string[];
  metadata: Record<string, unknown>;
};

/**
 * Test step type
 */
export type TTestStep = {
  stepId: string;
  name: string;
  description: string;
  action: string;
  parameters: Record<string, unknown>;
  expectedResult: string;
  timeout: number;
  metadata: Record<string, unknown>;
};

/**
 * Test assertion type
 */
export type TTestAssertion = {
  assertionId: string;
  name: string;
  type: TAssertionType;
  condition: string;
  expectedValue: unknown;
  actualValue?: unknown;
  passed?: boolean;
  message?: string;
  metadata: Record<string, unknown>;
};

/**
 * Test assertion type enumeration
 */
export type TAssertionType = 
  | 'equals'
  | 'not-equals'
  | 'greater-than'
  | 'less-than'
  | 'contains'
  | 'matches'
  | 'exists'
  | 'not-exists'
  | 'custom';

/**
 * Test priority enumeration
 */
export type TTestPriority = 
  | 'low'
  | 'medium'
  | 'high'
  | 'critical';

/**
 * Test results type
 */
export type TTestResults = {
  resultsId: string;
  testSuiteId: string;
  executionId: string;
  timestamp: Date;
  duration: number;
  status: TTestExecutionStatus;
  summary: TTestSummary;
  testCaseResults: TTestCaseResult[];
  coverage: TTestCoverage;
  performance: TTestPerformance;
  errors: TTestError[];
  warnings: TTestWarning[];
  metadata: Record<string, unknown>;
};

/**
 * Test summary type
 */
export type TTestSummary = {
  totalTests: number;
  passedTests: number;
  failedTests: number;
  skippedTests: number;
  errorTests: number;
  successRate: number;
  averageDuration: number;
  totalDuration: number;
  coverage: number;
};

/**
 * Test case result type
 */
export type TTestCaseResult = {
  caseId: string;
  name: string;
  status: TTestCaseStatus;
  duration: number;
  startTime: Date;
  endTime: Date;
  assertions: TTestAssertionResult[];
  errors: TTestError[];
  warnings: TTestWarning[];
  output: string;
  metadata: Record<string, unknown>;
};

/**
 * Test case status enumeration
 */
export type TTestCaseStatus = 
  | 'passed'
  | 'failed'
  | 'skipped'
  | 'error';

/**
 * Test assertion result type
 */
export type TTestAssertionResult = {
  assertionId: string;
  name: string;
  passed: boolean;
  expectedValue: unknown;
  actualValue: unknown;
  message: string;
  duration: number;
  metadata: Record<string, unknown>;
};

/**
 * Test coverage type
 */
export type TTestCoverage = {
  coverageId: string;
  testSuiteId: string;
  timestamp: Date;
  overall: TCoverageMetrics;
  components: TCoverageComponent[];
  uncoveredLines: TCoverageGap[];
  metadata: Record<string, unknown>;
};

/**
 * Coverage metrics type
 */
export type TCoverageMetrics = {
  linesCovered: number;
  totalLines: number;
  coveragePercentage: number;
  branchesCovered: number;
  totalBranches: number;
  branchCoveragePercentage: number;
  functionsCovered: number;
  totalFunctions: number;
  functionCoveragePercentage: number;
};

/**
 * Coverage component type
 */
export type TCoverageComponent = {
  componentId: string;
  name: string;
  filePath: string;
  metrics: TCoverageMetrics;
  coveredLines: number[];
  uncoveredLines: number[];
  metadata: Record<string, unknown>;
};

/**
 * Coverage gap type
 */
export type TCoverageGap = {
  componentId: string;
  filePath: string;
  lineNumber: number;
  lineContent: string;
  reason: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  recommendation: string;
};

/**
 * Test performance type
 */
export type TTestPerformance = {
  performanceId: string;
  testSuiteId: string;
  timestamp: Date;
  metrics: Record<string, number>;
  benchmarks: Record<string, number>;
  trends: Record<string, number>;
  bottlenecks: string[];
  metadata: Record<string, unknown>;
};

/**
 * Test error type
 */
export type TTestError = {
  errorId: string;
  testCaseId?: string;
  type: string;
  message: string;
  stackTrace: string;
  timestamp: Date;
  severity: 'low' | 'medium' | 'high' | 'critical';
  metadata: Record<string, unknown>;
};

/**
 * Test warning type
 */
export type TTestWarning = {
  warningId: string;
  testCaseId?: string;
  type: string;
  message: string;
  timestamp: Date;
  severity: 'low' | 'medium' | 'high';
  metadata: Record<string, unknown>;
};

/**
 * Load test configuration type
 */
export type TLoadTestConfiguration = {
  configurationId: string;
  name: string;
  description: string;
  targetUrl: string;
  loadPattern: TLoadPattern;
  duration: number;
  users: TUserConfiguration;
  scenarios: TLoadScenario[];
  monitoring: TLoadMonitoring;
  thresholds: TLoadThresholds;
  metadata: Record<string, unknown>;
};

/**
 * Load pattern type
 */
export type TLoadPattern = {
  type: 'constant' | 'ramp-up' | 'spike' | 'stress' | 'volume';
  initialLoad: number;
  maxLoad: number;
  rampUpTime: number;
  sustainTime: number;
  rampDownTime: number;
  spikes: TLoadSpike[];
};

/**
 * Load spike type
 */
export type TLoadSpike = {
  startTime: number;
  duration: number;
  intensity: number;
  description: string;
};

/**
 * User configuration type
 */
export type TUserConfiguration = {
  virtualUsers: number;
  userRampUp: number;
  userBehavior: TUserBehavior;
  thinkTime: number;
  sessionDuration: number;
  metadata: Record<string, unknown>;
};

/**
 * User behavior type
 */
export type TUserBehavior = {
  behaviorId: string;
  name: string;
  actions: TUserAction[];
  weights: number[];
  loops: number;
  metadata: Record<string, unknown>;
};

/**
 * User action type
 */
export type TUserAction = {
  actionId: string;
  name: string;
  type: 'request' | 'wait' | 'think' | 'validate';
  parameters: Record<string, unknown>;
  timeout: number;
  retries: number;
  metadata: Record<string, unknown>;
};

/**
 * Load scenario type
 */
export type TLoadScenario = {
  scenarioId: string;
  name: string;
  description: string;
  weight: number;
  steps: TLoadStep[];
  validations: TLoadValidation[];
  metadata: Record<string, unknown>;
};

/**
 * Load step type
 */
export type TLoadStep = {
  stepId: string;
  name: string;
  action: TUserAction;
  expectedResponse: TExpectedResponse;
  validations: TLoadValidation[];
  metadata: Record<string, unknown>;
};

/**
 * Expected response type
 */
export type TExpectedResponse = {
  statusCode: number;
  responseTime: number;
  contentType: string;
  bodyContains: string[];
  headers: Record<string, string>;
  size: number;
};

/**
 * Load validation type
 */
export type TLoadValidation = {
  validationId: string;
  name: string;
  type: 'response-time' | 'status-code' | 'content' | 'header' | 'custom';
  condition: string;
  expectedValue: unknown;
  severity: 'low' | 'medium' | 'high' | 'critical';
  metadata: Record<string, unknown>;
};

/**
 * Load monitoring type
 */
export type TLoadMonitoring = {
  enabled: boolean;
  interval: number;
  metrics: string[];
  alerts: TLoadAlert[];
  reporting: TLoadReporting;
  metadata: Record<string, unknown>;
};

/**
 * Load alert type
 */
export type TLoadAlert = {
  alertId: string;
  name: string;
  condition: string;
  threshold: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  actions: string[];
  metadata: Record<string, unknown>;
};

/**
 * Load reporting type
 */
export type TLoadReporting = {
  enabled: boolean;
  format: string[];
  destination: string;
  frequency: number;
  includeGraphs: boolean;
  metadata: Record<string, unknown>;
};

/**
 * Load thresholds type
 */
export type TLoadThresholds = {
  responseTime: number;
  errorRate: number;
  throughput: number;
  cpuUtilization: number;
  memoryUtilization: number;
  diskUtilization: number;
  networkUtilization: number;
  metadata: Record<string, unknown>;
};

/**
 * Load test results type
 */
export type TLoadTestResults = {
  resultsId: string;
  testId: string;
  timestamp: Date;
  duration: number;
  status: TTestExecutionStatus;
  summary: TLoadTestSummary;
  metrics: TLoadTestMetrics;
  scenarios: TLoadScenarioResult[];
  errors: TLoadTestError[];
  warnings: TLoadTestWarning[];
  metadata: Record<string, unknown>;
};

/**
 * Load test summary type
 */
export type TLoadTestSummary = {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  errorRate: number;
  averageResponseTime: number;
  minResponseTime: number;
  maxResponseTime: number;
  throughput: number;
  concurrentUsers: number;
  dataTransferred: number;
};

/**
 * Load test metrics type
 */
export type TLoadTestMetrics = {
  responseTime: TResponseTimeMetrics;
  throughput: TThroughputMetrics;
  errors: Record<string, number>;
  resources: TResourceMetrics;
  network: TNetworkMetrics;
  metadata: Record<string, unknown>;
};

/**
 * Response time metrics type
 */
export type TResponseTimeMetrics = {
  average: number;
  median: number;
  p90: number;
  p95: number;
  p99: number;
  min: number;
  max: number;
  standardDeviation: number;
};

/**
 * Throughput metrics type
 */
export type TThroughputMetrics = {
  requestsPerSecond: number;
  bytesPerSecond: number;
  transactionsPerSecond: number;
  peak: number;
  average: number;
  minimum: number;
};

/**
 * Resource metrics type
 */
export type TResourceMetrics = {
  cpu: number;
  memory: number;
  disk: number;
  network: number;
  connections: number;
  threads: number;
};

/**
 * Network metrics type
 */
export type TNetworkMetrics = {
  bandwidth: number;
  latency: number;
  packetLoss: number;
  jitter: number;
  connectionTime: number;
  dnsLookupTime: number;
};

/**
 * Load scenario result type
 */
export type TLoadScenarioResult = {
  scenarioId: string;
  name: string;
  executions: number;
  successes: number;
  failures: number;
  averageResponseTime: number;
  throughput: number;
  errorRate: number;
  validationResults: TLoadValidationResult[];
  metadata: Record<string, unknown>;
};

/**
 * Load validation result type
 */
export type TLoadValidationResult = {
  validationId: string;
  name: string;
  passed: boolean;
  expectedValue: unknown;
  actualValue: unknown;
  message: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  metadata: Record<string, unknown>;
};

/**
 * Load test error type
 */
export type TLoadTestError = {
  errorId: string;
  scenarioId?: string;
  stepId?: string;
  type: string;
  message: string;
  timestamp: Date;
  frequency: number;
  impact: string;
  metadata: Record<string, unknown>;
};

/**
 * Load test warning type
 */
export type TLoadTestWarning = {
  warningId: string;
  scenarioId?: string;
  type: string;
  message: string;
  timestamp: Date;
  frequency: number;
  metadata: Record<string, unknown>;
};

/**
 * Coverage report type
 */
export type TCoverageReport = {
  reportId: string;
  testSuiteId: string;
  timestamp: Date;
  format: string;
  coverage: TTestCoverage;
  summary: TCoverageSummary;
  details: TCoverageDetail[];
  recommendations: TCoverageRecommendation[];
  metadata: Record<string, unknown>;
};

/**
 * Coverage summary type
 */
export type TCoverageSummary = {
  overallCoverage: number;
  lineCoverage: number;
  branchCoverage: number;
  functionCoverage: number;
  statementCoverage: number;
  uncoveredLines: number;
  totalLines: number;
  coverageGoal: number;
  goalMet: boolean;
};

/**
 * Coverage detail type
 */
export type TCoverageDetail = {
  componentId: string;
  filePath: string;
  coverage: TCoverageMetrics;
  uncoveredAreas: TCoverageArea[];
  recommendations: string[];
  metadata: Record<string, unknown>;
};

/**
 * Coverage area type
 */
export type TCoverageArea = {
  startLine: number;
  endLine: number;
  type: 'statement' | 'branch' | 'function' | 'line';
  reason: string;
  complexity: number;
  priority: 'low' | 'medium' | 'high' | 'critical';
};

/**
 * Coverage recommendation type
 */
export type TCoverageRecommendation = {
  recommendationId: string;
  type: 'test-case' | 'refactor' | 'documentation' | 'removal';
  priority: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  effort: 'low' | 'medium' | 'high';
  impact: 'low' | 'medium' | 'high';
  metadata: Record<string, unknown>;
};

/**
 * CI/CD pipeline configuration type
 */
export type TCIPipelineConfig = {
  configId: string;
  name: string;
  description: string;
  triggers: TTriggerConfig[];
  stages: TPipelineStage[];
  environment: TPipelineEnvironment;
  notifications: TNotificationConfig[];
  artifacts: TArtifactConfig[];
  metadata: Record<string, unknown>;
};

/**
 * Trigger configuration type
 */
export type TTriggerConfig = {
  triggerId: string;
  type: 'push' | 'pull-request' | 'schedule' | 'manual' | 'webhook';
  conditions: TTriggerCondition[];
  branches: string[];
  schedule?: string;
  metadata: Record<string, unknown>;
};

/**
 * Trigger condition type
 */
export type TTriggerCondition = {
  conditionId: string;
  type: 'file-change' | 'branch-pattern' | 'tag-pattern' | 'custom';
  pattern: string;
  exclude: string[];
  metadata: Record<string, unknown>;
};

/**
 * Pipeline stage type
 */
export type TPipelineStage = {
  stageId: string;
  name: string;
  description: string;
  order: number;
  parallel: boolean;
  jobs: TPipelineJob[];
  dependencies: string[];
  conditions: TStageCondition[];
  timeout: number;
  metadata: Record<string, unknown>;
};

/**
 * Pipeline job type
 */
export type TPipelineJob = {
  jobId: string;
  name: string;
  description: string;
  steps: TJobStep[];
  environment: TJobEnvironment;
  timeout: number;
  retries: number;
  continueOnError: boolean;
  metadata: Record<string, unknown>;
};

/**
 * Job step type
 */
export type TJobStep = {
  stepId: string;
  name: string;
  action: string;
  parameters: Record<string, unknown>;
  timeout: number;
  continueOnError: boolean;
  conditions: TStepCondition[];
  metadata: Record<string, unknown>;
};

/**
 * Job environment type
 */
export type TJobEnvironment = {
  environmentId: string;
  name: string;
  variables: Record<string, string>;
  secrets: string[];
  resources: TJobResources;
  metadata: Record<string, unknown>;
};

/**
 * Job resources type
 */
export type TJobResources = {
  cpu: string;
  memory: string;
  storage: string;
  timeout: number;
  instances: number;
  metadata: Record<string, unknown>;
};

/**
 * Stage condition type
 */
export type TStageCondition = {
  conditionId: string;
  type: 'success' | 'failure' | 'always' | 'custom';
  expression: string;
  metadata: Record<string, unknown>;
};

/**
 * Step condition type
 */
export type TStepCondition = {
  conditionId: string;
  type: 'success' | 'failure' | 'always' | 'custom';
  expression: string;
  metadata: Record<string, unknown>;
};

/**
 * Pipeline environment type
 */
export type TPipelineEnvironment = {
  environmentId: string;
  name: string;
  type: 'development' | 'testing' | 'staging' | 'production';
  variables: Record<string, string>;
  secrets: string[];
  approvals: TApprovalConfig[];
  metadata: Record<string, unknown>;
};

/**
 * Approval configuration type
 */
export type TApprovalConfig = {
  approvalId: string;
  type: 'manual' | 'automatic';
  approvers: string[];
  conditions: TApprovalCondition[];
  timeout: number;
  metadata: Record<string, unknown>;
};

/**
 * Approval condition type
 */
export type TApprovalCondition = {
  conditionId: string;
  type: 'all' | 'any' | 'count' | 'custom';
  threshold: number;
  expression: string;
  metadata: Record<string, unknown>;
};

/**
 * Notification configuration type
 */
export type TNotificationConfig = {
  notificationId: string;
  type: 'email' | 'slack' | 'teams' | 'webhook' | 'sms';
  recipients: string[];
  events: string[];
  template: string;
  conditions: TNotificationCondition[];
  metadata: Record<string, unknown>;
};

/**
 * Notification condition type
 */
export type TNotificationCondition = {
  conditionId: string;
  event: string;
  status: string;
  severity: string;
  metadata: Record<string, unknown>;
};

/**
 * Artifact configuration type
 */
export type TArtifactConfig = {
  artifactId: string;
  name: string;
  type: 'build' | 'test' | 'report' | 'package' | 'documentation';
  path: string;
  retention: number;
  compression: boolean;
  encryption: boolean;
  metadata: Record<string, unknown>;
};

/**
 * Performance test configuration type
 */
export type TPerformanceTestConfig = {
  configId: string;
  name: string;
  description: string;
  testType: 'load' | 'stress' | 'volume' | 'spike' | 'endurance';
  scenarios: TPerformanceScenario[];
  environment: TTestEnvironment;
  monitoring: TPerformanceMonitoring;
  thresholds: TPerformanceThresholds;
  reporting: TPerformanceReporting;
  metadata: Record<string, unknown>;
};

/**
 * Performance scenario type
 */
export type TPerformanceScenario = {
  scenarioId: string;
  name: string;
  description: string;
  load: TLoadConfiguration;
  duration: number;
  rampUp: number;
  rampDown: number;
  transactions: TTransaction[];
  validations: TPerformanceValidation[];
  metadata: Record<string, unknown>;
};

/**
 * Load configuration type
 */
export type TLoadConfiguration = {
  users: number;
  rampUpTime: number;
  sustainTime: number;
  rampDownTime: number;
  thinkTime: number;
  distribution: 'linear' | 'exponential' | 'logarithmic';
  metadata: Record<string, unknown>;
};

/**
 * Transaction type
 */
export type TTransaction = {
  transactionId: string;
  name: string;
  description: string;
  steps: TTransactionStep[];
  weight: number;
  timeout: number;
  retries: number;
  metadata: Record<string, unknown>;
};

/**
 * Transaction step type
 */
export type TTransactionStep = {
  stepId: string;
  name: string;
  action: string;
  parameters: Record<string, unknown>;
  validations: TStepValidation[];
  timeout: number;
  metadata: Record<string, unknown>;
};

/**
 * Step validation type
 */
export type TStepValidation = {
  validationId: string;
  type: 'response-time' | 'status' | 'content' | 'custom';
  condition: string;
  expectedValue: unknown;
  severity: 'low' | 'medium' | 'high' | 'critical';
  metadata: Record<string, unknown>;
};

/**
 * Performance validation type
 */
export type TPerformanceValidation = {
  validationId: string;
  name: string;
  metric: string;
  operator: 'lt' | 'lte' | 'gt' | 'gte' | 'eq' | 'neq';
  threshold: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  metadata: Record<string, unknown>;
};

/**
 * Performance monitoring type
 */
export type TPerformanceMonitoring = {
  enabled: boolean;
  interval: number;
  metrics: string[];
  systemMetrics: boolean;
  applicationMetrics: boolean;
  customMetrics: TCustomMetric[];
  alerts: TPerformanceAlert[];
  metadata: Record<string, unknown>;
};

/**
 * Custom metric type
 */
export type TCustomMetric = {
  metricId: string;
  name: string;
  description: string;
  query: string;
  unit: string;
  aggregation: 'sum' | 'avg' | 'min' | 'max' | 'count';
  metadata: Record<string, unknown>;
};

/**
 * Performance alert type
 */
export type TPerformanceAlert = {
  alertId: string;
  name: string;
  metric: string;
  condition: string;
  threshold: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  actions: string[];
  metadata: Record<string, unknown>;
};

/**
 * Performance thresholds type
 */
export type TPerformanceThresholds = {
  responseTime: number;
  throughput: number;
  errorRate: number;
  cpuUtilization: number;
  memoryUtilization: number;
  diskUtilization: number;
  networkUtilization: number;
  customThresholds: TCustomThreshold[];
  metadata: Record<string, unknown>;
};

/**
 * Custom threshold type
 */
export type TCustomThreshold = {
  thresholdId: string;
  name: string;
  metric: string;
  value: number;
  operator: 'lt' | 'lte' | 'gt' | 'gte' | 'eq' | 'neq';
  severity: 'low' | 'medium' | 'high' | 'critical';
  metadata: Record<string, unknown>;
};

/**
 * Performance reporting type
 */
export type TPerformanceReporting = {
  enabled: boolean;
  format: string[];
  destination: string;
  frequency: number;
  includeGraphs: boolean;
  includeRawData: boolean;
  compression: boolean;
  metadata: Record<string, unknown>;
};

/**
 * Performance test results type
 */
export type TPerformanceTestResults = {
  resultsId: string;
  testId: string;
  timestamp: Date;
  duration: number;
  status: TTestExecutionStatus;
  summary: TPerformanceTestSummary;
  scenarios: TPerformanceScenarioResult[];
  metrics: TPerformanceTestMetrics;
  violations: TPerformanceViolation[];
  recommendations: TPerformanceRecommendation[];
  metadata: Record<string, unknown>;
};

/**
 * Performance test summary type
 */
export type TPerformanceTestSummary = {
  totalTransactions: number;
  successfulTransactions: number;
  failedTransactions: number;
  averageResponseTime: number;
  throughput: number;
  errorRate: number;
  peakUsers: number;
  dataTransferred: number;
  passedValidations: number;
  failedValidations: number;
};

/**
 * Performance scenario result type
 */
export type TPerformanceScenarioResult = {
  scenarioId: string;
  name: string;
  status: TTestCaseStatus;
  duration: number;
  transactions: TTransactionResult[];
  metrics: TScenarioMetrics;
  validationResults: TPerformanceValidationResult[];
  metadata: Record<string, unknown>;
};

/**
 * Transaction result type
 */
export type TTransactionResult = {
  transactionId: string;
  name: string;
  executions: number;
  successes: number;
  failures: number;
  averageResponseTime: number;
  minResponseTime: number;
  maxResponseTime: number;
  throughput: number;
  errorRate: number;
  stepResults: TTransactionStepResult[];
  metadata: Record<string, unknown>;
};

/**
 * Transaction step result type
 */
export type TTransactionStepResult = {
  stepId: string;
  name: string;
  status: TTestCaseStatus;
  duration: number;
  validationResults: TStepValidationResult[];
  errors: TTestError[];
  metadata: Record<string, unknown>;
};

/**
 * Step validation result type
 */
export type TStepValidationResult = {
  validationId: string;
  name: string;
  passed: boolean;
  expectedValue: unknown;
  actualValue: unknown;
  message: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  metadata: Record<string, unknown>;
};

/**
 * Scenario metrics type
 */
export type TScenarioMetrics = {
  responseTime: TResponseTimeMetrics;
  throughput: TThroughputMetrics;
  errors: Record<string, number>;
  resources: TResourceMetrics;
  metadata: Record<string, unknown>;
};

/**
 * Performance test metrics type
 */
export type TPerformanceTestMetrics = {
  responseTime: TResponseTimeMetrics;
  throughput: TThroughputMetrics;
  errors: Record<string, number>;
  resources: TResourceMetrics;
  network: TNetworkMetrics;
  custom: TCustomMetricResult[];
  metadata: Record<string, unknown>;
};

/**
 * Custom metric result type
 */
export type TCustomMetricResult = {
  metricId: string;
  name: string;
  value: number;
  unit: string;
  timestamp: Date;
  metadata: Record<string, unknown>;
};

/**
 * Performance validation result type
 */
export type TPerformanceValidationResult = {
  validationId: string;
  name: string;
  metric: string;
  passed: boolean;
  threshold: number;
  actualValue: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  metadata: Record<string, unknown>;
};

/**
 * Performance violation type
 */
export type TPerformanceViolation = {
  violationId: string;
  type: 'threshold' | 'sla' | 'regression' | 'custom';
  metric: string;
  threshold: number;
  actualValue: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  impact: string;
  recommendation: string;
  metadata: Record<string, unknown>;
};

/**
 * Performance recommendation type
 */
export type TPerformanceRecommendation = {
  recommendationId: string;
  type: 'optimization' | 'scaling' | 'configuration' | 'architecture';
  priority: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  impact: string;
  effort: 'low' | 'medium' | 'high';
  expectedImprovement: number;
  metadata: Record<string, unknown>;
};

/**
 * Test history data type
 */
export type TTestHistoryData = {
  historyId: string;
  testSuiteId: string;
  timestamp: Date;
  results: TTestResults;
  environment: TTestEnvironment;
  configuration: TTestConfiguration;
  metadata: Record<string, unknown>;
};

/**
 * Test trend analysis type
 */
export type TTestTrendAnalysis = {
  analysisId: string;
  testSuiteId: string;
  timestamp: Date;
  timeRange: Record<string, unknown>;
  trends: TTestTrend[];
  predictions: TTestPrediction[];
  insights: TTestInsight[];
  recommendations: TTestTrendRecommendation[];
  metadata: Record<string, unknown>;
};

/**
 * Test trend type
 */
export type TTestTrend = {
  trendId: string;
  metric: string;
  direction: 'improving' | 'stable' | 'degrading';
  rate: number;
  confidence: number;
  significance: 'low' | 'medium' | 'high';
  startValue: number;
  endValue: number;
  metadata: Record<string, unknown>;
};

/**
 * Test prediction type
 */
export type TTestPrediction = {
  predictionId: string;
  metric: string;
  timeHorizon: number;
  predictedValue: number;
  confidence: number;
  lowerBound: number;
  upperBound: number;
  factors: string[];
  metadata: Record<string, unknown>;
};

/**
 * Test insight type
 */
export type TTestInsight = {
  insightId: string;
  type: 'anomaly' | 'pattern' | 'correlation' | 'regression';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  confidence: number;
  evidence: string[];
  impact: string;
  metadata: Record<string, unknown>;
};

/**
 * Test trend recommendation type
 */
export type TTestTrendRecommendation = {
  recommendationId: string;
  type: 'test-improvement' | 'coverage-increase' | 'performance-optimization' | 'process-change';
  priority: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  rationale: string;
  expectedImpact: string;
  effort: 'low' | 'medium' | 'high';
  timeline: string;
  metadata: Record<string, unknown>;
};

// Re-export commonly used types from tracking for convenience
export type {
  TAuthorityData,
  TGovernanceValidation,
  TAuditResult,
  TGovernanceStatus,
  TGovernanceViolation,
  TMetrics
} from '../tracking/tracking-types'; 