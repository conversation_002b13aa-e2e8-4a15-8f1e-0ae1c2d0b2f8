/**
 * @file Utility Error Types
 * @filepath shared/src/types/platform/tracking/utilities/error-types.ts
 * @reference foundation-context.TYPES.011
 * @template on-demand-creation-with-latest-standards
 * @tier T2
 * @context foundation-context
 * @category Foundation
 * @created 2025-06-23
 * @modified 2025-06-23
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-tracking-architecture
 * @governance-dcr DCR-foundation-001-tracking-development
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/types/platform/tracking/core/base-types
 * @enables server/src/platform/tracking/core-data
 * @related-contexts foundation-context
 * @governance-impact framework-foundation, tracking-dependency
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type error-types
 * @lifecycle-stage implementation
 * @testing-status unit-tested, integration-tested
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/contexts/foundation-context/types/error-types.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 */

/**
 * OA Framework Utility Error Types
 * 
 * Error handling and validation types for the tracking infrastructure
 * 
 * @authority President & CEO, E.Z. Consultancy
 * @compliance OA Framework Development Standards v21
 * @created 2025-06-23 17:02:07 +03
 */

// ============================================================================
// ERROR UTILITY TYPES
// ============================================================================

/**
 * Error handling utility type
 * Used for standardized error processing across tracking components
 */
export type TErrorHandler = {
  errorType: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  handler: string;
  retryConfig?: {
    maxRetries: number;
    backoffStrategy: 'linear' | 'exponential';
    baseDelay: number;
  };
  escalationRules?: Array<{
    condition: string;
    action: string;
    parameters: Record<string, any>;
  }>;
};

/**
 * Error reporting utility type
 * Used for error reporting and notification across the tracking system
 */
export type TErrorReporter = {
  reporterId: string;
  enabled: boolean;
  channels: Array<'log' | 'email' | 'webhook' | 'dashboard'>;
  filters: {
    severityThreshold: 'low' | 'medium' | 'high' | 'critical';
    componentFilters?: string[];
  };
  formatting: {
    includeStackTrace: boolean;
    includeContext: boolean;
    includeMetadata: boolean;
  };
}; 